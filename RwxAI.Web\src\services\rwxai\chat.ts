import { httpRequest } from '@/utils/request';
import { getApiPrefix } from '@/utils/api';
import { ResponseHandleResult } from '@/types/response';

const API_PREFIX: string = getApiPrefix();

/**
 * 聊天会话相关API
 */

// 获取所有聊天会话
export async function getChatSessions(): Promise<ResponseHandleResult<RwxAI.ChatSession[]>> {
  return httpRequest.get<RwxAI.ChatSession[]>(`${API_PREFIX}/Chat/sessions`, {
    showErrorNotification: true,
  });
}

// 创建新的聊天会话
export async function createChatSession(data: RwxAI.ChatSession): Promise<ResponseHandleResult<RwxAI.ChatSession>> {
  return httpRequest.post<RwxAI.ChatSession>(`${API_PREFIX}/Chat/sessions`, data, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: '创建聊天会话成功',
  });
}

// 获取指定ID的聊天会话详情
export async function getChatSessionById(id: string): Promise<ResponseHandleResult<RwxAI.ChatSession>> {
  return httpRequest.get<RwxAI.ChatSession>(`${API_PREFIX}/Chat/sessions/${id}`, {
    showErrorNotification: true,
  });
}

// 更新现有聊天会话的信息
export async function updateChatSession(id: string, data: RwxAI.ChatSession): Promise<ResponseHandleResult<RwxAI.ChatSession>> {
  return httpRequest.put<RwxAI.ChatSession>(`${API_PREFIX}/Chat/sessions/${id}`, data, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: '更新聊天会话成功',
  });
}

// 删除指定ID的聊天会话
export async function deleteChatSession(id: string): Promise<ResponseHandleResult<void>> {
  return httpRequest.delete<void>(`${API_PREFIX}/Chat/sessions/${id}`, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: '删除聊天会话成功',
  });
}

// 发送消息并获取AI回复
export async function sendMessage(sessionId: string, data: RwxAI.ChatMessageRequest): Promise<ResponseHandleResult<RwxAI.ChatMessage>> {
  return httpRequest.post<RwxAI.ChatMessage>(`${API_PREFIX}/Chat/sessions/${sessionId}/messages`, data, {
    showErrorNotification: true,
  });
}

// 发送消息并以流式方式获取AI回复
export async function sendMessageStream(sessionId: string, data: RwxAI.ChatMessageRequest): Promise<ResponseHandleResult<any>> {
  return httpRequest.post(`${API_PREFIX}/Chat/sessions/${sessionId}/messages/stream`, data, {
    showErrorNotification: true,
  });
}
