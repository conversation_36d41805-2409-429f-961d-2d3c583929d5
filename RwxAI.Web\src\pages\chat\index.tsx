import React, { useRef, useState } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import { ProTable, ActionType, ProColumns } from '@ant-design/pro-table';
import { Button, Space, Tag, message, Modal, Switch } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, MessageOutlined, EyeOutlined } from '@ant-design/icons';
import { useIntl, FormattedMessage, history } from '@umijs/max';
import { getChatSessions, deleteChatSession } from '@/services/rwxai';
import { createSimpleProTableRequest, defaultPaginationConfig } from '@/utils/pageDataHandler';
import SessionForm from './components/SessionForm';

const ChatSessionsPage: React.FC = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<RwxAI.ChatSession>();

  const handleDelete = async (record: RwxAI.ChatSession) => {
    Modal.confirm({
      title: intl.formatMessage({ id: 'pages.chat.delete.confirm.title' }),
      content: intl.formatMessage({ id: 'pages.chat.delete.confirm.content' }),
      onOk: async () => {
        try {
          await deleteChatSession(record.Id);
          message.success(intl.formatMessage({ id: 'pages.chat.delete.success' }));
          actionRef.current?.reload();
        } catch (error) {
          message.error(intl.formatMessage({ id: 'pages.chat.delete.error' }));
        }
      },
    });
  };

  const handleChat = (record: RwxAI.ChatSession) => {
    history.push(`/chat/session/${record.Id}`);
  };

  const columns: ProColumns<RwxAI.ChatSession>[] = [
    {
      title: <FormattedMessage id="pages.chat.table.name" />,
      dataIndex: 'Name',
      key: 'Name',
      ellipsis: true,
    },
    {
      title: <FormattedMessage id="pages.chat.table.model" />,
      dataIndex: ['Model', 'Name'],
      key: 'Model',
      ellipsis: true,
      hideInSearch: true,
      render: (_, record) => {
        // 优先使用 Model 对象中的信息
        if (record.Model?.Name) {
          return record.Model.Name;
        }
        if (record.Model?.DisplayName) {
          return record.Model.DisplayName;
        }
        
        // 如果没有 Model 对象，尝试使用 ModelId
        if (record.ModelId) {
          return record.ModelId;
        }
        
        // 最后的默认值
        return '未配置模型';
      }
    },
    {
      title: <FormattedMessage id="pages.chat.table.status" />,
      dataIndex: 'IsActive',
      key: 'IsActive',
      render: (_, record) => (
        <Tag color={record.IsActive ? 'green' : 'default'}>
          {record.IsActive ? 
            intl.formatMessage({ id: 'pages.chat.status.active' }) : 
            intl.formatMessage({ id: 'pages.chat.status.inactive' })
          }
        </Tag>
      ),
    },
    {
      title: <FormattedMessage id="pages.chat.table.messageCount" />,
      key: 'messageCount',
      hideInSearch: true,
      render: (_, record) => record.Messages?.length || 0,
    },
    {
      title: <FormattedMessage id="pages.chat.table.temperature" />,
      dataIndex: 'Temperature',
      key: 'Temperature',
      hideInSearch: true,
    },
    {
      title: <FormattedMessage id="pages.chat.table.maxTokens" />,
      dataIndex: 'MaxTokens',
      key: 'MaxTokens',
      hideInSearch: true,
    },
    {
      title: <FormattedMessage id="pages.chat.table.createdTime" />,
      dataIndex: 'CreatedTime',
      key: 'CreatedTime',
      valueType: 'dateTime',
      width: 180,
      hideInSearch: true,
    },
    {
      title: <FormattedMessage id="pages.chat.table.actions" />,
      key: 'actions',
      width: 200,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<MessageOutlined />}
            onClick={() => handleChat(record)}
          >
            <FormattedMessage id="pages.chat.actions.chat" />
          </Button>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => {
              setCurrentRecord(record);
              setEditModalVisible(true);
            }}
          >
            <FormattedMessage id="pages.chat.actions.edit" />
          </Button>
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          >
            <FormattedMessage id="pages.chat.actions.delete" />
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <PageContainer>
      <ProTable<RwxAI.ChatSession>
        headerTitle={intl.formatMessage({ id: 'pages.chat.title' })}
        actionRef={actionRef}
        rowKey="Id"
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            icon={<PlusOutlined />}
            onClick={() => setCreateModalVisible(true)}
          >
            <FormattedMessage id="pages.chat.actions.create" />
          </Button>,
        ]}
        request={createSimpleProTableRequest(getChatSessions)}
        columns={columns}
      />

      <SessionForm
        visible={createModalVisible}
        onVisibleChange={setCreateModalVisible}
        onSuccess={() => {
          actionRef.current?.reload();
          setCreateModalVisible(false);
        }}
      />

      <SessionForm
        visible={editModalVisible}
        onVisibleChange={setEditModalVisible}
        initialValues={currentRecord}
        onSuccess={() => {
          actionRef.current?.reload();
          setEditModalVisible(false);
        }}
      />
    </PageContainer>
  );
};

export default ChatSessionsPage;
