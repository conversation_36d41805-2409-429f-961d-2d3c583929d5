{"version": 3, "sources": ["src_pages_chat_chat-interface_tsx-async.15970108932533672692.hot-update.js", "src/pages/chat/chat-interface.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/chat/chat-interface.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='16922430050210772433';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/.umi/plugin-openapi/openapi.tsx\":[\"vendors\",\"src/.umi/plugin-openapi/openapi.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Admin.tsx\":[\"p__Admin\"],\"src/pages/Welcome.tsx\":[\"p__Welcome\"],\"src/pages/ai-models/index.tsx\":[\"common\",\"src/pages/ai-models/index.tsx\"],\"src/pages/api-test/index.tsx\":[\"common\",\"src/pages/api-test/index.tsx\"],\"src/pages/apps/index.tsx\":[\"common\",\"p__apps__index\"],\"src/pages/auth-test/index.tsx\":[\"common\",\"src/pages/auth-test/index.tsx\"],\"src/pages/chat/chat-interface.tsx\":[\"vendors\",\"common\",\"src/pages/chat/chat-interface.tsx\"],\"src/pages/chat/index.tsx\":[\"common\",\"p__chat__index\"],\"src/pages/chat/session/[id].tsx\":[\"common\",\"p__chat__session__id\"],\"src/pages/knowledge/[id]/files.tsx\":[\"common\",\"p__knowledge__id__files\"],\"src/pages/knowledge/index.tsx\":[\"common\",\"p__knowledge__index\"],\"src/pages/plugins/index.tsx\":[\"common\",\"p__plugins__index\"],\"src/pages/response-demo/index.tsx\":[\"src/pages/response-demo/index.tsx\"],\"src/pages/table-list/index.tsx\":[\"src/pages/table-list/index.tsx\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"],\"src/pages/user/register/index.tsx\":[\"common\",\"p__user__register__index\"]});;\r\n  },\r\n);\r\n", "import React, { useState, useRef } from 'react';\nimport { Modal, Button, Space } from 'antd';\nimport { useIntl, FormattedMessage } from '@umijs/max';\nimport ChatInterface from './components/ChatInterface';\nimport SessionForm from './components/SessionForm';\n\nconst ChatInterfacePage: React.FC = () => {\n  const intl = useIntl();\n  const [sessionFormVisible, setSessionFormVisible] = useState(false);\n  const [editingSession, setEditingSession] = useState<RwxAI.ChatSession | null>(null);\n\n  const handleCreateSession = () => {\n    setEditingSession(null);\n    setSessionFormVisible(true);\n  };\n\n  const handleEditSession = (session: RwxAI.ChatSession) => {\n    setEditingSession(session);\n    setSessionFormVisible(true);\n  };\n\n  const handleSessionFormClose = () => {\n    setSessionFormVisible(false);\n    setEditingSession(null);\n  };\n\n  const handleSessionFormSuccess = () => {\n    setSessionFormVisible(false);\n    setEditingSession(null);\n    // 这里可以触发聊天界面的刷新\n    window.location.reload();\n  };\n\n  return (\n    <div style={{ height: '100vh', overflow: 'hidden' }}>\n      <ChatInterface\n        onCreateSession={handleCreateSession}\n        onEditSession={handleEditSession}\n      />\n      \n      <Modal\n        title={editingSession ? '编辑会话' : '创建新会话'}\n        open={sessionFormVisible}\n        onCancel={handleSessionFormClose}\n        footer={null}\n        width={600}\n      >\n        <SessionForm\n          visible={sessionFormVisible}\n          onVisibleChange={setSessionFormVisible}\n          onSuccess={handleSessionFormSuccess}\n          initialValues={editingSession}\n        />\n      </Modal>\n    </div>\n  );\n};\n\nexport default ChatInterfacePage;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,qCACA;IACE,SAAS;;;;;;wCCuDb;;;2BAAA;;;;;;;oFA1DwC;yCACH;wCACK;2FAChB;yFACF;;;;;;;;;;YAExB,MAAM,oBAA8B;;gBACrB,IAAA,YAAO;gBACpB,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,IAAA,eAAQ,EAAC;gBAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,eAAQ,EAA2B;gBAE/E,MAAM,sBAAsB;oBAC1B,kBAAkB;oBAClB,sBAAsB;gBACxB;gBAEA,MAAM,oBAAoB,CAAC;oBACzB,kBAAkB;oBAClB,sBAAsB;gBACxB;gBAEA,MAAM,yBAAyB;oBAC7B,sBAAsB;oBACtB,kBAAkB;gBACpB;gBAEA,MAAM,2BAA2B;oBAC/B,sBAAsB;oBACtB,kBAAkB;oBAClB,gBAAgB;oBAChB,OAAO,QAAQ,CAAC,MAAM;gBACxB;gBAEA,qBACE,2BAAC;oBAAI,OAAO;wBAAE,QAAQ;wBAAS,UAAU;oBAAS;;sCAChD,2BAAC,sBAAa;4BACZ,iBAAiB;4BACjB,eAAe;;;;;;sCAGjB,2BAAC,WAAK;4BACJ,OAAO,iBAAiB,SAAS;4BACjC,MAAM;4BACN,UAAU;4BACV,QAAQ;4BACR,OAAO;sCAEP,cAAA,2BAAC,oBAAW;gCACV,SAAS;gCACT,iBAAiB;gCACjB,WAAW;gCACX,eAAe;;;;;;;;;;;;;;;;;YAKzB;eAlDM;;oBACS,YAAO;;;iBADhB;gBAoDN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;IDvDD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,uCAAsC;YAAC;YAAU;SAAsC;QAAC,qBAAoB;YAAC;SAAS;QAAC,uBAAsB;YAAC;SAAW;QAAC,yBAAwB;YAAC;SAAa;QAAC,iCAAgC;YAAC;YAAS;SAAgC;QAAC,gCAA+B;YAAC;YAAS;SAA+B;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,iCAAgC;YAAC;YAAS;SAAgC;QAAC,qCAAoC;YAAC;YAAU;YAAS;SAAoC;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,mCAAkC;YAAC;YAAS;SAAuB;QAAC,sCAAqC;YAAC;YAAS;SAA0B;QAAC,iCAAgC;YAAC;YAAS;SAAsB;QAAC,+BAA8B;YAAC;YAAS;SAAoB;QAAC,qCAAoC;YAAC;SAAoC;QAAC,kCAAiC;YAAC;SAAiC;QAAC,kCAAiC;YAAC;YAAS;SAAwB;QAAC,qCAAoC;YAAC;YAAS;SAA2B;IAAA;;AAC9zC"}