import { httpRequest } from '@/utils/request';
import { getApiPrefix } from '@/utils/api';
import { ResponseHandleResult } from '@/types/response';

const API_PREFIX: string = getApiPrefix();

/**
 * 应用管理相关API
 */

// 获取所有应用
export async function getApps(): Promise<ResponseHandleResult<RwxAI.App[]>> {
  return httpRequest.get<RwxAI.App[]>(`${API_PREFIX}/Apps`, {
    showErrorNotification: true,
  });
}

// 创建新应用
export async function createApp(data: RwxAI.App): Promise<ResponseHandleResult<RwxAI.App>> {
  return httpRequest.post<RwxAI.App>(`${API_PREFIX}/Apps`, data, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: '创建应用成功',
  });
}

// 获取当前用户的所有应用
export async function getMyApps(): Promise<ResponseHandleResult<RwxAI.App[]>> {
  return httpRequest.get<RwxAI.App[]>(`${API_PREFIX}/Apps/my`, {
    showErrorNotification: true,
  });
}

// 获取指定应用详情
export async function getAppById(id: string): Promise<ResponseHandleResult<RwxAI.App>> {
  return httpRequest.get<RwxAI.App>(`${API_PREFIX}/Apps/${id}`, {
    showErrorNotification: true,
  });
}

// 更新应用信息
export async function updateApp(id: string, data: RwxAI.App): Promise<ResponseHandleResult<RwxAI.App>> {
  return httpRequest.put<RwxAI.App>(`${API_PREFIX}/Apps/${id}`, data, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: '更新应用成功',
  });
}

// 删除应用
export async function deleteApp(id: string): Promise<ResponseHandleResult<void>> {
  return httpRequest.delete<void>(`${API_PREFIX}/Apps/${id}`, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: '删除应用成功',
  });
}

// 重新生成API密钥
export async function regenerateApiKeys(id: string): Promise<ResponseHandleResult<RwxAI.App>> {
  return httpRequest.post<RwxAI.App>(`${API_PREFIX}/Apps/${id}/regenerate-keys`, {}, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: 'API密钥重新生成成功',
  });
}

// 修改应用状态
export async function updateAppStatus(id: string, data: { isEnabled: boolean }): Promise<ResponseHandleResult<RwxAI.App>> {
  return httpRequest.patch<RwxAI.App>(`${API_PREFIX}/Apps/${id}/status`, data, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: '应用状态更新成功',
  });
}

// 重置API调用计数
export async function resetApiCalls(id: string): Promise<ResponseHandleResult<void>> {
  return httpRequest.post<void>(`${API_PREFIX}/Apps/${id}/reset-api-calls`, {}, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: 'API调用计数重置成功',
  });
}
