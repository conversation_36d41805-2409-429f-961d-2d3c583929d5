((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] || []).push([
        ['p__plugins__index'],
{ "src/pages/plugins/components/PluginDetail.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const PluginDetail = ({ visible, onVisibleChange, data })=>{
    var _data_Functions;
    _s();
    const intl = (0, _max.useIntl)();
    const getStatusTag = (status)=>{
        const statusMap = {
            'Active': {
                color: 'green',
                text: intl.formatMessage({
                    id: 'pages.plugins.status.active'
                })
            },
            'Inactive': {
                color: 'default',
                text: intl.formatMessage({
                    id: 'pages.plugins.status.inactive'
                })
            },
            'Error': {
                color: 'red',
                text: intl.formatMessage({
                    id: 'pages.plugins.status.error'
                })
            }
        };
        const statusInfo = statusMap[status || 'Inactive'];
        return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
            color: statusInfo.color,
            children: statusInfo.text
        }, void 0, false, {
            fileName: "src/pages/plugins/components/PluginDetail.tsx",
            lineNumber: 25,
            columnNumber: 12
        }, this);
    };
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
        title: intl.formatMessage({
            id: 'pages.plugins.detail.title'
        }),
        open: visible,
        onCancel: ()=>onVisibleChange(false),
        footer: null,
        width: 800,
        children: data && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions, {
            column: 2,
            bordered: true,
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.plugins.detail.name"
                    }, void 0, false, {
                        fileName: "src/pages/plugins/components/PluginDetail.tsx",
                        lineNumber: 39,
                        columnNumber: 20
                    }, void 0),
                    span: 2,
                    children: data.Name
                }, void 0, false, {
                    fileName: "src/pages/plugins/components/PluginDetail.tsx",
                    lineNumber: 38,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.plugins.detail.description"
                    }, void 0, false, {
                        fileName: "src/pages/plugins/components/PluginDetail.tsx",
                        lineNumber: 46,
                        columnNumber: 20
                    }, void 0),
                    span: 2,
                    children: data.Description || '-'
                }, void 0, false, {
                    fileName: "src/pages/plugins/components/PluginDetail.tsx",
                    lineNumber: 45,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.plugins.detail.version"
                    }, void 0, false, {
                        fileName: "src/pages/plugins/components/PluginDetail.tsx",
                        lineNumber: 53,
                        columnNumber: 20
                    }, void 0),
                    children: data.Version
                }, void 0, false, {
                    fileName: "src/pages/plugins/components/PluginDetail.tsx",
                    lineNumber: 52,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.plugins.detail.author"
                    }, void 0, false, {
                        fileName: "src/pages/plugins/components/PluginDetail.tsx",
                        lineNumber: 59,
                        columnNumber: 20
                    }, void 0),
                    children: data.Author || '-'
                }, void 0, false, {
                    fileName: "src/pages/plugins/components/PluginDetail.tsx",
                    lineNumber: 58,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.plugins.detail.status"
                    }, void 0, false, {
                        fileName: "src/pages/plugins/components/PluginDetail.tsx",
                        lineNumber: 65,
                        columnNumber: 20
                    }, void 0),
                    children: getStatusTag(data.Status)
                }, void 0, false, {
                    fileName: "src/pages/plugins/components/PluginDetail.tsx",
                    lineNumber: 64,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.plugins.detail.enabled"
                    }, void 0, false, {
                        fileName: "src/pages/plugins/components/PluginDetail.tsx",
                        lineNumber: 71,
                        columnNumber: 20
                    }, void 0),
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Switch, {
                        checked: data.IsEnabled,
                        disabled: true,
                        size: "small"
                    }, void 0, false, {
                        fileName: "src/pages/plugins/components/PluginDetail.tsx",
                        lineNumber: 73,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/plugins/components/PluginDetail.tsx",
                    lineNumber: 70,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.plugins.detail.assemblyPath"
                    }, void 0, false, {
                        fileName: "src/pages/plugins/components/PluginDetail.tsx",
                        lineNumber: 77,
                        columnNumber: 20
                    }, void 0),
                    span: 2,
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("code", {
                        children: data.AssemblyPath
                    }, void 0, false, {
                        fileName: "src/pages/plugins/components/PluginDetail.tsx",
                        lineNumber: 80,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/plugins/components/PluginDetail.tsx",
                    lineNumber: 76,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.plugins.detail.typeName"
                    }, void 0, false, {
                        fileName: "src/pages/plugins/components/PluginDetail.tsx",
                        lineNumber: 84,
                        columnNumber: 20
                    }, void 0),
                    span: 2,
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("code", {
                        children: data.TypeName
                    }, void 0, false, {
                        fileName: "src/pages/plugins/components/PluginDetail.tsx",
                        lineNumber: 87,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/plugins/components/PluginDetail.tsx",
                    lineNumber: 83,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.plugins.detail.functions"
                    }, void 0, false, {
                        fileName: "src/pages/plugins/components/PluginDetail.tsx",
                        lineNumber: 91,
                        columnNumber: 20
                    }, void 0),
                    children: ((_data_Functions = data.Functions) === null || _data_Functions === void 0 ? void 0 : _data_Functions.length) || 0
                }, void 0, false, {
                    fileName: "src/pages/plugins/components/PluginDetail.tsx",
                    lineNumber: 90,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.plugins.detail.createdTime"
                    }, void 0, false, {
                        fileName: "src/pages/plugins/components/PluginDetail.tsx",
                        lineNumber: 97,
                        columnNumber: 20
                    }, void 0),
                    children: data.CreatedTime ? new Date(data.CreatedTime).toLocaleString() : '-'
                }, void 0, false, {
                    fileName: "src/pages/plugins/components/PluginDetail.tsx",
                    lineNumber: 96,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.plugins.detail.updatedTime"
                    }, void 0, false, {
                        fileName: "src/pages/plugins/components/PluginDetail.tsx",
                        lineNumber: 103,
                        columnNumber: 20
                    }, void 0),
                    span: 2,
                    children: data.UpdatedTime ? new Date(data.UpdatedTime).toLocaleString() : '-'
                }, void 0, false, {
                    fileName: "src/pages/plugins/components/PluginDetail.tsx",
                    lineNumber: 102,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true, {
            fileName: "src/pages/plugins/components/PluginDetail.tsx",
            lineNumber: 37,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "src/pages/plugins/components/PluginDetail.tsx",
        lineNumber: 29,
        columnNumber: 5
    }, this);
};
_s(PluginDetail, "rlSgSjbewJ1PrR/Ile8g/kr050o=", false, function() {
    return [
        _max.useIntl
    ];
});
_c = PluginDetail;
var _default = PluginDetail;
var _c;
$RefreshReg$(_c, "PluginDetail");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/plugins/components/PluginForm.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _rwxai = __mako_require__("src/services/rwxai/index.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const PluginForm = ({ visible, onVisibleChange, initialValues, onSuccess })=>{
    _s();
    const intl = (0, _max.useIntl)();
    const [form] = _antd.Form.useForm();
    const [loading, setLoading] = (0, _react.useState)(false);
    const isEdit = !!(initialValues === null || initialValues === void 0 ? void 0 : initialValues.Id);
    (0, _react.useEffect)(()=>{
        if (visible) {
            if (initialValues) form.setFieldsValue(initialValues);
            else form.resetFields();
        }
    }, [
        visible,
        initialValues
    ]);
    const handleSubmit = async ()=>{
        try {
            const values = await form.validateFields();
            setLoading(true);
            if (isEdit) {
                await (0, _rwxai.updatePlugin)(initialValues.Id, {
                    ...initialValues,
                    ...values
                });
                _antd.message.success(intl.formatMessage({
                    id: 'pages.plugins.form.update.success'
                }));
            } else {
                await (0, _rwxai.createPlugin)(values);
                _antd.message.success(intl.formatMessage({
                    id: 'pages.plugins.form.create.success'
                }));
            }
            onSuccess();
        } catch (error) {
            _antd.message.error(intl.formatMessage({
                id: isEdit ? 'pages.plugins.form.update.error' : 'pages.plugins.form.create.error'
            }));
        } finally{
            setLoading(false);
        }
    };
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
        title: intl.formatMessage({
            id: isEdit ? 'pages.plugins.form.edit.title' : 'pages.plugins.form.create.title'
        }),
        open: visible,
        onCancel: ()=>onVisibleChange(false),
        onOk: handleSubmit,
        confirmLoading: loading,
        width: 600,
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
            form: form,
            layout: "vertical",
            initialValues: {
                IsEnabled: true,
                Status: 'Inactive'
            },
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    name: "Name",
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.plugins.form.name"
                    }, void 0, false, {
                        fileName: "src/pages/plugins/components/PluginForm.tsx",
                        lineNumber: 81,
                        columnNumber: 18
                    }, void 0),
                    rules: [
                        {
                            required: true,
                            message: intl.formatMessage({
                                id: 'pages.plugins.form.name.required'
                            })
                        }
                    ],
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                        placeholder: intl.formatMessage({
                            id: 'pages.plugins.form.name.placeholder'
                        })
                    }, void 0, false, {
                        fileName: "src/pages/plugins/components/PluginForm.tsx",
                        lineNumber: 84,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/plugins/components/PluginForm.tsx",
                    lineNumber: 79,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    name: "Description",
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.plugins.form.description"
                    }, void 0, false, {
                        fileName: "src/pages/plugins/components/PluginForm.tsx",
                        lineNumber: 89,
                        columnNumber: 18
                    }, void 0),
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input.TextArea, {
                        rows: 3,
                        placeholder: intl.formatMessage({
                            id: 'pages.plugins.form.description.placeholder'
                        })
                    }, void 0, false, {
                        fileName: "src/pages/plugins/components/PluginForm.tsx",
                        lineNumber: 91,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/plugins/components/PluginForm.tsx",
                    lineNumber: 87,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    name: "Version",
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.plugins.form.version"
                    }, void 0, false, {
                        fileName: "src/pages/plugins/components/PluginForm.tsx",
                        lineNumber: 99,
                        columnNumber: 18
                    }, void 0),
                    rules: [
                        {
                            required: true,
                            message: intl.formatMessage({
                                id: 'pages.plugins.form.version.required'
                            })
                        }
                    ],
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                        placeholder: intl.formatMessage({
                            id: 'pages.plugins.form.version.placeholder'
                        })
                    }, void 0, false, {
                        fileName: "src/pages/plugins/components/PluginForm.tsx",
                        lineNumber: 102,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/plugins/components/PluginForm.tsx",
                    lineNumber: 97,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    name: "Author",
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.plugins.form.author"
                    }, void 0, false, {
                        fileName: "src/pages/plugins/components/PluginForm.tsx",
                        lineNumber: 107,
                        columnNumber: 18
                    }, void 0),
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                        placeholder: intl.formatMessage({
                            id: 'pages.plugins.form.author.placeholder'
                        })
                    }, void 0, false, {
                        fileName: "src/pages/plugins/components/PluginForm.tsx",
                        lineNumber: 109,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/plugins/components/PluginForm.tsx",
                    lineNumber: 105,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    name: "AssemblyPath",
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.plugins.form.assemblyPath"
                    }, void 0, false, {
                        fileName: "src/pages/plugins/components/PluginForm.tsx",
                        lineNumber: 114,
                        columnNumber: 18
                    }, void 0),
                    rules: [
                        {
                            required: true,
                            message: intl.formatMessage({
                                id: 'pages.plugins.form.assemblyPath.required'
                            })
                        }
                    ],
                    help: intl.formatMessage({
                        id: 'pages.plugins.form.assemblyPath.help'
                    }),
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                        placeholder: intl.formatMessage({
                            id: 'pages.plugins.form.assemblyPath.placeholder'
                        })
                    }, void 0, false, {
                        fileName: "src/pages/plugins/components/PluginForm.tsx",
                        lineNumber: 118,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/plugins/components/PluginForm.tsx",
                    lineNumber: 112,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    name: "TypeName",
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.plugins.form.typeName"
                    }, void 0, false, {
                        fileName: "src/pages/plugins/components/PluginForm.tsx",
                        lineNumber: 123,
                        columnNumber: 18
                    }, void 0),
                    rules: [
                        {
                            required: true,
                            message: intl.formatMessage({
                                id: 'pages.plugins.form.typeName.required'
                            })
                        }
                    ],
                    help: intl.formatMessage({
                        id: 'pages.plugins.form.typeName.help'
                    }),
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                        placeholder: intl.formatMessage({
                            id: 'pages.plugins.form.typeName.placeholder'
                        })
                    }, void 0, false, {
                        fileName: "src/pages/plugins/components/PluginForm.tsx",
                        lineNumber: 127,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/plugins/components/PluginForm.tsx",
                    lineNumber: 121,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    name: "IsEnabled",
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.plugins.form.isEnabled"
                    }, void 0, false, {
                        fileName: "src/pages/plugins/components/PluginForm.tsx",
                        lineNumber: 132,
                        columnNumber: 18
                    }, void 0),
                    valuePropName: "checked",
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Switch, {}, void 0, false, {
                        fileName: "src/pages/plugins/components/PluginForm.tsx",
                        lineNumber: 135,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/plugins/components/PluginForm.tsx",
                    lineNumber: 130,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "src/pages/plugins/components/PluginForm.tsx",
            lineNumber: 71,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "src/pages/plugins/components/PluginForm.tsx",
        lineNumber: 61,
        columnNumber: 5
    }, this);
};
_s(PluginForm, "G9fecnUMjs1JWq19zn6BW1ErtLA=", false, function() {
    return [
        _max.useIntl,
        _antd.Form.useForm
    ];
});
_c = PluginForm;
var _default = PluginForm;
var _c;
$RefreshReg$(_c, "PluginForm");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/plugins/index.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
var _protable = __mako_require__("node_modules/@ant-design/pro-table/es/index.js");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _rwxai = __mako_require__("src/services/rwxai/index.ts");
var _pageDataHandler = __mako_require__("src/utils/pageDataHandler.ts");
var _PluginForm = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/plugins/components/PluginForm.tsx"));
var _PluginDetail = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/plugins/components/PluginDetail.tsx"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const PluginsPage = ()=>{
    _s();
    const intl = (0, _max.useIntl)();
    const actionRef = (0, _react.useRef)();
    const [createModalVisible, setCreateModalVisible] = (0, _react.useState)(false);
    const [editModalVisible, setEditModalVisible] = (0, _react.useState)(false);
    const [detailModalVisible, setDetailModalVisible] = (0, _react.useState)(false);
    const [currentRecord, setCurrentRecord] = (0, _react.useState)();
    const handleDelete = async (record)=>{
        _antd.Modal.confirm({
            title: intl.formatMessage({
                id: 'pages.plugins.delete.confirm.title'
            }),
            content: intl.formatMessage({
                id: 'pages.plugins.delete.confirm.content'
            }),
            onOk: async ()=>{
                try {
                    var _actionRef_current;
                    await (0, _rwxai.deletePlugin)(record.Id);
                    _antd.message.success(intl.formatMessage({
                        id: 'pages.plugins.delete.success'
                    }));
                    (_actionRef_current = actionRef.current) === null || _actionRef_current === void 0 || _actionRef_current.reload();
                } catch (error) {
                    _antd.message.error(intl.formatMessage({
                        id: 'pages.plugins.delete.error'
                    }));
                }
            }
        });
    };
    const handleStatusChange = async (record, checked)=>{
        try {
            var _actionRef_current;
            await (0, _rwxai.togglePluginStatus)(record.Id, checked);
            _antd.message.success(intl.formatMessage({
                id: 'pages.plugins.status.update.success'
            }));
            (_actionRef_current = actionRef.current) === null || _actionRef_current === void 0 || _actionRef_current.reload();
        } catch (error) {
            _antd.message.error(intl.formatMessage({
                id: 'pages.plugins.status.update.error'
            }));
        }
    };
    const handleManageFunctions = (record)=>{
        _max.history.push(`/plugins/${record.Id}/functions`);
    };
    const getStatusTag = (status)=>{
        const statusMap = {
            'Active': {
                color: 'green',
                text: intl.formatMessage({
                    id: 'pages.plugins.status.active'
                })
            },
            'Inactive': {
                color: 'default',
                text: intl.formatMessage({
                    id: 'pages.plugins.status.inactive'
                })
            },
            'Error': {
                color: 'red',
                text: intl.formatMessage({
                    id: 'pages.plugins.status.error'
                })
            }
        };
        const statusInfo = statusMap[status || 'Inactive'];
        return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
            color: statusInfo.color,
            children: statusInfo.text
        }, void 0, false, {
            fileName: "src/pages/plugins/index.tsx",
            lineNumber: 57,
            columnNumber: 12
        }, this);
    };
    const columns = [
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.plugins.table.name"
            }, void 0, false, {
                fileName: "src/pages/plugins/index.tsx",
                lineNumber: 62,
                columnNumber: 14
            }, this),
            dataIndex: 'Name',
            key: 'Name',
            ellipsis: true
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.plugins.table.description"
            }, void 0, false, {
                fileName: "src/pages/plugins/index.tsx",
                lineNumber: 68,
                columnNumber: 14
            }, this),
            dataIndex: 'Description',
            key: 'Description',
            ellipsis: true,
            hideInSearch: true
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.plugins.table.version"
            }, void 0, false, {
                fileName: "src/pages/plugins/index.tsx",
                lineNumber: 75,
                columnNumber: 14
            }, this),
            dataIndex: 'Version',
            key: 'Version',
            hideInSearch: true,
            width: 100
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.plugins.table.author"
            }, void 0, false, {
                fileName: "src/pages/plugins/index.tsx",
                lineNumber: 82,
                columnNumber: 14
            }, this),
            dataIndex: 'Author',
            key: 'Author',
            hideInSearch: true,
            width: 120
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.plugins.table.status"
            }, void 0, false, {
                fileName: "src/pages/plugins/index.tsx",
                lineNumber: 89,
                columnNumber: 14
            }, this),
            dataIndex: 'Status',
            key: 'Status',
            render: (_, record)=>getStatusTag(record.Status)
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.plugins.table.enabled"
            }, void 0, false, {
                fileName: "src/pages/plugins/index.tsx",
                lineNumber: 95,
                columnNumber: 14
            }, this),
            dataIndex: 'IsEnabled',
            key: 'IsEnabled',
            render: (_, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Switch, {
                    checked: record.IsEnabled,
                    size: "small",
                    onChange: (checked)=>handleStatusChange(record, checked)
                }, void 0, false, {
                    fileName: "src/pages/plugins/index.tsx",
                    lineNumber: 99,
                    columnNumber: 9
                }, this)
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.plugins.table.functions"
            }, void 0, false, {
                fileName: "src/pages/plugins/index.tsx",
                lineNumber: 107,
                columnNumber: 14
            }, this),
            key: 'functions',
            hideInSearch: true,
            render: (_, record)=>{
                var _record_Functions;
                return ((_record_Functions = record.Functions) === null || _record_Functions === void 0 ? void 0 : _record_Functions.length) || 0;
            }
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.plugins.table.createdTime"
            }, void 0, false, {
                fileName: "src/pages/plugins/index.tsx",
                lineNumber: 113,
                columnNumber: 14
            }, this),
            dataIndex: 'CreatedTime',
            key: 'CreatedTime',
            valueType: 'dateTime',
            width: 180,
            hideInSearch: true
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.plugins.table.actions"
            }, void 0, false, {
                fileName: "src/pages/plugins/index.tsx",
                lineNumber: 121,
                columnNumber: 14
            }, this),
            key: 'actions',
            width: 250,
            render: (_, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "link",
                            size: "small",
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EyeOutlined, {}, void 0, false, {
                                fileName: "src/pages/plugins/index.tsx",
                                lineNumber: 129,
                                columnNumber: 19
                            }, void 0),
                            onClick: ()=>{
                                setCurrentRecord(record);
                                setDetailModalVisible(true);
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                id: "pages.plugins.actions.view"
                            }, void 0, false, {
                                fileName: "src/pages/plugins/index.tsx",
                                lineNumber: 135,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/plugins/index.tsx",
                            lineNumber: 126,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "link",
                            size: "small",
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.FunctionOutlined, {}, void 0, false, {
                                fileName: "src/pages/plugins/index.tsx",
                                lineNumber: 140,
                                columnNumber: 19
                            }, void 0),
                            onClick: ()=>handleManageFunctions(record),
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                id: "pages.plugins.actions.functions"
                            }, void 0, false, {
                                fileName: "src/pages/plugins/index.tsx",
                                lineNumber: 143,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/plugins/index.tsx",
                            lineNumber: 137,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "link",
                            size: "small",
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                                fileName: "src/pages/plugins/index.tsx",
                                lineNumber: 148,
                                columnNumber: 19
                            }, void 0),
                            onClick: ()=>{
                                setCurrentRecord(record);
                                setEditModalVisible(true);
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                id: "pages.plugins.actions.edit"
                            }, void 0, false, {
                                fileName: "src/pages/plugins/index.tsx",
                                lineNumber: 154,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/plugins/index.tsx",
                            lineNumber: 145,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "link",
                            size: "small",
                            danger: true,
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                fileName: "src/pages/plugins/index.tsx",
                                lineNumber: 160,
                                columnNumber: 19
                            }, void 0),
                            onClick: ()=>handleDelete(record),
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                id: "pages.plugins.actions.delete"
                            }, void 0, false, {
                                fileName: "src/pages/plugins/index.tsx",
                                lineNumber: 163,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/plugins/index.tsx",
                            lineNumber: 156,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/plugins/index.tsx",
                    lineNumber: 125,
                    columnNumber: 9
                }, this)
        }
    ];
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_protable.ProTable, {
                headerTitle: intl.formatMessage({
                    id: 'pages.plugins.title'
                }),
                actionRef: actionRef,
                rowKey: "Id",
                search: {
                    labelWidth: 120
                },
                toolBarRender: ()=>[
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "primary",
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PlusOutlined, {}, void 0, false, {
                                fileName: "src/pages/plugins/index.tsx",
                                lineNumber: 183,
                                columnNumber: 19
                            }, void 0),
                            onClick: ()=>setCreateModalVisible(true),
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                id: "pages.plugins.actions.create"
                            }, void 0, false, {
                                fileName: "src/pages/plugins/index.tsx",
                                lineNumber: 186,
                                columnNumber: 13
                            }, void 0)
                        }, "primary", false, {
                            fileName: "src/pages/plugins/index.tsx",
                            lineNumber: 180,
                            columnNumber: 11
                        }, void 0)
                    ],
                request: (0, _pageDataHandler.createSimpleProTableRequest)(_rwxai.getPlugins),
                columns: columns
            }, void 0, false, {
                fileName: "src/pages/plugins/index.tsx",
                lineNumber: 172,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_PluginForm.default, {
                visible: createModalVisible,
                onVisibleChange: setCreateModalVisible,
                onSuccess: ()=>{
                    var _actionRef_current;
                    (_actionRef_current = actionRef.current) === null || _actionRef_current === void 0 || _actionRef_current.reload();
                    setCreateModalVisible(false);
                }
            }, void 0, false, {
                fileName: "src/pages/plugins/index.tsx",
                lineNumber: 193,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_PluginForm.default, {
                visible: editModalVisible,
                onVisibleChange: setEditModalVisible,
                initialValues: currentRecord,
                onSuccess: ()=>{
                    var _actionRef_current;
                    (_actionRef_current = actionRef.current) === null || _actionRef_current === void 0 || _actionRef_current.reload();
                    setEditModalVisible(false);
                }
            }, void 0, false, {
                fileName: "src/pages/plugins/index.tsx",
                lineNumber: 202,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_PluginDetail.default, {
                visible: detailModalVisible,
                onVisibleChange: setDetailModalVisible,
                data: currentRecord
            }, void 0, false, {
                fileName: "src/pages/plugins/index.tsx",
                lineNumber: 212,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/plugins/index.tsx",
        lineNumber: 171,
        columnNumber: 5
    }, this);
};
_s(PluginsPage, "hTbNuNc6vHQfYm20aQVEQo9um4U=", false, function() {
    return [
        _max.useIntl
    ];
});
_c = PluginsPage;
var _default = PluginsPage;
var _c;
$RefreshReg$(_c, "PluginsPage");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=p__plugins__index-async.js.map