import React, { useState } from 'react';
import { Modal, Descriptions, Switch, Button, Typography, Space, message } from 'antd';
import { CopyOutlined, EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';
import { useIntl, FormattedMessage } from '@umijs/max';

const { Text } = Typography;

interface AppDetailProps {
  visible: boolean;
  onVisibleChange: (visible: boolean) => void;
  data?: RwxAI.App;
}

const AppDetail: React.FC<AppDetailProps> = ({
  visible,
  onVisibleChange,
  data,
}) => {
  const intl = useIntl();
  const [showApiKey, setShowApiKey] = useState(false);
  const [showApiSecret, setShowApiSecret] = useState(false);

  const copyToClipboard = (text: string, type: string) => {
    navigator.clipboard.writeText(text).then(() => {
      message.success(intl.formatMessage({ id: `pages.apps.detail.copy.${type}.success` }));
    });
  };

  const maskString = (str?: string) => {
    if (!str) return '-';
    if (str.length <= 8) return '*'.repeat(str.length);
    return str.substring(0, 4) + '*'.repeat(str.length - 8) + str.substring(str.length - 4);
  };

  return (
    <Modal
      title={intl.formatMessage({ id: 'pages.apps.detail.title' })}
      open={visible}
      onCancel={() => onVisibleChange(false)}
      footer={null}
      width={800}
    >
      {data && (
        <Descriptions column={2} bordered>
          <Descriptions.Item
            label={<FormattedMessage id="pages.apps.detail.name" />}
            span={2}
          >
            {data.Name}
          </Descriptions.Item>
          
          <Descriptions.Item
            label={<FormattedMessage id="pages.apps.detail.description" />}
            span={2}
          >
            {data.Description || '-'}
          </Descriptions.Item>
          
          <Descriptions.Item
            label={<FormattedMessage id="pages.apps.detail.apiKey" />}
            span={2}
          >
            <Space>
              <Text code copyable={false}>
                {showApiKey ? data.ApiKey : maskString(data.ApiKey)}
              </Text>
              <Button
                type="link"
                size="small"
                icon={showApiKey ? <EyeInvisibleOutlined /> : <EyeOutlined />}
                onClick={() => setShowApiKey(!showApiKey)}
              />
              <Button
                type="link"
                size="small"
                icon={<CopyOutlined />}
                onClick={() => copyToClipboard(data.ApiKey || '', 'apiKey')}
              />
            </Space>
          </Descriptions.Item>
          
          <Descriptions.Item
            label={<FormattedMessage id="pages.apps.detail.apiSecret" />}
            span={2}
          >
            <Space>
              <Text code copyable={false}>
                {showApiSecret ? data.ApiSecret : maskString(data.ApiSecret)}
              </Text>
              <Button
                type="link"
                size="small"
                icon={showApiSecret ? <EyeInvisibleOutlined /> : <EyeOutlined />}
                onClick={() => setShowApiSecret(!showApiSecret)}
              />
              <Button
                type="link"
                size="small"
                icon={<CopyOutlined />}
                onClick={() => copyToClipboard(data.ApiSecret || '', 'apiSecret')}
              />
            </Space>
          </Descriptions.Item>
          
          <Descriptions.Item
            label={<FormattedMessage id="pages.apps.detail.status" />}
          >
            <Switch checked={data.IsEnabled} disabled size="small" />
          </Descriptions.Item>
          
          <Descriptions.Item
            label={<FormattedMessage id="pages.apps.detail.apiCalls" />}
          >
            {data.ApiCallCount || 0} / {data.MaxApiCalls || '∞'}
          </Descriptions.Item>
          
          <Descriptions.Item
            label={<FormattedMessage id="pages.apps.detail.rateLimit" />}
          >
            {data.RateLimitPerMinute ? `${data.RateLimitPerMinute}/min` : '-'}
          </Descriptions.Item>
          
          <Descriptions.Item
            label={<FormattedMessage id="pages.apps.detail.allowedOrigins" />}
          >
            {data.AllowedOrigins || '-'}
          </Descriptions.Item>
          
          <Descriptions.Item
            label={<FormattedMessage id="pages.apps.detail.createdTime" />}
          >
            {data.CreatedTime ? new Date(data.CreatedTime).toLocaleString() : '-'}
          </Descriptions.Item>
          
          <Descriptions.Item
            label={<FormattedMessage id="pages.apps.detail.updatedTime" />}
          >
            {data.UpdatedTime ? new Date(data.UpdatedTime).toLocaleString() : '-'}
          </Descriptions.Item>
          
          <Descriptions.Item
            label={<FormattedMessage id="pages.apps.detail.configJson" />}
            span={2}
          >
            {data.ConfigJson ? (
              <pre style={{ whiteSpace: 'pre-wrap', fontSize: '12px' }}>
                {JSON.stringify(JSON.parse(data.ConfigJson), null, 2)}
              </pre>
            ) : (
              '-'
            )}
          </Descriptions.Item>
        </Descriptions>
      )}
    </Modal>
  );
};

export default AppDetail;
