{"version": 3, "sources": ["common-async.9249548573943173419.hot-update.js", "src/services/rwxai/aiModels.ts"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'common',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='18442320157183392171';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/.umi/plugin-openapi/openapi.tsx\":[\"vendors\",\"src/.umi/plugin-openapi/openapi.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Admin.tsx\":[\"p__Admin\"],\"src/pages/Welcome.tsx\":[\"p__Welcome\"],\"src/pages/ai-models/index.tsx\":[\"common\",\"src/pages/ai-models/index.tsx\"],\"src/pages/api-test/index.tsx\":[\"common\",\"src/pages/api-test/index.tsx\"],\"src/pages/apps/index.tsx\":[\"common\",\"p__apps__index\"],\"src/pages/auth-test/index.tsx\":[\"common\",\"src/pages/auth-test/index.tsx\"],\"src/pages/chat/chat-interface.tsx\":[\"vendors\",\"common\",\"src/pages/chat/chat-interface.tsx\"],\"src/pages/chat/index.tsx\":[\"common\",\"p__chat__index\"],\"src/pages/chat/session/[id].tsx\":[\"common\",\"p__chat__session__id\"],\"src/pages/knowledge/[id]/files.tsx\":[\"common\",\"p__knowledge__id__files\"],\"src/pages/knowledge/index.tsx\":[\"common\",\"p__knowledge__index\"],\"src/pages/plugins/index.tsx\":[\"common\",\"p__plugins__index\"],\"src/pages/response-demo/index.tsx\":[\"src/pages/response-demo/index.tsx\"],\"src/pages/table-list/index.tsx\":[\"src/pages/table-list/index.tsx\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"],\"src/pages/user/register/index.tsx\":[\"common\",\"p__user__register__index\"]});;\r\n  },\r\n);\r\n", "/**\r\n * 统一的AI模型管理API服务\r\n * 合并了原aiModels.ts和enhanced-aiModels.ts的功能\r\n * 提供完整的AI模型、模板、提供商管理功能\r\n */\r\n\r\nimport { httpRequest } from '@/utils/request';\r\nimport { toQueryString } from '@/utils/pageDataHandler';\r\nimport { getApiPrefix } from '@/utils/api';\r\nimport { ResponseHandleResult } from '@/types/response';\r\n\r\nconst API_PREFIX: string = getApiPrefix();\r\n\r\n/**\r\n * ===========================================\r\n * AI模型核心CRUD操作\r\n * ===========================================\r\n */\r\n\r\n/**\r\n * 获取AI模型列表（支持查询参数和分页）\r\n */\r\nexport async function getAIModels(params?: any): Promise<ResponseHandleResult<RwxAI.BackendPagedResponse<RwxAI.AIModel>>> {\r\n  const url = params ? `${API_PREFIX}/AIModels/PageModels?${toQueryString(params)}` : `${API_PREFIX}/AIModels/PageModels`;\r\n  return httpRequest.get<RwxAI.BackendPagedResponse<RwxAI.AIModel>>(url, {\r\n    showErrorNotification: true\r\n  });\r\n}\r\n\r\n/**\r\n * 根据ID获取AI模型详情\r\n */\r\nexport async function getAIModelById(id: string): Promise<ResponseHandleResult<RwxAI.AIModel>> {\r\n  return httpRequest.get<RwxAI.AIModel>(`${API_PREFIX}/AIModels/${id}`, {\r\n    showErrorNotification: true,\r\n  });\r\n}\r\n\r\n/**\r\n * 创建AI模型\r\n */\r\nexport async function createAIModel(data: RwxAI.CreateAIModelRequest): Promise<ResponseHandleResult<RwxAI.AIModel>> {\r\n  return httpRequest.post<RwxAI.AIModel>(`${API_PREFIX}/AIModels`, data, {\r\n    showSuccessMessage: true,\r\n    showErrorNotification: true,\r\n    successMessage: '创建AI模型成功',\r\n  });\r\n}\r\n\r\n/**\r\n * 更新AI模型\r\n */\r\nexport async function updateAIModel(\r\n  id: string,\r\n  data: RwxAI.UpdateAIModelRequest\r\n): Promise<ResponseHandleResult<RwxAI.AIModel>> {\r\n  return httpRequest.put<RwxAI.AIModel>(`${API_PREFIX}/AIModels/${id}`, data, {\r\n    showSuccessMessage: true,\r\n    showErrorNotification: true,\r\n    successMessage: '更新AI模型成功',\r\n  });\r\n}\r\n\r\n/**\r\n * 删除AI模型\r\n */\r\nexport async function deleteAIModel(id: string): Promise<ResponseHandleResult<void>> {\r\n  return httpRequest.delete<void>(`${API_PREFIX}/AIModels/${id}`, {\r\n    showSuccessMessage: true,\r\n    showErrorNotification: true,\r\n    successMessage: '删除AI模型成功',\r\n  });\r\n}\r\n\r\n/**\r\n * 批量删除AI模型\r\n */\r\nexport async function batchDeleteAIModels(ids: string[]): Promise<ResponseHandleResult<void>> {\r\n  return httpRequest.post<void>(`${API_PREFIX}/AIModels/batch-delete`, { ids }, {\r\n    showSuccessMessage: true,\r\n    showErrorNotification: true,\r\n    successMessage: `成功删除 ${ids.length} 个AI模型`,\r\n  });\r\n}\r\n\r\n/**\r\n * 测试AI模型连接\r\n */\r\nexport async function testAIModelConnection(id: string): Promise<ResponseHandleResult<RwxAI.TestConnectionResult>> {\r\n  return httpRequest.post<RwxAI.TestConnectionResult>(`${API_PREFIX}/AIModels/${id}/test`, {}, {\r\n    showSuccessMessage: true,\r\n    showErrorNotification: true,\r\n    successMessage: 'AI模型连接测试成功',\r\n  });\r\n}\r\n\r\n/**\r\n * 启用/禁用AI模型\r\n */\r\nexport async function toggleAIModelStatus(\r\n  id: string,\r\n  enabled: boolean\r\n): Promise<ResponseHandleResult<RwxAI.AIModel>> {\r\n  return httpRequest.patch<RwxAI.AIModel>(`${API_PREFIX}/AIModels/${id}/status`, { enabled }, {\r\n    showSuccessMessage: true,\r\n    showErrorNotification: true,\r\n    successMessage: enabled ? '启用AI模型成功' : '禁用AI模型成功',\r\n  });\r\n}\r\n\r\n/**\r\n * AI模型模板相关API\r\n */\r\n\r\n// 根据模型类型获取模型模板列表\r\nexport async function getAIModelTemplatesByType(modelType: RwxAI.ModelTypeEnum): Promise<ResponseHandleResult<RwxAI.AIModelTemplate[]>> {\r\n  return httpRequest.get<RwxAI.AIModelTemplate[]>(`${API_PREFIX}/AIModelTemplates/by-type/${modelType}`, {\r\n    showErrorNotification: true,\r\n  });\r\n}\r\n\r\n// 根据提供商ID和模型类型获取模型模板列表\r\nexport async function getAIModelTemplatesByProviderAndType(params: {\r\n  providerId?: string;\r\n  modelType?: RwxAI.ModelTypeEnum;\r\n}): Promise<ResponseHandleResult<RwxAI.AIModelTemplate[]>> {\r\n  const queryParams = new URLSearchParams();\r\n  if (params.providerId) queryParams.append('providerId', params.providerId);\r\n  if (params.modelType) queryParams.append('modelType', params.modelType.toString());\r\n\r\n  return httpRequest.get<RwxAI.AIModelTemplate[]>(`${API_PREFIX}/AIModelTemplates/by-provider-and-type?${queryParams}`, {\r\n    showErrorNotification: true,\r\n  });\r\n}\r\n\r\n// 获取模型模板详情\r\nexport async function getAIModelTemplateById(id: string): Promise<ResponseHandleResult<RwxAI.AIModelTemplate>> {\r\n  return httpRequest.get<RwxAI.AIModelTemplate>(`${API_PREFIX}/AIModelTemplates/${id}`, {\r\n    showErrorNotification: true,\r\n  });\r\n}\r\n\r\n/**\r\n * AI服务提供商相关API\r\n */\r\n\r\n// 获取所有启用的AI服务提供商\r\nexport async function getEnabledAIProviders(): Promise<ResponseHandleResult<RwxAI.AIProvider[]>> {\r\n  return httpRequest.get<RwxAI.AIProvider[]>(`${API_PREFIX}/AIProviders/enabled`, {\r\n    showErrorNotification: true,\r\n  });\r\n}\r\n\r\n// 根据提供商ID获取对应的模型列表\r\nexport async function getModelsByProviderId(providerId: string): Promise<ResponseHandleResult<RwxAI.AIModelTemplate[]>> {\r\n  return httpRequest.get<RwxAI.AIModelTemplate[]>(`${API_PREFIX}/AIProviders/${providerId}/models`, {\r\n    showErrorNotification: true,\r\n  });\r\n}\r\n\r\n// 根据提供商代码获取对应的模型列表\r\nexport async function getModelsByProviderCode(providerCode: string): Promise<ResponseHandleResult<RwxAI.AIModelTemplate[]>> {\r\n  return httpRequest.get<RwxAI.AIModelTemplate[]>(`${API_PREFIX}/AIProviders/by-code/${providerCode}/models`, {\r\n    showErrorNotification: true,\r\n  });\r\n}\r\n\r\n// 根据提供商类型获取模型模板列表\r\nexport async function getModelsByProviderType(providerType: number): Promise<ResponseHandleResult<RwxAI.AIModelTemplate[]>> {\r\n  return httpRequest.get<RwxAI.AIModelTemplate[]>(`${API_PREFIX}/AIProviders/type/${providerType}/models`, {\r\n    showErrorNotification: true,\r\n  });\r\n}\r\n\r\n// 获取所有启用的模型模板\r\nexport async function getAllEnabledModels(): Promise<ResponseHandleResult<RwxAI.AIModelTemplate[]>> {\r\n  return httpRequest.get<RwxAI.AIModelTemplate[]>(`${API_PREFIX}/AIProviders/models/all`, {\r\n    showErrorNotification: true,\r\n  });\r\n}\r\n\r\n/**\r\n * ===========================================\r\n * 统一的API导出对象\r\n * ===========================================\r\n */\r\n\r\n/**\r\n * AI模型管理API统一导出对象\r\n * 提供所有AI模型相关的API函数\r\n */\r\nexport const aiModelsApi = {\r\n  // 核心CRUD操作\r\n  getAIModels,\r\n  getAIModelById,\r\n  createAIModel,\r\n  updateAIModel,\r\n  deleteAIModel,\r\n\r\n  // 增强功能\r\n  batchDeleteAIModels,\r\n  testAIModelConnection,\r\n  toggleAIModelStatus,\r\n\r\n  // 模板管理\r\n  getAIModelTemplatesByType,\r\n  getAIModelTemplatesByProviderAndType,\r\n  getAIModelTemplateById,\r\n\r\n  // 提供商管理\r\n  getEnabledAIProviders,\r\n  getModelsByProviderId,\r\n  getModelsByProviderCode,\r\n  getModelsByProviderType,\r\n  getAllEnabledModels,\r\n};\r\n\r\n/**\r\n * 默认导出统一API对象（可选）\r\n */\r\nexport default aiModelsApi;\r\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,UACA;IACE,SAAS;;;;;;;;;;;;;gBC4LA,WAAW;2BAAX;;gBAlHS,mBAAmB;2BAAnB;;gBApCA,aAAa;2BAAb;;gBAgLtB;;CAEC,GACD,OAA2B;2BAA3B;;gBA1JsB,aAAa;2BAAb;;gBAlCA,cAAc;2BAAd;;gBAwGA,sBAAsB;2BAAtB;;gBAdA,oCAAoC;2BAApC;;gBAPA,yBAAyB;2BAAzB;;gBA7FA,WAAW;2BAAX;;gBAyJA,mBAAmB;2BAAnB;;gBA5BA,qBAAqB;2BAArB;;gBAcA,uBAAuB;2BAAvB;;gBAPA,qBAAqB;2BAArB;;gBAcA,uBAAuB;2BAAvB;;gBAhFA,qBAAqB;2BAArB;;gBAWA,mBAAmB;2BAAnB;;gBA/CA,aAAa;2BAAb;;;;;4CA9CM;oDACE;wCACD;;;;;;;;;YAG7B,MAAM,aAAqB,IAAA,iBAAY;YAWhC,eAAe,YAAY,MAAY;gBAC5C,MAAM,MAAM,SAAS,CAAC,EAAE,WAAW,qBAAqB,EAAE,IAAA,8BAAa,EAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,WAAW,oBAAoB,CAAC;gBACvH,OAAO,oBAAW,CAAC,GAAG,CAA4C,KAAK;oBACrE,uBAAuB;gBACzB;YACF;YAKO,eAAe,eAAe,EAAU;gBAC7C,OAAO,oBAAW,CAAC,GAAG,CAAgB,CAAC,EAAE,WAAW,UAAU,EAAE,GAAG,CAAC,EAAE;oBACpE,uBAAuB;gBACzB;YACF;YAKO,eAAe,cAAc,IAAgC;gBAClE,OAAO,oBAAW,CAAC,IAAI,CAAgB,CAAC,EAAE,WAAW,SAAS,CAAC,EAAE,MAAM;oBACrE,oBAAoB;oBACpB,uBAAuB;oBACvB,gBAAgB;gBAClB;YACF;YAKO,eAAe,cACpB,EAAU,EACV,IAAgC;gBAEhC,OAAO,oBAAW,CAAC,GAAG,CAAgB,CAAC,EAAE,WAAW,UAAU,EAAE,GAAG,CAAC,EAAE,MAAM;oBAC1E,oBAAoB;oBACpB,uBAAuB;oBACvB,gBAAgB;gBAClB;YACF;YAKO,eAAe,cAAc,EAAU;gBAC5C,OAAO,oBAAW,CAAC,MAAM,CAAO,CAAC,EAAE,WAAW,UAAU,EAAE,GAAG,CAAC,EAAE;oBAC9D,oBAAoB;oBACpB,uBAAuB;oBACvB,gBAAgB;gBAClB;YACF;YAKO,eAAe,oBAAoB,GAAa;gBACrD,OAAO,oBAAW,CAAC,IAAI,CAAO,CAAC,EAAE,WAAW,sBAAsB,CAAC,EAAE;oBAAE;gBAAI,GAAG;oBAC5E,oBAAoB;oBACpB,uBAAuB;oBACvB,gBAAgB,CAAC,KAAK,EAAE,IAAI,MAAM,CAAC,MAAM,CAAC;gBAC5C;YACF;YAKO,eAAe,sBAAsB,EAAU;gBACpD,OAAO,oBAAW,CAAC,IAAI,CAA6B,CAAC,EAAE,WAAW,UAAU,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG;oBAC3F,oBAAoB;oBACpB,uBAAuB;oBACvB,gBAAgB;gBAClB;YACF;YAKO,eAAe,oBACpB,EAAU,EACV,OAAgB;gBAEhB,OAAO,oBAAW,CAAC,KAAK,CAAgB,CAAC,EAAE,WAAW,UAAU,EAAE,GAAG,OAAO,CAAC,EAAE;oBAAE;gBAAQ,GAAG;oBAC1F,oBAAoB;oBACpB,uBAAuB;oBACvB,gBAAgB,UAAU,aAAa;gBACzC;YACF;YAOO,eAAe,0BAA0B,SAA8B;gBAC5E,OAAO,oBAAW,CAAC,GAAG,CAA0B,CAAC,EAAE,WAAW,0BAA0B,EAAE,UAAU,CAAC,EAAE;oBACrG,uBAAuB;gBACzB;YACF;YAGO,eAAe,qCAAqC,MAG1D;gBACC,MAAM,cAAc,IAAI;gBACxB,IAAI,OAAO,UAAU,EAAE,YAAY,MAAM,CAAC,cAAc,OAAO,UAAU;gBACzE,IAAI,OAAO,SAAS,EAAE,YAAY,MAAM,CAAC,aAAa,OAAO,SAAS,CAAC,QAAQ;gBAE/E,OAAO,oBAAW,CAAC,GAAG,CAA0B,CAAC,EAAE,WAAW,uCAAuC,EAAE,YAAY,CAAC,EAAE;oBACpH,uBAAuB;gBACzB;YACF;YAGO,eAAe,uBAAuB,EAAU;gBACrD,OAAO,oBAAW,CAAC,GAAG,CAAwB,CAAC,EAAE,WAAW,kBAAkB,EAAE,GAAG,CAAC,EAAE;oBACpF,uBAAuB;gBACzB;YACF;YAOO,eAAe;gBACpB,OAAO,oBAAW,CAAC,GAAG,CAAqB,CAAC,EAAE,WAAW,oBAAoB,CAAC,EAAE;oBAC9E,uBAAuB;gBACzB;YACF;YAGO,eAAe,sBAAsB,UAAkB;gBAC5D,OAAO,oBAAW,CAAC,GAAG,CAA0B,CAAC,EAAE,WAAW,aAAa,EAAE,WAAW,OAAO,CAAC,EAAE;oBAChG,uBAAuB;gBACzB;YACF;YAGO,eAAe,wBAAwB,YAAoB;gBAChE,OAAO,oBAAW,CAAC,GAAG,CAA0B,CAAC,EAAE,WAAW,qBAAqB,EAAE,aAAa,OAAO,CAAC,EAAE;oBAC1G,uBAAuB;gBACzB;YACF;YAGO,eAAe,wBAAwB,YAAoB;gBAChE,OAAO,oBAAW,CAAC,GAAG,CAA0B,CAAC,EAAE,WAAW,kBAAkB,EAAE,aAAa,OAAO,CAAC,EAAE;oBACvG,uBAAuB;gBACzB;YACF;YAGO,eAAe;gBACpB,OAAO,oBAAW,CAAC,GAAG,CAA0B,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE;oBACtF,uBAAuB;gBACzB;YACF;YAYO,MAAM,cAAc;gBACzB,WAAW;gBACX;gBACA;gBACA;gBACA;gBACA;gBAEA,OAAO;gBACP;gBACA;gBACA;gBAEA,OAAO;gBACP;gBACA;gBACA;gBAEA,QAAQ;gBACR;gBACA;gBACA;gBACA;gBACA;YACF;gBAKA,WAAe;;;;;;;;;;;;;;;;;;;;;;;IDzND;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,uCAAsC;YAAC;YAAU;SAAsC;QAAC,qBAAoB;YAAC;SAAS;QAAC,uBAAsB;YAAC;SAAW;QAAC,yBAAwB;YAAC;SAAa;QAAC,iCAAgC;YAAC;YAAS;SAAgC;QAAC,gCAA+B;YAAC;YAAS;SAA+B;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,iCAAgC;YAAC;YAAS;SAAgC;QAAC,qCAAoC;YAAC;YAAU;YAAS;SAAoC;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,mCAAkC;YAAC;YAAS;SAAuB;QAAC,sCAAqC;YAAC;YAAS;SAA0B;QAAC,iCAAgC;YAAC;YAAS;SAAsB;QAAC,+BAA8B;YAAC;YAAS;SAAoB;QAAC,qCAAoC;YAAC;SAAoC;QAAC,kCAAiC;YAAC;SAAiC;QAAC,kCAAiC;YAAC;YAAS;SAAwB;QAAC,qCAAoC;YAAC;YAAS;SAA2B;IAAA;;AAC9zC"}