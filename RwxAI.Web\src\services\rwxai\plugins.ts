import { httpRequest } from '@/utils/request';
import { getApiPrefix } from '@/utils/api';
import { ResponseHandleResult } from '@/types/response';

const API_PREFIX: string = getApiPrefix();

/**
 * 插件管理相关API
 */

// 获取所有插件列表
export async function getPlugins(): Promise<ResponseHandleResult<RwxAI.Plugin[]>> {
  return httpRequest.get<RwxAI.Plugin[]>(`${API_PREFIX}/Plugins`, {
    showErrorNotification: true,
  });
}

// 创建新插件
export async function createPlugin(data: RwxAI.Plugin): Promise<ResponseHandleResult<RwxAI.Plugin>> {
  return httpRequest.post<RwxAI.Plugin>(`${API_PREFIX}/Plugins`, data, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: '创建插件成功',
  });
}

// 根据ID获取指定插件
export async function getPluginById(id: string): Promise<ResponseHandleResult<RwxAI.Plugin>> {
  return httpRequest.get<RwxAI.Plugin>(`${API_PREFIX}/Plugins/${id}`, {
    showErrorNotification: true,
  });
}

// 更新插件信息
export async function updatePlugin(id: string, data: RwxAI.Plugin): Promise<ResponseHandleResult<RwxAI.Plugin>> {
  return httpRequest.put<RwxAI.Plugin>(`${API_PREFIX}/Plugins/${id}`, data, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: '更新插件成功',
  });
}

// 删除指定插件
export async function deletePlugin(id: string): Promise<ResponseHandleResult<void>> {
  return httpRequest.delete<void>(`${API_PREFIX}/Plugins/${id}`, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: '删除插件成功',
  });
}

// 启用/禁用插件
export async function togglePluginStatus(id: string, isEnabled: boolean): Promise<ResponseHandleResult<RwxAI.Plugin>> {
  return httpRequest.patch<RwxAI.Plugin>(`${API_PREFIX}/Plugins/${id}/toggle`, { IsEnabled: isEnabled }, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: isEnabled ? '启用插件成功' : '禁用插件成功',
  });
}

// 获取插件的所有功能
export async function getPluginFunctions(pluginId: string): Promise<ResponseHandleResult<RwxAI.PluginFunction[]>> {
  return httpRequest.get<RwxAI.PluginFunction[]>(`${API_PREFIX}/Plugins/${pluginId}/functions`, {
    showErrorNotification: true,
  });
}

// 执行插件功能
export async function executePluginFunction(pluginId: string, functionName: string, parameters: any): Promise<ResponseHandleResult<any>> {
  return httpRequest.post<any>(`${API_PREFIX}/Plugins/${pluginId}/functions/${functionName}/execute`, parameters, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: '插件功能执行成功',
  });
}
