export interface ProTableParams {
    current?: number;
    pageSize?: number;
    keyWord?: string;
    [key: string]: any;
}

export interface SmartQueryDto {
    SearchKeyword?: string;
    PageNumber?: number;
    PageSize?: number;
    OrderBy?: string;
    IsDescending?: boolean;
    StartTime?: string;
    EndTime?: string;
    // AIModelQueryDto 扩展可透传
    [key: string]: any;
}

export function buildSmartQuery(
    params: ProTableParams,
    mapper?: { [k: string]: string },
    extra?: Record<string, any>
): SmartQueryDto {
    const query: SmartQueryDto = {};
    query.PageNumber = params.current ?? 1;
    query.PageSize = params.pageSize ?? 20;
    if (params.keyWord) query.SearchKeyword = params.keyWord as string;

    // 字段映射：将列的 dataIndex 对应值映射到后端字段
    if (mapper) {
        Object.keys(mapper).forEach((from) => {
            const to = mapper[from];
            const value = (params as any)[from];
            if (value !== undefined && value !== null && value !== '') {
                (query as any)[to] = value;
            }
        });
    }

    if (extra) {
        Object.assign(query, extra);
    }

    return query;
}

export function toQueryString(obj: Record<string, any>): string {
    const qs = new URLSearchParams();
    Object.entries(obj).forEach(([k, v]) => {
        if (v === undefined || v === null || v === '') return;
        if (v instanceof Date) {
            qs.append(k, v.toISOString());
        } else {
            qs.append(k, String(v));
        }
    });
    return qs.toString();
}

