import React, { useRef, useState } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import { ProTable, ActionType, ProColumns } from '@ant-design/pro-table';
import { Button, Space, Tag, message, Modal, Switch } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, FunctionOutlined } from '@ant-design/icons';
import { useIntl, FormattedMessage, history } from '@umijs/max';
import { getPlugins, deletePlugin, togglePluginStatus } from '@/services/rwxai';
import PluginForm from './components/PluginForm';
import PluginDetail from './components/PluginDetail';

const PluginsPage: React.FC = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<RwxAI.Plugin>();

  const handleDelete = async (record: RwxAI.Plugin) => {
    Modal.confirm({
      title: intl.formatMessage({ id: 'pages.plugins.delete.confirm.title' }),
      content: intl.formatMessage({ id: 'pages.plugins.delete.confirm.content' }),
      onOk: async () => {
        try {
          await deletePlugin(record.Id);
          message.success(intl.formatMessage({ id: 'pages.plugins.delete.success' }));
          actionRef.current?.reload();
        } catch (error) {
          message.error(intl.formatMessage({ id: 'pages.plugins.delete.error' }));
        }
      },
    });
  };

  const handleStatusChange = async (record: RwxAI.Plugin, checked: boolean) => {
    try {
      await togglePluginStatus(record.Id, checked);
      message.success(intl.formatMessage({ id: 'pages.plugins.status.update.success' }));
      actionRef.current?.reload();
    } catch (error) {
      message.error(intl.formatMessage({ id: 'pages.plugins.status.update.error' }));
    }
  };

  const handleManageFunctions = (record: RwxAI.Plugin) => {
    history.push(`/plugins/${record.Id}/functions`);
  };

  const getStatusTag = (status?: string) => {
    const statusMap: Record<string, { color: string; text: string }> = {
      'Active': { color: 'green', text: intl.formatMessage({ id: 'pages.plugins.status.active' }) },
      'Inactive': { color: 'default', text: intl.formatMessage({ id: 'pages.plugins.status.inactive' }) },
      'Error': { color: 'red', text: intl.formatMessage({ id: 'pages.plugins.status.error' }) },
    };
    const statusInfo = statusMap[status || 'Inactive'];
    return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
  };

  const columns: ProColumns<RwxAI.Plugin>[] = [
    {
      title: <FormattedMessage id="pages.plugins.table.name" />,
      dataIndex: 'Name',
      key: 'Name',
      ellipsis: true,
    },
    {
      title: <FormattedMessage id="pages.plugins.table.description" />,
      dataIndex: 'Description',
      key: 'Description',
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: <FormattedMessage id="pages.plugins.table.version" />,
      dataIndex: 'Version',
      key: 'Version',
      hideInSearch: true,
      width: 100,
    },
    {
      title: <FormattedMessage id="pages.plugins.table.author" />,
      dataIndex: 'Author',
      key: 'Author',
      hideInSearch: true,
      width: 120,
    },
    {
      title: <FormattedMessage id="pages.plugins.table.status" />,
      dataIndex: 'Status',
      key: 'Status',
      render: (_, record) => getStatusTag(record.Status),
    },
    {
      title: <FormattedMessage id="pages.plugins.table.enabled" />,
      dataIndex: 'IsEnabled',
      key: 'IsEnabled',
      render: (_, record) => (
        <Switch
          checked={record.IsEnabled}
          size="small"
          onChange={(checked) => handleStatusChange(record, checked)}
        />
      ),
    },
    {
      title: <FormattedMessage id="pages.plugins.table.functions" />,
      key: 'functions',
      hideInSearch: true,
      render: (_, record) => record.Functions?.length || 0,
    },
    {
      title: <FormattedMessage id="pages.plugins.table.createdTime" />,
      dataIndex: 'CreatedTime',
      key: 'CreatedTime',
      valueType: 'dateTime',
      width: 180,
      hideInSearch: true,
    },
    {
      title: <FormattedMessage id="pages.plugins.table.actions" />,
      key: 'actions',
      width: 250,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => {
              setCurrentRecord(record);
              setDetailModalVisible(true);
            }}
          >
            <FormattedMessage id="pages.plugins.actions.view" />
          </Button>
          <Button
            type="link"
            size="small"
            icon={<FunctionOutlined />}
            onClick={() => handleManageFunctions(record)}
          >
            <FormattedMessage id="pages.plugins.actions.functions" />
          </Button>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => {
              setCurrentRecord(record);
              setEditModalVisible(true);
            }}
          >
            <FormattedMessage id="pages.plugins.actions.edit" />
          </Button>
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          >
            <FormattedMessage id="pages.plugins.actions.delete" />
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <PageContainer>
      <ProTable<RwxAI.Plugin>
        headerTitle={intl.formatMessage({ id: 'pages.plugins.title' })}
        actionRef={actionRef}
        rowKey="Id"
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            icon={<PlusOutlined />}
            onClick={() => setCreateModalVisible(true)}
          >
            <FormattedMessage id="pages.plugins.actions.create" />
          </Button>,
        ]}
        request={async () => {
          const response = await getPlugins();
          return {
            data: response.success ? (response.data || []) : [],
            success: response.success,
          };
        }}
        columns={columns}
      />

      <PluginForm
        visible={createModalVisible}
        onVisibleChange={setCreateModalVisible}
        onSuccess={() => {
          actionRef.current?.reload();
          setCreateModalVisible(false);
        }}
      />

      <PluginForm
        visible={editModalVisible}
        onVisibleChange={setEditModalVisible}
        initialValues={currentRecord}
        onSuccess={() => {
          actionRef.current?.reload();
          setEditModalVisible(false);
        }}
      />

      <PluginDetail
        visible={detailModalVisible}
        onVisibleChange={setDetailModalVisible}
        data={currentRecord}
      />
    </PageContainer>
  );
};

export default PluginsPage;
