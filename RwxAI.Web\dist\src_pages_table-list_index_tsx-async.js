((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] || []).push([
        ['src/pages/table-list/index.tsx'],
{ "src/pages/table-list/index.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const TableList = ()=>{
    _s();
    (0, _max.useIntl)();
    const actionRef = (0, _react.useRef)();
    const columns = [
        {
            title: 'ID',
            dataIndex: 'id',
            key: 'id',
            width: 80
        },
        {
            title: '名称',
            dataIndex: 'name',
            key: 'name',
            ellipsis: true
        },
        {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
            render: (_, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                    color: record.status === 'active' ? 'green' : 'red',
                    children: record.status === 'active' ? '启用' : '禁用'
                }, void 0, false, {
                    fileName: "src/pages/table-list/index.tsx",
                    lineNumber: 38,
                    columnNumber: 9
                }, this)
        },
        {
            title: '创建时间',
            dataIndex: 'createdAt',
            key: 'createdAt',
            valueType: 'dateTime'
        },
        {
            title: '更新时间',
            dataIndex: 'updatedAt',
            key: 'updatedAt',
            valueType: 'dateTime'
        },
        {
            title: '操作',
            valueType: 'option',
            key: 'option',
            render: ()=>[
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        type: "link",
                        size: "small",
                        children: "编辑"
                    }, "edit", false, {
                        fileName: "src/pages/table-list/index.tsx",
                        lineNumber: 60,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        type: "link",
                        size: "small",
                        danger: true,
                        children: "删除"
                    }, "delete", false, {
                        fileName: "src/pages/table-list/index.tsx",
                        lineNumber: 63,
                        columnNumber: 9
                    }, this)
                ]
        }
    ];
    // 模拟数据
    const mockData = [
        {
            id: '1',
            name: '示例项目1',
            status: 'active',
            createdAt: '2024-01-01 10:00:00',
            updatedAt: '2024-01-01 10:00:00'
        },
        {
            id: '2',
            name: '示例项目2',
            status: 'inactive',
            createdAt: '2024-01-02 10:00:00',
            updatedAt: '2024-01-02 10:00:00'
        }
    ];
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        title: "表格列表",
        subTitle: "标准的表格列表页面",
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProTable, {
            headerTitle: "表格列表",
            actionRef: actionRef,
            rowKey: "id",
            search: {
                labelWidth: 120
            },
            toolBarRender: ()=>[
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        type: "primary",
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PlusOutlined, {}, void 0, false, {
                            fileName: "src/pages/table-list/index.tsx",
                            lineNumber: 104,
                            columnNumber: 19
                        }, void 0),
                        children: "新建"
                    }, "primary", false, {
                        fileName: "src/pages/table-list/index.tsx",
                        lineNumber: 101,
                        columnNumber: 11
                    }, void 0)
                ],
            request: async ()=>{
                // 模拟API请求
                return {
                    data: mockData,
                    success: true,
                    total: mockData.length
                };
            },
            columns: columns
        }, void 0, false, {
            fileName: "src/pages/table-list/index.tsx",
            lineNumber: 93,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "src/pages/table-list/index.tsx",
        lineNumber: 89,
        columnNumber: 5
    }, this);
};
_s(TableList, "lAyw2T/uV4Uy3DasgwiZt6d71LQ=", false, function() {
    return [
        _max.useIntl
    ];
});
_c = TableList;
var _default = TableList;
var _c;
$RefreshReg$(_c, "TableList");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=src_pages_table-list_index_tsx-async.js.map