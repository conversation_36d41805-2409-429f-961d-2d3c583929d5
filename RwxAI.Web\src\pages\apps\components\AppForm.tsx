import React, { useEffect, useState } from 'react';
import { Modal, Form, Input, InputNumber, Switch, message } from 'antd';
import { useIntl, FormattedMessage } from '@umijs/max';
import { createApp, updateApp } from '@/services/rwxai';

interface AppFormProps {
  visible: boolean;
  onVisibleChange: (visible: boolean) => void;
  initialValues?: RwxAI.App;
  onSuccess: () => void;
}

const AppForm: React.FC<AppFormProps> = ({
  visible,
  onVisibleChange,
  initialValues,
  onSuccess,
}) => {
  const intl = useIntl();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const isEdit = !!initialValues?.Id;

  useEffect(() => {
    if (visible) {
      if (initialValues) {
        form.setFieldsValue(initialValues);
      } else {
        form.resetFields();
      }
    }
  }, [visible, initialValues]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      let response;
      if (isEdit) {
        response = await updateApp(initialValues!.Id, { ...initialValues, ...values });
      } else {
        response = await createApp(values);
      }

      if (response.success) {
        onSuccess();
      }
      // 成功和错误消息会由统一响应处理系统自动显示
    } catch (error) {
      // 表单验证错误等
      console.error('Form validation error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={intl.formatMessage({
        id: isEdit ? 'pages.apps.form.edit.title' : 'pages.apps.form.create.title',
      })}
      open={visible}
      onCancel={() => onVisibleChange(false)}
      onOk={handleSubmit}
      confirmLoading={loading}
      width={600}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          IsEnabled: true,
          RateLimitPerMinute: 60,
        }}
      >
        <Form.Item
          name="Name"
          label={<FormattedMessage id="pages.apps.form.name" />}
          rules={[{ required: true, message: intl.formatMessage({ id: 'pages.apps.form.name.required' }) }]}
        >
          <Input placeholder={intl.formatMessage({ id: 'pages.apps.form.name.placeholder' })} />
        </Form.Item>

        <Form.Item
          name="Description"
          label={<FormattedMessage id="pages.apps.form.description" />}
        >
          <Input.TextArea
            rows={3}
            placeholder={intl.formatMessage({ id: 'pages.apps.form.description.placeholder' })}
          />
        </Form.Item>

        <Form.Item
          name="MaxApiCalls"
          label={<FormattedMessage id="pages.apps.form.maxApiCalls" />}
          help={intl.formatMessage({ id: 'pages.apps.form.maxApiCalls.help' })}
        >
          <InputNumber
            min={0}
            style={{ width: '100%' }}
            placeholder={intl.formatMessage({ id: 'pages.apps.form.maxApiCalls.placeholder' })}
          />
        </Form.Item>

        <Form.Item
          name="RateLimitPerMinute"
          label={<FormattedMessage id="pages.apps.form.rateLimit" />}
          help={intl.formatMessage({ id: 'pages.apps.form.rateLimit.help' })}
        >
          <InputNumber
            min={1}
            max={10000}
            style={{ width: '100%' }}
            placeholder={intl.formatMessage({ id: 'pages.apps.form.rateLimit.placeholder' })}
          />
        </Form.Item>

        <Form.Item
          name="AllowedOrigins"
          label={<FormattedMessage id="pages.apps.form.allowedOrigins" />}
          help={intl.formatMessage({ id: 'pages.apps.form.allowedOrigins.help' })}
        >
          <Input.TextArea
            rows={2}
            placeholder={intl.formatMessage({ id: 'pages.apps.form.allowedOrigins.placeholder' })}
          />
        </Form.Item>

        <Form.Item
          name="IsEnabled"
          label={<FormattedMessage id="pages.apps.form.isEnabled" />}
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AppForm;
