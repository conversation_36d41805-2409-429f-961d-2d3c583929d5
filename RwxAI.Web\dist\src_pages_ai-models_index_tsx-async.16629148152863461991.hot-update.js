globalThis.makoModuleHotUpdate('src/pages/ai-models/index.tsx', {
    modules: {
        "src/pages/ai-models/components/ModelForm.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _rwxai = __mako_require__("src/services/rwxai/index.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const ModelForm = ({ visible, onVisibleChange, initialValues, onSuccess })=>{
                _s();
                const intl = (0, _max.useIntl)();
                const [form] = _antd.Form.useForm();
                const [loading, setLoading] = (0, _react.useState)(false);
                const [providers, setProviders] = (0, _react.useState)([]);
                const [modelTemplates, setModelTemplates] = (0, _react.useState)([]);
                const [loadingTemplates, setLoadingTemplates] = (0, _react.useState)(false);
                const isEdit = !!(initialValues === null || initialValues === void 0 ? void 0 : initialValues.Id);
                (0, _react.useEffect)(()=>{
                    if (visible) initializeForm();
                }, [
                    visible,
                    initialValues
                ]);
                const initializeForm = async ()=>{
                    // 先加载提供商数据
                    await loadProviders();
                    if (initialValues) {
                        var _initialValues_Template_Provider, _initialValues_Template, _initialValues_Template1, _initialValues_Template2, _initialValues_Template_Provider1, _initialValues_Template3;
                        // 映射后端数据结构到表单字段
                        const formData = {
                            // 基本字段
                            Name: initialValues.Name,
                            // 模型ID
                            ModelId: initialValues.ModelId,
                            // 模型服务端点
                            Endpoint: initialValues.Endpoint,
                            // 提供商代码
                            ProviderCode: initialValues.ProviderCode,
                            DisplayName: initialValues.DisplayName,
                            Description: initialValues.Description,
                            ApiKey: initialValues.ApiKey,
                            IsEnabled: initialValues.IsEnabled,
                            IsDefault: initialValues.IsDefault,
                            // 配置参数
                            MaxTokens: initialValues.MaxTokens,
                            Temperature: initialValues.Temperature,
                            FrequencyPenalty: initialValues.FrequencyPenalty,
                            PresencePenalty: initialValues.PresencePenalty,
                            SystemPrompt: initialValues.SystemPrompt,
                            // 关联字段
                            TemplateId: initialValues.ModelTemplateId,
                            ProviderId: (_initialValues_Template = initialValues.Template) === null || _initialValues_Template === void 0 ? void 0 : (_initialValues_Template_Provider = _initialValues_Template.Provider) === null || _initialValues_Template_Provider === void 0 ? void 0 : _initialValues_Template_Provider.Id,
                            // 只读字段，从模板中获取
                            ModelType: (_initialValues_Template1 = initialValues.Template) === null || _initialValues_Template1 === void 0 ? void 0 : _initialValues_Template1.Type,
                            SupportsStreaming: (_initialValues_Template2 = initialValues.Template) === null || _initialValues_Template2 === void 0 ? void 0 : _initialValues_Template2.SupportsStreaming
                        };
                        form.setFieldsValue(formData);
                        // 如果有提供商ID，加载对应的模型模板
                        const providerId = (_initialValues_Template3 = initialValues.Template) === null || _initialValues_Template3 === void 0 ? void 0 : (_initialValues_Template_Provider1 = _initialValues_Template3.Provider) === null || _initialValues_Template_Provider1 === void 0 ? void 0 : _initialValues_Template_Provider1.Id;
                        if (providerId) await loadModelTemplates(providerId);
                    } else {
                        form.resetFields();
                        setModelTemplates([]);
                    }
                };
                // 监听表单字段变化
                const handleFormValuesChange = (changedValues)=>{
                    // 当提供商发生变化时，重新加载模型模板
                    if (changedValues.ProviderId !== undefined) {
                        const providerId = changedValues.ProviderId;
                        loadModelTemplates(providerId);
                        // 清空模型模板选择
                        form.setFieldValue('TemplateId', undefined);
                        // 清空相关字段，因为要重新选择模板
                        form.setFieldsValue({
                            ModelId: undefined,
                            ModelType: undefined,
                            Endpoint: undefined,
                            SupportsStreaming: undefined,
                            Name: undefined,
                            Description: undefined,
                            DisplayName: undefined,
                            ProviderCode: undefined
                        });
                    }
                    // 当选择模型模板时，自动填充相关字段
                    if (changedValues.TemplateId !== undefined) {
                        const selectedTemplate = modelTemplates.find((template)=>template.Id === changedValues.TemplateId);
                        if (selectedTemplate) {
                            var _selectedTemplate_Provider;
                            // 自动填充模板的所有信息
                            const updates = {
                                ModelType: selectedTemplate.Type,
                                Name: selectedTemplate.Name,
                                ModelId: selectedTemplate.ModelId,
                                Endpoint: selectedTemplate.Endpoint,
                                Description: selectedTemplate.Description,
                                DisplayName: selectedTemplate.DisplayName,
                                SupportsStreaming: selectedTemplate.SupportsStreaming,
                                ProviderCode: (_selectedTemplate_Provider = selectedTemplate.Provider) === null || _selectedTemplate_Provider === void 0 ? void 0 : _selectedTemplate_Provider.Code
                            };
                            // 填充可选的配置参数
                            if (selectedTemplate.MaxOutputLength !== undefined) updates.MaxTokens = selectedTemplate.MaxOutputLength;
                            form.setFieldsValue(updates);
                        }
                    }
                };
                const loadProviders = async ()=>{
                    try {
                        const response = await (0, _rwxai.getEnabledAIProviders)();
                        if (response.success) setProviders(response.data || []);
                        else setProviders([]);
                    } catch (error) {
                        setProviders([]);
                    }
                // 错误消息会由统一响应处理系统自动显示
                };
                // 根据提供商加载模型模板
                const loadModelTemplates = async (providerId)=>{
                    if (!providerId) {
                        setModelTemplates([]);
                        return;
                    }
                    setLoadingTemplates(true);
                    try {
                        // 根据提供商获取所有模板
                        const response = await (0, _rwxai.getAIModelTemplatesByProviderAndType)({
                            providerId,
                            modelType: undefined
                        });
                        if (response.success) setModelTemplates(response.data || []);
                        else setModelTemplates([]);
                    } catch (error) {
                        setModelTemplates([]);
                    } finally{
                        setLoadingTemplates(false);
                    }
                };
                const handleSubmit = async ()=>{
                    try {
                        const values = await form.validateFields();
                        setLoading(true);
                        // 构建提交数据，只保存模板ID和必要字段
                        const submitData = {
                            modelTemplateId: values.TemplateId,
                            name: values.Name,
                            modelId: values.ModelId,
                            endpoint: values.Endpoint,
                            providerCode: values.ProviderCode,
                            displayName: values.DisplayName,
                            description: values.Description,
                            apiKey: values.ApiKey,
                            isEnabled: values.IsEnabled,
                            isDefault: values.IsDefault,
                            // 可选的自定义配置参数
                            maxTokens: values.MaxTokens,
                            temperature: values.Temperature,
                            frequencyPenalty: values.FrequencyPenalty,
                            presencePenalty: values.PresencePenalty,
                            systemPrompt: values.SystemPrompt
                        };
                        let response;
                        if (isEdit) response = await (0, _rwxai.updateAIModel)(initialValues.Id, {
                            ...initialValues,
                            ...submitData
                        });
                        else response = await (0, _rwxai.createAIModel)(submitData);
                        if (response.success) onSuccess();
                    // 成功和错误消息会由统一响应处理系统自动显示
                    } catch (error) {
                        // 表单验证错误等
                        console.error('Form validation error:', error);
                    } finally{
                        setLoading(false);
                    }
                };
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                    title: intl.formatMessage({
                        id: isEdit ? 'pages.aiModels.form.edit.title' : 'pages.aiModels.form.create.title'
                    }),
                    open: visible,
                    onCancel: ()=>onVisibleChange(false),
                    onOk: handleSubmit,
                    confirmLoading: loading,
                    width: 900,
                    style: {
                        top: 50
                    },
                    styles: {
                        body: {
                            maxHeight: '70vh',
                            overflowY: 'auto',
                            padding: '24px'
                        }
                    },
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                        form: form,
                        layout: "vertical",
                        onValuesChange: handleFormValuesChange,
                        initialValues: {
                            IsEnabled: true,
                            Temperature: 0.7
                        },
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    display: 'flex',
                                    gap: '16px'
                                },
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        name: "ProviderId",
                                        label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                            id: "pages.aiModels.form.provider"
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                            lineNumber: 258,
                                            columnNumber: 20
                                        }, void 0),
                                        rules: [
                                            {
                                                required: true,
                                                message: intl.formatMessage({
                                                    id: 'pages.aiModels.form.provider.required'
                                                })
                                            }
                                        ],
                                        tooltip: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                            id: "pages.aiModels.form.provider.tooltip"
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                            lineNumber: 260,
                                            columnNumber: 22
                                        }, void 0),
                                        style: {
                                            flex: 1
                                        },
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select, {
                                            placeholder: intl.formatMessage({
                                                id: 'pages.aiModels.form.provider.placeholder'
                                            }),
                                            children: providers.map((provider)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                                    value: provider.Id,
                                                    children: provider.DisplayName || provider.Name
                                                }, provider.Id, false, {
                                                    fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                                    lineNumber: 265,
                                                    columnNumber: 17
                                                }, this))
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                            lineNumber: 263,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                        lineNumber: 256,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        name: "TemplateId",
                                        label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                            id: "pages.aiModels.form.template"
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                            lineNumber: 274,
                                            columnNumber: 20
                                        }, void 0),
                                        rules: [
                                            {
                                                required: true,
                                                message: intl.formatMessage({
                                                    id: 'pages.aiModels.form.template.required'
                                                })
                                            }
                                        ],
                                        tooltip: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                            id: "pages.aiModels.form.template.tooltip"
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                            lineNumber: 276,
                                            columnNumber: 22
                                        }, void 0),
                                        style: {
                                            flex: 1
                                        },
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select, {
                                            placeholder: intl.formatMessage({
                                                id: 'pages.aiModels.form.template.placeholder'
                                            }),
                                            loading: loadingTemplates,
                                            allowClear: true,
                                            showSearch: true,
                                            optionFilterProp: "children",
                                            filterOption: (input, option)=>String((option === null || option === void 0 ? void 0 : option.children) || '').toLowerCase().includes(input.toLowerCase()),
                                            children: modelTemplates.map((template)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                                    value: template.Id,
                                                    children: [
                                                        template.DisplayName || template.Name,
                                                        " (",
                                                        template.ModelId,
                                                        ")"
                                                    ]
                                                }, template.Id, true, {
                                                    fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                                    lineNumber: 290,
                                                    columnNumber: 17
                                                }, this))
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                            lineNumber: 279,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                        lineNumber: 272,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                lineNumber: 255,
                                columnNumber: 9
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    display: 'flex',
                                    gap: '16px'
                                },
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        name: "Name",
                                        label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                            id: "pages.aiModels.form.name"
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                            lineNumber: 302,
                                            columnNumber: 20
                                        }, void 0),
                                        rules: [
                                            {
                                                required: true,
                                                message: intl.formatMessage({
                                                    id: 'pages.aiModels.form.name.required'
                                                })
                                            }
                                        ],
                                        style: {
                                            flex: 1
                                        },
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                            placeholder: intl.formatMessage({
                                                id: 'pages.aiModels.form.name.placeholder'
                                            })
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                            lineNumber: 306,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                        lineNumber: 300,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        name: "DisplayName",
                                        label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                            id: "pages.aiModels.form.displayName"
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                            lineNumber: 311,
                                            columnNumber: 20
                                        }, void 0),
                                        style: {
                                            flex: 1
                                        },
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                            placeholder: intl.formatMessage({
                                                id: 'pages.aiModels.form.displayName.placeholder'
                                            })
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                            lineNumber: 314,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                        lineNumber: 309,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                lineNumber: 299,
                                columnNumber: 9
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                name: "Description",
                                label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                    id: "pages.aiModels.form.description"
                                }, void 0, false, {
                                    fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                    lineNumber: 320,
                                    columnNumber: 18
                                }, void 0),
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input.TextArea, {
                                    rows: 2,
                                    placeholder: intl.formatMessage({
                                        id: 'pages.aiModels.form.description.placeholder'
                                    })
                                }, void 0, false, {
                                    fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                    lineNumber: 322,
                                    columnNumber: 11
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                lineNumber: 318,
                                columnNumber: 9
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                name: "SystemPrompt",
                                label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                    id: "pages.aiModels.form.systemPrompt"
                                }, void 0, false, {
                                    fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                    lineNumber: 330,
                                    columnNumber: 18
                                }, void 0),
                                tooltip: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                    id: "pages.aiModels.form.systemPrompt.tooltip"
                                }, void 0, false, {
                                    fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                    lineNumber: 331,
                                    columnNumber: 20
                                }, void 0),
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input.TextArea, {
                                    rows: 3,
                                    placeholder: intl.formatMessage({
                                        id: 'pages.aiModels.form.systemPrompt.placeholder'
                                    })
                                }, void 0, false, {
                                    fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                    lineNumber: 333,
                                    columnNumber: 11
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                lineNumber: 328,
                                columnNumber: 9
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                name: "ApiKey",
                                label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                    id: "pages.aiModels.form.apiKey"
                                }, void 0, false, {
                                    fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                    lineNumber: 341,
                                    columnNumber: 18
                                }, void 0),
                                tooltip: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                    id: "pages.aiModels.form.apiKey.tooltip"
                                }, void 0, false, {
                                    fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                    lineNumber: 342,
                                    columnNumber: 20
                                }, void 0),
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input.Password, {
                                    placeholder: intl.formatMessage({
                                        id: 'pages.aiModels.form.apiKey.placeholder'
                                    })
                                }, void 0, false, {
                                    fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                    lineNumber: 344,
                                    columnNumber: 11
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                lineNumber: 339,
                                columnNumber: 9
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    display: 'flex',
                                    gap: '16px'
                                },
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        name: "ModelId",
                                        label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                            id: "pages.aiModels.form.modelId"
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                            lineNumber: 351,
                                            columnNumber: 20
                                        }, void 0),
                                        style: {
                                            flex: 1
                                        },
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                            disabled: true,
                                            placeholder: intl.formatMessage({
                                                id: 'pages.aiModels.form.modelId.readonly'
                                            })
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                            lineNumber: 354,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                        lineNumber: 349,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        name: "ModelType",
                                        label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                            id: "pages.aiModels.form.modelType"
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                            lineNumber: 359,
                                            columnNumber: 20
                                        }, void 0),
                                        style: {
                                            flex: 1
                                        },
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select, {
                                            disabled: true,
                                            placeholder: intl.formatMessage({
                                                id: 'pages.aiModels.form.modelType.readonly'
                                            }),
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                                    value: "0",
                                                    children: "Chat"
                                                }, void 0, false, {
                                                    fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                                    lineNumber: 363,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                                    value: "1",
                                                    children: "Embedding"
                                                }, void 0, false, {
                                                    fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                                    lineNumber: 364,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                                    value: "2",
                                                    children: "Image Generation"
                                                }, void 0, false, {
                                                    fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                                    lineNumber: 365,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                                    value: "3",
                                                    children: "Text to Speech"
                                                }, void 0, false, {
                                                    fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                                    lineNumber: 366,
                                                    columnNumber: 15
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                            lineNumber: 362,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                        lineNumber: 357,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                lineNumber: 348,
                                columnNumber: 9
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    display: 'flex',
                                    gap: '16px'
                                },
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        name: "Endpoint",
                                        label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                            id: "pages.aiModels.form.endpoint"
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                            lineNumber: 374,
                                            columnNumber: 20
                                        }, void 0),
                                        style: {
                                            flex: 1
                                        },
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                            disabled: true,
                                            placeholder: "从模板自动获取"
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                            lineNumber: 377,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                        lineNumber: 372,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        name: "SupportsStreaming",
                                        label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                            id: "pages.aiModels.form.supportsStreaming"
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                            lineNumber: 382,
                                            columnNumber: 20
                                        }, void 0),
                                        style: {
                                            flex: 1
                                        },
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select, {
                                            disabled: true,
                                            placeholder: "从模板自动获取",
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                                    value: true,
                                                    children: "支持"
                                                }, void 0, false, {
                                                    fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                                    lineNumber: 386,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                                    value: false,
                                                    children: "不支持"
                                                }, void 0, false, {
                                                    fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                                    lineNumber: 387,
                                                    columnNumber: 15
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                            lineNumber: 385,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                        lineNumber: 380,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                lineNumber: 371,
                                columnNumber: 9
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    display: 'flex',
                                    gap: '16px'
                                },
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        name: "MaxTokens",
                                        label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                            id: "pages.aiModels.form.maxTokens"
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                            lineNumber: 396,
                                            columnNumber: 20
                                        }, void 0),
                                        style: {
                                            flex: 1
                                        },
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.InputNumber, {
                                            min: 1,
                                            max: 100000,
                                            style: {
                                                width: '100%'
                                            },
                                            placeholder: intl.formatMessage({
                                                id: 'pages.aiModels.form.maxTokens.placeholder'
                                            })
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                            lineNumber: 399,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                        lineNumber: 394,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        name: "Temperature",
                                        label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                            id: "pages.aiModels.form.temperature"
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                            lineNumber: 409,
                                            columnNumber: 20
                                        }, void 0),
                                        style: {
                                            flex: 1
                                        },
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.InputNumber, {
                                            min: 0,
                                            max: 2,
                                            step: 0.1,
                                            style: {
                                                width: '100%'
                                            },
                                            placeholder: intl.formatMessage({
                                                id: 'pages.aiModels.form.temperature.placeholder'
                                            })
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                            lineNumber: 412,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                        lineNumber: 407,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                lineNumber: 393,
                                columnNumber: 9
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    display: 'flex',
                                    gap: '16px',
                                    alignItems: 'flex-end'
                                },
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        name: "IsEnabled",
                                        label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                            id: "pages.aiModels.form.isEnabled"
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                            lineNumber: 425,
                                            columnNumber: 20
                                        }, void 0),
                                        valuePropName: "checked",
                                        style: {
                                            flex: 1
                                        },
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Switch, {}, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                            lineNumber: 429,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                        lineNumber: 423,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        name: "IsDefault",
                                        label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                            id: "pages.aiModels.form.isDefault"
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                            lineNumber: 434,
                                            columnNumber: 20
                                        }, void 0),
                                        valuePropName: "checked",
                                        tooltip: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                            id: "pages.aiModels.form.isDefault.tooltip"
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                            lineNumber: 436,
                                            columnNumber: 22
                                        }, void 0),
                                        style: {
                                            flex: 1
                                        },
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Switch, {}, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                            lineNumber: 439,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                        lineNumber: 432,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                lineNumber: 422,
                                columnNumber: 9
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/ai-models/components/ModelForm.tsx",
                        lineNumber: 245,
                        columnNumber: 7
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/ai-models/components/ModelForm.tsx",
                    lineNumber: 231,
                    columnNumber: 5
                }, this);
            };
            _s(ModelForm, "NGfFwAXKyFht0v74cvOAIlexsus=", false, function() {
                return [
                    _max.useIntl,
                    _antd.Form.useForm
                ];
            });
            _c = ModelForm;
            var _default = ModelForm;
            var _c;
            $RefreshReg$(_c, "ModelForm");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '5760159428415107075';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/.umi/plugin-openapi/openapi.tsx": [
            "vendors",
            "src/.umi/plugin-openapi/openapi.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Admin.tsx": [
            "p__Admin"
        ],
        "src/pages/Welcome.tsx": [
            "p__Welcome"
        ],
        "src/pages/ai-models/index.tsx": [
            "common",
            "src/pages/ai-models/index.tsx"
        ],
        "src/pages/api-test/index.tsx": [
            "common",
            "src/pages/api-test/index.tsx"
        ],
        "src/pages/apps/index.tsx": [
            "common",
            "p__apps__index"
        ],
        "src/pages/auth-test/index.tsx": [
            "common",
            "src/pages/auth-test/index.tsx"
        ],
        "src/pages/chat/chat-interface.tsx": [
            "vendors",
            "common",
            "src/pages/chat/chat-interface.tsx"
        ],
        "src/pages/chat/index.tsx": [
            "common",
            "p__chat__index"
        ],
        "src/pages/chat/session/[id].tsx": [
            "common",
            "p__chat__session__id"
        ],
        "src/pages/knowledge/[id]/files.tsx": [
            "common",
            "p__knowledge__id__files"
        ],
        "src/pages/knowledge/index.tsx": [
            "common",
            "p__knowledge__index"
        ],
        "src/pages/plugins/index.tsx": [
            "common",
            "p__plugins__index"
        ],
        "src/pages/response-demo/index.tsx": [
            "src/pages/response-demo/index.tsx"
        ],
        "src/pages/table-list/index.tsx": [
            "src/pages/table-list/index.tsx"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ],
        "src/pages/user/register/index.tsx": [
            "common",
            "p__user__register__index"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_ai-models_index_tsx-async.16629148152863461991.hot-update.js.map