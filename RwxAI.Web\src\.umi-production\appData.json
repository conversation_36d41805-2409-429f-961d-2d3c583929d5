{"cwd": "D:\\rwx-ai\\RwxAI.Web", "pkg": {"name": "ant-design-pro", "version": "6.0.0", "private": true, "description": "An out-of-box UI solution for enterprise applications", "repository": "**************:ant-design/ant-design-pro.git", "scripts": {"analyze": "cross-env ANALYZE=1 max build", "biome:lint": "npx @biomejs/biome lint", "build": "max build", "deploy": "npm run build && npm run gh-pages", "dev": "npm run start:dev", "gh-pages": "gh-pages -d dist", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "postinstall": "max setup", "jest": "jest", "lint": "npm run biome:lint && npm run tsc", "lint-staged": "lint-staged", "openapi": "max openapi", "prepare": "husky", "preview": "npm run build && max preview --port 8000", "record": "cross-env NODE_ENV=development REACT_APP_ENV=test max record --scene=login", "serve": "umi-serve", "start": "cross-env UMI_ENV=dev max dev", "start:dev": "cross-env REACT_APP_ENV=dev MOCK=none UMI_ENV=dev max dev", "start:no-mock": "cross-env MOCK=none UMI_ENV=dev max dev", "start:pre": "cross-env REACT_APP_ENV=pre UMI_ENV=dev max dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none UMI_ENV=dev max dev", "test": "jest", "test:coverage": "npm run jest -- --coverage", "test:update": "npm run jest -- -u", "tsc": "tsc --noEmit"}, "browserslist": ["defaults"], "dependencies": {"@ant-design/icons": "^5.6.1", "@ant-design/pro-components": "^2.7.19", "@ant-design/v5-patch-for-react-19": "^1.0.3", "antd": "^5.25.4", "antd-style": "^3.7.0", "classnames": "^2.5.1", "dayjs": "^1.11.13", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@ant-design/pro-cli": "^3.3.0", "@biomejs/biome": "^2.0.6", "@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.0.1", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/lodash": "^4.17.10", "@types/node": "^24.0.10", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "@types/react-helmet": "^6.1.11", "@umijs/max": "^4.3.24", "cross-env": "^7.0.3", "express": "^4.21.1", "gh-pages": "^6.1.1", "husky": "^9.1.7", "jest": "^30.0.4", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^16.1.2", "mockjs": "^1.1.0", "ts-node": "^10.9.2", "typescript": "^5.6.3", "umi-presets-pro": "^2.0.3", "umi-serve": "^1.9.11"}, "engines": {"node": ">=20.0.0"}}, "pkgPath": "D:\\rwx-ai\\RwxAI.Web\\package.json", "plugins": {"./node_modules/@umijs/core/dist/service/servicePlugin": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "preset", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/core/dist/service/servicePlugin.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/core/dist/service/servicePlugin", "key": "servicePlugin"}, "@umijs/preset-umi": {"config": {}, "time": {"hooks": {}, "register": 69}, "enableBy": "register", "type": "preset", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/index.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "@umijs/preset-umi", "key": "umi"}, "./node_modules/@umijs/max/dist/preset": {"config": {}, "time": {"hooks": {}, "register": 17}, "enableBy": "register", "type": "preset", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/max/dist/preset.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/max/dist/preset", "key": "preset"}, "umi-presets-pro": {"config": {}, "time": {"hooks": {"onStart": [2]}, "register": 10}, "enableBy": "register", "type": "preset", "path": "D:/rwx-ai/RwxAI.Web/node_modules/umi-presets-pro/dist/index.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "umi-presets-pro", "key": "umiPresetsPro"}, "./node_modules/@umijs/preset-umi/dist/registerMethods": {"config": {}, "time": {"hooks": {"onStart": [0]}, "register": 12}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/registerMethods.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/registerMethods", "key": "registerMethods"}, "@umijs/did-you-know": {"config": {}, "time": {"hooks": {"onStart": [5]}, "register": 8}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/did-you-know/dist/plugin.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "@umijs/did-you-know", "key": "umijsDidYouKnow"}, "./node_modules/@umijs/preset-umi/dist/features/404/404": {"config": {}, "time": {"hooks": {"modifyRoutes": [0]}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/404/404.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/404/404", "key": "404"}, "./node_modules/@umijs/preset-umi/dist/features/aiDev/aiDev": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/aiDev/aiDev.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/aiDev/aiDev", "key": "ai<PERSON>ev"}, "./node_modules/@umijs/preset-umi/dist/features/appData/appData": {"config": {}, "time": {"hooks": {"modifyAppData": [84]}, "register": 61}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/appData/appData.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/appData/appData", "key": "appData"}, "./node_modules/@umijs/preset-umi/dist/features/appData/umiInfo": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/appData/umiInfo.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/appData/umiInfo", "key": "umiInfo"}, "./node_modules/@umijs/preset-umi/dist/features/check/check": {"config": {}, "time": {"hooks": {"onCheckConfig": [0], "onCheck": [0]}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/check/check.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/check/check", "key": "check"}, "./node_modules/@umijs/preset-umi/dist/features/check/babel722": {"config": {}, "time": {"hooks": {"onCheck": [1]}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/check/babel722.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/check/babel722", "key": "babel722"}, "./node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting", "key": "codeSplitting"}, "./node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins": {"config": {}, "time": {"hooks": {"modifyConfig": [0]}, "register": 25}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins", "key": "configPlugins"}, "virtual: config-title": {"id": "virtual: config-title", "key": "title", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-styles": {"id": "virtual: config-styles", "key": "styles", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-scripts": {"id": "virtual: config-scripts", "key": "scripts", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-routes": {"id": "virtual: config-routes", "key": "routes", "config": {"onChange": "regenerateTmpFiles"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-routeLoader": {"id": "virtual: config-routeLoader", "key": "routeLoader", "config": {"default": {"moduleType": "esm"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-reactRouter5Compat": {"id": "virtual: config-reactRouter5Compat", "key": "reactRouter5Compat", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-presets": {"id": "virtual: config-presets", "key": "presets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-plugins": {"id": "virtual: config-plugins", "key": "plugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-npmClient": {"id": "virtual: config-npmClient", "key": "npmClient", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mountElementId": {"id": "virtual: config-mountElementId", "key": "mountElementId", "config": {"default": "root"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-metas": {"id": "virtual: config-metas", "key": "metas", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-links": {"id": "virtual: config-links", "key": "links", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-historyWithQuery": {"id": "virtual: config-historyWithQuery", "key": "historyWithQuery", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-history": {"id": "virtual: config-history", "key": "history", "config": {"default": {"type": "browser"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-headScripts": {"id": "virtual: config-headScripts", "key": "headScripts", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-esbuildMinifyIIFE": {"id": "virtual: config-esbuildMinifyIIFE", "key": "esbuildMinifyIIFE", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-conventionRoutes": {"id": "virtual: config-conventionRoutes", "key": "conventionRoutes", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-conventionLayout": {"id": "virtual: config-conventionLayout", "key": "conventionLayout", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-base": {"id": "virtual: config-base", "key": "base", "config": {"default": "/"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-analyze": {"id": "virtual: config-analyze", "key": "analyze", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-writeToDisk": {"id": "virtual: config-writeToDisk", "key": "writeToDisk", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-transformRuntime": {"id": "virtual: config-transformRuntime", "key": "transformRuntime", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-theme": {"id": "virtual: config-theme", "key": "theme", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-targets": {"id": "virtual: config-targets", "key": "targets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-svgr": {"id": "virtual: config-svgr", "key": "svgr", "config": {"default": {}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-svgo": {"id": "virtual: config-svgo", "key": "svgo", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-stylusLoader": {"id": "virtual: config-stylusLoader", "key": "stylus<PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-styleLoader": {"id": "virtual: config-style<PERSON>oader", "key": "<PERSON><PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-srcTranspilerOptions": {"id": "virtual: config-srcTranspilerOptions", "key": "srcTranspilerOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-srcTranspiler": {"id": "virtual: config-srcTranspiler", "key": "srcTranspiler", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-sassLoader": {"id": "virtual: config-sassLoader", "key": "sass<PERSON><PERSON>der", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-runtimePublicPath": {"id": "virtual: config-runtimePublicPath", "key": "runtimePublicPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-purgeCSS": {"id": "virtual: config-purgeCSS", "key": "purgeCSS", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-publicPath": {"id": "virtual: config-publicPath", "key": "publicPath", "config": {"default": "/"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-proxy": {"id": "virtual: config-proxy", "key": "proxy", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-postcssLoader": {"id": "virtual: config-postcssLoader", "key": "postcss<PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-outputPath": {"id": "virtual: config-outputPath", "key": "outputPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-normalCSSLoaderModules": {"id": "virtual: config-normalCSSLoaderModules", "key": "normalCSSLoaderModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mfsu": {"id": "virtual: config-mfsu", "key": "mfsu", "config": {"default": {"strategy": "eager"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mdx": {"id": "virtual: config-mdx", "key": "mdx", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-manifest": {"id": "virtual: config-manifest", "key": "manifest", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-lessLoader": {"id": "virtual: config-less<PERSON><PERSON>der", "key": "<PERSON><PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-jsMinifierOptions": {"id": "virtual: config-jsMinifierOptions", "key": "jsMinifierOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-jsMinifier": {"id": "virtual: config-jsMinifier", "key": "jsMinifier", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-inlineLimit": {"id": "virtual: config-inlineLimit", "key": "inlineLimit", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-ignoreMomentLocale": {"id": "virtual: config-ignoreMomentLocale", "key": "ignoreMomentLocale", "config": {"default": true}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-https": {"id": "virtual: config-https", "key": "https", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-hash": {"id": "virtual: config-hash", "key": "hash", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-forkTSChecker": {"id": "virtual: config-fork<PERSON><PERSON><PERSON><PERSON>", "key": "forkTSChecker", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-fastRefresh": {"id": "virtual: config-fastRefresh", "key": "fastRefresh", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraPostCSSPlugins": {"id": "virtual: config-extraPostCSSPlugins", "key": "extraPostCSSPlugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelPresets": {"id": "virtual: config-extraBabelPresets", "key": "extraBabelPresets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelPlugins": {"id": "virtual: config-extraBabelPlugins", "key": "extraBabelPlugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelIncludes": {"id": "virtual: config-extraBabelIncludes", "key": "extraBabelIncludes", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-externals": {"id": "virtual: config-externals", "key": "externals", "config": {"default": {}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-esm": {"id": "virtual: config-esm", "key": "esm", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-devtool": {"id": "virtual: config-devtool", "key": "devtool", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-depTranspiler": {"id": "virtual: config-depTranspiler", "key": "depTranspiler", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-define": {"id": "virtual: config-define", "key": "define", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-deadCode": {"id": "virtual: config-deadCode", "key": "deadCode", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssPublicPath": {"id": "virtual: config-cssPublicPath", "key": "cssPublicPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssMinifierOptions": {"id": "virtual: config-cssMinifierOptions", "key": "cssMinifierOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssMinifier": {"id": "virtual: config-cssMinifier", "key": "cssMinifier", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssLoaderModules": {"id": "virtual: config-cssLoaderModules", "key": "cssLoaderModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssLoader": {"id": "virtual: config-cssLoader", "key": "cssL<PERSON>der", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-copy": {"id": "virtual: config-copy", "key": "copy", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-checkDepCssModules": {"id": "virtual: config-checkDepCssModules", "key": "checkDepCssModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-chainWebpack": {"id": "virtual: config-chainWebpack", "key": "chainWebpack", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cacheDirectoryPath": {"id": "virtual: config-cacheDirectoryPath", "key": "cacheDirectoryPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 1}}, "virtual: config-babelLoaderCustomize": {"id": "virtual: config-babelLoaderCustomize", "key": "babelLoaderCustomize", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-autoprefixer": {"id": "virtual: config-autoprefixer", "key": "autoprefixer", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-autoCSSModules": {"id": "virtual: config-autoCSSModules", "key": "autoCSSModules", "config": {"default": true}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-alias": {"id": "virtual: config-alias", "key": "alias", "config": {"default": {"umi": "@@/exports", "react": "D:\\rwx-ai\\RwxAI.Web\\node_modules\\react", "react-dom": "D:\\rwx-ai\\RwxAI.Web\\node_modules\\react-dom", "react-router": "D:\\rwx-ai\\RwxAI.Web\\node_modules\\react-router", "react-router-dom": "D:\\rwx-ai\\RwxAI.Web\\node_modules\\react-router-dom"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "./node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin", "key": "crossorigin"}, "./node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand": {"config": {}, "time": {"hooks": {"onStart": [0]}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand", "key": "deps<PERSON>n<PERSON><PERSON><PERSON>"}, "./node_modules/@umijs/preset-umi/dist/features/devTool/devTool": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/devTool/devTool.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/devTool/devTool", "key": "devTool"}, "./node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker": {"config": {}, "time": {"hooks": {}, "register": 175}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker", "key": "esbuildHelperChecker"}, "./node_modules/@umijs/preset-umi/dist/features/esmi/esmi": {"config": {}, "time": {"hooks": {}, "register": 305}, "enableBy": "config", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/esmi/esmi.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/esmi/esmi", "key": "esmi"}, "./node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic": {"config": {}, "time": {"hooks": {"onCheck": [0]}, "register": 40}, "enableBy": "config", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic", "key": "exportStatic"}, "./node_modules/@umijs/preset-umi/dist/features/favicons/favicons": {"config": {}, "time": {"hooks": {"modifyAppData": [1]}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/favicons/favicons.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/favicons/favicons", "key": "favicons"}, "./node_modules/@umijs/preset-umi/dist/features/helmet/helmet": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/helmet/helmet.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/helmet/helmet", "key": "helmet"}, "./node_modules/@umijs/preset-umi/dist/features/icons/icons": {"config": {}, "time": {"hooks": {}, "register": 5}, "enableBy": "config", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/icons/icons.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/icons/icons", "key": "icons"}, "./node_modules/@umijs/preset-umi/dist/features/mock/mock": {"config": {}, "time": {"hooks": {}, "register": 86}, "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/mock/mock.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/mock/mock", "key": "mock"}, "./node_modules/@umijs/preset-umi/dist/features/mpa/mpa": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "config", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/mpa/mpa.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/mpa/mpa", "key": "mpa"}, "./node_modules/@umijs/preset-umi/dist/features/okam/okam": {"config": {}, "time": {"hooks": {"modifyAppData": [0], "onCheck": [0]}, "register": 3}, "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/okam/okam.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/okam/okam", "key": "okam"}, "./node_modules/@umijs/preset-umi/dist/features/overrides/overrides": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/overrides/overrides.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/overrides/overrides", "key": "overrides"}, "./node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "config", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency", "key": "phantomDependency"}, "./node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill": {"config": {}, "time": {"hooks": {"modifyConfig": [1]}, "register": 9}, "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill", "key": "polyfill"}, "./node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill", "key": "publicPathPolyfill"}, "./node_modules/@umijs/preset-umi/dist/features/prepare/prepare": {"config": {}, "time": {"hooks": {}, "register": 7}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/prepare/prepare.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/prepare/prepare", "key": "prepare"}, "./node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch", "key": "routePrefetch"}, "./node_modules/@umijs/preset-umi/dist/features/stagewise/stagewise": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/stagewise/stagewise.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/stagewise/stagewise", "key": "stagewise"}, "./node_modules/@umijs/preset-umi/dist/features/terminal/terminal": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/terminal/terminal.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/terminal/terminal", "key": "terminal"}, "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles": {"config": {}, "time": {"hooks": {}, "register": 14}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles", "key": "tmpFiles"}, "./node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "config", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader", "key": "clientLoader"}, "./node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps": {"config": {}, "time": {"hooks": {}, "register": 2}, "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps", "key": "routeProps"}, "./node_modules/@umijs/preset-umi/dist/features/ssr/ssr": {"config": {}, "time": {"hooks": {}, "register": 7}, "enableBy": "config", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/ssr/ssr.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/ssr/ssr", "key": "ssr"}, "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes": {"config": {}, "time": {"hooks": {}, "register": 8}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes", "key": "configTypes"}, "./node_modules/@umijs/preset-umi/dist/features/transform/transform": {"config": {}, "time": {"hooks": {}, "register": 11}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/transform/transform.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/transform/transform", "key": "transform"}, "./node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport": {"config": {}, "time": {"hooks": {}, "register": 14}, "enableBy": "config", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport", "key": "lowImport"}, "./node_modules/@umijs/preset-umi/dist/features/vite/vite": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/vite/vite.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/vite/vite", "key": "vite"}, "./node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute": {"config": {}, "time": {"hooks": {}, "register": 20}, "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute", "key": "apiRoute"}, "./node_modules/@umijs/preset-umi/dist/features/monorepo/redirect": {"config": {}, "time": {"hooks": {}, "register": 54}, "enableBy": "config", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/monorepo/redirect.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/monorepo/redirect", "key": "monorepoRedirect"}, "./node_modules/@umijs/preset-umi/dist/features/test/test": {"config": {}, "time": {"hooks": {}, "register": 3}, "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/test/test.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/test/test", "key": "test"}, "./node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent": {"config": {}, "time": {"hooks": {}, "register": 3}, "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent", "key": "clickToComponent"}, "./node_modules/@umijs/preset-umi/dist/features/legacy/legacy": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "config", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/legacy/legacy.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/legacy/legacy", "key": "legacy"}, "./node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose", "key": "classPropertiesLoose"}, "./node_modules/@umijs/preset-umi/dist/features/webpack/webpack": {"config": {}, "time": {"hooks": {}, "register": 4}, "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/webpack/webpack.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/webpack/webpack", "key": "preset-umi:webpack"}, "./node_modules/@umijs/preset-umi/dist/features/swc/swc": {"config": {}, "time": {"hooks": {"addOnDemandDeps": [0]}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/swc/swc.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/swc/swc", "key": "swc"}, "./node_modules/@umijs/preset-umi/dist/features/ui/ui": {"config": {}, "time": {"hooks": {}, "register": 10}, "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/ui/ui.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/ui/ui", "key": "ui"}, "./node_modules/@umijs/preset-umi/dist/features/mako/mako": {"config": {}, "time": {"hooks": {"modifyConfig": [1], "onStart": [4]}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/mako/mako.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/mako/mako", "key": "mako"}, "./node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian": {"config": {}, "time": {"hooks": {}, "register": 8}, "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian", "key": "hm<PERSON><PERSON><PERSON><PERSON>"}, "./node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad": {"config": {}, "time": {"hooks": {}, "register": 5}, "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad", "key": "routePreloadOnLoad"}, "./node_modules/@umijs/preset-umi/dist/features/forget/forget": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "config", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/forget/forget.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/forget/forget", "key": "forget"}, "./node_modules/@umijs/preset-umi/dist/features/bundler/bundler": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/features/bundler/bundler.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/features/bundler/bundler", "key": "preset-umi:bundler"}, "./node_modules/@umijs/preset-umi/dist/commands/build": {"config": {}, "time": {"hooks": {}, "register": 12}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/commands/build.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/commands/build", "key": "build"}, "./node_modules/@umijs/preset-umi/dist/commands/config/config": {"config": {}, "time": {"hooks": {}, "register": 95}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/commands/config/config.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/commands/config/config", "key": "config"}, "./node_modules/@umijs/preset-umi/dist/commands/dev/dev": {"config": {}, "time": {"hooks": {}, "register": 137}, "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/commands/dev/dev.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/commands/dev/dev", "key": "dev"}, "./node_modules/@umijs/preset-umi/dist/commands/help": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/commands/help.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/commands/help", "key": "help"}, "./node_modules/@umijs/preset-umi/dist/commands/lint": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/commands/lint.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/commands/lint", "key": "lint"}, "./node_modules/@umijs/preset-umi/dist/commands/setup": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/commands/setup.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/commands/setup", "key": "setup"}, "./node_modules/@umijs/preset-umi/dist/commands/deadcode": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/commands/deadcode.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/commands/deadcode", "key": "deadcode"}, "./node_modules/@umijs/preset-umi/dist/commands/version": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/commands/version.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/commands/version", "key": "version"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/page": {"config": {}, "time": {"hooks": {}, "register": 8}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/commands/generators/page.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/page", "key": "generator:page"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/prettier": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/commands/generators/prettier.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/prettier", "key": "generator:prettier"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig", "key": "generator:tsconfig"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/jest": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/commands/generators/jest.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/jest", "key": "generator:jest"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss", "key": "generator:tailwindcss"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/dva": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/commands/generators/dva.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/dva", "key": "generator:dva"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/component": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/commands/generators/component.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/component", "key": "generator:component"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/mock": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/commands/generators/mock.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/mock", "key": "generator:mock"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/cypress": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/commands/generators/cypress.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/cypress", "key": "generator:cypress"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/api": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/commands/generators/api.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/api", "key": "generator:api"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/precommit": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/commands/generators/precommit.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/precommit", "key": "generator:precommit"}, "./node_modules/@umijs/preset-umi/dist/commands/plugin": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/commands/plugin.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/commands/plugin", "key": "command:plugin"}, "./node_modules/@umijs/preset-umi/dist/commands/verify-commit": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/commands/verify-commit.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/commands/verify-commit", "key": "verifyCommit"}, "./node_modules/@umijs/preset-umi/dist/commands/preview": {"config": {}, "time": {"hooks": {}, "register": 52}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/commands/preview.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/commands/preview", "key": "preview"}, "./node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu", "key": "mfsu-cli"}, "@umijs/plugin-run": {"config": {}, "time": {"hooks": {}, "register": 7}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/plugin-run/dist/index.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "@umijs/plugin-run", "key": "run"}, "./node_modules/@umijs/plugins/dist/access": {"config": {}, "time": {"hooks": {}, "register": 6}, "enableBy": "config", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/plugins/dist/access.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/plugins/dist/access", "key": "access"}, "./node_modules/@umijs/plugins/dist/analytics": {"config": {"onChange": "reload"}, "time": {"hooks": {}, "register": 2}, "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/plugins/dist/analytics.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/plugins/dist/analytics", "key": "analytics"}, "./node_modules/@umijs/plugins/dist/antd": {"config": {}, "time": {"hooks": {"modifyConfig": [5], "modifyAppData": [0]}, "register": 16}, "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/plugins/dist/antd.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/plugins/dist/antd", "key": "antd"}, "./node_modules/@umijs/plugins/dist/dva": {"config": {}, "time": {"hooks": {}, "register": 16}, "enableBy": "config", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/plugins/dist/dva.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/plugins/dist/dva", "key": "dva"}, "./node_modules/@umijs/plugins/dist/initial-state": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/plugins/dist/initial-state.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/plugins/dist/initial-state", "key": "initialState"}, "./node_modules/@umijs/plugins/dist/layout": {"config": {"onChange": "regenerateTmpFiles"}, "time": {"hooks": {"modifyConfig": [0], "addLayouts": [1], "modifyAppData": [1]}, "register": 10}, "enableBy": "config", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/plugins/dist/layout.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/plugins/dist/layout", "key": "layout"}, "./node_modules/@umijs/plugins/dist/locale": {"config": {}, "time": {"hooks": {}, "register": 9}, "enableBy": "config", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/plugins/dist/locale.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/plugins/dist/locale", "key": "locale"}, "./node_modules/@umijs/plugins/dist/mf": {"config": {}, "time": {"hooks": {}, "register": 7}, "enableBy": "config", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/plugins/dist/mf.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/plugins/dist/mf", "key": "mf"}, "./node_modules/@umijs/plugins/dist/model": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "config", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/plugins/dist/model.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/plugins/dist/model", "key": "model"}, "./node_modules/@umijs/plugins/dist/moment2dayjs": {"config": {}, "time": {"hooks": {"modifyConfig": [2]}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/plugins/dist/moment2dayjs.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/plugins/dist/moment2dayjs", "key": "moment2dayjs"}, "./node_modules/@umijs/plugins/dist/qiankun": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/plugins/dist/qiankun.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/plugins/dist/qiankun", "key": "qiankun"}, "./node_modules/@umijs/plugins/dist/qiankun/master": {"config": {}, "time": {"hooks": {}, "register": 5}, "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/plugins/dist/qiankun/master.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/plugins/dist/qiankun/master", "key": "<PERSON><PERSON><PERSON>n-master"}, "./node_modules/@umijs/plugins/dist/qiankun/slave": {"config": {}, "time": {"hooks": {}, "register": 5}, "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/plugins/dist/qiankun/slave.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/plugins/dist/qiankun/slave", "key": "qiankun-slave"}, "./node_modules/@umijs/plugins/dist/react-query": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "config", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/plugins/dist/react-query.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/plugins/dist/react-query", "key": "reactQuery"}, "./node_modules/@umijs/plugins/dist/request": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "config", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/plugins/dist/request.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/plugins/dist/request", "key": "request"}, "./node_modules/@umijs/plugins/dist/styled-components": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "config", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/plugins/dist/styled-components.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/plugins/dist/styled-components", "key": "styledComponents"}, "./node_modules/@umijs/plugins/dist/tailwindcss": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/plugins/dist/tailwindcss.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/plugins/dist/tailwindcss", "key": "tailwindcss"}, "./node_modules/@umijs/plugins/dist/valtio": {"config": {}, "time": {"hooks": {}, "register": 28}, "enableBy": "config", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/plugins/dist/valtio.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/plugins/dist/valtio", "key": "valtio"}, "./node_modules/@umijs/max/dist/plugins/maxAlias": {"config": {}, "time": {"hooks": {"modifyConfig": [0]}, "register": 25}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/max/dist/plugins/maxAlias.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/max/dist/plugins/maxAlias", "key": "max<PERSON><PERSON><PERSON>"}, "./node_modules/@umijs/max/dist/plugins/maxAppData": {"config": {}, "time": {"hooks": {"modifyAppData": [0]}, "register": 33}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/max/dist/plugins/maxAppData.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/max/dist/plugins/maxAppData", "key": "maxAppData"}, "./node_modules/@umijs/max/dist/plugins/maxChecker": {"config": {}, "time": {"hooks": {"onCheckPkgJSON": [0]}, "register": 62}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/max/dist/plugins/maxChecker.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/max/dist/plugins/maxChecker", "key": "max<PERSON><PERSON><PERSON>"}, "./node_modules/umi-presets-pro/dist/features/proconfig": {"config": {}, "time": {"hooks": {"modifyConfig": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/umi-presets-pro/dist/features/proconfig.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/umi-presets-pro/dist/features/proconfig", "key": "proconfig"}, "./node_modules/umi-presets-pro/dist/features/maxtabs": {"config": {}, "time": {"hooks": {}, "register": 25}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/umi-presets-pro/dist/features/maxtabs.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/umi-presets-pro/dist/features/maxtabs", "key": "maxtabs"}, "@umijs/max-plugin-openapi": {"config": {}, "time": {"hooks": {"onStart": [1]}, "register": 623}, "enableBy": "config", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/max-plugin-openapi/dist/index.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "@umijs/max-plugin-openapi", "key": "openAPI"}, "./node_modules/@alita/plugins/dist/keepalive": {"config": {}, "time": {"hooks": {}, "register": 395}, "enableBy": "config", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@alita/plugins/dist/keepalive.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@alita/plugins/dist/keepalive", "key": "keepalive"}, "./node_modules/@alita/plugins/dist/tabs-layout": {"config": {"onChange": "regenerateTmpFiles"}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@alita/plugins/dist/tabs-layout.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@alita/plugins/dist/tabs-layout", "key": "tabsLayout"}, "@umijs/request-record": {"config": {"default": {"mock": {"outputDir": "./mock", "fileName": "requestRecord.mock.js", "usingRole": "default"}, "outputDir": "./types"}}, "time": {"hooks": {"modifyConfig": [0], "modifyAppData": [0]}, "register": 127}, "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/request-record/dist/cjs/index.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "@umijs/request-record", "key": "requestRecord"}, "./node_modules/@umijs/core/dist/service/generatePlugin": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "D:/rwx-ai/RwxAI.Web/node_modules/@umijs/core/dist/service/generatePlugin.js", "cwd": "D:\\rwx-ai\\RwxAI.Web", "id": "./node_modules/@umijs/core/dist/service/generatePlugin", "key": "generatePlugin"}}, "presets": [], "name": "build", "args": {"_": []}, "userConfig": {"hash": true, "publicPath": "/", "routes": [{"path": "/user", "layout": false, "routes": [{"name": "login", "path": "/user/login", "component": "./user/login"}, {"name": "register", "path": "/user/register", "component": "./user/register"}]}, {"path": "/welcome", "name": "welcome", "icon": "smile", "component": "./Welcome"}, {"path": "/ai-models", "name": "ai-models", "icon": "robot", "component": "./ai-models"}, {"path": "/apps", "name": "apps", "icon": "appstore", "component": "./apps"}, {"path": "/chat", "name": "chat", "icon": "message", "routes": [{"path": "/chat", "redirect": "/chat/sessions"}, {"path": "/chat/sessions", "name": "sessions", "component": "./chat"}, {"path": "/chat/session/:id", "name": "session-detail", "component": "./chat/session/[id]", "hideInMenu": true}]}, {"path": "/knowledge", "name": "knowledge", "icon": "book", "routes": [{"path": "/knowledge", "component": "./knowledge"}, {"path": "/knowledge/:id/files", "name": "knowledge-files", "component": "./knowledge/[id]/files", "hideInMenu": true}]}, {"path": "/plugins", "name": "plugins", "icon": "api", "component": "./plugins"}, {"path": "/auth-test", "name": "auth-test", "icon": "safety", "component": "./auth-test"}, {"path": "/response-demo", "name": "response-demo", "icon": "experiment", "component": "./response-demo"}, {"path": "/api-test", "name": "api-test", "icon": "api", "component": "./api-test"}, {"path": "/admin", "name": "admin", "icon": "crown", "access": "canAdmin", "routes": [{"path": "/admin", "redirect": "/admin/sub-page"}, {"path": "/admin/sub-page", "name": "sub-page", "component": "./Admin"}]}, {"name": "list.table-list", "icon": "table", "path": "/list", "component": "./table-list"}, {"path": "/", "redirect": "/welcome"}, {"path": "*", "layout": false, "component": "./404"}], "ignoreMomentLocale": true, "proxy": {"/api/": {"target": "http://localhost:5000", "changeOrigin": true, "pathRewrite": {"^": ""}}}, "fastRefresh": true, "model": {}, "initialState": {}, "title": "Ant Design Pro", "layout": {"locale": true, "navTheme": "light", "colorPrimary": "#1890ff", "layout": "mix", "contentWidth": "Fluid", "fixedHeader": false, "fixSiderbar": true, "colorWeak": false, "title": "Ant Design Pro", "pwa": true, "logo": "https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg", "iconfontUrl": "", "token": {}}, "moment2dayjs": {"preset": "antd", "plugins": ["duration"]}, "locale": {"default": "zh-CN", "antd": true, "baseNavigator": true}, "antd": {"appConfig": {}, "configProvider": {"theme": {"cssVar": true, "token": {"fontFamily": "AlibabaSans, sans-serif"}}}}, "request": {}, "access": {}, "headScripts": [{"src": "\\scripts\\loading.js", "async": true}], "presets": ["umi-presets-pro"], "openAPI": [{"requestLibPath": "import { request } from '@umijs/max'", "schemaPath": "D:\\rwx-ai\\RwxAI.Web\\config\\oneapi.json", "mock": false}, {"requestLibPath": "import { request } from '@umijs/max'", "schemaPath": "https://gw.alipayobjects.com/os/antfincdn/CA1dOm%2631B/openapi.json", "projectName": "swagger"}], "mock": {"include": ["mock/**/*", "src/pages/**/_mock.ts"]}, "mako": {}, "esbuildMinifyIIFE": true, "requestRecord": {}, "exportStatic": {}}, "mainConfigFile": "D:\\rwx-ai\\RwxAI.Web\\config\\config.ts", "config": {"routeLoader": {"moduleType": "esm"}, "mountElementId": "root", "history": {"type": "browser"}, "base": "/", "svgr": {}, "publicPath": "/", "mfsu": false, "ignoreMomentLocale": true, "externals": {}, "autoCSSModules": true, "alias": {"umi": "@@/exports", "react": "D:\\rwx-ai\\RwxAI.Web\\node_modules\\react", "react-dom": "D:\\rwx-ai\\RwxAI.Web\\node_modules\\react-dom", "react-router": "D:\\rwx-ai\\RwxAI.Web\\node_modules\\react-router", "react-router-dom": "D:\\rwx-ai\\RwxAI.Web\\node_modules\\react-router-dom", "@": "D:/rwx-ai/RwxAI.Web/src", "@@": "D:/rwx-ai/RwxAI.Web/src/.umi-production", "regenerator-runtime": "D:\\rwx-ai\\RwxAI.Web\\node_modules\\@umijs\\preset-umi\\node_modules\\regenerator-runtime", "antd": "D:\\rwx-ai\\RwxAI.Web\\node_modules\\antd", "moment": "D:\\rwx-ai\\RwxAI.Web\\node_modules\\dayjs", "@umijs/max": "@@/exports"}, "requestRecord": {"mock": {"outputDir": "./mock", "fileName": "requestRecord.mock.js", "usingRole": "default"}, "outputDir": "./types"}, "hash": true, "routes": [{"path": "/user", "layout": false, "routes": [{"name": "login", "path": "/user/login", "component": "./user/login"}, {"name": "register", "path": "/user/register", "component": "./user/register"}]}, {"path": "/welcome", "name": "welcome", "icon": "smile", "component": "./Welcome"}, {"path": "/ai-models", "name": "ai-models", "icon": "robot", "component": "./ai-models"}, {"path": "/apps", "name": "apps", "icon": "appstore", "component": "./apps"}, {"path": "/chat", "name": "chat", "icon": "message", "routes": [{"path": "/chat", "redirect": "/chat/sessions"}, {"path": "/chat/sessions", "name": "sessions", "component": "./chat"}, {"path": "/chat/session/:id", "name": "session-detail", "component": "./chat/session/[id]", "hideInMenu": true}]}, {"path": "/knowledge", "name": "knowledge", "icon": "book", "routes": [{"path": "/knowledge", "component": "./knowledge"}, {"path": "/knowledge/:id/files", "name": "knowledge-files", "component": "./knowledge/[id]/files", "hideInMenu": true}]}, {"path": "/plugins", "name": "plugins", "icon": "api", "component": "./plugins"}, {"path": "/auth-test", "name": "auth-test", "icon": "safety", "component": "./auth-test"}, {"path": "/response-demo", "name": "response-demo", "icon": "experiment", "component": "./response-demo"}, {"path": "/api-test", "name": "api-test", "icon": "api", "component": "./api-test"}, {"path": "/admin", "name": "admin", "icon": "crown", "access": "canAdmin", "routes": [{"path": "/admin", "redirect": "/admin/sub-page"}, {"path": "/admin/sub-page", "name": "sub-page", "component": "./Admin"}]}, {"name": "list.table-list", "icon": "table", "path": "/list", "component": "./table-list"}, {"path": "/", "redirect": "/welcome"}, {"path": "*", "layout": false, "component": "./404"}], "proxy": {"/api/": {"target": "http://localhost:5000", "changeOrigin": true, "pathRewrite": {"^": ""}}}, "fastRefresh": true, "model": {}, "initialState": {}, "title": "Ant Design Pro", "layout": {"locale": true, "navTheme": "light", "colorPrimary": "#1890ff", "layout": "mix", "contentWidth": "Fluid", "fixedHeader": false, "fixSiderbar": true, "colorWeak": false, "title": "Ant Design Pro", "pwa": true, "logo": "https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg", "iconfontUrl": "", "token": {}}, "moment2dayjs": {"preset": "antd", "plugins": ["duration"]}, "locale": {"default": "zh-CN", "antd": true, "baseNavigator": true}, "antd": {"appConfig": {}, "configProvider": {"theme": {"cssVar": true, "token": {"fontFamily": "AlibabaSans, sans-serif"}}}}, "request": {}, "access": {}, "headScripts": [{"src": "\\scripts\\loading.js", "async": true}], "presets": ["umi-presets-pro"], "openAPI": [{"requestLibPath": "import { request } from '@umijs/max'", "schemaPath": "D:\\rwx-ai\\RwxAI.Web\\config\\oneapi.json", "mock": false}, {"requestLibPath": "import { request } from '@umijs/max'", "schemaPath": "https://gw.alipayobjects.com/os/antfincdn/CA1dOm%2631B/openapi.json", "projectName": "swagger"}], "mock": {"include": ["mock/**/*", "src/pages/**/_mock.ts"]}, "mako": {"plugins": [{"name": "UmiHtmlGenerationMako"}]}, "esbuildMinifyIIFE": true, "exportStatic": {}, "targets": {"chrome": 80}, "hmrGuardian": false, "theme": {"blue-base": "#1890ff", "blue-1": "#e6f7ff", "blue-2": "#bae7ff", "blue-3": "#91d5ff", "blue-4": "#69c0ff", "blue-5": "#40a9ff", "blue-6": "#1890ff", "blue-7": "#096dd9", "blue-8": "#0050b3", "blue-9": "#003a8c", "blue-10": "#002766", "purple-base": "#722ed1", "purple-1": "#f9f0ff", "purple-2": "#efdbff", "purple-3": "#d3adf7", "purple-4": "#b37feb", "purple-5": "#9254de", "purple-6": "#722ed1", "purple-7": "#531dab", "purple-8": "#391085", "purple-9": "#22075e", "purple-10": "#120338", "cyan-base": "#13c2c2", "cyan-1": "#e6fffb", "cyan-2": "#b5f5ec", "cyan-3": "#87e8de", "cyan-4": "#5cdbd3", "cyan-5": "#36cfc9", "cyan-6": "#13c2c2", "cyan-7": "#08979c", "cyan-8": "#006d75", "cyan-9": "#00474f", "cyan-10": "#002329", "green-base": "#52c41a", "green-1": "#f6ffed", "green-2": "#d9f7be", "green-3": "#b7eb8f", "green-4": "#95de64", "green-5": "#73d13d", "green-6": "#52c41a", "green-7": "#389e0d", "green-8": "#237804", "green-9": "#135200", "green-10": "#092b00", "magenta-base": "#eb2f96", "magenta-1": "#fff0f6", "magenta-2": "#ffd6e7", "magenta-3": "#ffadd2", "magenta-4": "#ff85c0", "magenta-5": "#f759ab", "magenta-6": "#eb2f96", "magenta-7": "#c41d7f", "magenta-8": "#9e1068", "magenta-9": "#780650", "magenta-10": "#520339", "pink-base": "#eb2f96", "pink-1": "#fff0f6", "pink-2": "#ffd6e7", "pink-3": "#ffadd2", "pink-4": "#ff85c0", "pink-5": "#f759ab", "pink-6": "#eb2f96", "pink-7": "#c41d7f", "pink-8": "#9e1068", "pink-9": "#780650", "pink-10": "#520339", "red-base": "#f5222d", "red-1": "#fff1f0", "red-2": "#ffccc7", "red-3": "#ffa39e", "red-4": "#ff7875", "red-5": "#ff4d4f", "red-6": "#f5222d", "red-7": "#cf1322", "red-8": "#a8071a", "red-9": "#820014", "red-10": "#5c0011", "orange-base": "#fa8c16", "orange-1": "#fff7e6", "orange-2": "#ffe7ba", "orange-3": "#ffd591", "orange-4": "#ffc069", "orange-5": "#ffa940", "orange-6": "#fa8c16", "orange-7": "#d46b08", "orange-8": "#ad4e00", "orange-9": "#873800", "orange-10": "#612500", "yellow-base": "#fadb14", "yellow-1": "#feffe6", "yellow-2": "#ffffb8", "yellow-3": "#fffb8f", "yellow-4": "#fff566", "yellow-5": "#ffec3d", "yellow-6": "#fadb14", "yellow-7": "#d4b106", "yellow-8": "#ad8b00", "yellow-9": "#876800", "yellow-10": "#614700", "volcano-base": "#fa541c", "volcano-1": "#fff2e8", "volcano-2": "#ffd8bf", "volcano-3": "#ffbb96", "volcano-4": "#ff9c6e", "volcano-5": "#ff7a45", "volcano-6": "#fa541c", "volcano-7": "#d4380d", "volcano-8": "#ad2102", "volcano-9": "#871400", "volcano-10": "#610b00", "geekblue-base": "#2f54eb", "geekblue-1": "#f0f5ff", "geekblue-2": "#d6e4ff", "geekblue-3": "#adc6ff", "geekblue-4": "#85a5ff", "geekblue-5": "#597ef7", "geekblue-6": "#2f54eb", "geekblue-7": "#1d39c4", "geekblue-8": "#10239e", "geekblue-9": "#061178", "geekblue-10": "#030852", "lime-base": "#a0d911", "lime-1": "#fcffe6", "lime-2": "#f4ffb8", "lime-3": "#eaff8f", "lime-4": "#d3f261", "lime-5": "#bae637", "lime-6": "#a0d911", "lime-7": "#7cb305", "lime-8": "#5b8c00", "lime-9": "#3f6600", "lime-10": "#254000", "gold-base": "#faad14", "gold-1": "#fffbe6", "gold-2": "#fff1b8", "gold-3": "#ffe58f", "gold-4": "#ffd666", "gold-5": "#ffc53d", "gold-6": "#faad14", "gold-7": "#d48806", "gold-8": "#ad6800", "gold-9": "#874d00", "gold-10": "#613400", "preset-colors": "pink, magenta, red, volcano, orange, yellow, gold, cyan, lime, green, blue, geekblue,", "theme": "default", "ant-prefix": "ant", "html-selector": "html", "primary-color": "#1890ff", "primary-color-hover": "#40a9ff", "primary-color-active": "#096dd9", "primary-color-outline": "rgba(24, 144, 255, 0.2)", "processing-color": "#1890ff", "info-color": "#1890ff", "info-color-deprecated-bg": "#e6f7ff", "info-color-deprecated-border": "#91d5ff", "success-color": "#52c41a", "success-color-hover": "#73d13d", "success-color-active": "#389e0d", "success-color-outline": "rgba(82, 196, 26, 0.2)", "success-color-deprecated-bg": "#f6ffed", "success-color-deprecated-border": "#b7eb8f", "warning-color": "#faad14", "warning-color-hover": "#ffc53d", "warning-color-active": "#d48806", "warning-color-outline": "rgba(250, 173, 20, 0.2)", "warning-color-deprecated-bg": "#fffbe6", "warning-color-deprecated-border": "#ffe58f", "error-color": "#ff4d4f", "error-color-hover": "#ff7875", "error-color-active": "#d9363e", "error-color-outline": "rgba(255, 77, 79, 0.2)", "error-color-deprecated-bg": "#fff2f0", "error-color-deprecated-border": "#ffccc7", "highlight-color": "#ff4d4f", "normal-color": "#d9d9d9", "white": "#fff", "black": "#000", "primary-1": "#e6f7ff", "primary-2": "#bae7ff", "primary-3": "#91d5ff", "primary-4": "#69c0ff", "primary-5": "#40a9ff", "primary-6": "#1890ff", "primary-7": "#096dd9", "primary-8": "#0050b3", "primary-9": "#003a8c", "primary-10": "#002766", "component-background": "#fff", "popover-background": "#fff", "popover-customize-border-color": "#f0f0f0", "font-family": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "code-family": "'SFMono-Regular', <PERSON><PERSON><PERSON>, 'Liberation Mono', Menlo, Courier, monospace", "text-color": "rgba(0, 0, 0, 0.85)", "text-color-secondary": "rgba(0, 0, 0, 0.45)", "text-color-inverse": "#fff", "icon-color": "inherit", "icon-color-hover": "rgba(0, 0, 0, 0.75)", "heading-color": "rgba(0, 0, 0, 0.85)", "text-color-dark": "rgba(255, 255, 255, 0.85)", "text-color-secondary-dark": "rgba(255, 255, 255, 0.65)", "text-selection-bg": "#1890ff", "font-variant-base": "tabular-nums", "font-feature-settings-base": "tnum", "font-size-base": "14px", "font-size-lg": "16px", "font-size-sm": "12px", "heading-1-size": "38px", "heading-2-size": "30px", "heading-3-size": "24px", "heading-4-size": "20px", "heading-5-size": "16px", "line-height-base": "1.5715", "border-radius-base": "2px", "border-radius-sm": "2px", "control-border-radius": "2px", "arrow-border-radius": "2px", "padding-lg": "24px", "padding-md": "16px", "padding-sm": "12px", "padding-xs": "8px", "padding-xss": "4px", "control-padding-horizontal": "12px", "control-padding-horizontal-sm": "8px", "margin-lg": "24px", "margin-md": "16px", "margin-sm": "12px", "margin-xs": "8px", "margin-xss": "4px", "height-base": "32px", "height-lg": "40px", "height-sm": "24px", "item-active-bg": "#e6f7ff", "item-hover-bg": "#f5f5f5", "iconfont-css-prefix": "anticon", "link-color": "#1890ff", "link-hover-color": "#40a9ff", "link-active-color": "#096dd9", "link-decoration": "none", "link-hover-decoration": "none", "link-focus-decoration": "none", "link-focus-outline": "0", "ease-base-out": "cubic-bezier(0.7, 0.3, 0.1, 1)", "ease-base-in": "cubic-bezier(0.9, 0, 0.3, 0.7)", "ease-out": "cubic-bezier(0.215, 0.61, 0.355, 1)", "ease-in": "cubic-bezier(0.55, 0.055, 0.675, 0.19)", "ease-in-out": "cubic-bezier(0.645, 0.045, 0.355, 1)", "ease-out-back": "cubic-bezier(0.12, 0.4, 0.29, 1.46)", "ease-in-back": "cubic-bezier(0.71, -0.46, 0.88, 0.6)", "ease-in-out-back": "cubic-bezier(0.71, -0.46, 0.29, 1.46)", "ease-out-circ": "cubic-bezier(0.08, 0.82, 0.17, 1)", "ease-in-circ": "cubic-bezier(0.6, 0.04, 0.98, 0.34)", "ease-in-out-circ": "cubic-bezier(0.78, 0.14, 0.15, 0.86)", "ease-out-quint": "cubic-bezier(0.23, 1, 0.32, 1)", "ease-in-quint": "cubic-bezier(0.755, 0.05, 0.855, 0.06)", "ease-in-out-quint": "cubic-bezier(0.86, 0, 0.07, 1)", "border-color-base": "#d9d9d9", "border-color-split": "#f0f0f0", "border-color-inverse": "#fff", "border-width-base": "1px", "border-style-base": "solid", "outline-blur-size": "0", "outline-width": "2px", "outline-color": "#1890ff", "outline-fade": "20%", "background-color-light": "#fafafa", "background-color-base": "#f5f5f5", "disabled-color": "rgba(0, 0, 0, 0.25)", "disabled-bg": "#f5f5f5", "disabled-active-bg": "#e6e6e6", "disabled-color-dark": "rgba(255, 255, 255, 0.35)", "shadow-color": "rgba(0, 0, 0, 0.15)", "shadow-color-inverse": "#fff", "box-shadow-base": "0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05)", "shadow-1-up": "0 -6px 16px -8px rgba(0, 0, 0, 0.08), 0 -9px 28px 0 rgba(0, 0, 0, 0.05), 0 -12px 48px 16px rgba(0, 0, 0, 0.03)", "shadow-1-down": "0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03)", "shadow-1-left": "-6px 0 16px -8px rgba(0, 0, 0, 0.08), -9px 0 28px 0 rgba(0, 0, 0, 0.05), -12px 0 48px 16px rgba(0, 0, 0, 0.03)", "shadow-1-right": "6px 0 16px -8px rgba(0, 0, 0, 0.08), 9px 0 28px 0 rgba(0, 0, 0, 0.05), 12px 0 48px 16px rgba(0, 0, 0, 0.03)", "shadow-2": "0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05)", "btn-font-weight": "400", "btn-border-radius-base": "2px", "btn-border-radius-sm": "2px", "btn-border-width": "1px", "btn-border-style": "solid", "btn-shadow": "0 2px 0 rgba(0, 0, 0, 0.015)", "btn-primary-shadow": "0 2px 0 rgba(0, 0, 0, 0.045)", "btn-text-shadow": "0 -1px 0 rgba(0, 0, 0, 0.12)", "btn-primary-color": "#fff", "btn-primary-bg": "#1890ff", "btn-default-color": "rgba(0, 0, 0, 0.85)", "btn-default-bg": "#fff", "btn-default-border": "#d9d9d9", "btn-danger-color": "#fff", "btn-danger-bg": "#ff4d4f", "btn-danger-border": "#ff4d4f", "btn-disable-color": "rgba(0, 0, 0, 0.25)", "btn-disable-bg": "#f5f5f5", "btn-disable-border": "#d9d9d9", "btn-default-ghost-color": "#fff", "btn-default-ghost-bg": "transparent", "btn-default-ghost-border": "#fff", "btn-font-size-lg": "16px", "btn-font-size-sm": "14px", "btn-padding-horizontal-base": "15px", "btn-padding-horizontal-lg": "15px", "btn-padding-horizontal-sm": "7px", "btn-height-base": "32px", "btn-height-lg": "40px", "btn-height-sm": "24px", "btn-line-height": "1.5715", "btn-circle-size": "32px", "btn-circle-size-lg": "40px", "btn-circle-size-sm": "24px", "btn-square-size": "32px", "btn-square-size-lg": "40px", "btn-square-size-sm": "24px", "btn-square-only-icon-size": "16px", "btn-square-only-icon-size-sm": "14px", "btn-square-only-icon-size-lg": "18px", "btn-group-border": "#40a9ff", "btn-link-hover-bg": "transparent", "btn-text-hover-bg": "rgba(0, 0, 0, 0.018)", "checkbox-size": "16px", "checkbox-color": "#1890ff", "checkbox-check-color": "#fff", "checkbox-check-bg": "#fff", "checkbox-border-width": "1px", "checkbox-border-radius": "2px", "checkbox-group-item-margin-right": "8px", "descriptions-bg": "#fafafa", "descriptions-title-margin-bottom": "20px", "descriptions-default-padding": "16px 24px", "descriptions-middle-padding": "12px 24px", "descriptions-small-padding": "8px 16px", "descriptions-item-padding-bottom": "16px", "descriptions-item-trailing-colon": "true", "descriptions-item-label-colon-margin-right": "8px", "descriptions-item-label-colon-margin-left": "2px", "descriptions-extra-color": "rgba(0, 0, 0, 0.85)", "divider-text-padding": "1em", "divider-orientation-margin": "5%", "divider-color": "rgba(0, 0, 0, 0.06)", "divider-vertical-gutter": "8px", "dropdown-selected-color": "#1890ff", "dropdown-menu-submenu-disabled-bg": "#fff", "dropdown-selected-bg": "#e6f7ff", "empty-font-size": "14px", "radio-size": "16px", "radio-top": "0.2em", "radio-border-width": "1px", "radio-dot-size": "8px", "radio-dot-color": "#1890ff", "radio-dot-disabled-color": "rgba(0, 0, 0, 0.2)", "radio-solid-checked-color": "#fff", "radio-button-bg": "#fff", "radio-button-checked-bg": "#fff", "radio-button-color": "rgba(0, 0, 0, 0.85)", "radio-button-hover-color": "#40a9ff", "radio-button-active-color": "#096dd9", "radio-button-padding-horizontal": "15px", "radio-disabled-button-checked-bg": "#e6e6e6", "radio-disabled-button-checked-color": "rgba(0, 0, 0, 0.25)", "radio-wrapper-margin-right": "8px", "screen-xs": "480px", "screen-xs-min": "480px", "screen-sm": "576px", "screen-sm-min": "576px", "screen-md": "768px", "screen-md-min": "768px", "screen-lg": "992px", "screen-lg-min": "992px", "screen-xl": "1200px", "screen-xl-min": "1200px", "screen-xxl": "1600px", "screen-xxl-min": "1600px", "screen-xs-max": "575px", "screen-sm-max": "767px", "screen-md-max": "991px", "screen-lg-max": "1199px", "screen-xl-max": "1599px", "grid-columns": "24", "layout-header-background": "#001529", "layout-header-height": "64px", "layout-header-padding": "0 50px", "layout-header-color": "rgba(0, 0, 0, 0.85)", "layout-footer-padding": "24px 50px", "layout-footer-background": "#f0f2f5", "layout-sider-background": "#001529", "layout-trigger-height": "48px", "layout-trigger-background": "#002140", "layout-trigger-color": "#fff", "layout-zero-trigger-width": "36px", "layout-zero-trigger-height": "42px", "layout-sider-background-light": "#fff", "layout-trigger-background-light": "#fff", "layout-trigger-color-light": "rgba(0, 0, 0, 0.85)", "zindex-badge": "auto", "zindex-table-fixed": "2", "zindex-affix": "10", "zindex-back-top": "10", "zindex-picker-panel": "10", "zindex-popup-close": "10", "zindex-modal": "1000", "zindex-modal-mask": "1000", "zindex-message": "1010", "zindex-notification": "1010", "zindex-popover": "1030", "zindex-dropdown": "1050", "zindex-picker": "1050", "zindex-popoconfirm": "1060", "zindex-tooltip": "1070", "zindex-image": "1080", "animation-duration-slow": "0.3s", "animation-duration-base": "0.2s", "animation-duration-fast": "0.1s", "collapse-panel-border-radius": "2px", "dropdown-menu-bg": "#fff", "dropdown-vertical-padding": "5px", "dropdown-edge-child-vertical-padding": "4px", "dropdown-font-size": "14px", "dropdown-line-height": "22px", "label-required-color": "#ff4d4f", "label-color": "rgba(0, 0, 0, 0.85)", "form-warning-input-bg": "#fff", "form-item-margin-bottom": "24px", "form-item-trailing-colon": "true", "form-vertical-label-padding": "0 0 8px", "form-vertical-label-margin": "0", "form-item-label-font-size": "14px", "form-item-label-height": "32px", "form-item-label-colon-margin-right": "8px", "form-item-label-colon-margin-left": "2px", "form-error-input-bg": "#fff", "input-height-base": "32px", "input-height-lg": "40px", "input-height-sm": "24px", "input-padding-horizontal": "11px", "input-padding-horizontal-base": "11px", "input-padding-horizontal-sm": "7px", "input-padding-horizontal-lg": "11px", "input-padding-vertical-base": "4px", "input-padding-vertical-sm": "0px", "input-padding-vertical-lg": "6.5px", "input-placeholder-color": "#bfbfbf", "input-color": "rgba(0, 0, 0, 0.85)", "input-icon-color": "rgba(0, 0, 0, 0.85)", "input-border-color": "#d9d9d9", "input-bg": "#fff", "input-number-hover-border-color": "#40a9ff", "input-number-handler-active-bg": "#f4f4f4", "input-number-handler-hover-bg": "#40a9ff", "input-number-handler-bg": "#fff", "input-number-handler-border-color": "#d9d9d9", "input-addon-bg": "#fafafa", "input-hover-border-color": "#40a9ff", "input-disabled-bg": "#f5f5f5", "input-outline-offset": "0 0", "input-icon-hover-color": "rgba(0, 0, 0, 0.85)", "input-disabled-color": "rgba(0, 0, 0, 0.25)", "mentions-dropdown-bg": "#fff", "mentions-dropdown-menu-item-hover-bg": "#fff", "select-border-color": "#d9d9d9", "select-item-selected-color": "rgba(0, 0, 0, 0.85)", "select-item-selected-font-weight": "600", "select-dropdown-bg": "#fff", "select-item-selected-bg": "#e6f7ff", "select-item-active-bg": "#f5f5f5", "select-dropdown-vertical-padding": "5px", "select-dropdown-font-size": "14px", "select-dropdown-line-height": "22px", "select-dropdown-height": "32px", "select-background": "#fff", "select-clear-background": "#fff", "select-selection-item-bg": "#f5f5f5", "select-selection-item-border-color": "#f0f0f0", "select-single-item-height-lg": "40px", "select-multiple-item-height": "24px", "select-multiple-item-height-lg": "32px", "select-multiple-item-spacing-half": "2px", "select-multiple-disabled-background": "#f5f5f5", "select-multiple-item-disabled-color": "#bfbfbf", "select-multiple-item-disabled-border-color": "#d9d9d9", "cascader-bg": "#fff", "cascader-item-selected-bg": "#e6f7ff", "cascader-menu-bg": "#fff", "cascader-menu-border-color-split": "#f0f0f0", "cascader-dropdown-vertical-padding": "5px", "cascader-dropdown-edge-child-vertical-padding": "4px", "cascader-dropdown-font-size": "14px", "cascader-dropdown-line-height": "22px", "anchor-bg": "transparent", "anchor-border-color": "#f0f0f0", "anchor-link-top": "4px", "anchor-link-left": "16px", "anchor-link-padding": "4px 0 4px 16px", "tooltip-max-width": "250px", "tooltip-color": "#fff", "tooltip-bg": "rgba(0, 0, 0, 0.75)", "tooltip-arrow-width": "11.3137085px", "tooltip-distance": "14.3137085px", "tooltip-arrow-color": "rgba(0, 0, 0, 0.75)", "tooltip-border-radius": "2px", "popover-bg": "#fff", "popover-color": "rgba(0, 0, 0, 0.85)", "popover-min-width": "177px", "popover-min-height": "32px", "popover-arrow-width": "11.3137085px", "popover-arrow-color": "#fff", "popover-arrow-outer-color": "#fff", "popover-distance": "15.3137085px", "popover-padding-horizontal": "16px", "modal-header-padding-vertical": "16px", "modal-header-padding-horizontal": "24px", "modal-header-bg": "#fff", "modal-header-padding": "16px 24px", "modal-header-border-width": "1px", "modal-header-border-style": "solid", "modal-header-title-line-height": "22px", "modal-header-title-font-size": "16px", "modal-header-border-color-split": "#f0f0f0", "modal-header-close-size": "54px", "modal-content-bg": "#fff", "modal-heading-color": "rgba(0, 0, 0, 0.85)", "modal-close-color": "rgba(0, 0, 0, 0.45)", "modal-footer-bg": "transparent", "modal-footer-border-color-split": "#f0f0f0", "modal-footer-border-style": "solid", "modal-footer-padding-vertical": "10px", "modal-footer-padding-horizontal": "16px", "modal-footer-border-width": "1px", "modal-mask-bg": "rgba(0, 0, 0, 0.45)", "modal-confirm-title-font-size": "16px", "modal-border-radius": "2px", "progress-default-color": "#1890ff", "progress-remaining-color": "#f5f5f5", "progress-info-text-color": "rgba(0, 0, 0, 0.85)", "progress-radius": "100px", "progress-steps-item-bg": "#f3f3f3", "progress-text-font-size": "1em", "progress-text-color": "rgba(0, 0, 0, 0.85)", "progress-circle-text-font-size": "1em", "menu-inline-toplevel-item-height": "40px", "menu-item-height": "40px", "menu-item-group-height": "1.5715", "menu-collapsed-width": "80px", "menu-bg": "#fff", "menu-popup-bg": "#fff", "menu-item-color": "rgba(0, 0, 0, 0.85)", "menu-inline-submenu-bg": "#fafafa", "menu-highlight-color": "#1890ff", "menu-highlight-danger-color": "#ff4d4f", "menu-item-active-bg": "#e6f7ff", "menu-item-active-danger-bg": "#fff1f0", "menu-item-active-border-width": "3px", "menu-item-group-title-color": "rgba(0, 0, 0, 0.45)", "menu-item-vertical-margin": "4px", "menu-item-font-size": "14px", "menu-item-boundary-margin": "8px", "menu-item-padding-horizontal": "20px", "menu-item-padding": "0 20px", "menu-horizontal-line-height": "46px", "menu-icon-margin-right": "10px", "menu-icon-size": "14px", "menu-icon-size-lg": "16px", "menu-item-group-title-font-size": "14px", "menu-dark-color": "rgba(255, 255, 255, 0.65)", "menu-dark-danger-color": "#ff4d4f", "menu-dark-bg": "#001529", "menu-dark-arrow-color": "#fff", "menu-dark-inline-submenu-bg": "#000c17", "menu-dark-highlight-color": "#fff", "menu-dark-item-active-bg": "#1890ff", "menu-dark-item-active-danger-bg": "#ff4d4f", "menu-dark-selected-item-icon-color": "#fff", "menu-dark-selected-item-text-color": "#fff", "menu-dark-item-hover-bg": "transparent", "spin-dot-size-sm": "14px", "spin-dot-size": "20px", "spin-dot-size-lg": "32px", "table-bg": "#fff", "table-header-bg": "#fafafa", "table-header-color": "rgba(0, 0, 0, 0.85)", "table-header-sort-bg": "#f5f5f5", "table-row-hover-bg": "#fafafa", "table-selected-row-color": "inherit", "table-selected-row-bg": "#e6f7ff", "table-selected-row-hover-bg": "#dcf4ff", "table-expanded-row-bg": "#fbfbfb", "table-padding-vertical": "16px", "table-padding-horizontal": "16px", "table-padding-vertical-md": "12px", "table-padding-horizontal-md": "8px", "table-padding-vertical-sm": "8px", "table-padding-horizontal-sm": "8px", "table-border-color": "#f0f0f0", "table-border-radius-base": "2px", "table-footer-bg": "#fafafa", "table-footer-color": "rgba(0, 0, 0, 0.85)", "table-header-bg-sm": "#fafafa", "table-font-size": "14px", "table-font-size-md": "14px", "table-font-size-sm": "14px", "table-header-cell-split-color": "rgba(0, 0, 0, 0.06)", "table-header-sort-active-bg": "rgba(0, 0, 0, 0.04)", "table-fixed-header-sort-active-bg": "#f5f5f5", "table-header-filter-active-bg": "rgba(0, 0, 0, 0.04)", "table-filter-btns-bg": "inherit", "table-filter-dropdown-bg": "#fff", "table-expand-icon-bg": "#fff", "table-selection-column-width": "32px", "table-sticky-scroll-bar-bg": "rgba(0, 0, 0, 0.35)", "table-sticky-scroll-bar-radius": "4px", "tag-border-radius": "2px", "tag-default-bg": "#fafafa", "tag-default-color": "rgba(0, 0, 0, 0.85)", "tag-font-size": "12px", "tag-line-height": "20px", "picker-bg": "#fff", "picker-basic-cell-hover-color": "#f5f5f5", "picker-basic-cell-active-with-range-color": "#e6f7ff", "picker-basic-cell-hover-with-range-color": "#cbe6ff", "picker-basic-cell-disabled-bg": "rgba(0, 0, 0, 0.04)", "picker-border-color": "#f0f0f0", "picker-date-hover-range-border-color": "#7ec1ff", "picker-date-hover-range-color": "#cbe6ff", "picker-time-panel-column-width": "56px", "picker-time-panel-column-height": "224px", "picker-time-panel-cell-height": "28px", "picker-panel-cell-height": "24px", "picker-panel-cell-width": "36px", "picker-text-height": "40px", "picker-panel-without-time-cell-height": "66px", "calendar-bg": "#fff", "calendar-input-bg": "#fff", "calendar-border-color": "#fff", "calendar-item-active-bg": "#e6f7ff", "calendar-column-active-bg": "rgba(230, 247, 255, 0.2)", "calendar-full-bg": "#fff", "calendar-full-panel-bg": "#fff", "carousel-dot-width": "16px", "carousel-dot-height": "3px", "carousel-dot-active-width": "24px", "badge-height": "20px", "badge-height-sm": "14px", "badge-dot-size": "6px", "badge-font-size": "12px", "badge-font-size-sm": "12px", "badge-font-weight": "normal", "badge-status-size": "6px", "badge-text-color": "#fff", "badge-color": "#ff4d4f", "rate-star-color": "#fadb14", "rate-star-bg": "#f0f0f0", "rate-star-size": "20px", "rate-star-hover-scale": "scale(1.1)", "card-head-color": "rgba(0, 0, 0, 0.85)", "card-head-background": "transparent", "card-head-font-size": "16px", "card-head-font-size-sm": "14px", "card-head-padding": "16px", "card-head-padding-sm": "8px", "card-head-height": "48px", "card-head-height-sm": "36px", "card-inner-head-padding": "12px", "card-padding-base": "24px", "card-padding-base-sm": "12px", "card-actions-background": "#fff", "card-actions-li-margin": "12px 0", "card-skeleton-bg": "#cfd8dc", "card-background": "#fff", "card-shadow": "0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09)", "card-radius": "2px", "card-head-tabs-margin-bottom": "-17px", "card-head-extra-color": "rgba(0, 0, 0, 0.85)", "comment-bg": "inherit", "comment-padding-base": "16px 0", "comment-nest-indent": "44px", "comment-font-size-base": "14px", "comment-font-size-sm": "12px", "comment-author-name-color": "rgba(0, 0, 0, 0.45)", "comment-author-time-color": "#ccc", "comment-action-color": "rgba(0, 0, 0, 0.45)", "comment-action-hover-color": "#595959", "comment-actions-margin-bottom": "inherit", "comment-actions-margin-top": "12px", "comment-content-detail-p-margin-bottom": "inherit", "tabs-card-head-background": "#fafafa", "tabs-card-height": "40px", "tabs-card-active-color": "#1890ff", "tabs-card-horizontal-padding": "8px 16px", "tabs-card-horizontal-padding-sm": "6px 16px", "tabs-card-horizontal-padding-lg": "7px 16px 6px", "tabs-title-font-size": "14px", "tabs-title-font-size-lg": "16px", "tabs-title-font-size-sm": "14px", "tabs-ink-bar-color": "#1890ff", "tabs-bar-margin": "0 0 16px 0", "tabs-horizontal-gutter": "32px", "tabs-horizontal-margin": "0 0 0 32px", "tabs-horizontal-margin-rtl": "0 0 0 32px", "tabs-horizontal-padding": "12px 0", "tabs-horizontal-padding-lg": "16px 0", "tabs-horizontal-padding-sm": "8px 0", "tabs-vertical-padding": "8px 24px", "tabs-vertical-margin": "16px 0 0 0", "tabs-scrolling-size": "32px", "tabs-highlight-color": "#1890ff", "tabs-hover-color": "#40a9ff", "tabs-active-color": "#096dd9", "tabs-card-gutter": "2px", "tabs-card-tab-active-border-top": "2px solid transparent", "back-top-color": "#fff", "back-top-bg": "rgba(0, 0, 0, 0.45)", "back-top-hover-bg": "rgba(0, 0, 0, 0.85)", "avatar-size-base": "32px", "avatar-size-lg": "40px", "avatar-size-sm": "24px", "avatar-font-size-base": "18px", "avatar-font-size-lg": "24px", "avatar-font-size-sm": "14px", "avatar-bg": "#ccc", "avatar-color": "#fff", "avatar-border-radius": "2px", "avatar-group-overlapping": "-8px", "avatar-group-space": "3px", "avatar-group-border-color": "#fff", "switch-height": "22px", "switch-sm-height": "16px", "switch-min-width": "44px", "switch-sm-min-width": "28px", "switch-disabled-opacity": "0.4", "switch-color": "#1890ff", "switch-bg": "#fff", "switch-shadow-color": "rgba(0, 35, 11, 0.2)", "switch-padding": "2px", "switch-inner-margin-min": "7px", "switch-inner-margin-max": "25px", "switch-sm-inner-margin-min": "5px", "switch-sm-inner-margin-max": "18px", "pagination-item-bg": "#fff", "pagination-item-size": "32px", "pagination-item-size-sm": "24px", "pagination-font-family": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "pagination-font-weight-active": "500", "pagination-item-bg-active": "#fff", "pagination-item-link-bg": "#fff", "pagination-item-disabled-color-active": "rgba(0, 0, 0, 0.25)", "pagination-item-disabled-bg-active": "#e6e6e6", "pagination-item-input-bg": "#fff", "pagination-mini-options-size-changer-top": "0px", "page-header-padding": "24px", "page-header-padding-vertical": "16px", "page-header-padding-breadcrumb": "12px", "page-header-content-padding-vertical": "12px", "page-header-back-color": "#000", "page-header-ghost-bg": "inherit", "page-header-heading-title": "20px", "page-header-heading-sub-title": "14px", "page-header-tabs-tab-font-size": "16px", "breadcrumb-base-color": "rgba(0, 0, 0, 0.45)", "breadcrumb-last-item-color": "rgba(0, 0, 0, 0.85)", "breadcrumb-font-size": "14px", "breadcrumb-icon-font-size": "14px", "breadcrumb-link-color": "rgba(0, 0, 0, 0.45)", "breadcrumb-link-color-hover": "rgba(0, 0, 0, 0.85)", "breadcrumb-separator-color": "rgba(0, 0, 0, 0.45)", "breadcrumb-separator-margin": "0 8px", "slider-margin": "10px 6px 10px", "slider-rail-background-color": "#f5f5f5", "slider-rail-background-color-hover": "#e1e1e1", "slider-track-background-color": "#91d5ff", "slider-track-background-color-hover": "#69c0ff", "slider-handle-border-width": "2px", "slider-handle-background-color": "#fff", "slider-handle-color": "#91d5ff", "slider-handle-color-hover": "#69c0ff", "slider-handle-color-focus": "#46a6ff", "slider-handle-color-focus-shadow": "rgba(24, 144, 255, 0.12)", "slider-handle-color-tooltip-open": "#1890ff", "slider-handle-size": "14px", "slider-handle-margin-top": "-5px", "slider-handle-shadow": "0", "slider-dot-border-color": "#f0f0f0", "slider-dot-border-color-active": "#8cc8ff", "slider-disabled-color": "rgba(0, 0, 0, 0.25)", "slider-disabled-background-color": "#fff", "tree-bg": "#fff", "tree-title-height": "24px", "tree-child-padding": "18px", "tree-directory-selected-color": "#fff", "tree-directory-selected-bg": "#1890ff", "tree-node-hover-bg": "#f5f5f5", "tree-node-selected-bg": "#bae7ff", "collapse-header-padding": "12px 16px", "collapse-header-padding-extra": "40px", "collapse-header-bg": "#fafafa", "collapse-content-padding": "16px", "collapse-content-bg": "#fff", "collapse-header-arrow-left": "16px", "skeleton-color": "rgba(190, 190, 190, 0.2)", "skeleton-to-color": "rgba(129, 129, 129, 0.24)", "skeleton-paragraph-margin-top": "28px", "skeleton-paragraph-li-margin-top": "16px", "skeleton-paragraph-li-height": "16px", "skeleton-title-height": "16px", "skeleton-title-paragraph-margin-top": "24px", "transfer-header-height": "40px", "transfer-item-height": "32px", "transfer-disabled-bg": "#f5f5f5", "transfer-list-height": "200px", "transfer-item-hover-bg": "#f5f5f5", "transfer-item-selected-hover-bg": "#dcf4ff", "transfer-item-padding-vertical": "6px", "transfer-list-search-icon-top": "12px", "message-notice-content-padding": "10px 16px", "message-notice-content-bg": "#fff", "wave-animation-width": "6px", "alert-success-border-color": "#b7eb8f", "alert-success-bg-color": "#f6ffed", "alert-success-icon-color": "#52c41a", "alert-info-border-color": "#91d5ff", "alert-info-bg-color": "#e6f7ff", "alert-info-icon-color": "#1890ff", "alert-warning-border-color": "#ffe58f", "alert-warning-bg-color": "#fffbe6", "alert-warning-icon-color": "#faad14", "alert-error-border-color": "#ffccc7", "alert-error-bg-color": "#fff2f0", "alert-error-icon-color": "#ff4d4f", "alert-message-color": "rgba(0, 0, 0, 0.85)", "alert-text-color": "rgba(0, 0, 0, 0.85)", "alert-close-color": "rgba(0, 0, 0, 0.45)", "alert-close-hover-color": "rgba(0, 0, 0, 0.75)", "alert-no-icon-padding-vertical": "8px", "alert-with-description-no-icon-padding-vertical": "15px", "alert-with-description-padding-vertical": "15px", "alert-with-description-padding": "15px 15px 15px 24px", "alert-icon-top": "12.0005px", "alert-with-description-icon-size": "24px", "list-header-background": "transparent", "list-footer-background": "transparent", "list-empty-text-padding": "16px", "list-item-padding": "12px 0", "list-item-padding-sm": "8px 16px", "list-item-padding-lg": "16px 24px", "list-item-meta-margin-bottom": "16px", "list-item-meta-avatar-margin-right": "16px", "list-item-meta-title-margin-bottom": "12px", "list-customize-card-bg": "#fff", "list-item-meta-description-font-size": "14px", "statistic-title-font-size": "14px", "statistic-content-font-size": "24px", "statistic-unit-font-size": "24px", "statistic-font-family": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "drawer-header-padding": "16px 24px", "drawer-bg": "#fff", "drawer-footer-padding-vertical": "10px", "drawer-footer-padding-horizontal": "16px", "drawer-header-close-size": "56px", "drawer-title-font-size": "16px", "drawer-title-line-height": "22px", "timeline-width": "2px", "timeline-color": "#f0f0f0", "timeline-dot-border-width": "2px", "timeline-dot-color": "#1890ff", "timeline-dot-bg": "#fff", "timeline-item-padding-bottom": "20px", "typography-title-font-weight": "600", "typography-title-margin-top": "1.2em", "typography-title-margin-bottom": "0.5em", "upload-actions-color": "rgba(0, 0, 0, 0.45)", "process-tail-color": "#f0f0f0", "steps-nav-arrow-color": "rgba(0, 0, 0, 0.25)", "steps-background": "#fff", "steps-icon-size": "32px", "steps-icon-custom-size": "32px", "steps-icon-custom-top": "0px", "steps-icon-custom-font-size": "24px", "steps-icon-top": "-0.5px", "steps-icon-font-size": "16px", "steps-icon-margin": "0 8px 0 0", "steps-title-line-height": "32px", "steps-small-icon-size": "24px", "steps-small-icon-margin": "0 8px 0 0", "steps-dot-size": "8px", "steps-dot-top": "2px", "steps-current-dot-size": "10px", "steps-description-max-width": "140px", "steps-nav-content-max-width": "auto", "steps-vertical-icon-width": "16px", "steps-vertical-tail-width": "16px", "steps-vertical-tail-width-sm": "12px", "notification-bg": "#fff", "notification-padding-vertical": "16px", "notification-padding-horizontal": "24px", "result-title-font-size": "24px", "result-subtitle-font-size": "14px", "result-icon-font-size": "72px", "result-extra-margin": "24px 0 0 0", "image-size-base": "48px", "image-font-size-base": "24px", "image-bg": "#f5f5f5", "image-color": "#fff", "image-mask-font-size": "16px", "image-preview-operation-size": "18px", "image-preview-operation-color": "rgba(255, 255, 255, 0.85)", "image-preview-operation-disabled-color": "rgba(255, 255, 255, 0.25)", "segmented-bg": "rgba(0, 0, 0, 0.04)", "segmented-hover-bg": "rgba(0, 0, 0, 0.06)", "segmented-selected-bg": "#fff", "segmented-label-color": "rgba(0, 0, 0, 0.65)", "segmented-label-hover-color": "#262626"}, "define": {"ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION": "", "REACT_APP_ENV": "dev"}}, "routes": {"1": {"path": "/user", "layout": false, "id": "1", "absPath": "/user"}, "2": {"name": "login", "path": "/user/login", "file": "@/pages/user/login/index.tsx", "parentId": "1", "id": "2", "absPath": "/user/login", "__content": "import {\r\n  LockOutlined,\r\n  UserOutlined,\r\n  RobotOutlined,\r\n  SafetyCertificateOutlined,\r\n} from '@ant-design/icons';\r\nimport {\r\n  LoginForm,\r\n  ProFormCheckbox,\r\n  ProFormText,\r\n} from '@ant-design/pro-components';\r\nimport {\r\n  FormattedMessage,\r\n  Helmet,\r\n  SelectLang,\r\n  useIntl,\r\n  useModel,\r\n} from '@umijs/max';\r\nimport { Alert, App, Card, Typography } from 'antd';\r\nimport { createStyles } from 'antd-style';\r\nimport React, { useState } from 'react';\r\nimport { flushSync } from 'react-dom';\r\nimport { login } from '@/services/rwxai';\r\nimport { saveLoginInfo } from '@/utils/auth';\r\nimport Settings from '../../../../config/defaultSettings';\r\n\r\nconst { Title, Paragraph } = Typography;\r\n\r\nconst useStyles = createStyles(({ token }) => {\r\n  return {\r\n    container: {\r\n      display: 'flex',\r\n      height: '100vh',\r\n      overflow: 'hidden',\r\n      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\r\n      position: 'relative',\r\n    },\r\n    leftSection: {\r\n      flex: 1,\r\n      display: 'flex',\r\n      flexDirection: 'column',\r\n      justifyContent: 'center',\r\n      alignItems: 'center',\r\n      padding: '0 80px',\r\n      color: 'white',\r\n      position: 'relative',\r\n      '&::before': {\r\n        content: '\"\"',\r\n        position: 'absolute',\r\n        top: 0,\r\n        left: 0,\r\n        right: 0,\r\n        bottom: 0,\r\n        background: 'rgba(0, 0, 0, 0.1)',\r\n        backdropFilter: 'blur(10px)',\r\n      },\r\n    },\r\n    rightSection: {\r\n      width: '480px',\r\n      display: 'flex',\r\n      flexDirection: 'column',\r\n      justifyContent: 'center',\r\n      alignItems: 'center',\r\n      padding: '40px',\r\n      backgroundColor: 'rgba(255, 255, 255, 0.95)',\r\n      backdropFilter: 'blur(20px)',\r\n      boxShadow: '-10px 0 30px rgba(0, 0, 0, 0.1)',\r\n    },\r\n    brandSection: {\r\n      textAlign: 'center',\r\n      marginBottom: '60px',\r\n      position: 'relative',\r\n      zIndex: 1,\r\n    },\r\n    brandTitle: {\r\n      fontSize: '48px',\r\n      fontWeight: 'bold',\r\n      marginBottom: '16px',\r\n      background: 'linear-gradient(45deg, #fff, #f0f0f0)',\r\n      backgroundClip: 'text',\r\n      WebkitBackgroundClip: 'text',\r\n      WebkitTextFillColor: 'transparent',\r\n      textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)',\r\n    },\r\n    brandSubtitle: {\r\n      fontSize: '18px',\r\n      opacity: 0.9,\r\n      marginBottom: '40px',\r\n    },\r\n    featureList: {\r\n      listStyle: 'none',\r\n      padding: 0,\r\n      margin: 0,\r\n      '& li': {\r\n        display: 'flex',\r\n        alignItems: 'center',\r\n        marginBottom: '20px',\r\n        fontSize: '16px',\r\n        '& .anticon': {\r\n          marginRight: '12px',\r\n          fontSize: '20px',\r\n          color: '#fff',\r\n        },\r\n      },\r\n    },\r\n    loginCard: {\r\n      width: '100%',\r\n      maxWidth: '400px',\r\n      border: 'none',\r\n      borderRadius: '16px',\r\n      boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',\r\n      overflow: 'hidden',\r\n    },\r\n    loginHeader: {\r\n      textAlign: 'center',\r\n      marginBottom: '32px',\r\n    },\r\n    logo: {\r\n      width: '64px',\r\n      height: '64px',\r\n      marginBottom: '16px',\r\n    },\r\n    loginTitle: {\r\n      fontSize: '28px',\r\n      fontWeight: 'bold',\r\n      color: token.colorText,\r\n      marginBottom: '8px',\r\n    },\r\n    loginSubtitle: {\r\n      color: token.colorTextSecondary,\r\n      fontSize: '14px',\r\n    },\r\n    lang: {\r\n      position: 'fixed',\r\n      top: 16,\r\n      right: 16,\r\n      zIndex: 1000,\r\n      width: 42,\r\n      height: 42,\r\n      lineHeight: '42px',\r\n      borderRadius: token.borderRadius,\r\n      backgroundColor: 'rgba(255, 255, 255, 0.1)',\r\n      backdropFilter: 'blur(10px)',\r\n      ':hover': {\r\n        backgroundColor: 'rgba(255, 255, 255, 0.2)',\r\n      },\r\n    },\r\n    formItem: {\r\n      marginBottom: '24px',\r\n      '& .ant-input-affix-wrapper': {\r\n        height: '48px',\r\n        borderRadius: '8px',\r\n        border: '1px solid #e0e0e0',\r\n        '&:hover': {\r\n          borderColor: token.colorPrimary,\r\n        },\r\n        '&:focus-within': {\r\n          borderColor: token.colorPrimary,\r\n          boxShadow: `0 0 0 2px ${token.colorPrimary}20`,\r\n        },\r\n      },\r\n    },\r\n    submitButton: {\r\n      height: '48px',\r\n      borderRadius: '8px',\r\n      fontSize: '16px',\r\n      fontWeight: 'bold',\r\n      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\r\n      border: 'none',\r\n      '&:hover': {\r\n        background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',\r\n      },\r\n    },\r\n  };\r\n});\r\n\r\nconst BrandSection = () => {\r\n  const { styles } = useStyles();\r\n  const intl = useIntl();\r\n\r\n  return (\r\n    <div className={styles.brandSection}>\r\n      <Title className={styles.brandTitle}>RwxAI</Title>\r\n      <Paragraph className={styles.brandSubtitle}>\r\n        {intl.formatMessage({\r\n          id: 'pages.login.brand.subtitle',\r\n          defaultMessage: '智能AI助手平台，让AI为您的工作赋能',\r\n        })}\r\n      </Paragraph>\r\n      <ul className={styles.featureList}>\r\n        <li>\r\n          <RobotOutlined />\r\n          {intl.formatMessage({\r\n            id: 'pages.login.feature.ai',\r\n            defaultMessage: '强大的AI模型集成',\r\n          })}\r\n        </li>\r\n        <li>\r\n          <SafetyCertificateOutlined />\r\n          {intl.formatMessage({\r\n            id: 'pages.login.feature.security',\r\n            defaultMessage: '企业级安全保障',\r\n          })}\r\n        </li>\r\n        <li>\r\n          <UserOutlined />\r\n          {intl.formatMessage({\r\n            id: 'pages.login.feature.easy',\r\n            defaultMessage: '简单易用的界面',\r\n          })}\r\n        </li>\r\n      </ul>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst Lang = () => {\r\n  const { styles } = useStyles();\r\n\r\n  return (\r\n    <div className={styles.lang} data-lang>\r\n      {SelectLang && <SelectLang />}\r\n    </div>\r\n  );\r\n};\r\n\r\nconst LoginMessage: React.FC<{\r\n  content: string;\r\n}> = ({ content }) => {\r\n  return (\r\n    <Alert\r\n      style={{\r\n        marginBottom: 24,\r\n      }}\r\n      message={content}\r\n      type=\"error\"\r\n      showIcon\r\n    />\r\n  );\r\n};\r\n\r\nconst Login: React.FC = () => {\r\n  const [userLoginState, setUserLoginState] = useState<API.LoginResult>({});\r\n  const { initialState, setInitialState } = useModel('@@initialState');\r\n  const { styles } = useStyles();\r\n  const { message } = App.useApp();\r\n  const intl = useIntl();\r\n\r\n  const fetchUserInfo = async () => {\r\n    const userInfo = await initialState?.fetchUserInfo?.();\r\n    if (userInfo) {\r\n      flushSync(() => {\r\n        setInitialState((s) => ({\r\n          ...s,\r\n          currentUser: userInfo,\r\n        }));\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleSubmit = async (values: API.LoginParams) => {\r\n    try {\r\n      // 调用RwxAI登录接口\r\n      const loginData = {\r\n        Username: values.username,\r\n        Password: values.password,\r\n      };\r\n\r\n      const response = await login(loginData);\r\n\r\n      if (response.success && response.data?.token) {\r\n        // 保存JWT令牌和用户信息\r\n        saveLoginInfo(response.data);\r\n\r\n        const defaultLoginSuccessMessage = intl.formatMessage({\r\n          id: 'pages.login.success',\r\n          defaultMessage: '登录成功！',\r\n        });\r\n        message.success(defaultLoginSuccessMessage);\r\n\r\n        // 刷新用户信息\r\n        await fetchUserInfo();\r\n\r\n        // 跳转到目标页面\r\n        const urlParams = new URL(window.location.href).searchParams;\r\n        window.location.href = urlParams.get('redirect') || '/';\r\n        return;\r\n      }\r\n\r\n      // 登录失败\r\n      setUserLoginState({ status: 'error', type: 'account' });\r\n    } catch (error: any) {\r\n      const defaultLoginFailureMessage = intl.formatMessage({\r\n        id: 'pages.login.failure',\r\n        defaultMessage: '登录失败，请重试！',\r\n      });\r\n      console.error('登录错误:', error);\r\n      message.error(error.message || defaultLoginFailureMessage);\r\n      setUserLoginState({ status: 'error', type: 'account' });\r\n    }\r\n  };\r\n  const { status } = userLoginState;\r\n\r\n  return (\r\n    <div className={styles.container}>\r\n      <Helmet>\r\n        <title>\r\n          {intl.formatMessage({\r\n            id: 'menu.login',\r\n            defaultMessage: '登录页',\r\n          })}\r\n          {Settings.title && ` - ${Settings.title}`}\r\n        </title>\r\n      </Helmet>\r\n      <Lang />\r\n\r\n      {/* 左侧品牌展示区域 */}\r\n      <div className={styles.leftSection}>\r\n        <BrandSection />\r\n      </div>\r\n\r\n      {/* 右侧登录表单区域 */}\r\n      <div className={styles.rightSection}>\r\n        <Card className={styles.loginCard} bordered={false}>\r\n          <div className={styles.loginHeader}>\r\n            <img alt=\"logo\" src=\"/logo.svg\" className={styles.logo} />\r\n            <Title level={2} className={styles.loginTitle}>\r\n              {intl.formatMessage({\r\n                id: 'pages.login.title',\r\n                defaultMessage: '欢迎登录',\r\n              })}\r\n            </Title>\r\n            <div className={styles.loginSubtitle}>\r\n              {intl.formatMessage({\r\n                id: 'pages.login.subtitle',\r\n                defaultMessage: '请输入您的账号和密码',\r\n              })}\r\n            </div>\r\n          </div>\r\n\r\n          <LoginForm\r\n            submitter={{\r\n              searchConfig: {\r\n                submitText: intl.formatMessage({\r\n                  id: 'pages.login.submit',\r\n                  defaultMessage: '登录',\r\n                }),\r\n              },\r\n              submitButtonProps: {\r\n                className: styles.submitButton,\r\n                size: 'large',\r\n              },\r\n            }}\r\n            onFinish={async (values) => {\r\n              await handleSubmit(values as API.LoginParams);\r\n            }}\r\n          >\r\n            {status === 'error' && (\r\n              <LoginMessage\r\n                content={intl.formatMessage({\r\n                  id: 'pages.login.accountLogin.errorMessage',\r\n                  defaultMessage: '用户名或密码错误，请重试',\r\n                })}\r\n              />\r\n            )}\r\n\r\n            <div className={styles.formItem}>\r\n              <ProFormText\r\n                name=\"username\"\r\n                fieldProps={{\r\n                  size: 'large',\r\n                  prefix: <UserOutlined style={{ color: '#999' }} />,\r\n                }}\r\n                placeholder={intl.formatMessage({\r\n                  id: 'pages.login.username.placeholder',\r\n                  defaultMessage: '请输入用户名',\r\n                })}\r\n                rules={[\r\n                  {\r\n                    required: true,\r\n                    message: (\r\n                      <FormattedMessage\r\n                        id=\"pages.login.username.required\"\r\n                        defaultMessage=\"请输入用户名!\"\r\n                      />\r\n                    ),\r\n                  },\r\n                ]}\r\n              />\r\n            </div>\r\n\r\n            <div className={styles.formItem}>\r\n              <ProFormText.Password\r\n                name=\"password\"\r\n                fieldProps={{\r\n                  size: 'large',\r\n                  prefix: <LockOutlined style={{ color: '#999' }} />,\r\n                }}\r\n                placeholder={intl.formatMessage({\r\n                  id: 'pages.login.password.placeholder',\r\n                  defaultMessage: '请输入密码',\r\n                })}\r\n                rules={[\r\n                  {\r\n                    required: true,\r\n                    message: (\r\n                      <FormattedMessage\r\n                        id=\"pages.login.password.required\"\r\n                        defaultMessage=\"请输入密码！\"\r\n                      />\r\n                    ),\r\n                  },\r\n                ]}\r\n              />\r\n            </div>\r\n\r\n            <div\r\n              style={{\r\n                marginBottom: 24,\r\n                display: 'flex',\r\n                justifyContent: 'space-between',\r\n                alignItems: 'center',\r\n              }}\r\n            >\r\n              <ProFormCheckbox noStyle name=\"autoLogin\">\r\n                <FormattedMessage\r\n                  id=\"pages.login.rememberMe\"\r\n                  defaultMessage=\"记住我\"\r\n                />\r\n              </ProFormCheckbox>\r\n              <a\r\n                style={{\r\n                  color: '#667eea',\r\n                  textDecoration: 'none',\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  e.currentTarget.style.textDecoration = 'underline';\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  e.currentTarget.style.textDecoration = 'none';\r\n                }}\r\n              >\r\n                <FormattedMessage\r\n                  id=\"pages.login.forgotPassword\"\r\n                  defaultMessage=\"忘记密码？\"\r\n                />\r\n              </a>\r\n            </div>\r\n          </LoginForm>\r\n        </Card>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Login;\r\n", "__isJSFile": true, "__absFile": "D:/rwx-ai/RwxAI.Web/src/pages/user/login/index.tsx"}, "3": {"name": "register", "path": "/user/register", "file": "@/pages/user/register/index.tsx", "parentId": "1", "id": "3", "absPath": "/user/register", "__content": "import {\n  LockOutlined,\n  MailOutlined,\n  UserOutlined,\n} from '@ant-design/icons';\nimport {\n  LoginForm,\n  ProFormText,\n} from '@ant-design/pro-components';\nimport {\n  FormattedMessage,\n  Helmet,\n  SelectLang,\n  useIntl,\n  history,\n} from '@umijs/max';\nimport { Alert, App } from 'antd';\nimport { createStyles } from 'antd-style';\nimport React, { useState } from 'react';\nimport { Footer } from '@/components';\nimport { register } from '@/services/rwxai';\nimport Settings from '../../../../config/defaultSettings';\n\nconst useStyles = createStyles(({ token }) => {\n  return {\n    action: {\n      marginLeft: '8px',\n      color: 'rgba(0, 0, 0, 0.2)',\n      fontSize: '24px',\n      verticalAlign: 'middle',\n      cursor: 'pointer',\n      transition: 'color 0.3s',\n      '&:hover': {\n        color: token.colorPrimaryActive,\n      },\n    },\n    lang: {\n      width: 42,\n      height: 42,\n      lineHeight: '42px',\n      position: 'fixed',\n      right: 16,\n      borderRadius: token.borderRadius,\n      ':hover': {\n        backgroundColor: token.colorBgTextHover,\n      },\n    },\n    container: {\n      display: 'flex',\n      flexDirection: 'column',\n      height: '100vh',\n      overflow: 'auto',\n      backgroundImage:\n        \"url('https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr')\",\n      backgroundSize: '100% 100%',\n    },\n  };\n});\n\nconst RegisterMessage: React.FC<{\n  content: string;\n}> = ({ content }) => {\n  return (\n    <Alert\n      style={{\n        marginBottom: 24,\n      }}\n      message={content}\n      type=\"error\"\n      showIcon\n    />\n  );\n};\n\n// 定义注册结果类型\ninterface RegisterResult {\n  success?: boolean;\n  message?: string;\n  status?: string;\n}\n\nconst Register: React.FC = () => {\n  const [userRegisterState, setUserRegisterState] = useState<RegisterResult>({});\n  const { styles } = useStyles();\n  const intl = useIntl();\n  const { message } = App.useApp();\n\n  const handleSubmit = async (values: RwxAI.RegisterRequest) => {\n    try {\n      // 注册\n      const msg = await register(values);\n      if (msg.success) {\n        const defaultRegisterSuccessMessage = intl.formatMessage({\n          id: 'pages.register.success',\n          defaultMessage: '注册成功！',\n        });\n        message.success(defaultRegisterSuccessMessage);\n        \n        // 注册成功后跳转到登录页\n        setTimeout(() => {\n          history.push('/user/login');\n        }, 1000);\n        return;\n      }\n      \n      // 如果失败去设置用户错误信息\n      setUserRegisterState({ status: 'error', message: msg.message });\n    } catch (error) {\n      const defaultRegisterFailureMessage = intl.formatMessage({\n        id: 'pages.register.failure',\n        defaultMessage: '注册失败，请重试！',\n      });\n      message.error(defaultRegisterFailureMessage);\n    }\n  };\n\n  const { status, message: registerMessage } = userRegisterState;\n\n  return (\n    <div className={styles.container}>\n      <Helmet>\n        <title>\n          {intl.formatMessage({\n            id: 'menu.register',\n            defaultMessage: '注册页',\n          })}\n          - {Settings.title}\n        </title>\n      </Helmet>\n      <div\n        style={{\n          flex: '1',\n          padding: '32px 0',\n        }}\n      >\n        <LoginForm\n          contentStyle={{\n            minWidth: 280,\n            maxWidth: '75vw',\n          }}\n          logo={<img alt=\"logo\" src=\"/logo.svg\" />}\n          title=\"RwxAI\"\n          subTitle={intl.formatMessage({ id: 'pages.layouts.userLayout.title' })}\n          initialValues={{\n            autoLogin: true,\n          }}\n          onFinish={async (values) => {\n            await handleSubmit(values as RwxAI.RegisterRequest);\n          }}\n          submitter={{\n            searchConfig: {\n              submitText: intl.formatMessage({\n                id: 'pages.register.submit',\n                defaultMessage: '注册',\n              }),\n            },\n          }}\n        >\n          {status === 'error' && registerMessage && (\n            <RegisterMessage content={registerMessage} />\n          )}\n\n          <ProFormText\n            name=\"Username\"\n            fieldProps={{\n              size: 'large',\n              prefix: <UserOutlined />,\n            }}\n            placeholder={intl.formatMessage({\n              id: 'pages.register.username.placeholder',\n              defaultMessage: '用户名',\n            })}\n            rules={[\n              {\n                required: true,\n                message: (\n                  <FormattedMessage\n                    id=\"pages.register.username.required\"\n                    defaultMessage=\"请输入用户名!\"\n                  />\n                ),\n              },\n            ]}\n          />\n          \n          <ProFormText\n            name=\"Email\"\n            fieldProps={{\n              size: 'large',\n              prefix: <MailOutlined />,\n            }}\n            placeholder={intl.formatMessage({\n              id: 'pages.register.email.placeholder',\n              defaultMessage: '邮箱',\n            })}\n            rules={[\n              {\n                required: true,\n                message: (\n                  <FormattedMessage\n                    id=\"pages.register.email.required\"\n                    defaultMessage=\"请输入邮箱!\"\n                  />\n                ),\n              },\n              {\n                type: 'email',\n                message: (\n                  <FormattedMessage\n                    id=\"pages.register.email.invalid\"\n                    defaultMessage=\"邮箱格式错误!\"\n                  />\n                ),\n              },\n            ]}\n          />\n\n          <ProFormText\n            name=\"FirstName\"\n            fieldProps={{\n              size: 'large',\n              prefix: <UserOutlined />,\n            }}\n            placeholder={intl.formatMessage({\n              id: 'pages.register.firstName.placeholder',\n              defaultMessage: '名',\n            })}\n          />\n\n          <ProFormText\n            name=\"LastName\"\n            fieldProps={{\n              size: 'large',\n              prefix: <UserOutlined />,\n            }}\n            placeholder={intl.formatMessage({\n              id: 'pages.register.lastName.placeholder',\n              defaultMessage: '姓',\n            })}\n          />\n\n          <ProFormText.Password\n            name=\"Password\"\n            fieldProps={{\n              size: 'large',\n              prefix: <LockOutlined />,\n            }}\n            placeholder={intl.formatMessage({\n              id: 'pages.register.password.placeholder',\n              defaultMessage: '密码',\n            })}\n            rules={[\n              {\n                required: true,\n                message: (\n                  <FormattedMessage\n                    id=\"pages.register.password.required\"\n                    defaultMessage=\"请输入密码！\"\n                  />\n                ),\n              },\n              {\n                min: 6,\n                message: (\n                  <FormattedMessage\n                    id=\"pages.register.password.min\"\n                    defaultMessage=\"密码长度不能少于6位！\"\n                  />\n                ),\n              },\n            ]}\n          />\n\n          <div\n            style={{\n              marginBottom: 24,\n              textAlign: 'center',\n            }}\n          >\n            <a\n              style={{\n                float: 'right',\n              }}\n              onClick={() => history.push('/user/login')}\n            >\n              <FormattedMessage id=\"pages.register.loginAccount\" defaultMessage=\"已有账户？去登录\" />\n            </a>\n          </div>\n        </LoginForm>\n      </div>\n      <Footer />\n      <SelectLang className={styles.lang} />\n    </div>\n  );\n};\n\nexport default Register;\n", "__isJSFile": true, "__absFile": "D:/rwx-ai/RwxAI.Web/src/pages/user/register/index.tsx"}, "4": {"path": "/welcome", "name": "welcome", "icon": "smile", "file": "@/pages/Welcome.tsx", "parentId": "ant-design-pro-layout", "id": "4", "absPath": "/welcome", "__content": "import { PageContainer } from '@ant-design/pro-components';\r\nimport { useModel } from '@umijs/max';\r\nimport { Card, theme } from 'antd';\r\nimport React from 'react';\r\n\r\n/**\r\n * 每个单独的卡片，为了复用样式抽成了组件\r\n * @param param0\r\n * @returns\r\n */\r\nconst InfoCard: React.FC<{\r\n  title: string;\r\n  index: number;\r\n  desc: string;\r\n  href: string;\r\n}> = ({ title, href, index, desc }) => {\r\n  const { useToken } = theme;\r\n\r\n  const { token } = useToken();\r\n\r\n  return (\r\n    <div\r\n      style={{\r\n        backgroundColor: token.colorBgContainer,\r\n        boxShadow: token.boxShadow,\r\n        borderRadius: '8px',\r\n        fontSize: '14px',\r\n        color: token.colorTextSecondary,\r\n        lineHeight: '22px',\r\n        padding: '16px 19px',\r\n        minWidth: '220px',\r\n        flex: 1,\r\n      }}\r\n    >\r\n      <div\r\n        style={{\r\n          display: 'flex',\r\n          gap: '4px',\r\n          alignItems: 'center',\r\n        }}\r\n      >\r\n        <div\r\n          style={{\r\n            width: 48,\r\n            height: 48,\r\n            lineHeight: '22px',\r\n            backgroundSize: '100%',\r\n            textAlign: 'center',\r\n            padding: '8px 16px 16px 12px',\r\n            color: '#FFF',\r\n            fontWeight: 'bold',\r\n            backgroundImage:\r\n              \"url('https://gw.alipayobjects.com/zos/bmw-prod/daaf8d50-8e6d-4251-905d-676a24ddfa12.svg')\",\r\n          }}\r\n        >\r\n          {index}\r\n        </div>\r\n        <div\r\n          style={{\r\n            fontSize: '16px',\r\n            color: token.colorText,\r\n            paddingBottom: 8,\r\n          }}\r\n        >\r\n          {title}\r\n        </div>\r\n      </div>\r\n      <div\r\n        style={{\r\n          fontSize: '14px',\r\n          color: token.colorTextSecondary,\r\n          textAlign: 'justify',\r\n          lineHeight: '22px',\r\n          marginBottom: 8,\r\n        }}\r\n      >\r\n        {desc}\r\n      </div>\r\n      <a href={href} target=\"_blank\" rel=\"noreferrer\">\r\n        了解更多 {'>'}\r\n      </a>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst Welcome: React.FC = () => {\r\n  const { token } = theme.useToken();\r\n  const { initialState } = useModel('@@initialState');\r\n  return (\r\n    <PageContainer>\r\n      <Card\r\n        style={{\r\n          borderRadius: 8,\r\n        }}\r\n        styles={{\r\n          body: {\r\n            backgroundImage:\r\n              initialState?.settings?.navTheme === 'realDark'\r\n                ? 'background-image: linear-gradient(75deg, #1A1B1F 0%, #191C1F 100%)'\r\n                : 'background-image: linear-gradient(75deg, #FBFDFF 0%, #F5F7FF 100%)',\r\n          },\r\n        }}\r\n      >\r\n        <div\r\n          style={{\r\n            backgroundPosition: '100% -30%',\r\n            backgroundRepeat: 'no-repeat',\r\n            backgroundSize: '274px auto',\r\n            backgroundImage:\r\n              \"url('https://gw.alipayobjects.com/mdn/rms_a9745b/afts/img/A*BuFmQqsB2iAAAAAAAAAAAAAAARQnAQ')\",\r\n          }}\r\n        >\r\n          <div\r\n            style={{\r\n              fontSize: '20px',\r\n              color: token.colorTextHeading,\r\n            }}\r\n          >\r\n            欢迎使用 Ant Design Pro\r\n          </div>\r\n          <p\r\n            style={{\r\n              fontSize: '14px',\r\n              color: token.colorTextSecondary,\r\n              lineHeight: '22px',\r\n              marginTop: 16,\r\n              marginBottom: 32,\r\n              width: '65%',\r\n            }}\r\n          >\r\n            Ant Design Pro 是一个整合了 umi，Ant Design 和 ProComponents\r\n            的脚手架方案。致力于在设计规范和基础组件的基础上，继续向上构建，提炼出典型模板/业务组件/配套设计资源，进一步提升企业级中后台产品设计研发过程中的『用户』和『设计者』的体验。\r\n          </p>\r\n          <div\r\n            style={{\r\n              display: 'flex',\r\n              flexWrap: 'wrap',\r\n              gap: 16,\r\n            }}\r\n          >\r\n            <InfoCard\r\n              index={1}\r\n              href=\"https://umijs.org/docs/introduce/introduce\"\r\n              title=\"了解 umi\"\r\n              desc=\"umi 是一个可扩展的企业级前端应用框架,umi 以路由为基础的，同时支持配置式路由和约定式路由，保证路由的功能完备，并以此进行功能扩展。\"\r\n            />\r\n            <InfoCard\r\n              index={2}\r\n              title=\"了解 ant design\"\r\n              href=\"https://ant.design\"\r\n              desc=\"antd 是基于 Ant Design 设计体系的 React UI 组件库，主要用于研发企业级中后台产品。\"\r\n            />\r\n            <InfoCard\r\n              index={3}\r\n              title=\"了解 Pro Components\"\r\n              href=\"https://procomponents.ant.design\"\r\n              desc=\"ProComponents 是一个基于 Ant Design 做了更高抽象的模板组件，以 一个组件就是一个页面为开发理念，为中后台开发带来更好的体验。\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </Card>\r\n    </PageContainer>\r\n  );\r\n};\r\n\r\nexport default Welcome;\r\n", "__isJSFile": true, "__absFile": "D:/rwx-ai/RwxAI.Web/src/pages/Welcome.tsx"}, "5": {"path": "/ai-models", "name": "ai-models", "icon": "robot", "file": "@/pages/ai-models/index.tsx", "parentId": "ant-design-pro-layout", "id": "5", "absPath": "/ai-models", "__content": "import React, { useRef, useState } from 'react';\nimport { PageContainer } from '@ant-design/pro-components';\nimport { ProTable, ActionType, ProColumns } from '@ant-design/pro-table';\nimport { Button, Space, Tag, Modal, Switch } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';\nimport { useIntl, FormattedMessage } from '@umijs/max';\nimport { getAIModels, deleteAIModel } from '@/services/rwxai';\nimport ModelForm from './components/ModelForm';\nimport ModelDetail from './components/ModelDetail';\n\nconst AIModelsPage: React.FC = () => {\n  const intl = useIntl();\n  const actionRef = useRef<ActionType>();\n  const [createModalVisible, setCreateModalVisible] = useState(false);\n  const [editModalVisible, setEditModalVisible] = useState(false);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [currentRecord, setCurrentRecord] = useState<RwxAI.AIModel>();\n\n  const handleDelete = async (record: RwxAI.AIModel) => {\n    Modal.confirm({\n      title: intl.formatMessage({ id: 'pages.aiModels.delete.confirm.title' }),\n      content: intl.formatMessage({ id: 'pages.aiModels.delete.confirm.content' }),\n      onOk: async () => {\n        const response = await deleteAIModel(record.Id);\n        if (response.success) {\n          actionRef.current?.reload();\n        }\n        // 成功和错误消息会由统一响应处理系统自动显示\n      },\n    });\n  };\n\n  const columns: ProColumns<RwxAI.AIModel>[] = [\n    {\n      title: <FormattedMessage id=\"pages.aiModels.table.name\" />,\n      dataIndex: 'Name',\n      key: 'Name',\n      ellipsis: true,\n    },\n    {\n      title: <FormattedMessage id=\"pages.aiModels.table.modelKey\" />,\n      dataIndex: 'ModelKey',\n      key: 'ModelKey',\n      ellipsis: true,\n    },\n    {\n      title: <FormattedMessage id=\"pages.aiModels.table.modelType\" />,\n      dataIndex: 'ModelType',\n      key: 'ModelType',\n      render: (_, record) => {\n        const typeMap = {\n          0: { text: 'Chat', color: 'blue' },\n          1: { text: 'Embedding', color: 'green' },\n          2: { text: 'Image Generation', color: 'purple' },\n          3: { text: 'Text to Speech', color: 'orange' },\n        };\n        const type = typeMap[record.ModelType as keyof typeof typeMap];\n        return <Tag color={type?.color}>{type?.text}</Tag>;\n      },\n    },\n    {\n      title: <FormattedMessage id=\"pages.aiModels.table.provider\" />,\n      dataIndex: ['Provider', 'Name'],\n      key: 'Provider',\n      ellipsis: true,\n    },\n    {\n      title: <FormattedMessage id=\"pages.aiModels.table.status\" />,\n      dataIndex: 'IsEnabled',\n      key: 'IsEnabled',\n      render: (_, record) => (\n        <Switch\n          checked={record.IsEnabled}\n          size=\"small\"\n          disabled\n        />\n      ),\n    },\n    {\n      title: <FormattedMessage id=\"pages.aiModels.table.maxTokens\" />,\n      dataIndex: 'MaxTokens',\n      key: 'MaxTokens',\n      width: 120,\n    },\n    {\n      title: <FormattedMessage id=\"pages.aiModels.table.temperature\" />,\n      dataIndex: 'Temperature',\n      key: 'Temperature',\n      width: 120,\n    },\n    {\n      title: <FormattedMessage id=\"pages.aiModels.table.createdTime\" />,\n      dataIndex: 'CreatedTime',\n      key: 'CreatedTime',\n      valueType: 'dateTime',\n      width: 180,\n    },\n    {\n      title: <FormattedMessage id=\"pages.aiModels.table.actions\" />,\n      key: 'actions',\n      width: 200,\n      render: (_, record) => (\n        <Space>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EyeOutlined />}\n            onClick={() => {\n              setCurrentRecord(record);\n              setDetailModalVisible(true);\n            }}\n          >\n            <FormattedMessage id=\"pages.aiModels.actions.view\" />\n          </Button>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EditOutlined />}\n            onClick={() => {\n              setCurrentRecord(record);\n              setEditModalVisible(true);\n            }}\n          >\n            <FormattedMessage id=\"pages.aiModels.actions.edit\" />\n          </Button>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            danger\n            icon={<DeleteOutlined />}\n            onClick={() => handleDelete(record)}\n          >\n            <FormattedMessage id=\"pages.aiModels.actions.delete\" />\n          </Button>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <PageContainer>\n      <ProTable<RwxAI.AIModel>\n        headerTitle={intl.formatMessage({ id: 'pages.aiModels.title' })}\n        actionRef={actionRef}\n        rowKey=\"Id\"\n        search={{\n          labelWidth: 120,\n        }}\n        toolBarRender={() => [\n          <Button\n            type=\"primary\"\n            key=\"primary\"\n            icon={<PlusOutlined />}\n            onClick={() => setCreateModalVisible(true)}\n          >\n            <FormattedMessage id=\"pages.aiModels.actions.create\" />\n          </Button>,\n        ]}\n        request={async () => {\n          const response = await getAIModels();\n          return {\n            data: response.success ? (response.data || []) : [],\n            success: response.success,\n          };\n        }}\n        columns={columns}\n      />\n\n      <ModelForm\n        visible={createModalVisible}\n        onVisibleChange={setCreateModalVisible}\n        onSuccess={() => {\n          actionRef.current?.reload();\n          setCreateModalVisible(false);\n        }}\n      />\n\n      <ModelForm\n        visible={editModalVisible}\n        onVisibleChange={setEditModalVisible}\n        initialValues={currentRecord}\n        onSuccess={() => {\n          actionRef.current?.reload();\n          setEditModalVisible(false);\n        }}\n      />\n\n      <ModelDetail\n        visible={detailModalVisible}\n        onVisibleChange={setDetailModalVisible}\n        data={currentRecord}\n      />\n    </PageContainer>\n  );\n};\n\nexport default AIModelsPage;\n", "__isJSFile": true, "__absFile": "D:/rwx-ai/RwxAI.Web/src/pages/ai-models/index.tsx"}, "6": {"path": "/apps", "name": "apps", "icon": "appstore", "file": "@/pages/apps/index.tsx", "parentId": "ant-design-pro-layout", "id": "6", "absPath": "/apps", "__content": "import React, { useRef, useState } from 'react';\nimport { PageContainer } from '@ant-design/pro-components';\nimport { ProTable, ActionType, ProColumns } from '@ant-design/pro-table';\nimport { Button, Space, Tag, message, Modal, Switch, Tooltip } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, KeyOutlined, ReloadOutlined } from '@ant-design/icons';\nimport { useIntl, FormattedMessage } from '@umijs/max';\nimport { getMyApps, deleteApp, updateAppStatus, regenerateApiKeys, resetApiCalls } from '@/services/rwxai';\nimport AppForm from './components/AppForm';\nimport AppDetail from './components/AppDetail';\n\nconst AppsPage: React.FC = () => {\n  const intl = useIntl();\n  const actionRef = useRef<ActionType>();\n  const [createModalVisible, setCreateModalVisible] = useState(false);\n  const [editModalVisible, setEditModalVisible] = useState(false);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [currentRecord, setCurrentRecord] = useState<RwxAI.App>();\n\n  const handleDelete = async (record: RwxAI.App) => {\n    Modal.confirm({\n      title: intl.formatMessage({ id: 'pages.apps.delete.confirm.title' }),\n      content: intl.formatMessage({ id: 'pages.apps.delete.confirm.content' }),\n      onOk: async () => {\n        try {\n          await deleteApp(record.Id);\n          message.success(intl.formatMessage({ id: 'pages.apps.delete.success' }));\n          actionRef.current?.reload();\n        } catch (error) {\n          message.error(intl.formatMessage({ id: 'pages.apps.delete.error' }));\n        }\n      },\n    });\n  };\n\n  const handleStatusChange = async (record: RwxAI.App, checked: boolean) => {\n    try {\n      await updateAppStatus(record.Id, { IsEnabled: checked });\n      message.success(intl.formatMessage({ id: 'pages.apps.status.update.success' }));\n      actionRef.current?.reload();\n    } catch (error) {\n      message.error(intl.formatMessage({ id: 'pages.apps.status.update.error' }));\n    }\n  };\n\n  const handleRegenerateKeys = async (record: RwxAI.App) => {\n    Modal.confirm({\n      title: intl.formatMessage({ id: 'pages.apps.regenerateKeys.confirm.title' }),\n      content: intl.formatMessage({ id: 'pages.apps.regenerateKeys.confirm.content' }),\n      onOk: async () => {\n        try {\n          await regenerateApiKeys(record.Id);\n          message.success(intl.formatMessage({ id: 'pages.apps.regenerateKeys.success' }));\n          actionRef.current?.reload();\n        } catch (error) {\n          message.error(intl.formatMessage({ id: 'pages.apps.regenerateKeys.error' }));\n        }\n      },\n    });\n  };\n\n  const handleResetApiCalls = async (record: RwxAI.App) => {\n    Modal.confirm({\n      title: intl.formatMessage({ id: 'pages.apps.resetApiCalls.confirm.title' }),\n      content: intl.formatMessage({ id: 'pages.apps.resetApiCalls.confirm.content' }),\n      onOk: async () => {\n        try {\n          await resetApiCalls(record.Id);\n          message.success(intl.formatMessage({ id: 'pages.apps.resetApiCalls.success' }));\n          actionRef.current?.reload();\n        } catch (error) {\n          message.error(intl.formatMessage({ id: 'pages.apps.resetApiCalls.error' }));\n        }\n      },\n    });\n  };\n\n  const columns: ProColumns<RwxAI.App>[] = [\n    {\n      title: <FormattedMessage id=\"pages.apps.table.name\" />,\n      dataIndex: 'Name',\n      key: 'Name',\n      ellipsis: true,\n    },\n    {\n      title: <FormattedMessage id=\"pages.apps.table.description\" />,\n      dataIndex: 'Description',\n      key: 'Description',\n      ellipsis: true,\n      hideInSearch: true,\n    },\n    {\n      title: <FormattedMessage id=\"pages.apps.table.status\" />,\n      dataIndex: 'IsEnabled',\n      key: 'IsEnabled',\n      render: (_, record) => (\n        <Switch\n          checked={record.IsEnabled}\n          size=\"small\"\n          onChange={(checked) => handleStatusChange(record, checked)}\n        />\n      ),\n    },\n    {\n      title: <FormattedMessage id=\"pages.apps.table.apiCalls\" />,\n      key: 'apiCalls',\n      hideInSearch: true,\n      render: (_, record) => (\n        <span>\n          {record.ApiCallCount || 0} / {record.MaxApiCalls || '∞'}\n        </span>\n      ),\n    },\n    {\n      title: <FormattedMessage id=\"pages.apps.table.rateLimit\" />,\n      dataIndex: 'RateLimitPerMinute',\n      key: 'RateLimitPerMinute',\n      hideInSearch: true,\n      render: (value) => value ? `${value}/min` : '-',\n    },\n    {\n      title: <FormattedMessage id=\"pages.apps.table.createdTime\" />,\n      dataIndex: 'CreatedTime',\n      key: 'CreatedTime',\n      valueType: 'dateTime',\n      width: 180,\n      hideInSearch: true,\n    },\n    {\n      title: <FormattedMessage id=\"pages.apps.table.actions\" />,\n      key: 'actions',\n      width: 280,\n      render: (_, record) => (\n        <Space>\n          <Tooltip title={intl.formatMessage({ id: 'pages.apps.actions.view' })}>\n            <Button\n              type=\"link\"\n              size=\"small\"\n              icon={<EyeOutlined />}\n              onClick={() => {\n                setCurrentRecord(record);\n                setDetailModalVisible(true);\n              }}\n            />\n          </Tooltip>\n          <Tooltip title={intl.formatMessage({ id: 'pages.apps.actions.edit' })}>\n            <Button\n              type=\"link\"\n              size=\"small\"\n              icon={<EditOutlined />}\n              onClick={() => {\n                setCurrentRecord(record);\n                setEditModalVisible(true);\n              }}\n            />\n          </Tooltip>\n          <Tooltip title={intl.formatMessage({ id: 'pages.apps.actions.regenerateKeys' })}>\n            <Button\n              type=\"link\"\n              size=\"small\"\n              icon={<KeyOutlined />}\n              onClick={() => handleRegenerateKeys(record)}\n            />\n          </Tooltip>\n          <Tooltip title={intl.formatMessage({ id: 'pages.apps.actions.resetApiCalls' })}>\n            <Button\n              type=\"link\"\n              size=\"small\"\n              icon={<ReloadOutlined />}\n              onClick={() => handleResetApiCalls(record)}\n            />\n          </Tooltip>\n          <Tooltip title={intl.formatMessage({ id: 'pages.apps.actions.delete' })}>\n            <Button\n              type=\"link\"\n              size=\"small\"\n              danger\n              icon={<DeleteOutlined />}\n              onClick={() => handleDelete(record)}\n            />\n          </Tooltip>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <PageContainer>\n      <ProTable<RwxAI.App>\n        headerTitle={intl.formatMessage({ id: 'pages.apps.title' })}\n        actionRef={actionRef}\n        rowKey=\"Id\"\n        search={{\n          labelWidth: 120,\n        }}\n        toolBarRender={() => [\n          <Button\n            type=\"primary\"\n            key=\"primary\"\n            icon={<PlusOutlined />}\n            onClick={() => setCreateModalVisible(true)}\n          >\n            <FormattedMessage id=\"pages.apps.actions.create\" />\n          </Button>,\n        ]}\n        request={async () => {\n          const response = await getMyApps();\n          return {\n            data: response.success ? (response.data || []) : [],\n            success: response.success,\n          };\n        }}\n        columns={columns}\n      />\n\n      <AppForm\n        visible={createModalVisible}\n        onVisibleChange={setCreateModalVisible}\n        onSuccess={() => {\n          actionRef.current?.reload();\n          setCreateModalVisible(false);\n        }}\n      />\n\n      <AppForm\n        visible={editModalVisible}\n        onVisibleChange={setEditModalVisible}\n        initialValues={currentRecord}\n        onSuccess={() => {\n          actionRef.current?.reload();\n          setEditModalVisible(false);\n        }}\n      />\n\n      <AppDetail\n        visible={detailModalVisible}\n        onVisibleChange={setDetailModalVisible}\n        data={currentRecord}\n      />\n    </PageContainer>\n  );\n};\n\nexport default AppsPage;\n", "__isJSFile": true, "__absFile": "D:/rwx-ai/RwxAI.Web/src/pages/apps/index.tsx"}, "7": {"path": "/chat", "name": "chat", "icon": "message", "parentId": "ant-design-pro-layout", "id": "7", "absPath": "/chat"}, "8": {"path": "/chat", "redirect": "/chat/sessions", "parentId": "7", "id": "8", "absPath": "/chat"}, "9": {"path": "/chat/sessions", "name": "sessions", "file": "@/pages/chat/index.tsx", "parentId": "7", "id": "9", "absPath": "/chat/sessions", "__content": "import React, { useRef, useState } from 'react';\nimport { PageContainer } from '@ant-design/pro-components';\nimport { ProTable, ActionType, ProColumns } from '@ant-design/pro-table';\nimport { Button, Space, Tag, message, Modal, Switch } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, MessageOutlined, EyeOutlined } from '@ant-design/icons';\nimport { useIntl, FormattedMessage, history } from '@umijs/max';\nimport { getChatSessions, deleteChatSession } from '@/services/rwxai';\nimport SessionForm from './components/SessionForm';\n\nconst ChatSessionsPage: React.FC = () => {\n  const intl = useIntl();\n  const actionRef = useRef<ActionType>();\n  const [createModalVisible, setCreateModalVisible] = useState(false);\n  const [editModalVisible, setEditModalVisible] = useState(false);\n  const [currentRecord, setCurrentRecord] = useState<RwxAI.ChatSession>();\n\n  const handleDelete = async (record: RwxAI.ChatSession) => {\n    Modal.confirm({\n      title: intl.formatMessage({ id: 'pages.chat.delete.confirm.title' }),\n      content: intl.formatMessage({ id: 'pages.chat.delete.confirm.content' }),\n      onOk: async () => {\n        try {\n          await deleteChatSession(record.Id);\n          message.success(intl.formatMessage({ id: 'pages.chat.delete.success' }));\n          actionRef.current?.reload();\n        } catch (error) {\n          message.error(intl.formatMessage({ id: 'pages.chat.delete.error' }));\n        }\n      },\n    });\n  };\n\n  const handleChat = (record: RwxAI.ChatSession) => {\n    history.push(`/chat/session/${record.Id}`);\n  };\n\n  const columns: ProColumns<RwxAI.ChatSession>[] = [\n    {\n      title: <FormattedMessage id=\"pages.chat.table.name\" />,\n      dataIndex: 'Name',\n      key: 'Name',\n      ellipsis: true,\n    },\n    {\n      title: <FormattedMessage id=\"pages.chat.table.model\" />,\n      dataIndex: ['Model', 'Name'],\n      key: 'Model',\n      ellipsis: true,\n      hideInSearch: true,\n    },\n    {\n      title: <FormattedMessage id=\"pages.chat.table.status\" />,\n      dataIndex: 'IsActive',\n      key: 'IsActive',\n      render: (_, record) => (\n        <Tag color={record.IsActive ? 'green' : 'default'}>\n          {record.IsActive ? \n            intl.formatMessage({ id: 'pages.chat.status.active' }) : \n            intl.formatMessage({ id: 'pages.chat.status.inactive' })\n          }\n        </Tag>\n      ),\n    },\n    {\n      title: <FormattedMessage id=\"pages.chat.table.messageCount\" />,\n      key: 'messageCount',\n      hideInSearch: true,\n      render: (_, record) => record.Messages?.length || 0,\n    },\n    {\n      title: <FormattedMessage id=\"pages.chat.table.temperature\" />,\n      dataIndex: 'Temperature',\n      key: 'Temperature',\n      hideInSearch: true,\n    },\n    {\n      title: <FormattedMessage id=\"pages.chat.table.maxTokens\" />,\n      dataIndex: 'MaxTokens',\n      key: 'MaxTokens',\n      hideInSearch: true,\n    },\n    {\n      title: <FormattedMessage id=\"pages.chat.table.createdTime\" />,\n      dataIndex: 'CreatedTime',\n      key: 'CreatedTime',\n      valueType: 'dateTime',\n      width: 180,\n      hideInSearch: true,\n    },\n    {\n      title: <FormattedMessage id=\"pages.chat.table.actions\" />,\n      key: 'actions',\n      width: 200,\n      render: (_, record) => (\n        <Space>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<MessageOutlined />}\n            onClick={() => handleChat(record)}\n          >\n            <FormattedMessage id=\"pages.chat.actions.chat\" />\n          </Button>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EditOutlined />}\n            onClick={() => {\n              setCurrentRecord(record);\n              setEditModalVisible(true);\n            }}\n          >\n            <FormattedMessage id=\"pages.chat.actions.edit\" />\n          </Button>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            danger\n            icon={<DeleteOutlined />}\n            onClick={() => handleDelete(record)}\n          >\n            <FormattedMessage id=\"pages.chat.actions.delete\" />\n          </Button>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <PageContainer>\n      <ProTable<RwxAI.ChatSession>\n        headerTitle={intl.formatMessage({ id: 'pages.chat.title' })}\n        actionRef={actionRef}\n        rowKey=\"Id\"\n        search={{\n          labelWidth: 120,\n        }}\n        toolBarRender={() => [\n          <Button\n            type=\"primary\"\n            key=\"primary\"\n            icon={<PlusOutlined />}\n            onClick={() => setCreateModalVisible(true)}\n          >\n            <FormattedMessage id=\"pages.chat.actions.create\" />\n          </Button>,\n        ]}\n        request={async () => {\n          const response = await getChatSessions();\n          return {\n            data: response.success ? (response.data || []) : [],\n            success: response.success,\n          };\n        }}\n        columns={columns}\n      />\n\n      <SessionForm\n        visible={createModalVisible}\n        onVisibleChange={setCreateModalVisible}\n        onSuccess={() => {\n          actionRef.current?.reload();\n          setCreateModalVisible(false);\n        }}\n      />\n\n      <SessionForm\n        visible={editModalVisible}\n        onVisibleChange={setEditModalVisible}\n        initialValues={currentRecord}\n        onSuccess={() => {\n          actionRef.current?.reload();\n          setEditModalVisible(false);\n        }}\n      />\n    </PageContainer>\n  );\n};\n\nexport default ChatSessionsPage;\n", "__isJSFile": true, "__absFile": "D:/rwx-ai/RwxAI.Web/src/pages/chat/index.tsx"}, "10": {"path": "/chat/session/:id", "name": "session-detail", "hideInMenu": true, "file": "@/pages/chat/session/[id].tsx", "parentId": "7", "id": "10", "absPath": "/chat/session/:id", "__content": "import React, { useState, useEffect, useRef } from 'react';\nimport { PageContainer } from '@ant-design/pro-components';\nimport { Card, Input, Button, List, Avatar, Typography, Space, message, Spin } from 'antd';\nimport { SendOutlined, UserOutlined, RobotOutlined } from '@ant-design/icons';\nimport { useParams, useIntl, FormattedMessage } from '@umijs/max';\nimport { getChatSessionById, sendMessage } from '@/services/rwxai';\n\nconst { TextArea } = Input;\nconst { Text } = Typography;\n\nconst ChatSessionPage: React.FC = () => {\n  const intl = useIntl();\n  const { id } = useParams<{ id: string }>();\n  const [session, setSession] = useState<RwxAI.ChatSession>();\n  const [messages, setMessages] = useState<RwxAI.ChatMessage[]>([]);\n  const [inputValue, setInputValue] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [sessionLoading, setSessionLoading] = useState(true);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    if (id) {\n      loadSession();\n    }\n  }, [id]);\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  const loadSession = async () => {\n    try {\n      setSessionLoading(true);\n      const data = await getChatSessionById(id!);\n      setSession(data);\n      setMessages(data.Messages || []);\n    } catch (error) {\n      message.error(intl.formatMessage({ id: 'pages.chat.session.load.error' }));\n    } finally {\n      setSessionLoading(false);\n    }\n  };\n\n  const handleSendMessage = async () => {\n    if (!inputValue.trim() || !session) return;\n\n    const userMessage: RwxAI.ChatMessage = {\n      Id: `temp-${Date.now()}`,\n      Role: 'user',\n      Content: inputValue,\n      CreatedTime: new Date().toISOString(),\n      SessionId: session.Id,\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    setInputValue('');\n    setLoading(true);\n\n    try {\n      const response = await sendMessage(session.Id, {\n        Content: inputValue,\n        ModelId: session.ModelId,\n        Temperature: session.Temperature,\n        MaxTokens: session.MaxTokens,\n      });\n\n      setMessages(prev => [...prev, response]);\n    } catch (error) {\n      message.error(intl.formatMessage({ id: 'pages.chat.session.send.error' }));\n      // 移除临时用户消息\n      setMessages(prev => prev.filter(msg => msg.Id !== userMessage.Id));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  const renderMessage = (message: RwxAI.ChatMessage) => {\n    const isUser = message.Role === 'user';\n    return (\n      <List.Item\n        key={message.Id}\n        style={{\n          padding: '12px 0',\n          justifyContent: isUser ? 'flex-end' : 'flex-start',\n        }}\n      >\n        <div\n          style={{\n            display: 'flex',\n            flexDirection: isUser ? 'row-reverse' : 'row',\n            alignItems: 'flex-start',\n            maxWidth: '80%',\n          }}\n        >\n          <Avatar\n            icon={isUser ? <UserOutlined /> : <RobotOutlined />}\n            style={{\n              backgroundColor: isUser ? '#1890ff' : '#52c41a',\n              margin: isUser ? '0 0 0 8px' : '0 8px 0 0',\n            }}\n          />\n          <div\n            style={{\n              backgroundColor: isUser ? '#e6f7ff' : '#f6ffed',\n              padding: '8px 12px',\n              borderRadius: '8px',\n              border: `1px solid ${isUser ? '#91d5ff' : '#b7eb8f'}`,\n            }}\n          >\n            <Text>{message.Content}</Text>\n            <div style={{ marginTop: '4px', fontSize: '12px', color: '#999' }}>\n              {message.CreatedTime ? new Date(message.CreatedTime).toLocaleTimeString() : ''}\n            </div>\n          </div>\n        </div>\n      </List.Item>\n    );\n  };\n\n  if (sessionLoading) {\n    return (\n      <PageContainer>\n        <div style={{ textAlign: 'center', padding: '50px' }}>\n          <Spin size=\"large\" />\n        </div>\n      </PageContainer>\n    );\n  }\n\n  return (\n    <PageContainer\n      title={session?.Name}\n      subTitle={session?.Model?.Name}\n    >\n      <Card\n        style={{ height: 'calc(100vh - 200px)', display: 'flex', flexDirection: 'column' }}\n        bodyStyle={{ flex: 1, display: 'flex', flexDirection: 'column', padding: 0 }}\n      >\n        <div\n          style={{\n            flex: 1,\n            overflow: 'auto',\n            padding: '16px',\n            backgroundColor: '#fafafa',\n          }}\n        >\n          <List\n            dataSource={messages}\n            renderItem={renderMessage}\n            style={{ backgroundColor: 'transparent' }}\n          />\n          <div ref={messagesEndRef} />\n        </div>\n        \n        <div style={{ padding: '16px', borderTop: '1px solid #f0f0f0' }}>\n          <Space.Compact style={{ width: '100%' }}>\n            <TextArea\n              value={inputValue}\n              onChange={(e) => setInputValue(e.target.value)}\n              onKeyPress={handleKeyPress}\n              placeholder={intl.formatMessage({ id: 'pages.chat.session.input.placeholder' })}\n              autoSize={{ minRows: 1, maxRows: 4 }}\n              disabled={loading}\n            />\n            <Button\n              type=\"primary\"\n              icon={<SendOutlined />}\n              onClick={handleSendMessage}\n              loading={loading}\n              disabled={!inputValue.trim()}\n            >\n              <FormattedMessage id=\"pages.chat.session.send\" />\n            </Button>\n          </Space.Compact>\n        </div>\n      </Card>\n    </PageContainer>\n  );\n};\n\nexport default ChatSessionPage;\n", "__isJSFile": true, "__absFile": "D:/rwx-ai/RwxAI.Web/src/pages/chat/session/[id].tsx"}, "11": {"path": "/knowledge", "name": "knowledge", "icon": "book", "parentId": "ant-design-pro-layout", "id": "11", "absPath": "/knowledge"}, "12": {"path": "/knowledge", "file": "@/pages/knowledge/index.tsx", "parentId": "11", "id": "12", "absPath": "/knowledge", "__content": "import React, { useRef, useState } from 'react';\nimport { PageContainer } from '@ant-design/pro-components';\nimport { ProTable, ActionType, ProColumns } from '@ant-design/pro-table';\nimport { Button, Space, message, Modal } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, FileOutlined, EyeOutlined } from '@ant-design/icons';\nimport { useIntl, FormattedMessage, history } from '@umijs/max';\nimport { getKnowledgeBases, deleteKnowledgeBase } from '@/services/rwxai';\nimport KnowledgeForm from './components/KnowledgeForm';\nimport KnowledgeDetail from './components/KnowledgeDetail';\n\nconst KnowledgePage: React.FC = () => {\n  const intl = useIntl();\n  const actionRef = useRef<ActionType>();\n  const [createModalVisible, setCreateModalVisible] = useState(false);\n  const [editModalVisible, setEditModalVisible] = useState(false);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [currentRecord, setCurrentRecord] = useState<RwxAI.Knowledge>();\n\n  const handleDelete = async (record: RwxAI.Knowledge) => {\n    Modal.confirm({\n      title: intl.formatMessage({ id: 'pages.knowledge.delete.confirm.title' }),\n      content: intl.formatMessage({ id: 'pages.knowledge.delete.confirm.content' }),\n      onOk: async () => {\n        try {\n          await deleteKnowledgeBase(record.Id);\n          message.success(intl.formatMessage({ id: 'pages.knowledge.delete.success' }));\n          actionRef.current?.reload();\n        } catch (error) {\n          message.error(intl.formatMessage({ id: 'pages.knowledge.delete.error' }));\n        }\n      },\n    });\n  };\n\n  const handleManageFiles = (record: RwxAI.Knowledge) => {\n    history.push(`/knowledge/${record.Id}/files`);\n  };\n\n  const columns: ProColumns<RwxAI.Knowledge>[] = [\n    {\n      title: <FormattedMessage id=\"pages.knowledge.table.name\" />,\n      dataIndex: 'Name',\n      key: 'Name',\n      ellipsis: true,\n    },\n    {\n      title: <FormattedMessage id=\"pages.knowledge.table.description\" />,\n      dataIndex: 'Description',\n      key: 'Description',\n      ellipsis: true,\n      hideInSearch: true,\n    },\n    {\n      title: <FormattedMessage id=\"pages.knowledge.table.chunkSize\" />,\n      dataIndex: 'ChunkSize',\n      key: 'ChunkSize',\n      hideInSearch: true,\n      width: 120,\n    },\n    {\n      title: <FormattedMessage id=\"pages.knowledge.table.chunkOverlap\" />,\n      dataIndex: 'ChunkOverlap',\n      key: 'ChunkOverlap',\n      hideInSearch: true,\n      width: 120,\n    },\n    {\n      title: <FormattedMessage id=\"pages.knowledge.table.createdTime\" />,\n      dataIndex: 'CreatedTime',\n      key: 'CreatedTime',\n      valueType: 'dateTime',\n      width: 180,\n      hideInSearch: true,\n    },\n    {\n      title: <FormattedMessage id=\"pages.knowledge.table.actions\" />,\n      key: 'actions',\n      width: 250,\n      render: (_, record) => (\n        <Space>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EyeOutlined />}\n            onClick={() => {\n              setCurrentRecord(record);\n              setDetailModalVisible(true);\n            }}\n          >\n            <FormattedMessage id=\"pages.knowledge.actions.view\" />\n          </Button>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<FileOutlined />}\n            onClick={() => handleManageFiles(record)}\n          >\n            <FormattedMessage id=\"pages.knowledge.actions.files\" />\n          </Button>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EditOutlined />}\n            onClick={() => {\n              setCurrentRecord(record);\n              setEditModalVisible(true);\n            }}\n          >\n            <FormattedMessage id=\"pages.knowledge.actions.edit\" />\n          </Button>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            danger\n            icon={<DeleteOutlined />}\n            onClick={() => handleDelete(record)}\n          >\n            <FormattedMessage id=\"pages.knowledge.actions.delete\" />\n          </Button>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <PageContainer>\n      <ProTable<RwxAI.Knowledge>\n        headerTitle={intl.formatMessage({ id: 'pages.knowledge.title' })}\n        actionRef={actionRef}\n        rowKey=\"Id\"\n        search={{\n          labelWidth: 120,\n        }}\n        toolBarRender={() => [\n          <Button\n            type=\"primary\"\n            key=\"primary\"\n            icon={<PlusOutlined />}\n            onClick={() => setCreateModalVisible(true)}\n          >\n            <FormattedMessage id=\"pages.knowledge.actions.create\" />\n          </Button>,\n        ]}\n        request={async () => {\n          const response = await getKnowledgeBases();\n          return {\n            data: response.success ? (response.data || []) : [],\n            success: response.success,\n          };\n        }}\n        columns={columns}\n      />\n\n      <KnowledgeForm\n        visible={createModalVisible}\n        onVisibleChange={setCreateModalVisible}\n        onSuccess={() => {\n          actionRef.current?.reload();\n          setCreateModalVisible(false);\n        }}\n      />\n\n      <KnowledgeForm\n        visible={editModalVisible}\n        onVisibleChange={setEditModalVisible}\n        initialValues={currentRecord}\n        onSuccess={() => {\n          actionRef.current?.reload();\n          setEditModalVisible(false);\n        }}\n      />\n\n      <KnowledgeDetail\n        visible={detailModalVisible}\n        onVisibleChange={setDetailModalVisible}\n        data={currentRecord}\n      />\n    </PageContainer>\n  );\n};\n\nexport default KnowledgePage;\n", "__isJSFile": true, "__absFile": "D:/rwx-ai/RwxAI.Web/src/pages/knowledge/index.tsx"}, "13": {"path": "/knowledge/:id/files", "name": "knowledge-files", "hideInMenu": true, "file": "@/pages/knowledge/[id]/files.tsx", "parentId": "11", "id": "13", "absPath": "/knowledge/:id/files", "__content": "import React, { useRef, useState, useEffect } from 'react';\nimport { <PERSON>Container } from '@ant-design/pro-components';\nimport { ProTable, ActionType, ProColumns } from '@ant-design/pro-table';\nimport { Button, Space, message, Modal, Upload, Tag, Progress } from 'antd';\nimport { UploadOutlined, DeleteOutlined, PlayCircleOutlined, FileOutlined } from '@ant-design/icons';\nimport { useParams, useIntl, FormattedMessage } from '@umijs/max';\nimport { getKnowledgeFiles, uploadKnowledgeFile, deleteKnowledgeFile, processKnowledgeFile, getKnowledgeBaseById } from '@/services/rwxai';\n\nconst KnowledgeFilesPage: React.FC = () => {\n  const intl = useIntl();\n  const { id } = useParams<{ id: string }>();\n  const actionRef = useRef<ActionType>();\n  const [knowledgeBase, setKnowledgeBase] = useState<RwxAI.Knowledge>();\n  const [uploadModalVisible, setUploadModalVisible] = useState(false);\n  const [uploading, setUploading] = useState(false);\n\n  useEffect(() => {\n    if (id) {\n      loadKnowledgeBase();\n    }\n  }, [id]);\n\n  const loadKnowledgeBase = async () => {\n    try {\n      const data = await getKnowledgeBaseById(id!);\n      setKnowledgeBase(data);\n    } catch (error) {\n      message.error(intl.formatMessage({ id: 'pages.knowledge.files.loadKnowledge.error' }));\n    }\n  };\n\n  const handleDelete = async (record: RwxAI.KnowledgeFile) => {\n    Modal.confirm({\n      title: intl.formatMessage({ id: 'pages.knowledge.files.delete.confirm.title' }),\n      content: intl.formatMessage({ id: 'pages.knowledge.files.delete.confirm.content' }),\n      onOk: async () => {\n        try {\n          await deleteKnowledgeFile(record.Id);\n          message.success(intl.formatMessage({ id: 'pages.knowledge.files.delete.success' }));\n          actionRef.current?.reload();\n        } catch (error) {\n          message.error(intl.formatMessage({ id: 'pages.knowledge.files.delete.error' }));\n        }\n      },\n    });\n  };\n\n  const handleProcess = async (record: RwxAI.KnowledgeFile) => {\n    try {\n      await processKnowledgeFile(record.Id);\n      message.success(intl.formatMessage({ id: 'pages.knowledge.files.process.success' }));\n      actionRef.current?.reload();\n    } catch (error) {\n      message.error(intl.formatMessage({ id: 'pages.knowledge.files.process.error' }));\n    }\n  };\n\n  const handleUpload = async (file: File) => {\n    try {\n      setUploading(true);\n      await uploadKnowledgeFile(id!, file);\n      message.success(intl.formatMessage({ id: 'pages.knowledge.files.upload.success' }));\n      setUploadModalVisible(false);\n      actionRef.current?.reload();\n    } catch (error) {\n      message.error(intl.formatMessage({ id: 'pages.knowledge.files.upload.error' }));\n    } finally {\n      setUploading(false);\n    }\n  };\n\n  const formatFileSize = (bytes: number) => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  const columns: ProColumns<RwxAI.KnowledgeFile>[] = [\n    {\n      title: <FormattedMessage id=\"pages.knowledge.files.table.fileName\" />,\n      dataIndex: 'FileName',\n      key: 'FileName',\n      ellipsis: true,\n      render: (_, record) => (\n        <Space>\n          <FileOutlined />\n          {record.FileName}\n        </Space>\n      ),\n    },\n    {\n      title: <FormattedMessage id=\"pages.knowledge.files.table.contentType\" />,\n      dataIndex: 'ContentType',\n      key: 'ContentType',\n      hideInSearch: true,\n    },\n    {\n      title: <FormattedMessage id=\"pages.knowledge.files.table.fileSize\" />,\n      dataIndex: 'FileSize',\n      key: 'FileSize',\n      hideInSearch: true,\n      render: (_, record) => formatFileSize(record.FileSize),\n    },\n    {\n      title: <FormattedMessage id=\"pages.knowledge.files.table.status\" />,\n      dataIndex: 'IsProcessed',\n      key: 'IsProcessed',\n      render: (_, record) => (\n        <Tag color={record.IsProcessed ? 'green' : 'orange'}>\n          {record.IsProcessed ? \n            intl.formatMessage({ id: 'pages.knowledge.files.status.processed' }) : \n            intl.formatMessage({ id: 'pages.knowledge.files.status.pending' })\n          }\n        </Tag>\n      ),\n    },\n    {\n      title: <FormattedMessage id=\"pages.knowledge.files.table.processedTime\" />,\n      dataIndex: 'ProcessedTime',\n      key: 'ProcessedTime',\n      valueType: 'dateTime',\n      hideInSearch: true,\n      render: (_, record) => record.ProcessedTime ? new Date(record.ProcessedTime).toLocaleString() : '-',\n    },\n    {\n      title: <FormattedMessage id=\"pages.knowledge.files.table.createdTime\" />,\n      dataIndex: 'CreatedTime',\n      key: 'CreatedTime',\n      valueType: 'dateTime',\n      width: 180,\n      hideInSearch: true,\n    },\n    {\n      title: <FormattedMessage id=\"pages.knowledge.files.table.actions\" />,\n      key: 'actions',\n      width: 200,\n      render: (_, record) => (\n        <Space>\n          {!record.IsProcessed && (\n            <Button\n              type=\"link\"\n              size=\"small\"\n              icon={<PlayCircleOutlined />}\n              onClick={() => handleProcess(record)}\n            >\n              <FormattedMessage id=\"pages.knowledge.files.actions.process\" />\n            </Button>\n          )}\n          <Button\n            type=\"link\"\n            size=\"small\"\n            danger\n            icon={<DeleteOutlined />}\n            onClick={() => handleDelete(record)}\n          >\n            <FormattedMessage id=\"pages.knowledge.files.actions.delete\" />\n          </Button>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <PageContainer\n      title={knowledgeBase?.Name}\n      subTitle={intl.formatMessage({ id: 'pages.knowledge.files.subtitle' })}\n    >\n      <ProTable<RwxAI.KnowledgeFile>\n        headerTitle={intl.formatMessage({ id: 'pages.knowledge.files.title' })}\n        actionRef={actionRef}\n        rowKey=\"Id\"\n        search={{\n          labelWidth: 120,\n        }}\n        toolBarRender={() => [\n          <Button\n            type=\"primary\"\n            key=\"primary\"\n            icon={<UploadOutlined />}\n            onClick={() => setUploadModalVisible(true)}\n          >\n            <FormattedMessage id=\"pages.knowledge.files.actions.upload\" />\n          </Button>,\n        ]}\n        request={async () => {\n          try {\n            const data = await getKnowledgeFiles(id!);\n            return {\n              data: data || [],\n              success: true,\n            };\n          } catch (error) {\n            return {\n              data: [],\n              success: false,\n            };\n          }\n        }}\n        columns={columns}\n      />\n\n      <Modal\n        title={intl.formatMessage({ id: 'pages.knowledge.files.upload.title' })}\n        open={uploadModalVisible}\n        onCancel={() => setUploadModalVisible(false)}\n        footer={null}\n      >\n        <Upload.Dragger\n          name=\"file\"\n          multiple={false}\n          beforeUpload={(file) => {\n            handleUpload(file);\n            return false;\n          }}\n          disabled={uploading}\n        >\n          <p className=\"ant-upload-drag-icon\">\n            <UploadOutlined />\n          </p>\n          <p className=\"ant-upload-text\">\n            <FormattedMessage id=\"pages.knowledge.files.upload.dragText\" />\n          </p>\n          <p className=\"ant-upload-hint\">\n            <FormattedMessage id=\"pages.knowledge.files.upload.hint\" />\n          </p>\n        </Upload.Dragger>\n        {uploading && (\n          <div style={{ marginTop: 16 }}>\n            <Progress percent={100} status=\"active\" />\n          </div>\n        )}\n      </Modal>\n    </PageContainer>\n  );\n};\n\nexport default KnowledgeFilesPage;\n", "__isJSFile": true, "__absFile": "D:/rwx-ai/RwxAI.Web/src/pages/knowledge/[id]/files.tsx"}, "14": {"path": "/plugins", "name": "plugins", "icon": "api", "file": "@/pages/plugins/index.tsx", "parentId": "ant-design-pro-layout", "id": "14", "absPath": "/plugins", "__content": "import React, { useRef, useState } from 'react';\nimport { PageContainer } from '@ant-design/pro-components';\nimport { ProTable, ActionType, ProColumns } from '@ant-design/pro-table';\nimport { Button, Space, Tag, message, Modal, Switch } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, FunctionOutlined } from '@ant-design/icons';\nimport { useIntl, FormattedMessage, history } from '@umijs/max';\nimport { getPlugins, deletePlugin, togglePluginStatus } from '@/services/rwxai';\nimport PluginForm from './components/PluginForm';\nimport PluginDetail from './components/PluginDetail';\n\nconst PluginsPage: React.FC = () => {\n  const intl = useIntl();\n  const actionRef = useRef<ActionType>();\n  const [createModalVisible, setCreateModalVisible] = useState(false);\n  const [editModalVisible, setEditModalVisible] = useState(false);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [currentRecord, setCurrentRecord] = useState<RwxAI.Plugin>();\n\n  const handleDelete = async (record: RwxAI.Plugin) => {\n    Modal.confirm({\n      title: intl.formatMessage({ id: 'pages.plugins.delete.confirm.title' }),\n      content: intl.formatMessage({ id: 'pages.plugins.delete.confirm.content' }),\n      onOk: async () => {\n        try {\n          await deletePlugin(record.Id);\n          message.success(intl.formatMessage({ id: 'pages.plugins.delete.success' }));\n          actionRef.current?.reload();\n        } catch (error) {\n          message.error(intl.formatMessage({ id: 'pages.plugins.delete.error' }));\n        }\n      },\n    });\n  };\n\n  const handleStatusChange = async (record: RwxAI.Plugin, checked: boolean) => {\n    try {\n      await togglePluginStatus(record.Id, checked);\n      message.success(intl.formatMessage({ id: 'pages.plugins.status.update.success' }));\n      actionRef.current?.reload();\n    } catch (error) {\n      message.error(intl.formatMessage({ id: 'pages.plugins.status.update.error' }));\n    }\n  };\n\n  const handleManageFunctions = (record: RwxAI.Plugin) => {\n    history.push(`/plugins/${record.Id}/functions`);\n  };\n\n  const getStatusTag = (status?: string) => {\n    const statusMap: Record<string, { color: string; text: string }> = {\n      'Active': { color: 'green', text: intl.formatMessage({ id: 'pages.plugins.status.active' }) },\n      'Inactive': { color: 'default', text: intl.formatMessage({ id: 'pages.plugins.status.inactive' }) },\n      'Error': { color: 'red', text: intl.formatMessage({ id: 'pages.plugins.status.error' }) },\n    };\n    const statusInfo = statusMap[status || 'Inactive'];\n    return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;\n  };\n\n  const columns: ProColumns<RwxAI.Plugin>[] = [\n    {\n      title: <FormattedMessage id=\"pages.plugins.table.name\" />,\n      dataIndex: 'Name',\n      key: 'Name',\n      ellipsis: true,\n    },\n    {\n      title: <FormattedMessage id=\"pages.plugins.table.description\" />,\n      dataIndex: 'Description',\n      key: 'Description',\n      ellipsis: true,\n      hideInSearch: true,\n    },\n    {\n      title: <FormattedMessage id=\"pages.plugins.table.version\" />,\n      dataIndex: 'Version',\n      key: 'Version',\n      hideInSearch: true,\n      width: 100,\n    },\n    {\n      title: <FormattedMessage id=\"pages.plugins.table.author\" />,\n      dataIndex: 'Author',\n      key: 'Author',\n      hideInSearch: true,\n      width: 120,\n    },\n    {\n      title: <FormattedMessage id=\"pages.plugins.table.status\" />,\n      dataIndex: 'Status',\n      key: 'Status',\n      render: (_, record) => getStatusTag(record.Status),\n    },\n    {\n      title: <FormattedMessage id=\"pages.plugins.table.enabled\" />,\n      dataIndex: 'IsEnabled',\n      key: 'IsEnabled',\n      render: (_, record) => (\n        <Switch\n          checked={record.IsEnabled}\n          size=\"small\"\n          onChange={(checked) => handleStatusChange(record, checked)}\n        />\n      ),\n    },\n    {\n      title: <FormattedMessage id=\"pages.plugins.table.functions\" />,\n      key: 'functions',\n      hideInSearch: true,\n      render: (_, record) => record.Functions?.length || 0,\n    },\n    {\n      title: <FormattedMessage id=\"pages.plugins.table.createdTime\" />,\n      dataIndex: 'CreatedTime',\n      key: 'CreatedTime',\n      valueType: 'dateTime',\n      width: 180,\n      hideInSearch: true,\n    },\n    {\n      title: <FormattedMessage id=\"pages.plugins.table.actions\" />,\n      key: 'actions',\n      width: 250,\n      render: (_, record) => (\n        <Space>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EyeOutlined />}\n            onClick={() => {\n              setCurrentRecord(record);\n              setDetailModalVisible(true);\n            }}\n          >\n            <FormattedMessage id=\"pages.plugins.actions.view\" />\n          </Button>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<FunctionOutlined />}\n            onClick={() => handleManageFunctions(record)}\n          >\n            <FormattedMessage id=\"pages.plugins.actions.functions\" />\n          </Button>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EditOutlined />}\n            onClick={() => {\n              setCurrentRecord(record);\n              setEditModalVisible(true);\n            }}\n          >\n            <FormattedMessage id=\"pages.plugins.actions.edit\" />\n          </Button>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            danger\n            icon={<DeleteOutlined />}\n            onClick={() => handleDelete(record)}\n          >\n            <FormattedMessage id=\"pages.plugins.actions.delete\" />\n          </Button>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <PageContainer>\n      <ProTable<RwxAI.Plugin>\n        headerTitle={intl.formatMessage({ id: 'pages.plugins.title' })}\n        actionRef={actionRef}\n        rowKey=\"Id\"\n        search={{\n          labelWidth: 120,\n        }}\n        toolBarRender={() => [\n          <Button\n            type=\"primary\"\n            key=\"primary\"\n            icon={<PlusOutlined />}\n            onClick={() => setCreateModalVisible(true)}\n          >\n            <FormattedMessage id=\"pages.plugins.actions.create\" />\n          </Button>,\n        ]}\n        request={async () => {\n          const response = await getPlugins();\n          return {\n            data: response.success ? (response.data || []) : [],\n            success: response.success,\n          };\n        }}\n        columns={columns}\n      />\n\n      <PluginForm\n        visible={createModalVisible}\n        onVisibleChange={setCreateModalVisible}\n        onSuccess={() => {\n          actionRef.current?.reload();\n          setCreateModalVisible(false);\n        }}\n      />\n\n      <PluginForm\n        visible={editModalVisible}\n        onVisibleChange={setEditModalVisible}\n        initialValues={currentRecord}\n        onSuccess={() => {\n          actionRef.current?.reload();\n          setEditModalVisible(false);\n        }}\n      />\n\n      <PluginDetail\n        visible={detailModalVisible}\n        onVisibleChange={setDetailModalVisible}\n        data={currentRecord}\n      />\n    </PageContainer>\n  );\n};\n\nexport default PluginsPage;\n", "__isJSFile": true, "__absFile": "D:/rwx-ai/RwxAI.Web/src/pages/plugins/index.tsx"}, "15": {"path": "/auth-test", "name": "auth-test", "icon": "safety", "file": "@/pages/auth-test/index.tsx", "parentId": "ant-design-pro-layout", "id": "15", "absPath": "/auth-test", "__content": "import React, { useState, useEffect } from 'react';\nimport { PageContainer } from '@ant-design/pro-components';\nimport { Card, Button, Space, Typography, Descriptions, message } from 'antd';\nimport { getToken, getUserInfo, isLoggedIn, logout } from '@/utils/auth';\nimport { getAIModels } from '@/services/rwxai';\n\nconst { Title, Text, Paragraph } = Typography;\n\nconst AuthTestPage: React.FC = () => {\n  const [tokenInfo, setTokenInfo] = useState<any>(null);\n  const [userInfo, setUserInfo] = useState<any>(null);\n  const [apiTestResult, setApiTestResult] = useState<string>('');\n  const [loading, setLoading] = useState(false);\n\n  useEffect(() => {\n    loadAuthInfo();\n  }, []);\n\n  const loadAuthInfo = () => {\n    const token = getToken();\n    const user = getUserInfo();\n    \n    setTokenInfo({\n      token: token ? `${token.substring(0, 20)}...` : null,\n      isLoggedIn: isLoggedIn(),\n      fullToken: token,\n    });\n    \n    setUserInfo(user);\n  };\n\n  const testApiCall = async () => {\n    setLoading(true);\n    try {\n      const response = await getAIModels();\n      if (response.success) {\n        setApiTestResult(`✅ 成功获取 ${response.data?.length || 0} 个AI模型`);\n      } else {\n        setApiTestResult(`❌ API调用失败: ${response.message}`);\n      }\n    } catch (error: any) {\n      setApiTestResult(`❌ 异常: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleLogout = () => {\n    logout();\n    loadAuthInfo();\n    message.info('已退出登录');\n  };\n\n  return (\n    <PageContainer title=\"JWT认证测试页面\">\n      <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n        <Card title=\"认证状态\">\n          <Descriptions column={1} bordered>\n            <Descriptions.Item label=\"登录状态\">\n              <Text type={tokenInfo?.isLoggedIn ? 'success' : 'danger'}>\n                {tokenInfo?.isLoggedIn ? '已登录' : '未登录'}\n              </Text>\n            </Descriptions.Item>\n            <Descriptions.Item label=\"JWT令牌\">\n              <Text code>{tokenInfo?.token || '无'}</Text>\n            </Descriptions.Item>\n            <Descriptions.Item label=\"用户信息\">\n              {userInfo ? (\n                <div>\n                  <div>用户名: {userInfo.username || userInfo.name}</div>\n                  <div>邮箱: {userInfo.email}</div>\n                  <div>姓名: {userInfo.firstName} {userInfo.lastName}</div>\n                </div>\n              ) : (\n                <Text type=\"secondary\">无用户信息</Text>\n              )}\n            </Descriptions.Item>\n          </Descriptions>\n        </Card>\n\n        <Card title=\"API测试\">\n          <Space direction=\"vertical\" style={{ width: '100%' }}>\n            <Paragraph>\n              点击下面的按钮测试带JWT令牌的API调用：\n            </Paragraph>\n            <Space>\n              <Button \n                type=\"primary\" \n                onClick={testApiCall} \n                loading={loading}\n                disabled={!tokenInfo?.isLoggedIn}\n              >\n                测试获取AI模型列表\n              </Button>\n              <Button onClick={loadAuthInfo}>\n                刷新认证信息\n              </Button>\n              <Button danger onClick={handleLogout} disabled={!tokenInfo?.isLoggedIn}>\n                退出登录\n              </Button>\n            </Space>\n            {apiTestResult && (\n              <Card size=\"small\" style={{ marginTop: 16 }}>\n                <Text>{apiTestResult}</Text>\n              </Card>\n            )}\n          </Space>\n        </Card>\n\n        <Card title=\"JWT令牌详情\" size=\"small\">\n          <Paragraph>\n            <Text code style={{ wordBreak: 'break-all', fontSize: '12px' }}>\n              {tokenInfo?.fullToken || '无令牌'}\n            </Text>\n          </Paragraph>\n        </Card>\n\n        <Card title=\"使用说明\" size=\"small\">\n          <Paragraph>\n            <Title level={5}>测试步骤：</Title>\n            <ol>\n              <li>首先访问 <Text code>/user/login</Text> 页面进行登录</li>\n              <li>登录成功后会自动保存JWT令牌到localStorage</li>\n              <li>返回此页面查看认证状态</li>\n              <li>点击\"测试获取AI模型列表\"按钮验证API调用</li>\n              <li>所有后续的API请求都会自动携带JWT令牌</li>\n            </ol>\n          </Paragraph>\n        </Card>\n      </Space>\n    </PageContainer>\n  );\n};\n\nexport default AuthTestPage;\n", "__isJSFile": true, "__absFile": "D:/rwx-ai/RwxAI.Web/src/pages/auth-test/index.tsx"}, "16": {"path": "/response-demo", "name": "response-demo", "icon": "experiment", "file": "@/pages/response-demo/index.tsx", "parentId": "ant-design-pro-layout", "id": "16", "absPath": "/response-demo", "__content": "import React, { useState } from 'react';\nimport { PageContainer } from '@ant-design/pro-components';\nimport { Card, Button, Space, Typography, Alert, Divider, Input, Form } from 'antd';\nimport { aiModelsApi } from '@/services/rwxai/enhanced-aiModels';\nimport { httpRequest } from '@/utils/request';\nimport { getApiPrefix } from '@/utils/api';\n\nconst { Title, Paragraph, Text } = Typography;\n\nconst ResponseDemoPage: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [result, setResult] = useState<string>('');\n  const [form] = Form.useForm();\n\n  // 测试成功响应 (200)\n  const testSuccessResponse = async () => {\n    setLoading(true);\n    try {\n      const response = await aiModelsApi.getAIModels();\n      if (response.success) {\n        setResult(`✅ 成功获取 ${response.data?.length || 0} 个AI模型`);\n      } else {\n        setResult(`❌ 请求失败: ${response.message}`);\n      }\n    } catch (error) {\n      setResult(`❌ 异常: ${error}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 测试400错误 - 请求参数错误\n  const test400Error = async () => {\n    setLoading(true);\n    try {\n      const response = await httpRequest.post(`${getApiPrefix()}/AIModels`, {\n        // 故意发送无效数据\n        invalidField: 'invalid value',\n      });\n      setResult(`结果: ${response.success ? '成功' : response.message}`);\n    } catch (error) {\n      setResult(`异常: ${error}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 测试401错误 - 未授权\n  const test401Error = async () => {\n    setLoading(true);\n    try {\n      const response = await httpRequest.get(`${getApiPrefix()}/AIModels`, {\n        skipAuth: true, // 跳过认证\n      });\n      setResult(`结果: ${response.success ? '成功' : response.message}`);\n    } catch (error) {\n      setResult(`异常: ${error}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 测试403错误 - 权限不足\n  const test403Error = async () => {\n    setLoading(true);\n    try {\n      const response = await httpRequest.get(`${getApiPrefix()}/admin/system-settings`);\n      setResult(`结果: ${response.success ? '成功' : response.message}`);\n    } catch (error) {\n      setResult(`异常: ${error}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 测试404错误 - 资源不存在\n  const test404Error = async () => {\n    setLoading(true);\n    try {\n      const response = await aiModelsApi.getAIModelById('non-existent-id');\n      setResult(`结果: ${response.success ? '成功' : response.message}`);\n    } catch (error) {\n      setResult(`异常: ${error}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 测试422错误 - 数据验证失败\n  const test422Error = async () => {\n    setLoading(true);\n    try {\n      const response = await aiModelsApi.createAIModel({\n        name: '', // 空名称应该会触发验证错误\n        modelKey: '',\n        modelType: 'Chat',\n        provider: '',\n      } as any);\n      setResult(`结果: ${response.success ? '成功' : response.message}`);\n    } catch (error) {\n      setResult(`异常: ${error}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 测试500错误 - 服务器内部错误\n  const test500Error = async () => {\n    setLoading(true);\n    try {\n      const response = await httpRequest.get(`${getApiPrefix()}/test/server-error`);\n      setResult(`结果: ${response.success ? '成功' : response.message}`);\n    } catch (error) {\n      setResult(`异常: ${error}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 测试网络错误\n  const testNetworkError = async () => {\n    setLoading(true);\n    try {\n      const response = await httpRequest.get('http://invalid-domain.com/api/test', {\n        timeout: 3000,\n      });\n      setResult(`结果: ${response.success ? '成功' : response.message}`);\n    } catch (error) {\n      setResult(`异常: ${error}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 自定义API测试\n  const testCustomApi = async (values: any) => {\n    setLoading(true);\n    try {\n      const { url, method } = values;\n      const response = await httpRequest[method.toLowerCase()](url);\n      setResult(`结果: ${response.success ? '成功' : response.message}`);\n    } catch (error) {\n      setResult(`异常: ${error}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <PageContainer title=\"响应处理系统演示\">\n      <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n        <Alert\n          message=\"响应处理系统说明\"\n          description=\"此页面演示统一的API响应处理系统，包括各种HTTP状态码的处理和用户友好的错误提示。\"\n          type=\"info\"\n          showIcon\n        />\n\n        <Card title=\"成功响应测试\">\n          <Space wrap>\n            <Button type=\"primary\" onClick={testSuccessResponse} loading={loading}>\n              测试200 - 获取AI模型列表\n            </Button>\n          </Space>\n        </Card>\n\n        <Card title=\"客户端错误测试 (4xx)\">\n          <Space wrap>\n            <Button onClick={test400Error} loading={loading}>\n              测试400 - 请求参数错误\n            </Button>\n            <Button onClick={test401Error} loading={loading}>\n              测试401 - 未授权\n            </Button>\n            <Button onClick={test403Error} loading={loading}>\n              测试403 - 权限不足\n            </Button>\n            <Button onClick={test404Error} loading={loading}>\n              测试404 - 资源不存在\n            </Button>\n            <Button onClick={test422Error} loading={loading}>\n              测试422 - 数据验证失败\n            </Button>\n          </Space>\n        </Card>\n\n        <Card title=\"服务器错误测试 (5xx)\">\n          <Space wrap>\n            <Button onClick={test500Error} loading={loading}>\n              测试500 - 服务器内部错误\n            </Button>\n          </Space>\n        </Card>\n\n        <Card title=\"网络错误测试\">\n          <Space wrap>\n            <Button onClick={testNetworkError} loading={loading}>\n              测试网络连接错误\n            </Button>\n          </Space>\n        </Card>\n\n        <Card title=\"自定义API测试\">\n          <Form form={form} onFinish={testCustomApi} layout=\"inline\">\n            <Form.Item name=\"url\" label=\"API地址\" initialValue={`${getApiPrefix()}/AIModels`}>\n              <Input style={{ width: 300 }} />\n            </Form.Item>\n            <Form.Item name=\"method\" label=\"请求方法\" initialValue=\"GET\">\n              <Input style={{ width: 100 }} />\n            </Form.Item>\n            <Form.Item>\n              <Button type=\"primary\" htmlType=\"submit\" loading={loading}>\n                发送请求\n              </Button>\n            </Form.Item>\n          </Form>\n        </Card>\n\n        {result && (\n          <Card title=\"测试结果\" size=\"small\">\n            <Text code>{result}</Text>\n          </Card>\n        )}\n\n        <Card title=\"响应处理特性\" size=\"small\">\n          <Paragraph>\n            <Title level={5}>自动处理的功能：</Title>\n            <ul>\n              <li>✅ 自动添加JWT令牌到请求头</li>\n              <li>✅ 401错误自动刷新令牌并重试</li>\n              <li>✅ 根据错误类型显示不同的提示方式</li>\n              <li>✅ 验证错误显示详细的字段信息</li>\n              <li>✅ 服务器错误显示通知而非简单消息</li>\n              <li>✅ 网络错误自动重试机制</li>\n              <li>✅ 统一的成功/失败消息提示</li>\n              <li>✅ 可配置的消息显示选项</li>\n            </ul>\n          </Paragraph>\n        </Card>\n      </Space>\n    </PageContainer>\n  );\n};\n\nexport default ResponseDemoPage;\n", "__isJSFile": true, "__absFile": "D:/rwx-ai/RwxAI.Web/src/pages/response-demo/index.tsx"}, "17": {"path": "/api-test", "name": "api-test", "icon": "api", "file": "@/pages/api-test/index.tsx", "parentId": "ant-design-pro-layout", "id": "17", "absPath": "/api-test", "__content": "import React, { useState } from 'react';\nimport { PageContainer } from '@ant-design/pro-components';\nimport { Card, Button, Space, Typography, Alert, Tabs, List, Tag } from 'antd';\nimport { \n  getAIModels, \n  getApps, \n  getChatSessions, \n  getKnowledgeBases, \n  getPlugins \n} from '@/services/rwxai';\n\nconst { Title, Text, Paragraph } = Typography;\n\ninterface TestResult {\n  api: string;\n  status: 'success' | 'error' | 'loading';\n  message: string;\n  data?: any;\n  timestamp: string;\n}\n\nconst ApiTestPage: React.FC = () => {\n  const [testResults, setTestResults] = useState<TestResult[]>([]);\n  const [loading, setLoading] = useState<Record<string, boolean>>({});\n\n  const addTestResult = (result: TestResult) => {\n    setTestResults(prev => [result, ...prev.slice(0, 19)]); // 保留最近20条记录\n  };\n\n  const testApi = async (apiName: string, apiFunction: () => Promise<any>) => {\n    setLoading(prev => ({ ...prev, [apiName]: true }));\n    \n    const startTime = new Date().toLocaleTimeString();\n    \n    try {\n      const response = await apiFunction();\n      \n      if (response.success) {\n        addTestResult({\n          api: apiName,\n          status: 'success',\n          message: `✅ 成功 - ${response.data?.length || 0} 条记录`,\n          data: response.data,\n          timestamp: startTime,\n        });\n      } else {\n        addTestResult({\n          api: apiName,\n          status: 'error',\n          message: `❌ 失败 - ${response.message}`,\n          timestamp: startTime,\n        });\n      }\n    } catch (error: any) {\n      addTestResult({\n        api: apiName,\n        status: 'error',\n        message: `❌ 异常 - ${error.message}`,\n        timestamp: startTime,\n      });\n    } finally {\n      setLoading(prev => ({ ...prev, [apiName]: false }));\n    }\n  };\n\n  const testAllApis = async () => {\n    const apis = [\n      { name: 'AI模型列表', func: getAIModels },\n      { name: '应用列表', func: getApps },\n      { name: '聊天会话列表', func: getChatSessions },\n      { name: '知识库列表', func: getKnowledgeBases },\n      { name: '插件列表', func: getPlugins },\n    ];\n\n    for (const api of apis) {\n      await testApi(api.name, api.func);\n      // 添加小延迟避免请求过于频繁\n      await new Promise(resolve => setTimeout(resolve, 500));\n    }\n  };\n\n  const clearResults = () => {\n    setTestResults([]);\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'success': return 'green';\n      case 'error': return 'red';\n      case 'loading': return 'blue';\n      default: return 'default';\n    }\n  };\n\n  const apiTests = [\n    {\n      category: 'AI模型管理',\n      tests: [\n        { name: 'AI模型列表', func: getAIModels, desc: '获取所有AI模型' },\n      ]\n    },\n    {\n      category: '应用管理',\n      tests: [\n        { name: '应用列表', func: getApps, desc: '获取所有应用' },\n      ]\n    },\n    {\n      category: '聊天管理',\n      tests: [\n        { name: '聊天会话列表', func: getChatSessions, desc: '获取所有聊天会话' },\n      ]\n    },\n    {\n      category: '知识库管理',\n      tests: [\n        { name: '知识库列表', func: getKnowledgeBases, desc: '获取所有知识库' },\n      ]\n    },\n    {\n      category: '插件管理',\n      tests: [\n        { name: '插件列表', func: getPlugins, desc: '获取所有插件' },\n      ]\n    },\n  ];\n\n  const tabItems = [\n    {\n      key: 'test',\n      label: 'API测试',\n      children: (\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          <Card title=\"批量测试\" size=\"small\">\n            <Space>\n              <Button type=\"primary\" onClick={testAllApis} loading={Object.values(loading).some(Boolean)}>\n                测试所有API\n              </Button>\n              <Button onClick={clearResults}>\n                清空结果\n              </Button>\n            </Space>\n          </Card>\n\n          {apiTests.map((category) => (\n            <Card key={category.category} title={category.category} size=\"small\">\n              <Space wrap>\n                {category.tests.map((test) => (\n                  <Button\n                    key={test.name}\n                    onClick={() => testApi(test.name, test.func)}\n                    loading={loading[test.name]}\n                    title={test.desc}\n                  >\n                    {test.name}\n                  </Button>\n                ))}\n              </Space>\n            </Card>\n          ))}\n        </Space>\n      ),\n    },\n    {\n      key: 'results',\n      label: `测试结果 (${testResults.length})`,\n      children: (\n        <Card title=\"测试结果\" size=\"small\">\n          {testResults.length === 0 ? (\n            <Text type=\"secondary\">暂无测试结果</Text>\n          ) : (\n            <List\n              dataSource={testResults}\n              renderItem={(item) => (\n                <List.Item>\n                  <List.Item.Meta\n                    title={\n                      <Space>\n                        <Text strong>{item.api}</Text>\n                        <Tag color={getStatusColor(item.status)}>\n                          {item.status.toUpperCase()}\n                        </Tag>\n                        <Text type=\"secondary\">{item.timestamp}</Text>\n                      </Space>\n                    }\n                    description={item.message}\n                  />\n                </List.Item>\n              )}\n            />\n          )}\n        </Card>\n      ),\n    },\n    {\n      key: 'info',\n      label: '系统信息',\n      children: (\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          <Card title=\"迁移状态\" size=\"small\">\n            <Paragraph>\n              <Title level={5}>✅ 已完成迁移的API服务：</Title>\n              <ul>\n                <li>✅ AI模型管理 (aiModels.ts) - 支持统一响应处理</li>\n                <li>✅ 应用管理 (apps.ts) - 支持统一响应处理</li>\n                <li>✅ 用户认证 (auth.ts) - 支持统一响应处理</li>\n                <li>✅ 聊天管理 (chat.ts) - 支持统一响应处理</li>\n                <li>✅ 知识库管理 (knowledge.ts) - 支持统一响应处理</li>\n                <li>✅ 插件管理 (plugins.ts) - 支持统一响应处理</li>\n              </ul>\n            </Paragraph>\n          </Card>\n\n          <Card title=\"响应处理特性\" size=\"small\">\n            <Paragraph>\n              <Title level={5}>🎯 统一响应处理系统特性：</Title>\n              <ul>\n                <li>✅ 自动处理所有HTTP状态码 (200, 400, 401, 403, 404, 422, 500等)</li>\n                <li>✅ 用户友好的错误提示</li>\n                <li>✅ 自动JWT认证和令牌刷新</li>\n                <li>✅ 可配置的成功/失败消息</li>\n                <li>✅ 统一的响应格式</li>\n                <li>✅ 智能的通知显示策略</li>\n                <li>✅ 详细的验证错误信息</li>\n                <li>✅ 网络错误自动处理</li>\n              </ul>\n            </Paragraph>\n          </Card>\n\n          <Card title=\"测试说明\" size=\"small\">\n            <Paragraph>\n              <Title level={5}>📝 如何使用：</Title>\n              <ol>\n                <li>确保已登录并有有效的JWT令牌</li>\n                <li>点击\"测试所有API\"进行批量测试</li>\n                <li>或单独测试各个API接口</li>\n                <li>查看\"测试结果\"标签页了解详细结果</li>\n                <li>观察错误处理和成功提示的效果</li>\n              </ol>\n            </Paragraph>\n          </Card>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <PageContainer title=\"API统一响应处理测试\">\n      <Alert\n        message=\"API迁移完成\"\n        description=\"所有API服务已成功迁移到统一响应处理系统，支持完整的HTTP状态码处理和用户友好的错误提示。\"\n        type=\"success\"\n        showIcon\n        style={{ marginBottom: 24 }}\n      />\n      \n      <Tabs items={tabItems} />\n    </PageContainer>\n  );\n};\n\nexport default ApiTestPage;\n", "__isJSFile": true, "__absFile": "D:/rwx-ai/RwxAI.Web/src/pages/api-test/index.tsx"}, "18": {"path": "/admin", "name": "admin", "icon": "crown", "access": "canAdmin", "parentId": "ant-design-pro-layout", "id": "18", "absPath": "/admin"}, "19": {"path": "/admin", "redirect": "/admin/sub-page", "parentId": "18", "id": "19", "absPath": "/admin"}, "20": {"path": "/admin/sub-page", "name": "sub-page", "file": "@/pages/Admin.tsx", "parentId": "18", "id": "20", "absPath": "/admin/sub-page", "__content": "import { HeartTwoTone, SmileTwoTone } from '@ant-design/icons';\r\nimport { PageContainer } from '@ant-design/pro-components';\r\nimport { useIntl } from '@umijs/max';\r\nimport { Alert, Card, Typography } from 'antd';\r\nimport React from 'react';\r\n\r\nconst Admin: React.FC = () => {\r\n  const intl = useIntl();\r\n  return (\r\n    <PageContainer\r\n      content={intl.formatMessage({\r\n        id: 'pages.admin.subPage.title',\r\n        defaultMessage: 'This page can only be viewed by admin',\r\n      })}\r\n    >\r\n      <Card>\r\n        <Alert\r\n          message={intl.formatMessage({\r\n            id: 'pages.welcome.alertMessage',\r\n            defaultMessage:\r\n              'Faster and stronger heavy-duty components have been released.',\r\n          })}\r\n          type=\"success\"\r\n          showIcon\r\n          banner\r\n          style={{\r\n            margin: -12,\r\n            marginBottom: 48,\r\n          }}\r\n        />\r\n        <Typography.Title level={2} style={{ textAlign: 'center' }}>\r\n          <SmileTwoTone /> Ant Design Pro{' '}\r\n          <HeartTwoTone twoToneColor=\"#eb2f96\" /> You\r\n        </Typography.Title>\r\n      </Card>\r\n      <p style={{ textAlign: 'center', marginTop: 24 }}>\r\n        Want to add more pages? Please refer to{' '}\r\n        <a\r\n          href=\"https://pro.ant.design/docs/block-cn\"\r\n          target=\"_blank\"\r\n          rel=\"noopener noreferrer\"\r\n        >\r\n          use block\r\n        </a>\r\n        。\r\n      </p>\r\n    </PageContainer>\r\n  );\r\n};\r\n\r\nexport default Admin;\r\n", "__isJSFile": true, "__absFile": "D:/rwx-ai/RwxAI.Web/src/pages/Admin.tsx"}, "21": {"name": "list.table-list", "icon": "table", "path": "/list", "file": "@/pages/table-list/index.tsx", "parentId": "ant-design-pro-layout", "id": "21", "absPath": "/list", "__content": "import type {\r\n  ActionType,\r\n  ProColumns,\r\n  ProDescriptionsItemProps,\r\n} from '@ant-design/pro-components';\r\nimport {\r\n  FooterToolbar,\r\n  PageContainer,\r\n  ProDescriptions,\r\n  ProTable,\r\n} from '@ant-design/pro-components';\r\nimport { FormattedMessage, useIntl, useRequest } from '@umijs/max';\r\nimport { Button, Drawer, Input, message } from 'antd';\r\nimport React, { useCallback, useRef, useState } from 'react';\r\nimport { removeRule, rule } from '@/services/ant-design-pro/api';\r\nimport CreateForm from './components/CreateForm';\r\nimport UpdateForm from './components/UpdateForm';\r\n\r\nconst TableList: React.FC = () => {\r\n  const actionRef = useRef<ActionType | null>(null);\r\n\r\n  const [showDetail, setShowDetail] = useState<boolean>(false);\r\n  const [currentRow, setCurrentRow] = useState<API.RuleListItem>();\r\n  const [selectedRowsState, setSelectedRows] = useState<API.RuleListItem[]>([]);\r\n\r\n  /**\r\n   * @en-US International configuration\r\n   * @zh-CN 国际化配置\r\n   * */\r\n  const intl = useIntl();\r\n\r\n  const [messageApi, contextHolder] = message.useMessage();\r\n\r\n  const { run: delRun, loading } = useRequest(removeRule, {\r\n    manual: true,\r\n    onSuccess: () => {\r\n      setSelectedRows([]);\r\n      actionRef.current?.reloadAndRest?.();\r\n\r\n      messageApi.success('Deleted successfully and will refresh soon');\r\n    },\r\n    onError: () => {\r\n      messageApi.error('Delete failed, please try again');\r\n    },\r\n  });\r\n\r\n  const columns: ProColumns<API.RuleListItem>[] = [\r\n    {\r\n      title: (\r\n        <FormattedMessage\r\n          id=\"pages.searchTable.updateForm.ruleName.nameLabel\"\r\n          defaultMessage=\"Rule name\"\r\n        />\r\n      ),\r\n      dataIndex: 'name',\r\n      render: (dom, entity) => {\r\n        return (\r\n          <a\r\n            onClick={() => {\r\n              setCurrentRow(entity);\r\n              setShowDetail(true);\r\n            }}\r\n          >\r\n            {dom}\r\n          </a>\r\n        );\r\n      },\r\n    },\r\n    {\r\n      title: (\r\n        <FormattedMessage\r\n          id=\"pages.searchTable.titleDesc\"\r\n          defaultMessage=\"Description\"\r\n        />\r\n      ),\r\n      dataIndex: 'desc',\r\n      valueType: 'textarea',\r\n    },\r\n    {\r\n      title: (\r\n        <FormattedMessage\r\n          id=\"pages.searchTable.titleCallNo\"\r\n          defaultMessage=\"Number of service calls\"\r\n        />\r\n      ),\r\n      dataIndex: 'callNo',\r\n      sorter: true,\r\n      hideInForm: true,\r\n      renderText: (val: string) =>\r\n        `${val}${intl.formatMessage({\r\n          id: 'pages.searchTable.tenThousand',\r\n          defaultMessage: ' 万 ',\r\n        })}`,\r\n    },\r\n    {\r\n      title: (\r\n        <FormattedMessage\r\n          id=\"pages.searchTable.titleStatus\"\r\n          defaultMessage=\"Status\"\r\n        />\r\n      ),\r\n      dataIndex: 'status',\r\n      hideInForm: true,\r\n      valueEnum: {\r\n        0: {\r\n          text: (\r\n            <FormattedMessage\r\n              id=\"pages.searchTable.nameStatus.default\"\r\n              defaultMessage=\"Shut down\"\r\n            />\r\n          ),\r\n          status: 'Default',\r\n        },\r\n        1: {\r\n          text: (\r\n            <FormattedMessage\r\n              id=\"pages.searchTable.nameStatus.running\"\r\n              defaultMessage=\"Running\"\r\n            />\r\n          ),\r\n          status: 'Processing',\r\n        },\r\n        2: {\r\n          text: (\r\n            <FormattedMessage\r\n              id=\"pages.searchTable.nameStatus.online\"\r\n              defaultMessage=\"Online\"\r\n            />\r\n          ),\r\n          status: 'Success',\r\n        },\r\n        3: {\r\n          text: (\r\n            <FormattedMessage\r\n              id=\"pages.searchTable.nameStatus.abnormal\"\r\n              defaultMessage=\"Abnormal\"\r\n            />\r\n          ),\r\n          status: 'Error',\r\n        },\r\n      },\r\n    },\r\n    {\r\n      title: (\r\n        <FormattedMessage\r\n          id=\"pages.searchTable.titleUpdatedAt\"\r\n          defaultMessage=\"Last scheduled time\"\r\n        />\r\n      ),\r\n      sorter: true,\r\n      dataIndex: 'updatedAt',\r\n      valueType: 'dateTime',\r\n      renderFormItem: (item, { defaultRender, ...rest }, form) => {\r\n        const status = form.getFieldValue('status');\r\n        if (`${status}` === '0') {\r\n          return false;\r\n        }\r\n        if (`${status}` === '3') {\r\n          return (\r\n            <Input\r\n              {...rest}\r\n              placeholder={intl.formatMessage({\r\n                id: 'pages.searchTable.exception',\r\n                defaultMessage: 'Please enter the reason for the exception!',\r\n              })}\r\n            />\r\n          );\r\n        }\r\n        return defaultRender(item);\r\n      },\r\n    },\r\n    {\r\n      title: (\r\n        <FormattedMessage\r\n          id=\"pages.searchTable.titleOption\"\r\n          defaultMessage=\"Operating\"\r\n        />\r\n      ),\r\n      dataIndex: 'option',\r\n      valueType: 'option',\r\n      render: (_, record) => [\r\n        <UpdateForm\r\n          trigger={\r\n            <a>\r\n              <FormattedMessage\r\n                id=\"pages.searchTable.config\"\r\n                defaultMessage=\"Configuration\"\r\n              />\r\n            </a>\r\n          }\r\n          key=\"config\"\r\n          onOk={actionRef.current?.reload}\r\n          values={record}\r\n        />,\r\n        <a key=\"subscribeAlert\" href=\"https://procomponents.ant.design/\">\r\n          <FormattedMessage\r\n            id=\"pages.searchTable.subscribeAlert\"\r\n            defaultMessage=\"Subscribe to alerts\"\r\n          />\r\n        </a>,\r\n      ],\r\n    },\r\n  ];\r\n\r\n  /**\r\n   *  Delete node\r\n   * @zh-CN 删除节点\r\n   *\r\n   * @param selectedRows\r\n   */\r\n  const handleRemove = useCallback(\r\n    async (selectedRows: API.RuleListItem[]) => {\r\n      if (!selectedRows?.length) {\r\n        messageApi.warning('请选择删除项');\r\n\r\n        return;\r\n      }\r\n\r\n      await delRun({\r\n        data: {\r\n          key: selectedRows.map((row) => row.key),\r\n        },\r\n      });\r\n    },\r\n    [delRun, messageApi.warning],\r\n  );\r\n\r\n  return (\r\n    <PageContainer>\r\n      {contextHolder}\r\n      <ProTable<API.RuleListItem, API.PageParams>\r\n        headerTitle={intl.formatMessage({\r\n          id: 'pages.searchTable.title',\r\n          defaultMessage: 'Enquiry form',\r\n        })}\r\n        actionRef={actionRef}\r\n        rowKey=\"key\"\r\n        search={{\r\n          labelWidth: 120,\r\n        }}\r\n        toolBarRender={() => [\r\n          <CreateForm key=\"create\" reload={actionRef.current?.reload} />,\r\n        ]}\r\n        request={rule}\r\n        columns={columns}\r\n        rowSelection={{\r\n          onChange: (_, selectedRows) => {\r\n            setSelectedRows(selectedRows);\r\n          },\r\n        }}\r\n      />\r\n      {selectedRowsState?.length > 0 && (\r\n        <FooterToolbar\r\n          extra={\r\n            <div>\r\n              <FormattedMessage\r\n                id=\"pages.searchTable.chosen\"\r\n                defaultMessage=\"Chosen\"\r\n              />{' '}\r\n              <a style={{ fontWeight: 600 }}>{selectedRowsState.length}</a>{' '}\r\n              <FormattedMessage\r\n                id=\"pages.searchTable.item\"\r\n                defaultMessage=\"项\"\r\n              />\r\n              &nbsp;&nbsp;\r\n              <span>\r\n                <FormattedMessage\r\n                  id=\"pages.searchTable.totalServiceCalls\"\r\n                  defaultMessage=\"Total number of service calls\"\r\n                />{' '}\r\n                {selectedRowsState.reduce(\r\n                  (pre, item) => pre + (item.callNo ?? 0),\r\n                  0,\r\n                )}{' '}\r\n                <FormattedMessage\r\n                  id=\"pages.searchTable.tenThousand\"\r\n                  defaultMessage=\"万\"\r\n                />\r\n              </span>\r\n            </div>\r\n          }\r\n        >\r\n          <Button\r\n            loading={loading}\r\n            onClick={() => {\r\n              handleRemove(selectedRowsState);\r\n            }}\r\n          >\r\n            <FormattedMessage\r\n              id=\"pages.searchTable.batchDeletion\"\r\n              defaultMessage=\"Batch deletion\"\r\n            />\r\n          </Button>\r\n          <Button type=\"primary\">\r\n            <FormattedMessage\r\n              id=\"pages.searchTable.batchApproval\"\r\n              defaultMessage=\"Batch approval\"\r\n            />\r\n          </Button>\r\n        </FooterToolbar>\r\n      )}\r\n\r\n      <Drawer\r\n        width={600}\r\n        open={showDetail}\r\n        onClose={() => {\r\n          setCurrentRow(undefined);\r\n          setShowDetail(false);\r\n        }}\r\n        closable={false}\r\n      >\r\n        {currentRow?.name && (\r\n          <ProDescriptions<API.RuleListItem>\r\n            column={2}\r\n            title={currentRow?.name}\r\n            request={async () => ({\r\n              data: currentRow || {},\r\n            })}\r\n            params={{\r\n              id: currentRow?.name,\r\n            }}\r\n            columns={columns as ProDescriptionsItemProps<API.RuleListItem>[]}\r\n          />\r\n        )}\r\n      </Drawer>\r\n    </PageContainer>\r\n  );\r\n};\r\n\r\nexport default TableList;\r\n", "__isJSFile": true, "__absFile": "D:/rwx-ai/RwxAI.Web/src/pages/table-list/index.tsx"}, "22": {"path": "/", "redirect": "/welcome", "parentId": "ant-design-pro-layout", "id": "22", "absPath": "/"}, "23": {"path": "*", "layout": false, "file": "@/pages/404.tsx", "id": "23", "absPath": "/*", "__content": "import { history, useIntl } from '@umijs/max';\r\nimport { Button, Card, Result } from 'antd';\r\nimport React from 'react';\r\n\r\nconst NoFoundPage: React.FC = () => (\r\n  <Card variant=\"borderless\">\r\n    <Result\r\n      status=\"404\"\r\n      title=\"404\"\r\n      subTitle={useIntl().formatMessage({ id: 'pages.404.subTitle' })}\r\n      extra={\r\n        <Button type=\"primary\" onClick={() => history.push('/')}>\r\n          {useIntl().formatMessage({ id: 'pages.404.buttonText' })}\r\n        </Button>\r\n      }\r\n    />\r\n  </Card>\r\n);\r\n\r\nexport default NoFoundPage;\r\n", "__isJSFile": true, "__absFile": "D:/rwx-ai/RwxAI.Web/src/pages/404.tsx"}, "ant-design-pro-layout": {"id": "ant-design-pro-layout", "path": "/", "file": "D:/rwx-ai/RwxAI.Web/src/.umi-production/plugin-layout/Layout.tsx", "absPath": "/", "isLayout": true, "__absFile": "D:/rwx-ai/RwxAI.Web/src/.umi-production/plugin-layout/Layout.tsx"}}, "apiRoutes": {}, "hasSrcDir": true, "npmClient": "cnpm", "umi": {"version": "4.4.12", "name": "<PERSON><PERSON>", "importSource": "@umijs/max", "cliName": "max"}, "bundleStatus": {"done": false}, "react": {"version": "19.1.1", "path": "D:\\rwx-ai\\RwxAI.Web\\node_modules\\react"}, "react-dom": {"version": "19.1.1", "path": "D:\\rwx-ai\\RwxAI.Web\\node_modules\\react-dom"}, "appJS": {"path": "D:\\rwx-ai\\RwxAI.Web\\src\\app.tsx", "exports": ["getInitialState", "layout", "request"]}, "locale": "zh-CN", "globalCSS": ["D:\\rwx-ai\\RwxAI.Web\\src\\global.less"], "globalJS": ["D:\\rwx-ai\\RwxAI.Web\\src\\global.tsx"], "overridesCSS": [], "globalLoading": "D:/rwx-ai/RwxAI.Web/src/loading.tsx", "bundler": "mako", "git": {"originUrl": "https://gitee.com/bjrwx/rwx-ai.git"}, "framework": "react", "typescript": {"tsVersion": "5.9.2", "tslibVersion": "2.8.1"}, "faviconFiles": [], "antd": {"pkgPath": "D:\\rwx-ai\\RwxAI.Web\\node_modules\\antd", "version": "5.27.1"}, "pluginLayout": {"pkgPath": "D:/rwx-ai/RwxAI.Web/node_modules/@ant-design/pro-components", "version": "2.8.10"}}