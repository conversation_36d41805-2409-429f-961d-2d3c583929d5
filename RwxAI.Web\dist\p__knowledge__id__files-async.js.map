{"version": 3, "sources": ["src/pages/knowledge/[id]/files.tsx"], "sourcesContent": ["import React, { useRef, useState, useEffect } from 'react';\nimport { <PERSON>Container } from '@ant-design/pro-components';\nimport { ProTable, ActionType, ProColumns } from '@ant-design/pro-table';\nimport { Button, Space, message, Modal, Upload, Tag, Progress } from 'antd';\nimport { UploadOutlined, DeleteOutlined, PlayCircleOutlined, FileOutlined } from '@ant-design/icons';\nimport { useParams, useIntl, FormattedMessage } from '@umijs/max';\nimport { getKnowledgeFiles, uploadKnowledgeFile, deleteKnowledgeFile, processKnowledgeFile, getKnowledgeBaseById } from '@/services/rwxai';\n\nconst KnowledgeFilesPage: React.FC = () => {\n  const intl = useIntl();\n  const { id } = useParams<{ id: string }>();\n  const actionRef = useRef<ActionType>();\n  const [knowledgeBase, setKnowledgeBase] = useState<RwxAI.Knowledge>();\n  const [uploadModalVisible, setUploadModalVisible] = useState(false);\n  const [uploading, setUploading] = useState(false);\n\n  useEffect(() => {\n    if (id) {\n      loadKnowledgeBase();\n    }\n  }, [id]);\n\n  const loadKnowledgeBase = async () => {\n    try {\n      const data = await getKnowledgeBaseById(id!);\n      setKnowledgeBase(data);\n    } catch (error) {\n      message.error(intl.formatMessage({ id: 'pages.knowledge.files.loadKnowledge.error' }));\n    }\n  };\n\n  const handleDelete = async (record: RwxAI.KnowledgeFile) => {\n    Modal.confirm({\n      title: intl.formatMessage({ id: 'pages.knowledge.files.delete.confirm.title' }),\n      content: intl.formatMessage({ id: 'pages.knowledge.files.delete.confirm.content' }),\n      onOk: async () => {\n        try {\n          await deleteKnowledgeFile(record.Id);\n          message.success(intl.formatMessage({ id: 'pages.knowledge.files.delete.success' }));\n          actionRef.current?.reload();\n        } catch (error) {\n          message.error(intl.formatMessage({ id: 'pages.knowledge.files.delete.error' }));\n        }\n      },\n    });\n  };\n\n  const handleProcess = async (record: RwxAI.KnowledgeFile) => {\n    try {\n      await processKnowledgeFile(record.Id);\n      message.success(intl.formatMessage({ id: 'pages.knowledge.files.process.success' }));\n      actionRef.current?.reload();\n    } catch (error) {\n      message.error(intl.formatMessage({ id: 'pages.knowledge.files.process.error' }));\n    }\n  };\n\n  const handleUpload = async (file: File) => {\n    try {\n      setUploading(true);\n      await uploadKnowledgeFile(id!, file);\n      message.success(intl.formatMessage({ id: 'pages.knowledge.files.upload.success' }));\n      setUploadModalVisible(false);\n      actionRef.current?.reload();\n    } catch (error) {\n      message.error(intl.formatMessage({ id: 'pages.knowledge.files.upload.error' }));\n    } finally {\n      setUploading(false);\n    }\n  };\n\n  const formatFileSize = (bytes: number) => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  const columns: ProColumns<RwxAI.KnowledgeFile>[] = [\n    {\n      title: <FormattedMessage id=\"pages.knowledge.files.table.fileName\" />,\n      dataIndex: 'FileName',\n      key: 'FileName',\n      ellipsis: true,\n      render: (_, record) => (\n        <Space>\n          <FileOutlined />\n          {record.FileName}\n        </Space>\n      ),\n    },\n    {\n      title: <FormattedMessage id=\"pages.knowledge.files.table.contentType\" />,\n      dataIndex: 'ContentType',\n      key: 'ContentType',\n      hideInSearch: true,\n    },\n    {\n      title: <FormattedMessage id=\"pages.knowledge.files.table.fileSize\" />,\n      dataIndex: 'FileSize',\n      key: 'FileSize',\n      hideInSearch: true,\n      render: (_, record) => formatFileSize(record.FileSize),\n    },\n    {\n      title: <FormattedMessage id=\"pages.knowledge.files.table.status\" />,\n      dataIndex: 'IsProcessed',\n      key: 'IsProcessed',\n      render: (_, record) => (\n        <Tag color={record.IsProcessed ? 'green' : 'orange'}>\n          {record.IsProcessed ? \n            intl.formatMessage({ id: 'pages.knowledge.files.status.processed' }) : \n            intl.formatMessage({ id: 'pages.knowledge.files.status.pending' })\n          }\n        </Tag>\n      ),\n    },\n    {\n      title: <FormattedMessage id=\"pages.knowledge.files.table.processedTime\" />,\n      dataIndex: 'ProcessedTime',\n      key: 'ProcessedTime',\n      valueType: 'dateTime',\n      hideInSearch: true,\n      render: (_, record) => record.ProcessedTime ? new Date(record.ProcessedTime).toLocaleString() : '-',\n    },\n    {\n      title: <FormattedMessage id=\"pages.knowledge.files.table.createdTime\" />,\n      dataIndex: 'CreatedTime',\n      key: 'CreatedTime',\n      valueType: 'dateTime',\n      width: 180,\n      hideInSearch: true,\n    },\n    {\n      title: <FormattedMessage id=\"pages.knowledge.files.table.actions\" />,\n      key: 'actions',\n      width: 200,\n      render: (_, record) => (\n        <Space>\n          {!record.IsProcessed && (\n            <Button\n              type=\"link\"\n              size=\"small\"\n              icon={<PlayCircleOutlined />}\n              onClick={() => handleProcess(record)}\n            >\n              <FormattedMessage id=\"pages.knowledge.files.actions.process\" />\n            </Button>\n          )}\n          <Button\n            type=\"link\"\n            size=\"small\"\n            danger\n            icon={<DeleteOutlined />}\n            onClick={() => handleDelete(record)}\n          >\n            <FormattedMessage id=\"pages.knowledge.files.actions.delete\" />\n          </Button>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <PageContainer\n      title={knowledgeBase?.Name}\n      subTitle={intl.formatMessage({ id: 'pages.knowledge.files.subtitle' })}\n    >\n      <ProTable<RwxAI.KnowledgeFile>\n        headerTitle={intl.formatMessage({ id: 'pages.knowledge.files.title' })}\n        actionRef={actionRef}\n        rowKey=\"Id\"\n        search={{\n          labelWidth: 120,\n        }}\n        toolBarRender={() => [\n          <Button\n            type=\"primary\"\n            key=\"primary\"\n            icon={<UploadOutlined />}\n            onClick={() => setUploadModalVisible(true)}\n          >\n            <FormattedMessage id=\"pages.knowledge.files.actions.upload\" />\n          </Button>,\n        ]}\n        request={async () => {\n          try {\n            const data = await getKnowledgeFiles(id!);\n            return {\n              data: data || [],\n              success: true,\n            };\n          } catch (error) {\n            return {\n              data: [],\n              success: false,\n            };\n          }\n        }}\n        columns={columns}\n      />\n\n      <Modal\n        title={intl.formatMessage({ id: 'pages.knowledge.files.upload.title' })}\n        open={uploadModalVisible}\n        onCancel={() => setUploadModalVisible(false)}\n        footer={null}\n      >\n        <Upload.Dragger\n          name=\"file\"\n          multiple={false}\n          beforeUpload={(file) => {\n            handleUpload(file);\n            return false;\n          }}\n          disabled={uploading}\n        >\n          <p className=\"ant-upload-drag-icon\">\n            <UploadOutlined />\n          </p>\n          <p className=\"ant-upload-text\">\n            <FormattedMessage id=\"pages.knowledge.files.upload.dragText\" />\n          </p>\n          <p className=\"ant-upload-hint\">\n            <FormattedMessage id=\"pages.knowledge.files.upload.hint\" />\n          </p>\n        </Upload.Dragger>\n        {uploading && (\n          <div style={{ marginTop: 16 }}>\n            <Progress percent={100} status=\"active\" />\n          </div>\n        )}\n      </Modal>\n    </PageContainer>\n  );\n};\n\nexport default KnowledgeFilesPage;\n"], "names": [], "mappings": ";;;;;;;4BA8OA;;;eAAA;;;;;;wEA9OmD;sCACrB;iCACmB;6BACoB;8BACY;4BAC5B;8BACmE;;;;;;;;;;AAExH,MAAM,qBAA+B;;IACnC,MAAM,OAAO,IAAA,YAAO;IACpB,MAAM,EAAE,EAAE,EAAE,GAAG,IAAA,cAAS;IACxB,MAAM,YAAY,IAAA,aAAM;IACxB,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ;IAClD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,IAAA,eAAQ,EAAC;IAC7D,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAC;IAE3C,IAAA,gBAAS,EAAC;QACR,IAAI,IACF;IAEJ,GAAG;QAAC;KAAG;IAEP,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,OAAO,MAAM,IAAA,2BAAoB,EAAC;YACxC,iBAAiB;QACnB,EAAE,OAAO,OAAO;YACd,aAAO,CAAC,KAAK,CAAC,KAAK,aAAa,CAAC;gBAAE,IAAI;YAA4C;QACrF;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,WAAK,CAAC,OAAO,CAAC;YACZ,OAAO,KAAK,aAAa,CAAC;gBAAE,IAAI;YAA6C;YAC7E,SAAS,KAAK,aAAa,CAAC;gBAAE,IAAI;YAA+C;YACjF,MAAM;gBACJ,IAAI;wBAGF;oBAFA,MAAM,IAAA,0BAAmB,EAAC,OAAO,EAAE;oBACnC,aAAO,CAAC,OAAO,CAAC,KAAK,aAAa,CAAC;wBAAE,IAAI;oBAAuC;qBAChF,qBAAA,UAAU,OAAO,cAAjB,iCAAA,mBAAmB,MAAM;gBAC3B,EAAE,OAAO,OAAO;oBACd,aAAO,CAAC,KAAK,CAAC,KAAK,aAAa,CAAC;wBAAE,IAAI;oBAAqC;gBAC9E;YACF;QACF;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI;gBAGF;YAFA,MAAM,IAAA,2BAAoB,EAAC,OAAO,EAAE;YACpC,aAAO,CAAC,OAAO,CAAC,KAAK,aAAa,CAAC;gBAAE,IAAI;YAAwC;aACjF,qBAAA,UAAU,OAAO,cAAjB,iCAAA,mBAAmB,MAAM;QAC3B,EAAE,OAAO,OAAO;YACd,aAAO,CAAC,KAAK,CAAC,KAAK,aAAa,CAAC;gBAAE,IAAI;YAAsC;QAC/E;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI;gBAKF;YAJA,aAAa;YACb,MAAM,IAAA,0BAAmB,EAAC,IAAK;YAC/B,aAAO,CAAC,OAAO,CAAC,KAAK,aAAa,CAAC;gBAAE,IAAI;YAAuC;YAChF,sBAAsB;aACtB,qBAAA,UAAU,OAAO,cAAjB,iCAAA,mBAAmB,MAAM;QAC3B,EAAE,OAAO,OAAO;YACd,aAAO,CAAC,KAAK,CAAC,KAAK,aAAa,CAAC;gBAAE,IAAI;YAAqC;QAC9E,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,AAAC,CAAA,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAC,EAAG,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,MAAM,UAA6C;QACjD;YACE,qBAAO,2BAAC,qBAAgB;gBAAC,IAAG;;;;;;YAC5B,WAAW;YACX,KAAK;YACL,UAAU;YACV,QAAQ,CAAC,GAAG,uBACV,2BAAC,WAAK;;sCACJ,2BAAC,mBAAY;;;;;wBACZ,OAAO,QAAQ;;;;;;;QAGtB;QACA;YACE,qBAAO,2BAAC,qBAAgB;gBAAC,IAAG;;;;;;YAC5B,WAAW;YACX,KAAK;YACL,cAAc;QAChB;QACA;YACE,qBAAO,2BAAC,qBAAgB;gBAAC,IAAG;;;;;;YAC5B,WAAW;YACX,KAAK;YACL,cAAc;YACd,QAAQ,CAAC,GAAG,SAAW,eAAe,OAAO,QAAQ;QACvD;QACA;YACE,qBAAO,2BAAC,qBAAgB;gBAAC,IAAG;;;;;;YAC5B,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,GAAG,uBACV,2BAAC,SAAG;oBAAC,OAAO,OAAO,WAAW,GAAG,UAAU;8BACxC,OAAO,WAAW,GACjB,KAAK,aAAa,CAAC;wBAAE,IAAI;oBAAyC,KAClE,KAAK,aAAa,CAAC;wBAAE,IAAI;oBAAuC;;;;;;QAIxE;QACA;YACE,qBAAO,2BAAC,qBAAgB;gBAAC,IAAG;;;;;;YAC5B,WAAW;YACX,KAAK;YACL,WAAW;YACX,cAAc;YACd,QAAQ,CAAC,GAAG,SAAW,OAAO,aAAa,GAAG,IAAI,KAAK,OAAO,aAAa,EAAE,cAAc,KAAK;QAClG;QACA;YACE,qBAAO,2BAAC,qBAAgB;gBAAC,IAAG;;;;;;YAC5B,WAAW;YACX,KAAK;YACL,WAAW;YACX,OAAO;YACP,cAAc;QAChB;QACA;YACE,qBAAO,2BAAC,qBAAgB;gBAAC,IAAG;;;;;;YAC5B,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,GAAG,uBACV,2BAAC,WAAK;;wBACH,CAAC,OAAO,WAAW,kBAClB,2BAAC,YAAM;4BACL,MAAK;4BACL,MAAK;4BACL,oBAAM,2BAAC,yBAAkB;;;;;4BACzB,SAAS,IAAM,cAAc;sCAE7B,cAAA,2BAAC,qBAAgB;gCAAC,IAAG;;;;;;;;;;;sCAGzB,2BAAC,YAAM;4BACL,MAAK;4BACL,MAAK;4BACL,MAAM;4BACN,oBAAM,2BAAC,qBAAc;;;;;4BACrB,SAAS,IAAM,aAAa;sCAE5B,cAAA,2BAAC,qBAAgB;gCAAC,IAAG;;;;;;;;;;;;;;;;;QAI7B;KACD;IAED,qBACE,2BAAC,4BAAa;QACZ,KAAK,EAAE,0BAAA,oCAAA,cAAe,IAAI;QAC1B,UAAU,KAAK,aAAa,CAAC;YAAE,IAAI;QAAiC;;0BAEpE,2BAAC,kBAAQ;gBACP,aAAa,KAAK,aAAa,CAAC;oBAAE,IAAI;gBAA8B;gBACpE,WAAW;gBACX,QAAO;gBACP,QAAQ;oBACN,YAAY;gBACd;gBACA,eAAe,IAAM;sCACnB,2BAAC,YAAM;4BACL,MAAK;4BAEL,oBAAM,2BAAC,qBAAc;;;;;4BACrB,SAAS,IAAM,sBAAsB;sCAErC,cAAA,2BAAC,qBAAgB;gCAAC,IAAG;;;;;;2BAJjB;;;;;qBAMP;gBACD,SAAS;oBACP,IAAI;wBACF,MAAM,OAAO,MAAM,IAAA,wBAAiB,EAAC;wBACrC,OAAO;4BACL,MAAM,QAAQ,EAAE;4BAChB,SAAS;wBACX;oBACF,EAAE,OAAO,OAAO;wBACd,OAAO;4BACL,MAAM,EAAE;4BACR,SAAS;wBACX;oBACF;gBACF;gBACA,SAAS;;;;;;0BAGX,2BAAC,WAAK;gBACJ,OAAO,KAAK,aAAa,CAAC;oBAAE,IAAI;gBAAqC;gBACrE,MAAM;gBACN,UAAU,IAAM,sBAAsB;gBACtC,QAAQ;;kCAER,2BAAC,YAAM,CAAC,OAAO;wBACb,MAAK;wBACL,UAAU;wBACV,cAAc,CAAC;4BACb,aAAa;4BACb,OAAO;wBACT;wBACA,UAAU;;0CAEV,2BAAC;gCAAE,WAAU;0CACX,cAAA,2BAAC,qBAAc;;;;;;;;;;0CAEjB,2BAAC;gCAAE,WAAU;0CACX,cAAA,2BAAC,qBAAgB;oCAAC,IAAG;;;;;;;;;;;0CAEvB,2BAAC;gCAAE,WAAU;0CACX,cAAA,2BAAC,qBAAgB;oCAAC,IAAG;;;;;;;;;;;;;;;;;oBAGxB,2BACC,2BAAC;wBAAI,OAAO;4BAAE,WAAW;wBAAG;kCAC1B,cAAA,2BAAC,cAAQ;4BAAC,SAAS;4BAAK,QAAO;;;;;;;;;;;;;;;;;;;;;;;AAM3C;GApOM;;QACS,YAAO;QACL,cAAS;;;KAFpB;IAsON,WAAe"}