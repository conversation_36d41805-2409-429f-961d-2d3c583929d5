((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] || []).push([
        ['p__knowledge__index'],
{ "src/pages/knowledge/components/KnowledgeDetail.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const KnowledgeDetail = ({ visible, onVisibleChange, data })=>{
    _s();
    const intl = (0, _max.useIntl)();
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
        title: intl.formatMessage({
            id: 'pages.knowledge.detail.title'
        }),
        open: visible,
        onCancel: ()=>onVisibleChange(false),
        footer: null,
        width: 800,
        children: data && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions, {
            column: 2,
            bordered: true,
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.knowledge.detail.name"
                    }, void 0, false, {
                        fileName: "src/pages/knowledge/components/KnowledgeDetail.tsx",
                        lineNumber: 29,
                        columnNumber: 20
                    }, void 0),
                    span: 2,
                    children: data.Name
                }, void 0, false, {
                    fileName: "src/pages/knowledge/components/KnowledgeDetail.tsx",
                    lineNumber: 28,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.knowledge.detail.description"
                    }, void 0, false, {
                        fileName: "src/pages/knowledge/components/KnowledgeDetail.tsx",
                        lineNumber: 36,
                        columnNumber: 20
                    }, void 0),
                    span: 2,
                    children: data.Description || '-'
                }, void 0, false, {
                    fileName: "src/pages/knowledge/components/KnowledgeDetail.tsx",
                    lineNumber: 35,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.knowledge.detail.chunkSize"
                    }, void 0, false, {
                        fileName: "src/pages/knowledge/components/KnowledgeDetail.tsx",
                        lineNumber: 43,
                        columnNumber: 20
                    }, void 0),
                    children: data.ChunkSize
                }, void 0, false, {
                    fileName: "src/pages/knowledge/components/KnowledgeDetail.tsx",
                    lineNumber: 42,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.knowledge.detail.chunkOverlap"
                    }, void 0, false, {
                        fileName: "src/pages/knowledge/components/KnowledgeDetail.tsx",
                        lineNumber: 49,
                        columnNumber: 20
                    }, void 0),
                    children: data.ChunkOverlap
                }, void 0, false, {
                    fileName: "src/pages/knowledge/components/KnowledgeDetail.tsx",
                    lineNumber: 48,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.knowledge.detail.embeddingModelId"
                    }, void 0, false, {
                        fileName: "src/pages/knowledge/components/KnowledgeDetail.tsx",
                        lineNumber: 55,
                        columnNumber: 20
                    }, void 0),
                    span: 2,
                    children: data.EmbeddingModelId || '-'
                }, void 0, false, {
                    fileName: "src/pages/knowledge/components/KnowledgeDetail.tsx",
                    lineNumber: 54,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.knowledge.detail.createdTime"
                    }, void 0, false, {
                        fileName: "src/pages/knowledge/components/KnowledgeDetail.tsx",
                        lineNumber: 62,
                        columnNumber: 20
                    }, void 0),
                    children: data.CreatedTime ? new Date(data.CreatedTime).toLocaleString() : '-'
                }, void 0, false, {
                    fileName: "src/pages/knowledge/components/KnowledgeDetail.tsx",
                    lineNumber: 61,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.knowledge.detail.updatedTime"
                    }, void 0, false, {
                        fileName: "src/pages/knowledge/components/KnowledgeDetail.tsx",
                        lineNumber: 68,
                        columnNumber: 20
                    }, void 0),
                    children: data.UpdatedTime ? new Date(data.UpdatedTime).toLocaleString() : '-'
                }, void 0, false, {
                    fileName: "src/pages/knowledge/components/KnowledgeDetail.tsx",
                    lineNumber: 67,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.knowledge.detail.metadataJson"
                    }, void 0, false, {
                        fileName: "src/pages/knowledge/components/KnowledgeDetail.tsx",
                        lineNumber: 74,
                        columnNumber: 20
                    }, void 0),
                    span: 2,
                    children: data.MetadataJson ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("pre", {
                        style: {
                            whiteSpace: 'pre-wrap',
                            fontSize: '12px'
                        },
                        children: JSON.stringify(JSON.parse(data.MetadataJson), null, 2)
                    }, void 0, false, {
                        fileName: "src/pages/knowledge/components/KnowledgeDetail.tsx",
                        lineNumber: 78,
                        columnNumber: 15
                    }, this) : '-'
                }, void 0, false, {
                    fileName: "src/pages/knowledge/components/KnowledgeDetail.tsx",
                    lineNumber: 73,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true, {
            fileName: "src/pages/knowledge/components/KnowledgeDetail.tsx",
            lineNumber: 27,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "src/pages/knowledge/components/KnowledgeDetail.tsx",
        lineNumber: 19,
        columnNumber: 5
    }, this);
};
_s(KnowledgeDetail, "rlSgSjbewJ1PrR/Ile8g/kr050o=", false, function() {
    return [
        _max.useIntl
    ];
});
_c = KnowledgeDetail;
var _default = KnowledgeDetail;
var _c;
$RefreshReg$(_c, "KnowledgeDetail");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/knowledge/components/KnowledgeForm.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _rwxai = __mako_require__("src/services/rwxai/index.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const KnowledgeForm = ({ visible, onVisibleChange, initialValues, onSuccess })=>{
    _s();
    const intl = (0, _max.useIntl)();
    const [form] = _antd.Form.useForm();
    const [loading, setLoading] = (0, _react.useState)(false);
    const [embeddingModels, setEmbeddingModels] = (0, _react.useState)([]);
    const isEdit = !!(initialValues === null || initialValues === void 0 ? void 0 : initialValues.Id);
    (0, _react.useEffect)(()=>{
        if (visible) {
            loadEmbeddingModels();
            if (initialValues) form.setFieldsValue(initialValues);
            else form.resetFields();
        }
    }, [
        visible,
        initialValues
    ]);
    const loadEmbeddingModels = async ()=>{
        try {
            const response = await (0, _rwxai.getAIModels)();
            if (response.success && response.data) {
                // 只显示嵌入类型的模型
                const allModels = response.data.Items || [];
                const embeddingModels = allModels.filter((model)=>{
                    var _model_Template;
                    return ((_model_Template = model.Template) === null || _model_Template === void 0 ? void 0 : _model_Template.Type) === 'Embedding' && model.IsEnabled;
                });
                setEmbeddingModels(embeddingModels);
            }
        } catch (error) {
            _antd.message.error(intl.formatMessage({
                id: 'pages.knowledge.form.loadModels.error'
            }));
        }
    };
    const handleSubmit = async ()=>{
        try {
            const values = await form.validateFields();
            setLoading(true);
            if (isEdit) {
                await (0, _rwxai.updateKnowledgeBase)(initialValues.Id, {
                    ...initialValues,
                    ...values
                });
                _antd.message.success(intl.formatMessage({
                    id: 'pages.knowledge.form.update.success'
                }));
            } else {
                await (0, _rwxai.createKnowledgeBase)(values);
                _antd.message.success(intl.formatMessage({
                    id: 'pages.knowledge.form.create.success'
                }));
            }
            onSuccess();
        } catch (error) {
            _antd.message.error(intl.formatMessage({
                id: isEdit ? 'pages.knowledge.form.update.error' : 'pages.knowledge.form.create.error'
            }));
        } finally{
            setLoading(false);
        }
    };
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
        title: intl.formatMessage({
            id: isEdit ? 'pages.knowledge.form.edit.title' : 'pages.knowledge.form.create.title'
        }),
        open: visible,
        onCancel: ()=>onVisibleChange(false),
        onOk: handleSubmit,
        confirmLoading: loading,
        width: 600,
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
            form: form,
            layout: "vertical",
            initialValues: {
                ChunkSize: 1000,
                ChunkOverlap: 200
            },
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    name: "Name",
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.knowledge.form.name"
                    }, void 0, false, {
                        fileName: "src/pages/knowledge/components/KnowledgeForm.tsx",
                        lineNumber: 97,
                        columnNumber: 18
                    }, void 0),
                    rules: [
                        {
                            required: true,
                            message: intl.formatMessage({
                                id: 'pages.knowledge.form.name.required'
                            })
                        }
                    ],
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                        placeholder: intl.formatMessage({
                            id: 'pages.knowledge.form.name.placeholder'
                        })
                    }, void 0, false, {
                        fileName: "src/pages/knowledge/components/KnowledgeForm.tsx",
                        lineNumber: 100,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/knowledge/components/KnowledgeForm.tsx",
                    lineNumber: 95,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    name: "Description",
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.knowledge.form.description"
                    }, void 0, false, {
                        fileName: "src/pages/knowledge/components/KnowledgeForm.tsx",
                        lineNumber: 105,
                        columnNumber: 18
                    }, void 0),
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input.TextArea, {
                        rows: 3,
                        placeholder: intl.formatMessage({
                            id: 'pages.knowledge.form.description.placeholder'
                        })
                    }, void 0, false, {
                        fileName: "src/pages/knowledge/components/KnowledgeForm.tsx",
                        lineNumber: 107,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/knowledge/components/KnowledgeForm.tsx",
                    lineNumber: 103,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    name: "EmbeddingModelId",
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.knowledge.form.embeddingModel"
                    }, void 0, false, {
                        fileName: "src/pages/knowledge/components/KnowledgeForm.tsx",
                        lineNumber: 115,
                        columnNumber: 18
                    }, void 0),
                    rules: [
                        {
                            required: true,
                            message: intl.formatMessage({
                                id: 'pages.knowledge.form.embeddingModel.required'
                            })
                        }
                    ],
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select, {
                        placeholder: intl.formatMessage({
                            id: 'pages.knowledge.form.embeddingModel.placeholder'
                        }),
                        children: embeddingModels.map((model)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                value: model.Id,
                                children: [
                                    model.Name,
                                    " (",
                                    model.ModelKey,
                                    ")"
                                ]
                            }, model.Id, true, {
                                fileName: "src/pages/knowledge/components/KnowledgeForm.tsx",
                                lineNumber: 120,
                                columnNumber: 15
                            }, this))
                    }, void 0, false, {
                        fileName: "src/pages/knowledge/components/KnowledgeForm.tsx",
                        lineNumber: 118,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/knowledge/components/KnowledgeForm.tsx",
                    lineNumber: 113,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    name: "ChunkSize",
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.knowledge.form.chunkSize"
                    }, void 0, false, {
                        fileName: "src/pages/knowledge/components/KnowledgeForm.tsx",
                        lineNumber: 129,
                        columnNumber: 18
                    }, void 0),
                    help: intl.formatMessage({
                        id: 'pages.knowledge.form.chunkSize.help'
                    }),
                    rules: [
                        {
                            required: true,
                            message: intl.formatMessage({
                                id: 'pages.knowledge.form.chunkSize.required'
                            })
                        }
                    ],
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.InputNumber, {
                        min: 100,
                        max: 10000,
                        style: {
                            width: '100%'
                        },
                        placeholder: intl.formatMessage({
                            id: 'pages.knowledge.form.chunkSize.placeholder'
                        })
                    }, void 0, false, {
                        fileName: "src/pages/knowledge/components/KnowledgeForm.tsx",
                        lineNumber: 133,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/knowledge/components/KnowledgeForm.tsx",
                    lineNumber: 127,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    name: "ChunkOverlap",
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.knowledge.form.chunkOverlap"
                    }, void 0, false, {
                        fileName: "src/pages/knowledge/components/KnowledgeForm.tsx",
                        lineNumber: 143,
                        columnNumber: 18
                    }, void 0),
                    help: intl.formatMessage({
                        id: 'pages.knowledge.form.chunkOverlap.help'
                    }),
                    rules: [
                        {
                            required: true,
                            message: intl.formatMessage({
                                id: 'pages.knowledge.form.chunkOverlap.required'
                            })
                        }
                    ],
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.InputNumber, {
                        min: 0,
                        max: 1000,
                        style: {
                            width: '100%'
                        },
                        placeholder: intl.formatMessage({
                            id: 'pages.knowledge.form.chunkOverlap.placeholder'
                        })
                    }, void 0, false, {
                        fileName: "src/pages/knowledge/components/KnowledgeForm.tsx",
                        lineNumber: 147,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/knowledge/components/KnowledgeForm.tsx",
                    lineNumber: 141,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "src/pages/knowledge/components/KnowledgeForm.tsx",
            lineNumber: 87,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "src/pages/knowledge/components/KnowledgeForm.tsx",
        lineNumber: 77,
        columnNumber: 5
    }, this);
};
_s(KnowledgeForm, "tpkW2aAIEIIp5hN7rLa+zIMFIKU=", false, function() {
    return [
        _max.useIntl,
        _antd.Form.useForm
    ];
});
_c = KnowledgeForm;
var _default = KnowledgeForm;
var _c;
$RefreshReg$(_c, "KnowledgeForm");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/knowledge/index.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
var _protable = __mako_require__("node_modules/@ant-design/pro-table/es/index.js");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _rwxai = __mako_require__("src/services/rwxai/index.ts");
var _pageDataHandler = __mako_require__("src/utils/pageDataHandler.ts");
var _KnowledgeForm = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/knowledge/components/KnowledgeForm.tsx"));
var _KnowledgeDetail = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/knowledge/components/KnowledgeDetail.tsx"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const KnowledgePage = ()=>{
    _s();
    const intl = (0, _max.useIntl)();
    const actionRef = (0, _react.useRef)();
    const [createModalVisible, setCreateModalVisible] = (0, _react.useState)(false);
    const [editModalVisible, setEditModalVisible] = (0, _react.useState)(false);
    const [detailModalVisible, setDetailModalVisible] = (0, _react.useState)(false);
    const [currentRecord, setCurrentRecord] = (0, _react.useState)();
    const handleDelete = async (record)=>{
        _antd.Modal.confirm({
            title: intl.formatMessage({
                id: 'pages.knowledge.delete.confirm.title'
            }),
            content: intl.formatMessage({
                id: 'pages.knowledge.delete.confirm.content'
            }),
            onOk: async ()=>{
                try {
                    var _actionRef_current;
                    await (0, _rwxai.deleteKnowledgeBase)(record.Id);
                    _antd.message.success(intl.formatMessage({
                        id: 'pages.knowledge.delete.success'
                    }));
                    (_actionRef_current = actionRef.current) === null || _actionRef_current === void 0 || _actionRef_current.reload();
                } catch (error) {
                    _antd.message.error(intl.formatMessage({
                        id: 'pages.knowledge.delete.error'
                    }));
                }
            }
        });
    };
    const handleManageFiles = (record)=>{
        _max.history.push(`/knowledge/${record.Id}/files`);
    };
    const columns = [
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.knowledge.table.name"
            }, void 0, false, {
                fileName: "src/pages/knowledge/index.tsx",
                lineNumber: 42,
                columnNumber: 14
            }, this),
            dataIndex: 'Name',
            key: 'Name',
            ellipsis: true
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.knowledge.table.description"
            }, void 0, false, {
                fileName: "src/pages/knowledge/index.tsx",
                lineNumber: 48,
                columnNumber: 14
            }, this),
            dataIndex: 'Description',
            key: 'Description',
            ellipsis: true,
            hideInSearch: true
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.knowledge.table.chunkSize"
            }, void 0, false, {
                fileName: "src/pages/knowledge/index.tsx",
                lineNumber: 55,
                columnNumber: 14
            }, this),
            dataIndex: 'ChunkSize',
            key: 'ChunkSize',
            hideInSearch: true,
            width: 120
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.knowledge.table.chunkOverlap"
            }, void 0, false, {
                fileName: "src/pages/knowledge/index.tsx",
                lineNumber: 62,
                columnNumber: 14
            }, this),
            dataIndex: 'ChunkOverlap',
            key: 'ChunkOverlap',
            hideInSearch: true,
            width: 120
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.knowledge.table.createdTime"
            }, void 0, false, {
                fileName: "src/pages/knowledge/index.tsx",
                lineNumber: 69,
                columnNumber: 14
            }, this),
            dataIndex: 'CreatedTime',
            key: 'CreatedTime',
            valueType: 'dateTime',
            width: 180,
            hideInSearch: true
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.knowledge.table.actions"
            }, void 0, false, {
                fileName: "src/pages/knowledge/index.tsx",
                lineNumber: 77,
                columnNumber: 14
            }, this),
            key: 'actions',
            width: 250,
            render: (_, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "link",
                            size: "small",
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EyeOutlined, {}, void 0, false, {
                                fileName: "src/pages/knowledge/index.tsx",
                                lineNumber: 85,
                                columnNumber: 19
                            }, void 0),
                            onClick: ()=>{
                                setCurrentRecord(record);
                                setDetailModalVisible(true);
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                id: "pages.knowledge.actions.view"
                            }, void 0, false, {
                                fileName: "src/pages/knowledge/index.tsx",
                                lineNumber: 91,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/knowledge/index.tsx",
                            lineNumber: 82,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "link",
                            size: "small",
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.FileOutlined, {}, void 0, false, {
                                fileName: "src/pages/knowledge/index.tsx",
                                lineNumber: 96,
                                columnNumber: 19
                            }, void 0),
                            onClick: ()=>handleManageFiles(record),
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                id: "pages.knowledge.actions.files"
                            }, void 0, false, {
                                fileName: "src/pages/knowledge/index.tsx",
                                lineNumber: 99,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/knowledge/index.tsx",
                            lineNumber: 93,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "link",
                            size: "small",
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                                fileName: "src/pages/knowledge/index.tsx",
                                lineNumber: 104,
                                columnNumber: 19
                            }, void 0),
                            onClick: ()=>{
                                setCurrentRecord(record);
                                setEditModalVisible(true);
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                id: "pages.knowledge.actions.edit"
                            }, void 0, false, {
                                fileName: "src/pages/knowledge/index.tsx",
                                lineNumber: 110,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/knowledge/index.tsx",
                            lineNumber: 101,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "link",
                            size: "small",
                            danger: true,
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                fileName: "src/pages/knowledge/index.tsx",
                                lineNumber: 116,
                                columnNumber: 19
                            }, void 0),
                            onClick: ()=>handleDelete(record),
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                id: "pages.knowledge.actions.delete"
                            }, void 0, false, {
                                fileName: "src/pages/knowledge/index.tsx",
                                lineNumber: 119,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/knowledge/index.tsx",
                            lineNumber: 112,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/knowledge/index.tsx",
                    lineNumber: 81,
                    columnNumber: 9
                }, this)
        }
    ];
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_protable.ProTable, {
                headerTitle: intl.formatMessage({
                    id: 'pages.knowledge.title'
                }),
                actionRef: actionRef,
                rowKey: "Id",
                search: {
                    labelWidth: 120
                },
                toolBarRender: ()=>[
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "primary",
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PlusOutlined, {}, void 0, false, {
                                fileName: "src/pages/knowledge/index.tsx",
                                lineNumber: 139,
                                columnNumber: 19
                            }, void 0),
                            onClick: ()=>setCreateModalVisible(true),
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                id: "pages.knowledge.actions.create"
                            }, void 0, false, {
                                fileName: "src/pages/knowledge/index.tsx",
                                lineNumber: 142,
                                columnNumber: 13
                            }, void 0)
                        }, "primary", false, {
                            fileName: "src/pages/knowledge/index.tsx",
                            lineNumber: 136,
                            columnNumber: 11
                        }, void 0)
                    ],
                request: (0, _pageDataHandler.createSimpleProTableRequest)(_rwxai.getKnowledgeBases),
                columns: columns
            }, void 0, false, {
                fileName: "src/pages/knowledge/index.tsx",
                lineNumber: 128,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_KnowledgeForm.default, {
                visible: createModalVisible,
                onVisibleChange: setCreateModalVisible,
                onSuccess: ()=>{
                    var _actionRef_current;
                    (_actionRef_current = actionRef.current) === null || _actionRef_current === void 0 || _actionRef_current.reload();
                    setCreateModalVisible(false);
                }
            }, void 0, false, {
                fileName: "src/pages/knowledge/index.tsx",
                lineNumber: 149,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_KnowledgeForm.default, {
                visible: editModalVisible,
                onVisibleChange: setEditModalVisible,
                initialValues: currentRecord,
                onSuccess: ()=>{
                    var _actionRef_current;
                    (_actionRef_current = actionRef.current) === null || _actionRef_current === void 0 || _actionRef_current.reload();
                    setEditModalVisible(false);
                }
            }, void 0, false, {
                fileName: "src/pages/knowledge/index.tsx",
                lineNumber: 158,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_KnowledgeDetail.default, {
                visible: detailModalVisible,
                onVisibleChange: setDetailModalVisible,
                data: currentRecord
            }, void 0, false, {
                fileName: "src/pages/knowledge/index.tsx",
                lineNumber: 168,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/knowledge/index.tsx",
        lineNumber: 127,
        columnNumber: 5
    }, this);
};
_s(KnowledgePage, "hTbNuNc6vHQfYm20aQVEQo9um4U=", false, function() {
    return [
        _max.useIntl
    ];
});
_c = KnowledgePage;
var _default = KnowledgePage;
var _c;
$RefreshReg$(_c, "KnowledgePage");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=p__knowledge__index-async.js.map