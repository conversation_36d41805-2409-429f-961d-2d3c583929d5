/**
 * 统一的AI模型管理API服务
 * 合并了原aiModels.ts和enhanced-aiModels.ts的功能
 * 提供完整的AI模型、模板、提供商管理功能
 */

import { httpRequest } from '@/utils/request';
import { toQueryString } from '@/utils/pageDataHandler';
import { getApiPrefix } from '@/utils/api';
import { ResponseHandleResult } from '@/types/response';

const API_PREFIX: string = getApiPrefix();

/**
 * ===========================================
 * AI模型核心CRUD操作
 * ===========================================
 */

/**
 * 获取AI模型列表（支持查询参数和分页）
 */
export async function getAIModels(params?: any): Promise<ResponseHandleResult<RwxAI.BackendPagedResponse<RwxAI.AIModel>>> {
  const url = params ? `${API_PREFIX}/AIModels/Paged?${toQueryString(params)}` : `${API_PREFIX}/AIModels/Paged`;
  return httpRequest.get<RwxAI.BackendPagedResponse<RwxAI.AIModel>>(url, {
    showErrorNotification: true
  });
}

/**
 * 获取所有AI模型列表（用于下拉框选择等场景）
 */
export async function getAllAIModels(): Promise<ResponseHandleResult<RwxAI.AIModel[]>> {
  return httpRequest.get<RwxAI.AIModel[]>(`${API_PREFIX}/AIModels`, {
    showErrorNotification: true
  });
}

/**
 * 根据ID获取AI模型详情
 */
export async function getAIModelById(id: string): Promise<ResponseHandleResult<RwxAI.AIModel>> {
  return httpRequest.get<RwxAI.AIModel>(`${API_PREFIX}/AIModels/${id}`, {
    showErrorNotification: true,
  });
}

/**
 * 创建AI模型
 */
export async function createAIModel(data: RwxAI.CreateAIModelRequest): Promise<ResponseHandleResult<RwxAI.AIModel>> {
  return httpRequest.post<RwxAI.AIModel>(`${API_PREFIX}/AIModels`, data, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: '创建AI模型成功',
  });
}

/**
 * 更新AI模型
 */
export async function updateAIModel(
  id: string,
  data: RwxAI.UpdateAIModelRequest
): Promise<ResponseHandleResult<RwxAI.AIModel>> {
  return httpRequest.put<RwxAI.AIModel>(`${API_PREFIX}/AIModels/${id}`, data, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: '更新AI模型成功',
  });
}

/**
 * 删除AI模型
 */
export async function deleteAIModel(id: string): Promise<ResponseHandleResult<void>> {
  return httpRequest.delete<void>(`${API_PREFIX}/AIModels/${id}`, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: '删除AI模型成功',
  });
}

/**
 * 批量删除AI模型
 */
export async function batchDeleteAIModels(ids: string[]): Promise<ResponseHandleResult<void>> {
  return httpRequest.post<void>(`${API_PREFIX}/AIModels/batch-delete`, { ids }, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: `成功删除 ${ids.length} 个AI模型`,
  });
}

/**
 * 测试AI模型连接
 */
export async function testAIModelConnection(id: string): Promise<ResponseHandleResult<RwxAI.TestConnectionResult>> {
  return httpRequest.post<RwxAI.TestConnectionResult>(`${API_PREFIX}/AIModels/${id}/test`, {}, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: 'AI模型连接测试成功',
  });
}

/**
 * 启用/禁用AI模型
 */
export async function toggleAIModelStatus(
  id: string,
  enabled: boolean
): Promise<ResponseHandleResult<RwxAI.AIModel>> {
  return httpRequest.patch<RwxAI.AIModel>(`${API_PREFIX}/AIModels/${id}/status`, { enabled }, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: enabled ? '启用AI模型成功' : '禁用AI模型成功',
  });
}

/**
 * AI模型模板相关API
 */

// 根据模型类型获取模型模板列表
export async function getAIModelTemplatesByType(modelType: RwxAI.ModelTypeEnum): Promise<ResponseHandleResult<RwxAI.AIModelTemplate[]>> {
  return httpRequest.get<RwxAI.AIModelTemplate[]>(`${API_PREFIX}/AIModelTemplates/by-type/${modelType}`, {
    showErrorNotification: true,
  });
}

// 根据提供商ID和模型类型获取模型模板列表
export async function getAIModelTemplatesByProviderAndType(params: {
  providerId?: string;
  modelType?: RwxAI.ModelTypeEnum;
}): Promise<ResponseHandleResult<RwxAI.AIModelTemplate[]>> {
  const queryParams = new URLSearchParams();
  if (params.providerId) queryParams.append('providerId', params.providerId);
  if (params.modelType) queryParams.append('modelType', params.modelType.toString());

  return httpRequest.get<RwxAI.AIModelTemplate[]>(`${API_PREFIX}/AIModelTemplates/by-provider-and-type?${queryParams}`, {
    showErrorNotification: true,
  });
}

// 获取模型模板详情
export async function getAIModelTemplateById(id: string): Promise<ResponseHandleResult<RwxAI.AIModelTemplate>> {
  return httpRequest.get<RwxAI.AIModelTemplate>(`${API_PREFIX}/AIModelTemplates/${id}`, {
    showErrorNotification: true,
  });
}

/**
 * AI服务提供商相关API
 */

// 获取所有启用的AI服务提供商
export async function getEnabledAIProviders(): Promise<ResponseHandleResult<RwxAI.AIProvider[]>> {
  return httpRequest.get<RwxAI.AIProvider[]>(`${API_PREFIX}/AIProviders/enabled`, {
    showErrorNotification: true,
  });
}

// 根据提供商ID获取对应的模型列表
export async function getModelsByProviderId(providerId: string): Promise<ResponseHandleResult<RwxAI.AIModelTemplate[]>> {
  return httpRequest.get<RwxAI.AIModelTemplate[]>(`${API_PREFIX}/AIProviders/${providerId}/models`, {
    showErrorNotification: true,
  });
}

// 根据提供商代码获取对应的模型列表
export async function getModelsByProviderCode(providerCode: string): Promise<ResponseHandleResult<RwxAI.AIModelTemplate[]>> {
  return httpRequest.get<RwxAI.AIModelTemplate[]>(`${API_PREFIX}/AIProviders/by-code/${providerCode}/models`, {
    showErrorNotification: true,
  });
}

// 根据提供商类型获取模型模板列表
export async function getModelsByProviderType(providerType: number): Promise<ResponseHandleResult<RwxAI.AIModelTemplate[]>> {
  return httpRequest.get<RwxAI.AIModelTemplate[]>(`${API_PREFIX}/AIProviders/type/${providerType}/models`, {
    showErrorNotification: true,
  });
}

// 获取所有启用的模型模板
export async function getAllEnabledModels(): Promise<ResponseHandleResult<RwxAI.AIModelTemplate[]>> {
  return httpRequest.get<RwxAI.AIModelTemplate[]>(`${API_PREFIX}/AIProviders/models/all`, {
    showErrorNotification: true,
  });
}

/**
 * ===========================================
 * 统一的API导出对象
 * ===========================================
 */

/**
 * AI模型管理API统一导出对象
 * 提供所有AI模型相关的API函数
 */
export const aiModelsApi = {
  // 核心CRUD操作
  getAIModels,
  getAllAIModels,
  getAIModelById,
  createAIModel,
  updateAIModel,
  deleteAIModel,

  // 增强功能
  batchDeleteAIModels,
  testAIModelConnection,
  toggleAIModelStatus,

  // 模板管理
  getAIModelTemplatesByType,
  getAIModelTemplatesByProviderAndType,
  getAIModelTemplateById,

  // 提供商管理
  getEnabledAIProviders,
  getModelsByProviderId,
  getModelsByProviderCode,
  getModelsByProviderType,
  getAllEnabledModels,
};

/**
 * 默认导出统一API对象（可选）
 */
export default aiModelsApi;
