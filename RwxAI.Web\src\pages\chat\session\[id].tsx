import React, { useState, useEffect, useRef } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import { Card, Input, Button, List, Avatar, Typography, Space, message, Spin } from 'antd';
import { SendOutlined, UserOutlined, RobotOutlined } from '@ant-design/icons';
import { useParams, useIntl, FormattedMessage } from '@umijs/max';
import { getChatSessionById, sendMessage } from '@/services/rwxai';

const { TextArea } = Input;
const { Text } = Typography;

const ChatSessionPage: React.FC = () => {
  const intl = useIntl();
  const { id } = useParams<{ id: string }>();
  const [session, setSession] = useState<RwxAI.ChatSession>();
  const [messages, setMessages] = useState<RwxAI.ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [loading, setLoading] = useState(false);
  const [sessionLoading, setSessionLoading] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (id) {
      loadSession();
    }
  }, [id]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const loadSession = async () => {
    try {
      setSessionLoading(true);
      const data = await getChatSessionById(id!);
      setSession(data);
      setMessages(data.Messages || []);
    } catch (error) {
      message.error(intl.formatMessage({ id: 'pages.chat.session.load.error' }));
    } finally {
      setSessionLoading(false);
    }
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim() || !session) return;

    const userMessage: RwxAI.ChatMessage = {
      Id: `temp-${Date.now()}`,
      Role: 'user',
      Content: inputValue,
      CreatedTime: new Date().toISOString(),
      SessionId: session.Id,
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setLoading(true);

    try {
      const response = await sendMessage(session.Id, {
        Content: inputValue,
        ModelId: session.ModelId,
        Temperature: session.Temperature,
        MaxTokens: session.MaxTokens,
      });

      setMessages(prev => [...prev, response]);
    } catch (error) {
      message.error(intl.formatMessage({ id: 'pages.chat.session.send.error' }));
      // 移除临时用户消息
      setMessages(prev => prev.filter(msg => msg.Id !== userMessage.Id));
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const renderMessage = (message: RwxAI.ChatMessage) => {
    const isUser = message.Role === 'user';
    return (
      <List.Item
        key={message.Id}
        style={{
          padding: '12px 0',
          justifyContent: isUser ? 'flex-end' : 'flex-start',
        }}
      >
        <div
          style={{
            display: 'flex',
            flexDirection: isUser ? 'row-reverse' : 'row',
            alignItems: 'flex-start',
            maxWidth: '80%',
          }}
        >
          <Avatar
            icon={isUser ? <UserOutlined /> : <RobotOutlined />}
            style={{
              backgroundColor: isUser ? '#1890ff' : '#52c41a',
              margin: isUser ? '0 0 0 8px' : '0 8px 0 0',
            }}
          />
          <div
            style={{
              backgroundColor: isUser ? '#e6f7ff' : '#f6ffed',
              padding: '8px 12px',
              borderRadius: '8px',
              border: `1px solid ${isUser ? '#91d5ff' : '#b7eb8f'}`,
            }}
          >
            <Text>{message.Content}</Text>
            <div style={{ marginTop: '4px', fontSize: '12px', color: '#999' }}>
              {message.CreatedTime ? new Date(message.CreatedTime).toLocaleTimeString() : ''}
            </div>
          </div>
        </div>
      </List.Item>
    );
  };

  if (sessionLoading) {
    return (
      <PageContainer>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer
      title={session?.Name}
      subTitle={session?.Model?.Name}
    >
      <Card
        style={{ height: 'calc(100vh - 200px)', display: 'flex', flexDirection: 'column' }}
        bodyStyle={{ flex: 1, display: 'flex', flexDirection: 'column', padding: 0 }}
      >
        <div
          style={{
            flex: 1,
            overflow: 'auto',
            padding: '16px',
            backgroundColor: '#fafafa',
          }}
        >
          <List
            dataSource={messages}
            renderItem={renderMessage}
            style={{ backgroundColor: 'transparent' }}
          />
          <div ref={messagesEndRef} />
        </div>
        
        <div style={{ padding: '16px', borderTop: '1px solid #f0f0f0' }}>
          <Space.Compact style={{ width: '100%' }}>
            <TextArea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder={intl.formatMessage({ id: 'pages.chat.session.input.placeholder' })}
              autoSize={{ minRows: 1, maxRows: 4 }}
              disabled={loading}
            />
            <Button
              type="primary"
              icon={<SendOutlined />}
              onClick={handleSendMessage}
              loading={loading}
              disabled={!inputValue.trim()}
            >
              <FormattedMessage id="pages.chat.session.send" />
            </Button>
          </Space.Compact>
        </div>
      </Card>
    </PageContainer>
  );
};

export default ChatSessionPage;
