((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] || []).push([
        ['p__chat__index'],
{ "src/pages/chat/index.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
var _protable = __mako_require__("node_modules/@ant-design/pro-table/es/index.js");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _rwxai = __mako_require__("src/services/rwxai/index.ts");
var _pageDataHandler = __mako_require__("src/utils/pageDataHandler.ts");
var _SessionForm = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/chat/components/SessionForm.tsx"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const ChatSessionsPage = ()=>{
    _s();
    const intl = (0, _max.useIntl)();
    const actionRef = (0, _react.useRef)();
    const [createModalVisible, setCreateModalVisible] = (0, _react.useState)(false);
    const [editModalVisible, setEditModalVisible] = (0, _react.useState)(false);
    const [currentRecord, setCurrentRecord] = (0, _react.useState)();
    const handleDelete = async (record)=>{
        _antd.Modal.confirm({
            title: intl.formatMessage({
                id: 'pages.chat.delete.confirm.title'
            }),
            content: intl.formatMessage({
                id: 'pages.chat.delete.confirm.content'
            }),
            onOk: async ()=>{
                try {
                    var _actionRef_current;
                    await (0, _rwxai.deleteChatSession)(record.Id);
                    _antd.message.success(intl.formatMessage({
                        id: 'pages.chat.delete.success'
                    }));
                    (_actionRef_current = actionRef.current) === null || _actionRef_current === void 0 || _actionRef_current.reload();
                } catch (error) {
                    _antd.message.error(intl.formatMessage({
                        id: 'pages.chat.delete.error'
                    }));
                }
            }
        });
    };
    const handleChat = (record)=>{
        _max.history.push(`/chat/session/${record.Id}`);
    };
    const columns = [
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.chat.table.name"
            }, void 0, false, {
                fileName: "src/pages/chat/index.tsx",
                lineNumber: 40,
                columnNumber: 14
            }, this),
            dataIndex: 'Name',
            key: 'Name',
            ellipsis: true
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.chat.table.model"
            }, void 0, false, {
                fileName: "src/pages/chat/index.tsx",
                lineNumber: 46,
                columnNumber: 14
            }, this),
            dataIndex: [
                'Model',
                'Name'
            ],
            key: 'Model',
            ellipsis: true,
            hideInSearch: true,
            render: (_, record)=>{
                var _record_Model, _record_Model1;
                // 优先使用 Model 对象中的信息
                if ((_record_Model = record.Model) === null || _record_Model === void 0 ? void 0 : _record_Model.Name) return record.Model.Name;
                if ((_record_Model1 = record.Model) === null || _record_Model1 === void 0 ? void 0 : _record_Model1.DisplayName) return record.Model.DisplayName;
                // 如果没有 Model 对象，尝试使用 ModelId
                if (record.ModelId) return record.ModelId;
                // 最后的默认值
                return '未配置模型';
            }
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.chat.table.status"
            }, void 0, false, {
                fileName: "src/pages/chat/index.tsx",
                lineNumber: 70,
                columnNumber: 14
            }, this),
            dataIndex: 'IsActive',
            key: 'IsActive',
            render: (_, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                    color: record.IsActive ? 'green' : 'default',
                    children: record.IsActive ? intl.formatMessage({
                        id: 'pages.chat.status.active'
                    }) : intl.formatMessage({
                        id: 'pages.chat.status.inactive'
                    })
                }, void 0, false, {
                    fileName: "src/pages/chat/index.tsx",
                    lineNumber: 74,
                    columnNumber: 9
                }, this)
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.chat.table.messageCount"
            }, void 0, false, {
                fileName: "src/pages/chat/index.tsx",
                lineNumber: 83,
                columnNumber: 14
            }, this),
            key: 'messageCount',
            hideInSearch: true,
            render: (_, record)=>{
                var _record_Messages;
                return ((_record_Messages = record.Messages) === null || _record_Messages === void 0 ? void 0 : _record_Messages.length) || 0;
            }
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.chat.table.temperature"
            }, void 0, false, {
                fileName: "src/pages/chat/index.tsx",
                lineNumber: 89,
                columnNumber: 14
            }, this),
            dataIndex: 'Temperature',
            key: 'Temperature',
            hideInSearch: true
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.chat.table.maxTokens"
            }, void 0, false, {
                fileName: "src/pages/chat/index.tsx",
                lineNumber: 95,
                columnNumber: 14
            }, this),
            dataIndex: 'MaxTokens',
            key: 'MaxTokens',
            hideInSearch: true
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.chat.table.createdTime"
            }, void 0, false, {
                fileName: "src/pages/chat/index.tsx",
                lineNumber: 101,
                columnNumber: 14
            }, this),
            dataIndex: 'CreatedTime',
            key: 'CreatedTime',
            valueType: 'dateTime',
            width: 180,
            hideInSearch: true
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.chat.table.actions"
            }, void 0, false, {
                fileName: "src/pages/chat/index.tsx",
                lineNumber: 109,
                columnNumber: 14
            }, this),
            key: 'actions',
            width: 200,
            render: (_, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "link",
                            size: "small",
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MessageOutlined, {}, void 0, false, {
                                fileName: "src/pages/chat/index.tsx",
                                lineNumber: 117,
                                columnNumber: 19
                            }, void 0),
                            onClick: ()=>handleChat(record),
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                id: "pages.chat.actions.chat"
                            }, void 0, false, {
                                fileName: "src/pages/chat/index.tsx",
                                lineNumber: 120,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/chat/index.tsx",
                            lineNumber: 114,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "link",
                            size: "small",
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                                fileName: "src/pages/chat/index.tsx",
                                lineNumber: 125,
                                columnNumber: 19
                            }, void 0),
                            onClick: ()=>{
                                setCurrentRecord(record);
                                setEditModalVisible(true);
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                id: "pages.chat.actions.edit"
                            }, void 0, false, {
                                fileName: "src/pages/chat/index.tsx",
                                lineNumber: 131,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/chat/index.tsx",
                            lineNumber: 122,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "link",
                            size: "small",
                            danger: true,
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                fileName: "src/pages/chat/index.tsx",
                                lineNumber: 137,
                                columnNumber: 19
                            }, void 0),
                            onClick: ()=>handleDelete(record),
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                id: "pages.chat.actions.delete"
                            }, void 0, false, {
                                fileName: "src/pages/chat/index.tsx",
                                lineNumber: 140,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/chat/index.tsx",
                            lineNumber: 133,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/chat/index.tsx",
                    lineNumber: 113,
                    columnNumber: 9
                }, this)
        }
    ];
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_protable.ProTable, {
                headerTitle: intl.formatMessage({
                    id: 'pages.chat.title'
                }),
                actionRef: actionRef,
                rowKey: "Id",
                search: {
                    labelWidth: 120
                },
                toolBarRender: ()=>[
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "primary",
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PlusOutlined, {}, void 0, false, {
                                fileName: "src/pages/chat/index.tsx",
                                lineNumber: 160,
                                columnNumber: 19
                            }, void 0),
                            onClick: ()=>setCreateModalVisible(true),
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                id: "pages.chat.actions.create"
                            }, void 0, false, {
                                fileName: "src/pages/chat/index.tsx",
                                lineNumber: 163,
                                columnNumber: 13
                            }, void 0)
                        }, "primary", false, {
                            fileName: "src/pages/chat/index.tsx",
                            lineNumber: 157,
                            columnNumber: 11
                        }, void 0)
                    ],
                request: (0, _pageDataHandler.createSimpleProTableRequest)(_rwxai.getChatSessions),
                columns: columns
            }, void 0, false, {
                fileName: "src/pages/chat/index.tsx",
                lineNumber: 149,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_SessionForm.default, {
                visible: createModalVisible,
                onVisibleChange: setCreateModalVisible,
                onSuccess: ()=>{
                    var _actionRef_current;
                    (_actionRef_current = actionRef.current) === null || _actionRef_current === void 0 || _actionRef_current.reload();
                    setCreateModalVisible(false);
                }
            }, void 0, false, {
                fileName: "src/pages/chat/index.tsx",
                lineNumber: 170,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_SessionForm.default, {
                visible: editModalVisible,
                onVisibleChange: setEditModalVisible,
                initialValues: currentRecord,
                onSuccess: ()=>{
                    var _actionRef_current;
                    (_actionRef_current = actionRef.current) === null || _actionRef_current === void 0 || _actionRef_current.reload();
                    setEditModalVisible(false);
                }
            }, void 0, false, {
                fileName: "src/pages/chat/index.tsx",
                lineNumber: 179,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/chat/index.tsx",
        lineNumber: 148,
        columnNumber: 5
    }, this);
};
_s(ChatSessionsPage, "R5X/uciyej3EU+Nn3XSJ0vWv9+Q=", false, function() {
    return [
        _max.useIntl
    ];
});
_c = ChatSessionsPage;
var _default = ChatSessionsPage;
var _c;
$RefreshReg$(_c, "ChatSessionsPage");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=p__chat__index-async.js.map