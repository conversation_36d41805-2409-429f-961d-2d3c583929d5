import React from 'react';
import { Modal, Descriptions, Tag, Switch, Button, Space, Card, Row, Col, Typography } from 'antd';
import { EditOutlined, CloseOutlined } from '@ant-design/icons';
import { useIntl, FormattedMessage } from '@umijs/max';

const { Title, Text } = Typography;

interface ModelDetailProps {
  visible: boolean;
  onVisibleChange: (visible: boolean) => void;
  onEdit?: () => void;
  data?: RwxAI.AIModel;
}

const ModelDetail: React.FC<ModelDetailProps> = ({
  visible,
  onVisibleChange,
  onEdit,
  data,
}) => {
  const intl = useIntl();

  const getModelTypeTag = (type?: string) => {
    const typeMap: Record<string, { text: string; color: string }> = {
      'Chat': { text: '对话模型', color: 'blue' },
      'Embedding': { text: '嵌入模型', color: 'green' },
      'TextToImage': { text: '文本生成图像模型', color: 'purple' },
      'ImageToText': { text: '图像描述模型', color: 'orange' },
    };
    const typeInfo = typeMap[type || ''];
    return <Tag color={typeInfo?.color}>{typeInfo?.text}</Tag>;
  };

  return (
    <Modal
      title={intl.formatMessage({ id: 'pages.aiModels.detail.title' })}
      open={visible}
      onCancel={() => onVisibleChange(false)}
      footer={
        <Space>
          <Button
            icon={<CloseOutlined />}
            onClick={() => onVisibleChange(false)}
          >
            <FormattedMessage id="pages.common.close" />
          </Button>
          {onEdit && (
            <Button
              type="primary"
              icon={<EditOutlined />}
              onClick={() => {
                onEdit();
                onVisibleChange(false);
              }}
            >
              <FormattedMessage id="pages.common.edit" />
            </Button>
          )}
        </Space>
      }
      width={900}
      style={{ top: 50 }}
      styles={{
        body: { maxHeight: '70vh', overflowY: 'auto', padding: '24px' }
      }}
    >
      {data && (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
          {/* 第一步：AI模型提供商和模板信息 */}
          <Card size="small" title={<FormattedMessage id="pages.aiModels.detail.templateInfo" />}>
            <Row gutter={16}>
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong><FormattedMessage id="pages.aiModels.detail.provider" />：</Text>
                  <Text>{data.Template?.Provider?.DisplayName || data.Template?.Provider?.Name || '-'}</Text>
                </div>
              </Col>
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong><FormattedMessage id="pages.aiModels.detail.modelId" />：</Text>
                  <Text>{data.Template?.ModelId || '-'}</Text>
                </div>
              </Col>
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong><FormattedMessage id="pages.aiModels.detail.modelType" />：</Text>
                  {getModelTypeTag(data.Template?.Type)}
                </div>
              </Col>
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong><FormattedMessage id="pages.aiModels.detail.endpoint" />：</Text>
                  <Text>{data.Endpoint || '-'}</Text>
                </div>
              </Col>
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong><FormattedMessage id="pages.aiModels.detail.supportsStreaming" />：</Text>
                  <Tag color={data.Template?.SupportsStreaming ? 'green' : 'red'}>
                    {data.Template?.SupportsStreaming ? '支持' : '不支持'}
                  </Tag>
                </div>
              </Col>
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong><FormattedMessage id="pages.aiModels.detail.supportsFunctionCalling" />：</Text>
                  <Tag color={data.Template?.SupportsFunctionCalling ? 'green' : 'red'}>
                    {data.Template?.SupportsFunctionCalling ? '支持' : '不支持'}
                  </Tag>
                </div>
              </Col>
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong><FormattedMessage id="pages.aiModels.detail.supportsVision" />：</Text>
                  <Tag color={data.Template?.SupportsVision ? 'green' : 'red'}>
                    {data.Template?.SupportsVision ? '支持' : '不支持'}
                  </Tag>
                </div>
              </Col>
            </Row>
          </Card>

          {/* 基本信息 */}
          <Card size="small" title={<FormattedMessage id="pages.aiModels.detail.basicInfo" />}>
            <Row gutter={16}>
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong><FormattedMessage id="pages.aiModels.detail.name" />：</Text>
                  <Text>{data.Name || '-'}</Text>
                </div>
              </Col>
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong><FormattedMessage id="pages.aiModels.detail.displayName" />：</Text>
                  <Text>{data.DisplayName || '-'}</Text>
                </div>
              </Col>
              <Col span={24}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong><FormattedMessage id="pages.aiModels.detail.description" />：</Text>
                  <Text>{data.Description || '-'}</Text>
                </div>
              </Col>
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong><FormattedMessage id="pages.aiModels.detail.apiKey" />：</Text>
                  <Text>{data.ApiKey ? '***' : '-'}</Text>
                </div>
              </Col>
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong><FormattedMessage id="pages.aiModels.detail.isDefault" />：</Text>
                  <Switch checked={data.IsDefault} disabled size="small" />
                </div>
              </Col>
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong><FormattedMessage id="pages.aiModels.detail.status" />：</Text>
                  <Switch checked={data.IsEnabled} disabled size="small" />
                </div>
              </Col>
            </Row>
          </Card>

          {/* 可配置参数 */}
          <Card size="small" title={<FormattedMessage id="pages.aiModels.detail.configParams" />}>
            <Row gutter={16}>
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong><FormattedMessage id="pages.aiModels.detail.maxTokens" />：</Text>
                  <Text>{data.MaxTokens || '-'}</Text>
                </div>
              </Col>
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong><FormattedMessage id="pages.aiModels.detail.temperature" />：</Text>
                  <Text>{data.Temperature || '-'}</Text>
                </div>
              </Col>
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong><FormattedMessage id="pages.aiModels.detail.maxContextLength" />：</Text>
                  <Text>{data.Template?.MaxContextLength || '-'}</Text>
                </div>
              </Col>
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong><FormattedMessage id="pages.aiModels.detail.maxOutputLength" />：</Text>
                  <Text>{data.Template?.MaxOutputLength || '-'}</Text>
                </div>
              </Col>
            </Row>
          </Card>

          {/* 其他信息 */}
          <Card size="small" title={<FormattedMessage id="pages.aiModels.detail.otherInfo" />}>
            <Row gutter={16}>
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong><FormattedMessage id="pages.aiModels.detail.createdTime" />：</Text>
                  <Text>{data.CreatedTime ? new Date(data.CreatedTime).toLocaleString() : '-'}</Text>
                </div>
              </Col>
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong><FormattedMessage id="pages.aiModels.detail.updatedTime" />：</Text>
                  <Text>{data.UpdatedTime ? new Date(data.UpdatedTime).toLocaleString() : '-'}</Text>
                </div>
              </Col>
              <Col span={24}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong><FormattedMessage id="pages.aiModels.detail.notes" />：</Text>
                  <Text>{data.Template?.Notes || '-'}</Text>
                </div>
              </Col>
            </Row>
          </Card>
        </div>
      )}
    </Modal>
  );
};

export default ModelDetail;
