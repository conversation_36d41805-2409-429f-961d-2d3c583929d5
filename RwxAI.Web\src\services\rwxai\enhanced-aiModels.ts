/**
 * 增强的AI模型管理API服务
 * 使用统一的响应处理系统
 */

import { httpRequest } from '@/utils/request';
import { getApiPrefix } from '@/utils/api';
import { ResponseHandleResult } from '@/types/response';

const API_PREFIX: string = getApiPrefix();

/**
 * 获取AI模型列表
 */
export async function getAIModels(): Promise<ResponseHandleResult<RwxAI.AIModel[]>> {
  return httpRequest.get<RwxAI.AIModel[]>(`${API_PREFIX}/AIModels`, {
    showErrorNotification: true,
  });
}

/**
 * 根据ID获取AI模型详情
 */
export async function getAIModelById(id: string): Promise<ResponseHandleResult<RwxAI.AIModel>> {
  return httpRequest.get<RwxAI.AIModel>(`${API_PREFIX}/AIModels/${id}`, {
    showErrorNotification: true,
  });
}

/**
 * 创建AI模型
 */
export async function createAIModel(data: RwxAI.CreateAIModelRequest): Promise<ResponseHandleResult<RwxAI.AIModel>> {
  return httpRequest.post<RwxAI.AIModel>(`${API_PREFIX}/AIModels`, data, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: '创建AI模型成功',
  });
}

/**
 * 更新AI模型
 */
export async function updateAIModel(
  id: string, 
  data: RwxAI.UpdateAIModelRequest
): Promise<ResponseHandleResult<RwxAI.AIModel>> {
  return httpRequest.put<RwxAI.AIModel>(`${API_PREFIX}/AIModels/${id}`, data, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: '更新AI模型成功',
  });
}

/**
 * 删除AI模型
 */
export async function deleteAIModel(id: string): Promise<ResponseHandleResult<void>> {
  return httpRequest.delete<void>(`${API_PREFIX}/AIModels/${id}`, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: '删除AI模型成功',
  });
}

/**
 * 批量删除AI模型
 */
export async function batchDeleteAIModels(ids: string[]): Promise<ResponseHandleResult<void>> {
  return httpRequest.post<void>(`${API_PREFIX}/AIModels/batch-delete`, { ids }, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: `成功删除 ${ids.length} 个AI模型`,
  });
}

/**
 * 获取AI模型提供商列表
 */
export async function getAIModelProviders(): Promise<ResponseHandleResult<RwxAI.AIModelProvider[]>> {
  return httpRequest.get<RwxAI.AIModelProvider[]>(`${API_PREFIX}/AIModels/providers`, {
    showErrorNotification: true,
  });
}

/**
 * 测试AI模型连接
 */
export async function testAIModelConnection(id: string): Promise<ResponseHandleResult<RwxAI.TestConnectionResult>> {
  return httpRequest.post<RwxAI.TestConnectionResult>(`${API_PREFIX}/AIModels/${id}/test`, {}, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: 'AI模型连接测试成功',
  });
}

/**
 * 启用/禁用AI模型
 */
export async function toggleAIModelStatus(
  id: string, 
  enabled: boolean
): Promise<ResponseHandleResult<RwxAI.AIModel>> {
  return httpRequest.patch<RwxAI.AIModel>(`${API_PREFIX}/AIModels/${id}/status`, { enabled }, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: enabled ? '启用AI模型成功' : '禁用AI模型成功',
  });
}

// 导出所有API函数
export const aiModelsApi = {
  getAIModels,
  getAIModelById,
  createAIModel,
  updateAIModel,
  deleteAIModel,
  batchDeleteAIModels,
  getAIModelProviders,
  testAIModelConnection,
  toggleAIModelStatus,
};
