{"openapi": "3.0.4", "info": {"title": "RwxAI API", "description": "RwxAI 智能助手系统 API 接口文档", "contact": {"name": "RwxAI Team", "email": "<EMAIL>"}, "version": "v1"}, "paths": {"/api/AIModels": {"get": {"tags": ["AIModels"], "summary": "获取所有AI模型列表", "responses": {"200": {"description": "成功返回AI模型列表", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AIModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AIModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AIModel"}}}}}, "500": {"description": "服务器内部错误"}}}, "post": {"tags": ["AIModels"], "summary": "创建新的AI模型", "requestBody": {"description": "AI模型信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AIModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AIModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AIModel"}}}}, "responses": {"201": {"description": "成功创建AI模型", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AIModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AIModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AIModel"}}}}, "400": {"description": "请求参数无效", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "服务器内部错误"}}}, "put": {"tags": ["AIModels"], "summary": "更新AI模型信息", "requestBody": {"description": "更新的AI模型信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AIModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AIModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AIModel"}}}}, "responses": {"204": {"description": "成功更新AI模型"}, "400": {"description": "请求参数无效", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "404": {"description": "未找到指定的AI模型", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "服务器内部错误"}}}}, "/api/AIModels/{id}": {"get": {"tags": ["AIModels"], "summary": "根据ID获取指定AI模型", "parameters": [{"name": "id", "in": "path", "description": "AI模型ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "成功返回AI模型详情", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AIModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AIModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AIModel"}}}}, "404": {"description": "未找到指定的AI模型", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "服务器内部错误"}}}, "delete": {"tags": ["AIModels"], "summary": "删除指定的AI模型", "parameters": [{"name": "id", "in": "path", "description": "AI模型ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"204": {"description": "成功删除AI模型"}, "404": {"description": "未找到指定的AI模型", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "服务器内部错误"}}}}, "/api/AIModelTemplates/by-type/{modelType}": {"get": {"tags": ["AIModelTemplates"], "summary": "根据模型类型获取模型模板列表", "parameters": [{"name": "modelType", "in": "path", "description": "模型类型", "required": true, "schema": {"$ref": "#/components/schemas/ModelTypeEnum"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AIModelTemplate"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AIModelTemplate"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AIModelTemplate"}}}}}}}}, "/api/AIModelTemplates/by-provider-and-type": {"get": {"tags": ["AIModelTemplates"], "summary": "根据提供商ID和模型类型获取模型模板列表", "parameters": [{"name": "providerId", "in": "query", "description": "提供商ID", "schema": {"type": "string", "format": "uuid"}}, {"name": "modelType", "in": "query", "description": "模型类型", "schema": {"$ref": "#/components/schemas/ModelTypeEnum"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AIModelTemplate"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AIModelTemplate"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AIModelTemplate"}}}}}}}}, "/api/AIModelTemplates/{id}": {"get": {"tags": ["AIModelTemplates"], "summary": "获取模型模板详情", "parameters": [{"name": "id", "in": "path", "description": "模型模板ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AIModelTemplate"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AIModelTemplate"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AIModelTemplate"}}}}}}}, "/api/AIProviders/enabled": {"get": {"tags": ["AIProviders"], "summary": "获取所有启用的AI服务提供商", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AIProvider"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AIProvider"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AIProvider"}}}}}}}}, "/api/AIProviders/{providerId}/models": {"get": {"tags": ["AIProviders"], "summary": "根据提供商ID获取对应的模型列表", "parameters": [{"name": "providerId", "in": "path", "description": "提供商ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AIModelTemplate"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AIModelTemplate"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AIModelTemplate"}}}}}}}}, "/api/AIProviders/by-code/{providerCode}/models": {"get": {"tags": ["AIProviders"], "summary": "根据提供商代码获取对应的模型列表", "parameters": [{"name": "providerCode", "in": "path", "description": "提供商代码", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AIModelTemplate"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AIModelTemplate"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AIModelTemplate"}}}}}}}}, "/api/AIProviders/type/{providerType}/models": {"get": {"tags": ["AIProviders"], "summary": "根据提供商类型获取模型模板列表", "parameters": [{"name": "providerType", "in": "path", "description": "提供商类型", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AIModelTemplate"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AIModelTemplate"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AIModelTemplate"}}}}}}}}, "/api/AIProviders/models/all": {"get": {"tags": ["AIProviders"], "summary": "获取所有启用的模型模板", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AIModelTemplate"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AIModelTemplate"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AIModelTemplate"}}}}}}}}, "/api/Apps": {"get": {"tags": ["Apps"], "summary": "获取所有应用", "responses": {"200": {"description": "成功返回应用列表", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/App"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/App"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/App"}}}}}, "401": {"description": "未授权访问", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "403": {"description": "权限不足，需要管理员权限", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "服务器内部错误"}}}, "post": {"tags": ["Apps"], "summary": "创建新应用", "requestBody": {"description": "新应用信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/App"}}, "text/json": {"schema": {"$ref": "#/components/schemas/App"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/App"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/App"}}, "application/json": {"schema": {"$ref": "#/components/schemas/App"}}, "text/json": {"schema": {"$ref": "#/components/schemas/App"}}}}}}}, "/api/Apps/my": {"get": {"tags": ["Apps"], "summary": "获取当前用户的所有应用", "responses": {"200": {"description": "成功返回用户应用列表", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/App"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/App"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/App"}}}}}, "401": {"description": "未授权访问", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "服务器内部错误"}}}}, "/api/Apps/{id}": {"get": {"tags": ["Apps"], "summary": "获取指定应用详情", "parameters": [{"name": "id", "in": "path", "description": "应用ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "成功返回应用详情", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/App"}}, "application/json": {"schema": {"$ref": "#/components/schemas/App"}}, "text/json": {"schema": {"$ref": "#/components/schemas/App"}}}}, "401": {"description": "未授权访问", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "403": {"description": "权限不足", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "404": {"description": "未找到指定应用", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "服务器内部错误"}}}, "put": {"tags": ["Apps"], "summary": "更新应用信息", "parameters": [{"name": "id", "in": "path", "description": "应用ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"description": "更新的应用信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/App"}}, "text/json": {"schema": {"$ref": "#/components/schemas/App"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/App"}}}}, "responses": {"200": {"description": "Success"}}}, "delete": {"tags": ["Apps"], "summary": "删除应用", "parameters": [{"name": "id", "in": "path", "description": "应用ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Apps/{id}/regenerate-keys": {"post": {"tags": ["Apps"], "summary": "重新生成API密钥", "parameters": [{"name": "id", "in": "path", "description": "应用ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/App"}}, "application/json": {"schema": {"$ref": "#/components/schemas/App"}}, "text/json": {"schema": {"$ref": "#/components/schemas/App"}}}}}}}, "/api/Apps/{id}/status": {"patch": {"tags": ["Apps"], "summary": "修改应用状态", "parameters": [{"name": "id", "in": "path", "description": "应用ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"description": "状态更新请求", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AppStatusUpdateRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AppStatusUpdateRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AppStatusUpdateRequest"}}}}, "responses": {"200": {"description": "成功更新应用状态", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/App"}}, "application/json": {"schema": {"$ref": "#/components/schemas/App"}}, "text/json": {"schema": {"$ref": "#/components/schemas/App"}}}}, "400": {"description": "请求参数无效", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "未授权访问", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "403": {"description": "权限不足", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "404": {"description": "未找到指定的应用", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "服务器内部错误"}}}}, "/api/Apps/{id}/reset-api-calls": {"post": {"tags": ["Apps"], "summary": "重置API调用计数", "parameters": [{"name": "id", "in": "path", "description": "应用ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "用户登录接口，验证用户凭据并返回JWT令牌", "requestBody": {"description": "登录请求，包含用户名和密码", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"200": {"description": "登录成功，返回JWT令牌和用户信息"}, "400": {"description": "请求参数无效", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "用户名或密码错误", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "服务器内部错误"}}}}, "/api/Auth/register": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "用户注册接口，创建新的用户账户", "requestBody": {"description": "注册请求，包含用户名、邮箱、密码和个人信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Auth/refresh-token": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "刷新令牌接口，使用刷新令牌获取新的JWT令牌", "requestBody": {"description": "刷新令牌请求，包含刷新令牌字符串", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RefreshTokenRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Auth/change-password": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "修改密码接口，需要用户已登录且验证当前密码", "requestBody": {"description": "密码修改请求，包含当前密码和新密码", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Chat/sessions": {"get": {"tags": ["Cha<PERSON>"], "summary": "获取所有聊天会话", "responses": {"200": {"description": "成功返回聊天会话列表", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ChatSession"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ChatSession"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ChatSession"}}}}}, "401": {"description": "未授权访问", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "服务器内部错误"}}}, "post": {"tags": ["Cha<PERSON>"], "summary": "创建新的聊天会话", "requestBody": {"description": "会话信息，包含名称等属性", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatSession"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChatSession"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChatSession"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ChatSession"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ChatSession"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChatSession"}}}}}}}, "/api/Chat/sessions/{id}": {"get": {"tags": ["Cha<PERSON>"], "summary": "获取指定ID的聊天会话详情", "parameters": [{"name": "id", "in": "path", "description": "会话ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ChatSession"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ChatSession"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChatSession"}}}}}}, "put": {"tags": ["Cha<PERSON>"], "summary": "更新现有聊天会话的信息", "parameters": [{"name": "id", "in": "path", "description": "会话ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"description": "更新后的会话信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatSession"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChatSession"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChatSession"}}}}, "responses": {"200": {"description": "Success"}}}, "delete": {"tags": ["Cha<PERSON>"], "summary": "删除指定ID的聊天会话", "parameters": [{"name": "id", "in": "path", "description": "会话ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Chat/sessions/{id}/messages": {"post": {"tags": ["Cha<PERSON>"], "summary": "发送消息并获取AI回复", "parameters": [{"name": "id", "in": "path", "description": "会话ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"description": "消息请求，包含消息内容和可选附件", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatMessageRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChatMessageRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChatMessageRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ChatMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ChatMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChatMessage"}}}}}}}, "/api/Chat/sessions/{id}/messages/stream": {"post": {"tags": ["Cha<PERSON>"], "summary": "发送消息并以流式方式获取AI回复", "parameters": [{"name": "id", "in": "path", "description": "会话ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"description": "消息请求，包含消息内容和可选附件", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatMessageRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChatMessageRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChatMessageRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Knowledge": {"get": {"tags": ["Knowledge"], "summary": "获取所有知识库列表", "responses": {"200": {"description": "成功返回知识库列表", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Knowledge"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Knowledge"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Knowledge"}}}}}, "401": {"description": "未授权访问", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "服务器内部错误"}}}, "post": {"tags": ["Knowledge"], "summary": "创建新的知识库", "requestBody": {"description": "知识库信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Knowledge"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Knowledge"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Knowledge"}}}}, "responses": {"201": {"description": "成功创建知识库", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Knowledge"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Knowledge"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Knowledge"}}}}, "400": {"description": "请求参数无效", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "未授权访问", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "服务器内部错误"}}}}, "/api/Knowledge/{id}": {"get": {"tags": ["Knowledge"], "summary": "根据ID获取指定知识库", "parameters": [{"name": "id", "in": "path", "description": "知识库ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "成功返回知识库详情", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Knowledge"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Knowledge"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Knowledge"}}}}, "401": {"description": "未授权访问", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "404": {"description": "未找到指定的知识库", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "服务器内部错误"}}}, "put": {"tags": ["Knowledge"], "summary": "更新知识库信息", "parameters": [{"name": "id", "in": "path", "description": "知识库ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"description": "更新的知识库信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Knowledge"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Knowledge"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Knowledge"}}}}, "responses": {"204": {"description": "成功更新知识库"}, "400": {"description": "请求参数无效", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "未授权访问", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "404": {"description": "未找到指定的知识库", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "服务器内部错误"}}}, "delete": {"tags": ["Knowledge"], "summary": "删除指定的知识库", "parameters": [{"name": "id", "in": "path", "description": "知识库ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"204": {"description": "成功删除知识库"}, "401": {"description": "未授权访问", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "404": {"description": "未找到指定的知识库", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "服务器内部错误"}}}}, "/api/Knowledge/{id}/files": {"get": {"tags": ["Knowledge"], "summary": "获取知识库中的所有文件", "parameters": [{"name": "id", "in": "path", "description": "知识库ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "成功返回文件列表", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/KnowledgeFile"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/KnowledgeFile"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/KnowledgeFile"}}}}}, "401": {"description": "未授权访问", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "404": {"description": "未找到指定的知识库", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "服务器内部错误"}}}, "post": {"tags": ["Knowledge"], "summary": "上传文件到知识库", "parameters": [{"name": "id", "in": "path", "description": "知识库ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "成功上传文件", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/KnowledgeFile"}}, "application/json": {"schema": {"$ref": "#/components/schemas/KnowledgeFile"}}, "text/json": {"schema": {"$ref": "#/components/schemas/KnowledgeFile"}}}}, "400": {"description": "请求参数无效或文件格式不支持", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "未授权访问", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "404": {"description": "未找到指定的知识库", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "服务器内部错误"}}}}, "/api/Knowledge/files/{fileId}/process": {"post": {"tags": ["Knowledge"], "summary": "处理知识库文件（解析和向量化）", "parameters": [{"name": "fileId", "in": "path", "description": "文件ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "成功开始处理文件"}, "400": {"description": "请求参数无效或处理失败", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "未授权访问", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "404": {"description": "未找到指定的文件", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "服务器内部错误"}}}}, "/api/Knowledge/files/{fileId}": {"delete": {"tags": ["Knowledge"], "summary": "删除知识库文件", "parameters": [{"name": "fileId", "in": "path", "description": "文件ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"204": {"description": "成功删除文件"}, "400": {"description": "请求参数无效", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "未授权访问", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "404": {"description": "未找到指定的文件", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "服务器内部错误"}}}}, "/api/Plugins": {"get": {"tags": ["Plugins"], "summary": "获取所有可用插件的列表", "responses": {"200": {"description": "成功返回插件列表", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Plugin"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Plugin"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Plugin"}}}}}, "401": {"description": "未授权访问", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "服务器内部错误"}}}, "post": {"tags": ["Plugins"], "summary": "上传并注册插件DLL文件", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/Plugins/{id}": {"get": {"tags": ["Plugins"], "summary": "根据ID获取插件详情", "parameters": [{"name": "id", "in": "path", "description": "插件ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "成功返回插件详情", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Plugin"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Plugin"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Plugin"}}}}, "401": {"description": "未授权访问", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "404": {"description": "未找到指定的插件", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "服务器内部错误"}}}, "delete": {"tags": ["Plugins"], "summary": "删除插件", "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/Plugins/byname/{name}": {"get": {"tags": ["Plugins"], "summary": "根据名称获取插件详情", "parameters": [{"name": "name", "in": "path", "description": "插件名称", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "成功返回插件详情", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Plugin"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Plugin"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Plugin"}}}}, "401": {"description": "未授权访问", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "404": {"description": "未找到指定的插件", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "服务器内部错误"}}}}, "/api/Plugins/enable/{pluginId}": {"put": {"tags": ["Plugins"], "summary": "启用插件", "parameters": [{"name": "pluginId", "in": "path", "description": "", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/Plugins/disable/{pluginId}": {"put": {"tags": ["Plugins"], "summary": "禁用插件", "parameters": [{"name": "pluginId", "in": "path", "description": "", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/Plugins/update": {"put": {"tags": ["Plugins"], "summary": "更新插件", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Plugin"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Plugin"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Plugin"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}}, "components": {"schemas": {"AIModel": {"type": "object", "properties": {"Id": {"type": "string", "format": "uuid"}, "CreatedTime": {"type": "string", "format": "date-time"}, "UpdatedTime": {"type": "string", "format": "date-time"}, "IsDeleted": {"type": "boolean"}, "Name": {"type": "string", "nullable": true}, "Provider": {"type": "string", "nullable": true}, "ModelId": {"type": "string", "nullable": true}, "ApiKey": {"type": "string", "nullable": true}, "Endpoint": {"type": "string", "nullable": true}, "IsEnabled": {"type": "boolean"}, "IsDefault": {"type": "boolean"}, "Type": {"$ref": "#/components/schemas/ModelTypeEnum"}, "MaxTokens": {"type": "integer", "format": "int32"}, "Temperature": {"type": "number", "format": "float"}, "Description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AIModelTemplate": {"type": "object", "properties": {"Id": {"type": "string", "format": "uuid"}, "CreatedTime": {"type": "string", "format": "date-time"}, "UpdatedTime": {"type": "string", "format": "date-time"}, "IsDeleted": {"type": "boolean"}, "ProviderId": {"type": "string", "format": "uuid"}, "Name": {"type": "string", "nullable": true}, "DisplayName": {"type": "string", "nullable": true}, "ModelId": {"type": "string", "nullable": true}, "Type": {"$ref": "#/components/schemas/ModelTypeEnum"}, "Description": {"type": "string", "nullable": true}, "MaxContextLength": {"type": "integer", "format": "int32"}, "MaxOutputLength": {"type": "integer", "format": "int32"}, "DefaultTemperature": {"type": "number", "format": "float"}, "SupportsStreaming": {"type": "boolean"}, "SupportsFunctionCalling": {"type": "boolean"}, "SupportsVision": {"type": "boolean"}, "InputPricePerMillion": {"type": "number", "format": "double"}, "OutputPricePerMillion": {"type": "number", "format": "double"}, "IsEnabled": {"type": "boolean"}, "SortOrder": {"type": "integer", "format": "int32"}, "ReleaseDate": {"type": "string", "format": "date-time", "nullable": true}, "TrainingDataCutoff": {"type": "string", "format": "date-time", "nullable": true}, "Notes": {"type": "string", "nullable": true}, "Provider": {"$ref": "#/components/schemas/AIProvider"}}, "additionalProperties": false}, "AIProvider": {"type": "object", "properties": {"Id": {"type": "string", "format": "uuid"}, "CreatedTime": {"type": "string", "format": "date-time"}, "UpdatedTime": {"type": "string", "format": "date-time"}, "IsDeleted": {"type": "boolean"}, "ProviderType": {"$ref": "#/components/schemas/AIProviderEnum"}, "Name": {"type": "string", "nullable": true}, "DisplayName": {"type": "string", "nullable": true}, "Code": {"type": "string", "nullable": true}, "Description": {"type": "string", "nullable": true}, "Website": {"type": "string", "nullable": true}, "ApiDocumentationUrl": {"type": "string", "nullable": true}, "DefaultApiEndpoint": {"type": "string", "nullable": true}, "IsEnabled": {"type": "boolean"}, "SortOrder": {"type": "integer", "format": "int32"}, "RequiresApiKey": {"type": "boolean"}, "IconUrl": {"type": "string", "nullable": true}, "Notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AIProviderEnum": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 99], "type": "integer", "format": "int32"}, "App": {"type": "object", "properties": {"Id": {"type": "string", "format": "uuid"}, "CreatedTime": {"type": "string", "format": "date-time"}, "UpdatedTime": {"type": "string", "format": "date-time"}, "IsDeleted": {"type": "boolean"}, "Name": {"type": "string", "nullable": true}, "IconUrl": {"type": "string", "nullable": true}, "Description": {"type": "string", "nullable": true}, "OwnerId": {"type": "string", "format": "uuid"}, "ModelId": {"type": "string", "format": "uuid"}, "IsEnabled": {"type": "boolean"}, "Status": {"$ref": "#/components/schemas/AppStatusEnum"}, "ApiKey": {"type": "string", "nullable": true}, "ApiSecret": {"type": "string", "nullable": true}, "McpPluginList": {"type": "string", "nullable": true}, "LogicAppList": {"type": "string", "nullable": true}, "ApiPluginList": {"type": "string", "nullable": true}, "PluginFunctionList": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AppStatusEnum": {"enum": [0, 1, 2, 3], "type": "integer", "format": "int32"}, "AppStatusUpdateRequest": {"type": "object", "properties": {"Status": {"$ref": "#/components/schemas/AppStatusEnum"}}, "additionalProperties": false, "description": "应用状态更新请求"}, "ChangePasswordRequest": {"type": "object", "properties": {"CurrentPassword": {"type": "string", "description": "当前密码", "nullable": true}, "NewPassword": {"type": "string", "description": "新密码", "nullable": true}}, "additionalProperties": false, "description": "修改密码请求模型，用于更改用户密码"}, "ChatMessage": {"type": "object", "properties": {"Id": {"type": "string", "format": "uuid"}, "CreatedTime": {"type": "string", "format": "date-time"}, "UpdatedTime": {"type": "string", "format": "date-time"}, "IsDeleted": {"type": "boolean"}, "ChatSessionId": {"type": "string", "format": "uuid"}, "Role": {"type": "string", "nullable": true}, "Content": {"type": "string", "nullable": true}, "Timestamp": {"type": "string", "format": "date-time"}, "Context": {"type": "string", "nullable": true}, "Metadata": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ChatMessageRequest": {"type": "object", "properties": {"Message": {"type": "string", "description": "消息文本内容", "nullable": true}, "Attachments": {"type": "array", "items": {"$ref": "#/components/schemas/MessageAttachment"}, "description": "可选的消息附件列表（文件、图片等）", "nullable": true}}, "additionalProperties": false, "description": "聊天消息请求模型，用于发送新消息"}, "ChatSession": {"type": "object", "properties": {"Id": {"type": "string", "format": "uuid"}, "CreatedTime": {"type": "string", "format": "date-time"}, "UpdatedTime": {"type": "string", "format": "date-time"}, "IsDeleted": {"type": "boolean"}, "Name": {"type": "string", "nullable": true}, "UserId": {"type": "string", "format": "uuid"}, "AppId": {"type": "string", "format": "uuid"}, "SystemPrompt": {"type": "string", "nullable": true}, "KnowledgeId": {"type": "string", "format": "uuid", "nullable": true}, "EnableReference": {"type": "boolean"}, "Settings": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Knowledge": {"type": "object", "properties": {"Id": {"type": "string", "format": "uuid"}, "CreatedTime": {"type": "string", "format": "date-time"}, "UpdatedTime": {"type": "string", "format": "date-time"}, "IsDeleted": {"type": "boolean"}, "Name": {"type": "string", "nullable": true}, "Description": {"type": "string", "nullable": true}, "EmbeddingModelId": {"type": "string", "nullable": true}, "ChunkSize": {"type": "integer", "format": "int32"}, "ChunkOverlap": {"type": "integer", "format": "int32"}, "Title": {"type": "string", "nullable": true}, "Content": {"type": "string", "nullable": true}, "ContentType": {"type": "string", "nullable": true}, "MetadataJson": {"type": "string", "nullable": true}, "KnowledgeBaseId": {"type": "string", "format": "uuid", "nullable": true}, "VectorId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "KnowledgeFile": {"type": "object", "properties": {"Id": {"type": "string", "format": "uuid"}, "CreatedTime": {"type": "string", "format": "date-time"}, "UpdatedTime": {"type": "string", "format": "date-time"}, "IsDeleted": {"type": "boolean"}, "KnowledgeId": {"type": "string", "format": "uuid"}, "FileName": {"type": "string", "nullable": true}, "FilePath": {"type": "string", "nullable": true}, "ContentType": {"type": "string", "nullable": true}, "FileSize": {"type": "integer", "format": "int64"}, "IsProcessed": {"type": "boolean"}, "ProcessedTime": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "LoginRequest": {"type": "object", "properties": {"Username": {"type": "string", "description": "用户名或邮箱", "nullable": true}, "Password": {"type": "string", "description": "用户密码", "nullable": true}}, "additionalProperties": false, "description": "登录请求模型，用于用户登录"}, "MessageAttachment": {"type": "object", "properties": {"Id": {"type": "string", "format": "uuid"}, "CreatedTime": {"type": "string", "format": "date-time"}, "UpdatedTime": {"type": "string", "format": "date-time"}, "IsDeleted": {"type": "boolean"}, "ChatMessageId": {"type": "string", "format": "uuid"}, "FileName": {"type": "string", "nullable": true}, "ContentType": {"type": "string", "nullable": true}, "FilePath": {"type": "string", "nullable": true}, "FileSize": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "ModelTypeEnum": {"enum": [0, 1, 2, 3], "type": "integer", "format": "int32"}, "Plugin": {"type": "object", "properties": {"Id": {"type": "string", "format": "uuid"}, "CreatedTime": {"type": "string", "format": "date-time"}, "UpdatedTime": {"type": "string", "format": "date-time"}, "IsDeleted": {"type": "boolean"}, "Name": {"type": "string", "nullable": true}, "Description": {"type": "string", "nullable": true}, "Version": {"type": "string", "nullable": true}, "Author": {"type": "string", "nullable": true}, "AssemblyPath": {"type": "string", "nullable": true}, "TypeName": {"type": "string", "nullable": true}, "Status": {"type": "string", "nullable": true}, "IsEnabled": {"type": "boolean"}, "Functions": {"type": "array", "items": {"$ref": "#/components/schemas/PluginFunction"}, "nullable": true}}, "additionalProperties": false}, "PluginFunction": {"type": "object", "properties": {"Id": {"type": "string", "format": "uuid"}, "CreatedTime": {"type": "string", "format": "date-time"}, "UpdatedTime": {"type": "string", "format": "date-time"}, "IsDeleted": {"type": "boolean"}, "PluginId": {"type": "string", "format": "uuid"}, "Name": {"type": "string", "nullable": true}, "Description": {"type": "string", "nullable": true}, "MethodName": {"type": "string", "nullable": true}, "ReturnType": {"type": "string", "nullable": true}, "Parameters": {"type": "array", "items": {"$ref": "#/components/schemas/PluginParameterInfo"}, "nullable": true}}, "additionalProperties": false}, "PluginParameterInfo": {"type": "object", "properties": {"Name": {"type": "string", "nullable": true}, "Description": {"type": "string", "nullable": true}, "Type": {"type": "string", "nullable": true}, "IsRequired": {"type": "boolean"}, "DefaultValue": {"type": "string", "nullable": true}, "Order": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ProblemDetails": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "detail": {"type": "string", "nullable": true}, "instance": {"type": "string", "nullable": true}}, "additionalProperties": {}}, "RefreshTokenRequest": {"type": "object", "properties": {"RefreshToken": {"type": "string", "description": "刷新令牌字符串", "nullable": true}}, "additionalProperties": false, "description": "刷新令牌请求模型，用于刷新JWT令牌"}, "RegisterRequest": {"type": "object", "properties": {"Username": {"type": "string", "description": "用户名", "nullable": true}, "Email": {"type": "string", "description": "电子邮箱", "nullable": true}, "Password": {"type": "string", "description": "用户密码", "nullable": true}, "FirstName": {"type": "string", "description": "用户名字", "nullable": true}, "LastName": {"type": "string", "description": "用户姓氏", "nullable": true}}, "additionalProperties": false, "description": "注册请求模型，用于创建新用户"}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization header using the <PERSON><PERSON> scheme. Example: \"Authorization: Bearer {token}\"", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}