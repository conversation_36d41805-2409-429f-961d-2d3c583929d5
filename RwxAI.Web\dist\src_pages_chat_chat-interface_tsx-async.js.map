{"version": 3, "sources": ["src/pages/chat/chat-interface.tsx", "src/pages/chat/components/ChatInterface.tsx"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport { Modal, Button, Space } from 'antd';\nimport { useIntl, FormattedMessage } from '@umijs/max';\nimport ChatInterface from './components/ChatInterface';\nimport SessionForm from './components/SessionForm';\n\nconst ChatInterfacePage: React.FC = () => {\n  const intl = useIntl();\n  const [sessionFormVisible, setSessionFormVisible] = useState(false);\n  const [editingSession, setEditingSession] = useState<RwxAI.ChatSession | null>(null);\n  const [formLoading, setFormLoading] = useState(false);\n  const formRef = useRef<any>(null);\n\n  const handleCreateSession = () => {\n    setEditingSession(null);\n    setSessionFormVisible(true);\n  };\n\n  const handleEditSession = (session: RwxAI.ChatSession) => {\n    setEditingSession(session);\n    setSessionFormVisible(true);\n  };\n\n  const handleSessionFormClose = () => {\n    setSessionFormVisible(false);\n    setEditingSession(null);\n    setFormLoading(false);\n  };\n\n  const handleSessionFormSuccess = () => {\n    setSessionFormVisible(false);\n    setEditingSession(null);\n    setFormLoading(false);\n    // 这里可以触发聊天界面的刷新\n    window.location.reload();\n  };\n\n  const handleFormSubmit = () => {\n    if (formRef.current) {\n      setFormLoading(true);\n      formRef.current.submit();\n    }\n  };\n\n  return (\n    <div style={{ height: '100vh', overflow: 'hidden' }}>\n      <ChatInterface\n        onCreateSession={handleCreateSession}\n        onEditSession={handleEditSession}\n      />\n      \n      <Modal\n        title={editingSession ? '编辑会话' : '创建新会话'}\n        open={sessionFormVisible}\n        onCancel={handleSessionFormClose}\n        onOk={handleFormSubmit}\n        confirmLoading={formLoading}\n        width={600}\n        footer={[\n          <Button key=\"cancel\" onClick={handleSessionFormClose}>\n            取消\n          </Button>,\n          <Button\n            key=\"submit\"\n            type=\"primary\"\n            loading={formLoading}\n            onClick={handleFormSubmit}\n          >\n            {editingSession ? '更新' : '创建'}\n          </Button>,\n        ]}\n      >\n        <SessionForm\n          ref={formRef}\n          visible={sessionFormVisible}\n          onVisibleChange={setSessionFormVisible}\n          onSuccess={handleSessionFormSuccess}\n          initialValues={editingSession}\n        />\n      </Modal>\n    </div>\n  );\n};\n\nexport default ChatInterfacePage;\n", "import React, { useState, useEffect, useRef } from 'react';\nimport { \n  Layout, \n  List, \n  Button, \n  Typography, \n  Space, \n  Avatar, \n  Card,\n  Divider,\n  Badge,\n  Tooltip,\n  Input,\n  message\n} from 'antd';\nimport {\n  Conversations,\n  Bubble,\n  Sender,\n  Welcome,\n  Prompts\n} from '@ant-design/x';\nimport {\n  PlusOutlined,\n  MessageOutlined,\n  DeleteOutlined,\n  EditOutlined,\n  MoreOutlined,\n  UserOutlined,\n  RobotOutlined\n} from '@ant-design/icons';\nimport { useIntl, FormattedMessage } from '@umijs/max';\nimport { getChatSessions, deleteChatSession, sendMessage } from '@/services/rwxai';\n\nconst { Sider, Content } = Layout;\nconst { Text, Title } = Typography;\n\ninterface ChatInterfaceProps {\n  onCreateSession?: () => void;\n  onEditSession?: (session: RwxAI.ChatSession) => void;\n}\n\nconst ChatInterface: React.FC<ChatInterfaceProps> = ({\n  onCreateSession,\n  onEditSession\n}) => {\n  const intl = useIntl();\n  const [sessions, setSessions] = useState<RwxAI.ChatSession[]>([]);\n  const [currentSession, setCurrentSession] = useState<RwxAI.ChatSession | null>(null);\n  const [messages, setMessages] = useState<any[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [sessionsLoading, setSessionsLoading] = useState(true);\n\n  // 加载会话列表\n  const loadSessions = async () => {\n    try {\n      setSessionsLoading(true);\n      const response = await getChatSessions();\n      if (response.success) {\n        setSessions(response.data || []);\n        // 如果有会话且没有选中的会话，选中第一个\n        if (response.data?.length > 0 && !currentSession) {\n          setCurrentSession(response.data[0]);\n        }\n      }\n    } catch (error) {\n      message.error('加载会话列表失败');\n    } finally {\n      setSessionsLoading(false);\n    }\n  };\n\n  // 加载消息\n  const loadMessages = (session: RwxAI.ChatSession) => {\n    if (session.Messages) {\n      const formattedMessages = session.Messages.map(msg => ({\n        id: msg.Id,\n        content: msg.Content,\n        role: msg.Role,\n        createdAt: msg.CreatedTime,\n      }));\n      setMessages(formattedMessages);\n    } else {\n      setMessages([]);\n    }\n  };\n\n  useEffect(() => {\n    loadSessions();\n  }, []);\n\n  useEffect(() => {\n    if (currentSession) {\n      loadMessages(currentSession);\n    }\n  }, [currentSession]);\n\n  // 发送消息的处理函数\n  const handleSendMessage = async (messageText: string) => {\n    if (!currentSession) {\n      message.error('请先选择一个会话');\n      return;\n    }\n\n    if (!messageText.trim()) {\n      return;\n    }\n\n    try {\n      setLoading(true);\n\n      // 添加用户消息到本地状态\n      const userMessage = {\n        id: Date.now().toString(),\n        content: messageText,\n        role: 'user' as const,\n        timestamp: new Date().toISOString()\n      };\n\n      setMessages(prev => [...prev, userMessage]);\n\n      // 发送消息到后端\n      const response = await sendMessage(currentSession.Id, {\n        Content: messageText,\n        ModelId: currentSession.ModelId,\n        Temperature: currentSession.Temperature,\n        MaxTokens: currentSession.MaxTokens,\n      });\n\n      // 添加AI回复到本地状态\n      if (response.success && response.data) {\n        const aiMessage = {\n          id: (Date.now() + 1).toString(),\n          content: response.data.Content || '抱歉，我现在无法回复。',\n          role: 'assistant' as const,\n          timestamp: new Date().toISOString()\n        };\n\n        setMessages(prev => [...prev, aiMessage]);\n      }\n    } catch (error) {\n      console.error('发送消息失败:', error);\n      message.error('发送消息失败，请重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 删除会话\n  const handleDeleteSession = async (session: RwxAI.ChatSession) => {\n    try {\n      await deleteChatSession(session.Id);\n      message.success('删除成功');\n      await loadSessions();\n      if (currentSession?.Id === session.Id) {\n        setCurrentSession(null);\n        setMessages([]);\n      }\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n\n  // 会话列表项渲染\n  const renderSessionItem = (session: RwxAI.ChatSession) => {\n    const isActive = currentSession?.Id === session.Id;\n    const messageCount = session.Messages?.length || 0;\n    \n    return (\n      <div\n        key={session.Id}\n        className={`session-item ${isActive ? 'active' : ''}`}\n        onClick={() => setCurrentSession(session)}\n        onDoubleClick={() => {\n          // 双击激活对话，确保会话被选中并且可以继续对话\n          setCurrentSession(session);\n          // 可以在这里添加额外的激活逻辑，比如聚焦到输入框\n          setTimeout(() => {\n            const inputElement = document.querySelector('.ant-input') as HTMLInputElement;\n            if (inputElement) {\n              inputElement.focus();\n            }\n          }, 100);\n        }}\n        style={{\n          padding: '12px 16px',\n          cursor: 'pointer',\n          borderRadius: '8px',\n          margin: '4px 8px',\n          backgroundColor: isActive ? '#e6f7ff' : 'transparent',\n          border: isActive ? '1px solid #91d5ff' : '1px solid transparent',\n          transition: 'all 0.2s ease',\n        }}\n        onMouseEnter={(e) => {\n          if (!isActive) {\n            e.currentTarget.style.backgroundColor = '#f5f5f5';\n          }\n        }}\n        onMouseLeave={(e) => {\n          if (!isActive) {\n            e.currentTarget.style.backgroundColor = 'transparent';\n          }\n        }}\n      >\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>\n          <div style={{ flex: 1, minWidth: 0 }}>\n            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '4px' }}>\n              <Text strong ellipsis style={{ fontSize: '14px' }}>\n                {session.Name}\n              </Text>\n              {session.IsActive && (\n                <Badge \n                  status=\"processing\" \n                  style={{ marginLeft: '8px' }}\n                />\n              )}\n            </div>\n            <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n              {session.Model?.Name || '未知模型'}\n            </Text>\n            <div style={{ marginTop: '4px' }}>\n              <Text type=\"secondary\" style={{ fontSize: '11px' }}>\n                {messageCount} 条消息\n              </Text>\n            </div>\n          </div>\n          <Space size=\"small\">\n            <Tooltip title=\"编辑\">\n              <Button\n                type=\"text\"\n                size=\"small\"\n                icon={<EditOutlined />}\n                onClick={(e) => {\n                  e.stopPropagation();\n                  onEditSession?.(session);\n                }}\n              />\n            </Tooltip>\n            <Tooltip title=\"删除\">\n              <Button\n                type=\"text\"\n                size=\"small\"\n                danger\n                icon={<DeleteOutlined />}\n                onClick={(e) => {\n                  e.stopPropagation();\n                  handleDeleteSession(session);\n                }}\n              />\n            </Tooltip>\n          </Space>\n        </div>\n      </div>\n    );\n  };\n\n  // 欢迎页面的建议提示\n  const welcomePrompts = [\n    {\n      key: 'help',\n      label: '如何使用这个AI助手？',\n    },\n    {\n      key: 'features',\n      label: '有哪些功能可以使用？',\n    },\n    {\n      key: 'examples',\n      label: '给我一些使用示例',\n    },\n    {\n      key: 'settings',\n      label: '如何调整对话设置？',\n    },\n  ];\n\n  return (\n    <Layout style={{ height: '100vh', backgroundColor: '#fff' }}>\n      {/* 左侧会话列表 */}\n      <Sider \n        width={280} \n        style={{ \n          backgroundColor: '#fafafa',\n          borderRight: '1px solid #f0f0f0',\n          overflow: 'hidden'\n        }}\n      >\n        <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n          {/* 头部 */}\n          <div style={{ padding: '16px', borderBottom: '1px solid #f0f0f0' }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n              <Title level={4} style={{ margin: 0 }}>\n                <FormattedMessage id=\"pages.chat.sessions\" />\n              </Title>\n              <Button\n                type=\"primary\"\n                size=\"small\"\n                icon={<PlusOutlined />}\n                onClick={onCreateSession}\n              >\n                新建\n              </Button>\n            </div>\n          </div>\n          \n          {/* 会话列表 */}\n          <div style={{ flex: 1, overflow: 'auto' }}>\n            {sessionsLoading ? (\n              <div style={{ padding: '20px', textAlign: 'center' }}>\n                <Text type=\"secondary\">加载中...</Text>\n              </div>\n            ) : sessions.length === 0 ? (\n              <div style={{ padding: '20px', textAlign: 'center' }}>\n                <Text type=\"secondary\">暂无会话</Text>\n              </div>\n            ) : (\n              <div style={{ padding: '8px 0' }}>\n                {sessions.map(renderSessionItem)}\n              </div>\n            )}\n          </div>\n        </div>\n      </Sider>\n\n      {/* 右侧聊天区域 */}\n      <Content style={{ display: 'flex', flexDirection: 'column' }}>\n        {currentSession ? (\n          <Conversations\n            items={messages}\n            renderItem={(item) => (\n              <Bubble\n                content={item.content}\n                avatar={\n                  item.role === 'user' ? (\n                    <Avatar icon={<UserOutlined />} style={{ backgroundColor: '#1890ff' }} />\n                  ) : (\n                    <Avatar icon={<RobotOutlined />} style={{ backgroundColor: '#52c41a' }} />\n                  )\n                }\n                placement={item.role === 'user' ? 'end' : 'start'}\n                typing={item.role === 'assistant' && loading}\n              />\n            )}\n            renderHeader={() => (\n              <div style={{ \n                padding: '16px 24px', \n                borderBottom: '1px solid #f0f0f0',\n                backgroundColor: '#fff'\n              }}>\n                <Title level={4} style={{ margin: 0 }}>\n                  {currentSession.Name}\n                </Title>\n                <Text type=\"secondary\">\n                  {currentSession.Model?.Name} • 温度: {currentSession.Temperature} • 最大令牌: {currentSession.MaxTokens}\n                </Text>\n              </div>\n            )}\n            renderFooter={() => (\n              <div style={{ padding: '16px 24px', borderTop: '1px solid #f0f0f0' }}>\n                <Sender\n                  loading={loading}\n                  onSubmit={handleSendMessage}\n                  placeholder=\"输入消息...\"\n                />\n              </div>\n            )}\n            style={{ height: '100%' }}\n          />\n        ) : (\n          <div style={{ \n            height: '100%', \n            display: 'flex', \n            alignItems: 'center', \n            justifyContent: 'center',\n            flexDirection: 'column'\n          }}>\n            <Welcome\n              title=\"欢迎使用 RwxAI 聊天助手\"\n              description=\"选择一个会话开始对话，或创建新的会话\"\n              extra={\n                <Prompts\n                  title=\"快速开始\"\n                  items={welcomePrompts}\n                  onItemClick={(item) => {\n                    // 这里可以处理快速提示的点击\n                    message.info(`您选择了: ${item.label}`);\n                  }}\n                />\n              }\n            />\n          </div>\n        )}\n      </Content>\n    </Layout>\n  );\n};\n\nexport default ChatInterface;\n"], "names": [], "mappings": ";;;;;;;4BAoFA;;;eAAA;;;;;;;wEApFwC;6BACH;4BACK;+EAChB;6EACF;;;;;;;;;;AAExB,MAAM,oBAA8B;;IACrB,IAAA,YAAO;IACpB,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,IAAA,eAAQ,EAAC;IAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,eAAQ,EAA2B;IAC/E,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,eAAQ,EAAC;IAC/C,MAAM,UAAU,IAAA,aAAM,EAAM;IAE5B,MAAM,sBAAsB;QAC1B,kBAAkB;QAClB,sBAAsB;IACxB;IAEA,MAAM,oBAAoB,CAAC;QACzB,kBAAkB;QAClB,sBAAsB;IACxB;IAEA,MAAM,yBAAyB;QAC7B,sBAAsB;QACtB,kBAAkB;QAClB,eAAe;IACjB;IAEA,MAAM,2BAA2B;QAC/B,sBAAsB;QACtB,kBAAkB;QAClB,eAAe;QACf,gBAAgB;QAChB,OAAO,QAAQ,CAAC,MAAM;IACxB;IAEA,MAAM,mBAAmB;QACvB,IAAI,QAAQ,OAAO,EAAE;YACnB,eAAe;YACf,QAAQ,OAAO,CAAC,MAAM;QACxB;IACF;IAEA,qBACE,2BAAC;QAAI,OAAO;YAAE,QAAQ;YAAS,UAAU;QAAS;;0BAChD,2BAAC,sBAAa;gBACZ,iBAAiB;gBACjB,eAAe;;;;;;0BAGjB,2BAAC,WAAK;gBACJ,OAAO,iBAAiB,SAAS;gBACjC,MAAM;gBACN,UAAU;gBACV,MAAM;gBACN,gBAAgB;gBAChB,OAAO;gBACP,QAAQ;kCACN,2BAAC,YAAM;wBAAc,SAAS;kCAAwB;uBAA1C;;;;;kCAGZ,2BAAC,YAAM;wBAEL,MAAK;wBACL,SAAS;wBACT,SAAS;kCAER,iBAAiB,OAAO;uBALrB;;;;;iBAOP;0BAED,cAAA,2BAAC,oBAAW;oBACV,KAAK;oBACL,SAAS;oBACT,iBAAiB;oBACjB,WAAW;oBACX,eAAe;;;;;;;;;;;;;;;;;AAKzB;GA5EM;;QACS,YAAO;;;KADhB;IA8EN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BCyTf;;;eAAA;;;;;;wEA7YmD;6BAc5C;0BAOA;8BASA;4BACmC;8BACsB;;;;;;;;;;AAEhE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,YAAM;AACjC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,gBAAU;AAOlC,MAAM,gBAA8C,CAAC,EACnD,eAAe,EACf,aAAa,EACd;;IACc,IAAA,YAAO;IACpB,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAsB,EAAE;IAChE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,eAAQ,EAA2B;IAC/E,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAQ,EAAE;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAC;IAEvD,SAAS;IACT,MAAM,eAAe;QACnB,IAAI;YACF,mBAAmB;YACnB,MAAM,WAAW,MAAM,IAAA,sBAAe;YACtC,IAAI,SAAS,OAAO,EAAE;oBAGhB;gBAFJ,YAAY,SAAS,IAAI,IAAI,EAAE;gBAC/B,sBAAsB;gBACtB,IAAI,EAAA,iBAAA,SAAS,IAAI,cAAb,qCAAA,eAAe,MAAM,IAAG,KAAK,CAAC,gBAChC,kBAAkB,SAAS,IAAI,CAAC,EAAE;YAEtC;QACF,EAAE,OAAO,OAAO;YACd,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,mBAAmB;QACrB;IACF;IAEA,OAAO;IACP,MAAM,eAAe,CAAC;QACpB,IAAI,QAAQ,QAAQ,EAAE;YACpB,MAAM,oBAAoB,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAA,MAAQ,CAAA;oBACrD,IAAI,IAAI,EAAE;oBACV,SAAS,IAAI,OAAO;oBACpB,MAAM,IAAI,IAAI;oBACd,WAAW,IAAI,WAAW;gBAC5B,CAAA;YACA,YAAY;QACd,OACE,YAAY,EAAE;IAElB;IAEA,IAAA,gBAAS,EAAC;QACR;IACF,GAAG,EAAE;IAEL,IAAA,gBAAS,EAAC;QACR,IAAI,gBACF,aAAa;IAEjB,GAAG;QAAC;KAAe;IAEnB,YAAY;IACZ,MAAM,oBAAoB,OAAO;QAC/B,IAAI,CAAC,gBAAgB;YACnB,aAAO,CAAC,KAAK,CAAC;YACd;QACF;QAEA,IAAI,CAAC,YAAY,IAAI,IACnB;QAGF,IAAI;YACF,WAAW;YAEX,cAAc;YACd,MAAM,cAAc;gBAClB,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,SAAS;gBACT,MAAM;gBACN,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAY;YAE1C,UAAU;YACV,MAAM,WAAW,MAAM,IAAA,kBAAW,EAAC,eAAe,EAAE,EAAE;gBACpD,SAAS;gBACT,SAAS,eAAe,OAAO;gBAC/B,aAAa,eAAe,WAAW;gBACvC,WAAW,eAAe,SAAS;YACrC;YAEA,cAAc;YACd,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,MAAM,YAAY;oBAChB,IAAI,AAAC,CAAA,KAAK,GAAG,KAAK,CAAA,EAAG,QAAQ;oBAC7B,SAAS,SAAS,IAAI,CAAC,OAAO,IAAI;oBAClC,MAAM;oBACN,WAAW,IAAI,OAAO,WAAW;gBACnC;gBAEA,YAAY,CAAA,OAAQ;2BAAI;wBAAM;qBAAU;YAC1C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,OAAO;IACP,MAAM,sBAAsB,OAAO;QACjC,IAAI;YACF,MAAM,IAAA,wBAAiB,EAAC,QAAQ,EAAE;YAClC,aAAO,CAAC,OAAO,CAAC;YAChB,MAAM;YACN,IAAI,CAAA,2BAAA,qCAAA,eAAgB,EAAE,MAAK,QAAQ,EAAE,EAAE;gBACrC,kBAAkB;gBAClB,YAAY,EAAE;YAChB;QACF,EAAE,OAAO,OAAO;YACd,aAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,UAAU;IACV,MAAM,oBAAoB,CAAC;YAEJ,mBAoDV;QArDX,MAAM,WAAW,CAAA,2BAAA,qCAAA,eAAgB,EAAE,MAAK,QAAQ,EAAE;QAClD,MAAM,eAAe,EAAA,oBAAA,QAAQ,QAAQ,cAAhB,wCAAA,kBAAkB,MAAM,KAAI;QAEjD,qBACE,2BAAC;YAEC,WAAW,CAAC,aAAa,EAAE,WAAW,WAAW,GAAG,CAAC;YACrD,SAAS,IAAM,kBAAkB;YACjC,eAAe;gBACb,yBAAyB;gBACzB,kBAAkB;gBAClB,0BAA0B;gBAC1B,WAAW;oBACT,MAAM,eAAe,SAAS,aAAa,CAAC;oBAC5C,IAAI,cACF,aAAa,KAAK;gBAEtB,GAAG;YACL;YACA,OAAO;gBACL,SAAS;gBACT,QAAQ;gBACR,cAAc;gBACd,QAAQ;gBACR,iBAAiB,WAAW,YAAY;gBACxC,QAAQ,WAAW,sBAAsB;gBACzC,YAAY;YACd;YACA,cAAc,CAAC;gBACb,IAAI,CAAC,UACH,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;YAE5C;YACA,cAAc,CAAC;gBACb,IAAI,CAAC,UACH,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;YAE5C;sBAEA,cAAA,2BAAC;gBAAI,OAAO;oBAAE,SAAS;oBAAQ,gBAAgB;oBAAiB,YAAY;gBAAa;;kCACvF,2BAAC;wBAAI,OAAO;4BAAE,MAAM;4BAAG,UAAU;wBAAE;;0CACjC,2BAAC;gCAAI,OAAO;oCAAE,SAAS;oCAAQ,YAAY;oCAAU,cAAc;gCAAM;;kDACvE,2BAAC;wCAAK,MAAM;wCAAC,QAAQ;wCAAC,OAAO;4CAAE,UAAU;wCAAO;kDAC7C,QAAQ,IAAI;;;;;;oCAEd,QAAQ,QAAQ,kBACf,2BAAC,WAAK;wCACJ,QAAO;wCACP,OAAO;4CAAE,YAAY;wCAAM;;;;;;;;;;;;0CAIjC,2BAAC;gCAAK,MAAK;gCAAY,OAAO;oCAAE,UAAU;gCAAO;0CAC9C,EAAA,iBAAA,QAAQ,KAAK,cAAb,qCAAA,eAAe,IAAI,KAAI;;;;;;0CAE1B,2BAAC;gCAAI,OAAO;oCAAE,WAAW;gCAAM;0CAC7B,cAAA,2BAAC;oCAAK,MAAK;oCAAY,OAAO;wCAAE,UAAU;oCAAO;;wCAC9C;wCAAa;;;;;;;;;;;;;;;;;;kCAIpB,2BAAC,WAAK;wBAAC,MAAK;;0CACV,2BAAC,aAAO;gCAAC,OAAM;0CACb,cAAA,2BAAC,YAAM;oCACL,MAAK;oCACL,MAAK;oCACL,oBAAM,2BAAC,mBAAY;;;;;oCACnB,SAAS,CAAC;wCACR,EAAE,eAAe;wCACjB,0BAAA,4BAAA,cAAgB;oCAClB;;;;;;;;;;;0CAGJ,2BAAC,aAAO;gCAAC,OAAM;0CACb,cAAA,2BAAC,YAAM;oCACL,MAAK;oCACL,MAAK;oCACL,MAAM;oCACN,oBAAM,2BAAC,qBAAc;;;;;oCACrB,SAAS,CAAC;wCACR,EAAE,eAAe;wCACjB,oBAAoB;oCACtB;;;;;;;;;;;;;;;;;;;;;;;WA7EH,QAAQ,EAAE;;;;;IAoFrB;IAEA,YAAY;IACZ,MAAM,iBAAiB;QACrB;YACE,KAAK;YACL,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;QACT;KACD;IAED,qBACE,2BAAC,YAAM;QAAC,OAAO;YAAE,QAAQ;YAAS,iBAAiB;QAAO;;0BAExD,2BAAC;gBACC,OAAO;gBACP,OAAO;oBACL,iBAAiB;oBACjB,aAAa;oBACb,UAAU;gBACZ;0BAEA,cAAA,2BAAC;oBAAI,OAAO;wBAAE,QAAQ;wBAAQ,SAAS;wBAAQ,eAAe;oBAAS;;sCAErE,2BAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,cAAc;4BAAoB;sCAC/D,cAAA,2BAAC;gCAAI,OAAO;oCAAE,SAAS;oCAAQ,gBAAgB;oCAAiB,YAAY;gCAAS;;kDACnF,2BAAC;wCAAM,OAAO;wCAAG,OAAO;4CAAE,QAAQ;wCAAE;kDAClC,cAAA,2BAAC,qBAAgB;4CAAC,IAAG;;;;;;;;;;;kDAEvB,2BAAC,YAAM;wCACL,MAAK;wCACL,MAAK;wCACL,oBAAM,2BAAC,mBAAY;;;;;wCACnB,SAAS;kDACV;;;;;;;;;;;;;;;;;sCAOL,2BAAC;4BAAI,OAAO;gCAAE,MAAM;gCAAG,UAAU;4BAAO;sCACrC,gCACC,2BAAC;gCAAI,OAAO;oCAAE,SAAS;oCAAQ,WAAW;gCAAS;0CACjD,cAAA,2BAAC;oCAAK,MAAK;8CAAY;;;;;;;;;;uCAEvB,SAAS,MAAM,KAAK,kBACtB,2BAAC;gCAAI,OAAO;oCAAE,SAAS;oCAAQ,WAAW;gCAAS;0CACjD,cAAA,2BAAC;oCAAK,MAAK;8CAAY;;;;;;;;;;qDAGzB,2BAAC;gCAAI,OAAO;oCAAE,SAAS;gCAAQ;0CAC5B,SAAS,GAAG,CAAC;;;;;;;;;;;;;;;;;;;;;;0BAQxB,2BAAC;gBAAQ,OAAO;oBAAE,SAAS;oBAAQ,eAAe;gBAAS;0BACxD,+BACC,2BAAC,gBAAa;oBACZ,OAAO;oBACP,YAAY,CAAC,qBACX,2BAAC,SAAM;4BACL,SAAS,KAAK,OAAO;4BACrB,QACE,KAAK,IAAI,KAAK,uBACZ,2BAAC,YAAM;gCAAC,oBAAM,2BAAC,mBAAY;;;;;gCAAK,OAAO;oCAAE,iBAAiB;gCAAU;;;;;uDAEpE,2BAAC,YAAM;gCAAC,oBAAM,2BAAC,oBAAa;;;;;gCAAK,OAAO;oCAAE,iBAAiB;gCAAU;;;;;;4BAGzE,WAAW,KAAK,IAAI,KAAK,SAAS,QAAQ;4BAC1C,QAAQ,KAAK,IAAI,KAAK,eAAe;;;;;;oBAGzC,cAAc;4BAUP;6CATL,2BAAC;4BAAI,OAAO;gCACV,SAAS;gCACT,cAAc;gCACd,iBAAiB;4BACnB;;8CACE,2BAAC;oCAAM,OAAO;oCAAG,OAAO;wCAAE,QAAQ;oCAAE;8CACjC,eAAe,IAAI;;;;;;8CAEtB,2BAAC;oCAAK,MAAK;;yCACR,wBAAA,eAAe,KAAK,cAApB,4CAAA,sBAAsB,IAAI;wCAAC;wCAAQ,eAAe,WAAW;wCAAC;wCAAU,eAAe,SAAS;;;;;;;;;;;;;;oBAIvG,cAAc,kBACZ,2BAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAa,WAAW;4BAAoB;sCACjE,cAAA,2BAAC,SAAM;gCACL,SAAS;gCACT,UAAU;gCACV,aAAY;;;;;;;;;;;oBAIlB,OAAO;wBAAE,QAAQ;oBAAO;;;;;yCAG1B,2BAAC;oBAAI,OAAO;wBACV,QAAQ;wBACR,SAAS;wBACT,YAAY;wBACZ,gBAAgB;wBAChB,eAAe;oBACjB;8BACE,cAAA,2BAAC,UAAO;wBACN,OAAM;wBACN,aAAY;wBACZ,qBACE,2BAAC,UAAO;4BACN,OAAM;4BACN,OAAO;4BACP,aAAa,CAAC;gCACZ,gBAAgB;gCAChB,aAAO,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,KAAK,KAAK,CAAC,CAAC;4BACpC;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlB;GAjWM;;QAIS,YAAO;;;KAJhB;IAmWN,WAAe"}