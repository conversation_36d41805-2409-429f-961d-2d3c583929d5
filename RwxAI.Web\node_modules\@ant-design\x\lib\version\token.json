{"ThoughtChain": {"global": ["fontSizeSM", "controlHeightXS", "controlHeightSM", "marginSM", "fontSize", "controlHeight", "margin", "fontSizeLG", "controlHeightLG", "marginLG", "colorPrimaryText", "colorSuccessText", "colorErrorText", "blue", "purple", "cyan", "green", "magenta", "pink", "red", "orange", "yellow", "volcano", "geekblue", "gold", "lime", "colorPrimary", "colorSuccess", "colorWarning", "colorError", "colorInfo", "colorLink", "colorTextBase", "colorBgBase", "fontFamily", "fontFamilyCode", "lineWidth", "lineType", "motionUnit", "motionBase", "motionEaseOutCirc", "motionEaseInOutCirc", "motionEaseOut", "motionEaseInOut", "motionEaseOutBack", "motionEaseInBack", "motionEaseInQuint", "motionEaseOutQuint", "borderRadius", "sizeUnit", "sizeStep", "sizePopupArrow", "zIndexBase", "zIndexPopupBase", "opacityImage", "wireframe", "motion", "blue-1", "blue1", "blue-2", "blue2", "blue-3", "blue3", "blue-4", "blue4", "blue-5", "blue5", "blue-6", "blue6", "blue-7", "blue7", "blue-8", "blue8", "blue-9", "blue9", "blue-10", "blue10", "purple-1", "purple1", "purple-2", "purple2", "purple-3", "purple3", "purple-4", "purple4", "purple-5", "purple5", "purple-6", "purple6", "purple-7", "purple7", "purple-8", "purple8", "purple-9", "purple9", "purple-10", "purple10", "cyan-1", "cyan1", "cyan-2", "cyan2", "cyan-3", "cyan3", "cyan-4", "cyan4", "cyan-5", "cyan5", "cyan-6", "cyan6", "cyan-7", "cyan7", "cyan-8", "cyan8", "cyan-9", "cyan9", "cyan-10", "cyan10", "green-1", "green1", "green-2", "green2", "green-3", "green3", "green-4", "green4", "green-5", "green5", "green-6", "green6", "green-7", "green7", "green-8", "green8", "green-9", "green9", "green-10", "green10", "magenta-1", "magenta1", "magenta-2", "magenta2", "magenta-3", "magenta3", "magenta-4", "magenta4", "magenta-5", "magenta5", "magenta-6", "magenta6", "magenta-7", "magenta7", "magenta-8", "magenta8", "magenta-9", "magenta9", "magenta-10", "magenta10", "pink-1", "pink1", "pink-2", "pink2", "pink-3", "pink3", "pink-4", "pink4", "pink-5", "pink5", "pink-6", "pink6", "pink-7", "pink7", "pink-8", "pink8", "pink-9", "pink9", "pink-10", "pink10", "red-1", "red1", "red-2", "red2", "red-3", "red3", "red-4", "red4", "red-5", "red5", "red-6", "red6", "red-7", "red7", "red-8", "red8", "red-9", "red9", "red-10", "red10", "orange-1", "orange1", "orange-2", "orange2", "orange-3", "orange3", "orange-4", "orange4", "orange-5", "orange5", "orange-6", "orange6", "orange-7", "orange7", "orange-8", "orange8", "orange-9", "orange9", "orange-10", "orange10", "yellow-1", "yellow1", "yellow-2", "yellow2", "yellow-3", "yellow3", "yellow-4", "yellow4", "yellow-5", "yellow5", "yellow-6", "yellow6", "yellow-7", "yellow7", "yellow-8", "yellow8", "yellow-9", "yellow9", "yellow-10", "yellow10", "volcano-1", "volcano1", "volcano-2", "volcano2", "volcano-3", "volcano3", "volcano-4", "volcano4", "volcano-5", "volcano5", "volcano-6", "volcano6", "volcano-7", "volcano7", "volcano-8", "volcano8", "volcano-9", "volcano9", "volcano-10", "volcano10", "geekblue-1", "geekblue1", "geekblue-2", "geekblue2", "geekblue-3", "geekblue3", "geekblue-4", "geekblue4", "geekblue-5", "geekblue5", "geekblue-6", "geekblue6", "geekblue-7", "geekblue7", "geekblue-8", "geekblue8", "geekblue-9", "geekblue9", "geekblue-10", "geekblue10", "gold-1", "gold1", "gold-2", "gold2", "gold-3", "gold3", "gold-4", "gold4", "gold-5", "gold5", "gold-6", "gold6", "gold-7", "gold7", "gold-8", "gold8", "gold-9", "gold9", "gold-10", "gold10", "lime-1", "lime1", "lime-2", "lime2", "lime-3", "lime3", "lime-4", "lime4", "lime-5", "lime5", "lime-6", "lime6", "lime-7", "lime7", "lime-8", "lime8", "lime-9", "lime9", "lime-10", "lime10", "colorText", "colorTextSecondary", "colorTextTertiary", "colorTextQuaternary", "colorFill", "colorFillSecondary", "colorFillTertiary", "colorFillQuaternary", "colorBgSolid", "colorBgSolidHover", "colorBgSolidActive", "colorBgLayout", "colorBgContainer", "colorBgElevated", "colorBgSpotlight", "colorBgBlur", "colorBorder", "colorBorderSecondary", "colorPrimaryBg", "colorPrimaryBgHover", "colorPrimaryBorder", "colorPrimaryBorderHover", "colorPrimaryHover", "colorPrimaryActive", "colorPrimaryTextHover", "colorPrimaryTextActive", "colorSuccessBg", "colorSuccessBgHover", "colorSuccessBorder", "colorSuccessBorderHover", "colorSuccessHover", "colorSuccessActive", "colorSuccessTextHover", "colorSuccessTextActive", "colorErrorBg", "colorErrorBgHover", "colorErrorBgFilledHover", "colorErrorBgActive", "colorErrorBorder", "colorErrorBorderHover", "colorErrorHover", "colorErrorActive", "colorErrorTextHover", "colorErrorTextActive", "colorWarningBg", "colorWarningBgHover", "colorWarningBorder", "colorWarningBorderHover", "colorWarningHover", "colorWarningActive", "colorWarningTextHover", "colorWarningText", "colorWarningTextActive", "colorInfoBg", "colorInfoBgHover", "colorInfoBorder", "colorInfoBorderHover", "colorInfoHover", "colorInfoActive", "colorInfoTextHover", "colorInfoText", "colorInfoTextActive", "colorLinkHover", "colorLinkActive", "colorBgMask", "colorWhite", "fontSizeXL", "fontSizeHeading1", "fontSizeHeading2", "fontSizeHeading3", "fontSizeHeading4", "fontSizeHeading5", "lineHeight", "lineHeightLG", "lineHeightSM", "fontHeight", "fontHeightLG", "fontHeightSM", "lineHeightHeading1", "lineHeightHeading2", "lineHeightHeading3", "lineHeightHeading4", "lineHeightHeading5", "sizeXXL", "sizeXL", "sizeLG", "sizeMD", "sizeMS", "size", "sizeSM", "sizeXS", "sizeXXS", "motionDurationFast", "motionDurationMid", "motionDurationSlow", "lineWidthBold", "borderRadiusXS", "borderRadiusSM", "borderRadiusLG", "borderRadiusOuter", "colorFillContent", "colorFillContentHover", "colorFillAlter", "colorBgContainerDisabled", "colorBorderBg", "colorSplit", "colorTextPlaceholder", "colorTextDisabled", "colorTextHeading", "colorTextLabel", "colorTextDescription", "colorTextLightSolid", "colorHighlight", "colorBgTextHover", "colorBgTextActive", "colorIcon", "colorIconHover", "colorErrorOutline", "colorWarningOutline", "fontSizeIcon", "lineWidthFocus", "controlOutlineWidth", "controlInteractiveSize", "controlItemBgHover", "controlItemBgActive", "controlItemBgActiveHover", "controlItemBgActiveDisabled", "controlTmpOutline", "controlOutline", "fontWeightStrong", "opacityLoading", "linkDecoration", "linkHoverDecoration", "linkFocusDecoration", "controlPaddingHorizontal", "controlPaddingHorizontalSM", "paddingXXS", "paddingXS", "paddingSM", "padding", "paddingMD", "paddingLG", "paddingXL", "paddingContentHorizontalLG", "paddingContentVerticalLG", "paddingContentHorizontal", "paddingContentVertical", "paddingContentHorizontalSM", "paddingContentVerticalSM", "marginXXS", "marginXS", "marginMD", "marginXL", "marginXXL", "boxShadow", "boxShadowSecondary", "boxShadowTertiary", "screenXS", "screenXSMin", "screenXSMax", "screenSM", "screenSMMin", "screenSMMax", "screenMD", "screenMDMin", "screenMDMax", "screenLG", "screenLGMin", "screenLGMax", "screenXL", "screenXLMin", "screenXLMax", "screenXXL", "screenXXLMin", "boxShadowPopoverArrow", "boxShadowCard", "boxShadowDrawerRight", "boxShadowDrawerLeft", "boxShadowDrawerUp", "boxShadowDrawerDown", "boxShadowTabsOverflowLeft", "boxShadowTabsOverflowRight", "boxShadowTabsOverflowTop", "boxShadowTabsOverflowBottom", "_token<PERSON>ey", "_theme<PERSON>ey", "_hashId"], "component": {}}, "Suggestion": {"global": ["paddingXXS", "padding"], "component": {}}, "Sender": {"global": ["paddingXS", "padding", "paddingSM", "paddingXXS", "lineWidth", "lineWidthBold", "boxShadowTertiary", "motionDurationSlow", "borderRadius", "colorBorder", "boxShadowSecondary", "colorPrimary", "colorBgContainerDisabled", "controlHeight", "colorFillAlter", "fontSize", "lineHeight"], "component": {}}, "Welcome": {"global": ["fontSizeHeading3", "lineHeightHeading3", "fontSize", "lineHeight", "padding", "paddingXXS", "paddingXS", "paddingSM", "colorFillContent", "borderRadiusLG"], "component": {}}, "Prompts": {"global": ["colorTextTertiary", "paddingSM", "paddingXS", "padding", "colorBgContainer", "borderRadiusLG", "motionDurationSlow", "lineWidth", "lineType", "colorBorderSecondary", "colorFillTertiary", "colorFill", "paddingXXS", "fontSize", "lineHeight", "colorTextHeading", "colorBgContainerDisabled", "fontSizeLG", "lineHeightLG", "colorFillQuaternary"], "component": {}}, "Conversations": {"global": ["paddingXXS", "paddingSM", "paddingXL", "colorTextDescription", "controlHeightLG", "paddingXS", "borderRadiusLG", "motionDurationMid", "motionEaseInOut", "colorBgTextHover", "colorText", "colorTextDisabled", "fontSizeXL"], "component": {}}, "Bubble": {"global": ["fontSize", "lineHeight", "paddingSM", "colorText", "paddingXXS", "marginXS", "colorPrimary", "padding", "colorTextTertiary", "borderRadiusSM", "borderRadiusLG", "colorFillContent", "colorBorderSecondary", "boxShadowTertiary", "borderRadiusXS"], "component": {}}, "Attachments": {"global": ["zIndexPopupBase", "borderRadius", "lineWidthBold", "padding", "colorPrimaryHover", "paddingXXS", "fontSizeHeading2", "fontSize", "lineHeight", "paddingSM", "colorText", "colorBgContainer", "motionDurationSlow", "boxShadowTertiary", "colorFillContent", "lineWidth", "paddingXS", "fontSizeLG", "colorTextTertiary", "opacityLoading", "colorError"], "component": {"colorBgPlaceholderHover": "rgba(255,255,255,0.85)"}}, "Actions": {"global": ["paddingXS", "colorTextDescription", "paddingXXS", "borderRadius", "controlHeightSM", "fontSize", "colorBgTextHover", "paddingSM", "borderRadiusLG", "colorBorderSecondary", "colorTextSecondary", "lineHeight", "fontSizeLG"], "component": {}}}