import React, { useEffect, useState } from 'react';
import { Modal, Form, Input, Select, InputNumber, Switch } from 'antd';
import { useIntl, FormattedMessage } from '@umijs/max';
import {
  createAIModel,
  updateAIModel,
  getEnabledAIProviders,
  getAIModelTemplatesByProviderAndType
} from '@/services/rwxai';

interface ModelFormProps {
  visible: boolean;
  onVisibleChange: (visible: boolean) => void;
  initialValues?: RwxAI.AIModel;
  onSuccess: () => void;
}

const ModelForm: React.FC<ModelFormProps> = ({
  visible,
  onVisibleChange,
  initialValues,
  onSuccess,
}) => {
  const intl = useIntl();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [providers, setProviders] = useState<RwxAI.AIProvider[]>([]);
  const [modelTemplates, setModelTemplates] = useState<RwxAI.AIModelTemplate[]>([]);
  const [loadingTemplates, setLoadingTemplates] = useState(false);

  const isEdit = !!initialValues?.Id;

  useEffect(() => {
    if (visible) {
      initializeForm();
    }
  }, [visible, initialValues]);

  const initializeForm = async () => {
    // 先加载提供商数据
    await loadProviders();

    if (initialValues) {
      // 映射后端数据结构到表单字段
      const formData = {
        // 基本字段
        Name: initialValues.Name,

        // 模型ID
        ModelId: initialValues.ModelId,

        // 模型服务端点
        Endpoint: initialValues.Endpoint,

        // 提供商代码
        ProviderCode: initialValues.ProviderCode,

        DisplayName: initialValues.DisplayName,
        Description: initialValues.Description,
        ApiKey: initialValues.ApiKey,
        IsEnabled: initialValues.IsEnabled,
        IsDefault: initialValues.IsDefault,

        // 配置参数
        MaxTokens: initialValues.MaxTokens,
        Temperature: initialValues.Temperature,

        // 关联字段
        TemplateId: initialValues.ModelTemplateId,
        ProviderId: initialValues.Template?.Provider?.Id, // 添加提供商ID

        // 只读字段，从模板中获取
        ModelType: initialValues.Template?.Type,
        SupportsStreaming: initialValues.Template?.SupportsStreaming,
      };

      form.setFieldsValue(formData);

      // 如果有提供商ID，加载对应的模型模板
      const providerId = initialValues.Template?.Provider?.Id;
      if (providerId) {
        await loadModelTemplates(providerId);
      }
    } else {
      form.resetFields();
      setModelTemplates([]);
    }
  };

  // 监听表单字段变化
  const handleFormValuesChange = (changedValues: any) => {
    // 当提供商发生变化时，重新加载模型模板
    if (changedValues.ProviderId !== undefined) {
      const providerId = changedValues.ProviderId;

      loadModelTemplates(providerId);

      // 清空模型模板选择
      form.setFieldValue('TemplateId', undefined);

      // 清空相关字段，因为要重新选择模板
      form.setFieldsValue({
        ModelId: undefined,
        ModelType: undefined,
        Endpoint: undefined,
        SupportsStreaming: undefined,
        Name: undefined,
        Description: undefined,
        DisplayName: undefined,
        ProviderCode: undefined,
      });
    }

    // 当选择模型模板时，自动填充相关字段
    if (changedValues.TemplateId !== undefined) {
      const selectedTemplate = modelTemplates.find(template =>
        template.Id === changedValues.TemplateId
      );
      if (selectedTemplate) {
        // 自动填充模板的所有信息
        const updates: any = {
          ModelType: selectedTemplate.Type,
          Name: selectedTemplate.Name,
          ModelId: selectedTemplate.ModelId,
          Endpoint: selectedTemplate.Endpoint,
          Description: selectedTemplate.Description,
          DisplayName: selectedTemplate.DisplayName,
          SupportsStreaming: selectedTemplate.SupportsStreaming,
          ProviderCode: selectedTemplate.Provider?.Code,
        };

        // 填充可选的配置参数
        if (selectedTemplate.MaxOutputLength !== undefined) {
          updates.MaxTokens = selectedTemplate.MaxOutputLength;
        }

        form.setFieldsValue(updates);
      }
    }
  };

  const loadProviders = async () => {
    try {
      const response = await getEnabledAIProviders();
      if (response.success) {
        setProviders(response.data || []);
      } else {
        setProviders([]);
      }
    } catch (error) {
      setProviders([]);
    }
    // 错误消息会由统一响应处理系统自动显示
  };

  // 根据提供商加载模型模板
  const loadModelTemplates = async (providerId?: string) => {
    if (!providerId) {
      setModelTemplates([]);
      return;
    }

    setLoadingTemplates(true);
    try {
      // 根据提供商获取所有模板
      const response = await getAIModelTemplatesByProviderAndType({
        providerId,
        modelType: undefined, // 不限制模型类型，获取该提供商的所有模板
      });

      if (response.success) {
        setModelTemplates(response.data || []);
      } else {
        setModelTemplates([]);
      }
    } catch (error) {
      setModelTemplates([]);
    } finally {
      setLoadingTemplates(false);
    }
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // 构建提交数据，只保存模板ID和必要字段
      const submitData: any = {
        modelTemplateId: values.TemplateId, // 只保存模板ID        
        name: values.Name,
        modelId: values.ModelId,
        endpoint: values.Endpoint,
        providerCode: values.ProviderCode,
        displayName: values.DisplayName,
        description: values.Description,
        apiKey: values.ApiKey,
        isEnabled: values.IsEnabled,
        isDefault: values.IsDefault,
        // 可选的自定义配置参数
        maxTokens: values.MaxTokens,
        temperature: values.Temperature,
      };

      let response;
      if (isEdit) {
        response = await updateAIModel(initialValues!.Id, { ...initialValues, ...submitData });
      } else {
        response = await createAIModel(submitData);
      }

      if (response.success) {
        onSuccess();
      }
      // 成功和错误消息会由统一响应处理系统自动显示
    } catch (error) {
      // 表单验证错误等
      console.error('Form validation error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={intl.formatMessage({
        id: isEdit ? 'pages.aiModels.form.edit.title' : 'pages.aiModels.form.create.title',
      })}
      open={visible}
      onCancel={() => onVisibleChange(false)}
      onOk={handleSubmit}
      confirmLoading={loading}
      width={900}
      style={{ top: 50 }}
      styles={{
        body: { maxHeight: '70vh', overflowY: 'auto', padding: '24px' }
      }}
    >
      <Form
        form={form}
        layout="vertical"
        onValuesChange={handleFormValuesChange}
        initialValues={{
          IsEnabled: true,
          Temperature: 0.7,
        }}
      >
        {/* 第一步：选择AI模型提供商和模板 */}
        <div style={{ display: 'flex', gap: '16px' }}>
          <Form.Item
            name="ProviderId"
            label={<FormattedMessage id="pages.aiModels.form.provider" />}
            rules={[{ required: true, message: intl.formatMessage({ id: 'pages.aiModels.form.provider.required' }) }]}
            tooltip={<FormattedMessage id="pages.aiModels.form.provider.tooltip" />}
            style={{ flex: 1 }}
          >
            <Select placeholder={intl.formatMessage({ id: 'pages.aiModels.form.provider.placeholder' })}>
              {providers.map((provider) => (
                <Select.Option key={provider.Id} value={provider.Id}>
                  {provider.DisplayName || provider.Name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="TemplateId"
            label={<FormattedMessage id="pages.aiModels.form.template" />}
            rules={[{ required: true, message: intl.formatMessage({ id: 'pages.aiModels.form.template.required' }) }]}
            tooltip={<FormattedMessage id="pages.aiModels.form.template.tooltip" />}
            style={{ flex: 1 }}
          >
            <Select
              placeholder={intl.formatMessage({ id: 'pages.aiModels.form.template.placeholder' })}
              loading={loadingTemplates}
              allowClear
              showSearch
              optionFilterProp="children"
              filterOption={(input, option) =>
                String(option?.children || '').toLowerCase().includes(input.toLowerCase())
              }
            >
              {modelTemplates.map((template) => (
                <Select.Option key={template.Id} value={template.Id}>
                  {template.DisplayName || template.Name} ({template.ModelId})
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </div>

        {/* 基本信息（从模板自动填充） */}
        <div style={{ display: 'flex', gap: '16px' }}>
          <Form.Item
            name="Name"
            label={<FormattedMessage id="pages.aiModels.form.name" />}
            rules={[{ required: true, message: intl.formatMessage({ id: 'pages.aiModels.form.name.required' }) }]}
            style={{ flex: 1 }}
          >
            <Input placeholder={intl.formatMessage({ id: 'pages.aiModels.form.name.placeholder' })} />
          </Form.Item>

          <Form.Item
            name="DisplayName"
            label={<FormattedMessage id="pages.aiModels.form.displayName" />}
            style={{ flex: 1 }}
          >
            <Input placeholder={intl.formatMessage({ id: 'pages.aiModels.form.displayName.placeholder' })} />
          </Form.Item>
        </div>

        <Form.Item
          name="Description"
          label={<FormattedMessage id="pages.aiModels.form.description" />}
        >
          <Input.TextArea
            rows={2}
            placeholder={intl.formatMessage({ id: 'pages.aiModels.form.description.placeholder' })}
          />
        </Form.Item>

        <Form.Item
          name="ApiKey"
          label={<FormattedMessage id="pages.aiModels.form.apiKey" />}
          tooltip={<FormattedMessage id="pages.aiModels.form.apiKey.tooltip" />}
        >
          <Input.Password placeholder={intl.formatMessage({ id: 'pages.aiModels.form.apiKey.placeholder' })} />
        </Form.Item>

        {/* 只读字段，显示从模板获取的信息 */}
        <div style={{ display: 'flex', gap: '16px' }}>
          <Form.Item
            name="ModelId"
            label={<FormattedMessage id="pages.aiModels.form.modelId" />}
            style={{ flex: 1 }}
          >
            <Input disabled placeholder={intl.formatMessage({ id: 'pages.aiModels.form.modelId.readonly' })} />
          </Form.Item>

          <Form.Item
            name="ModelType"
            label={<FormattedMessage id="pages.aiModels.form.modelType" />}
            style={{ flex: 1 }}
          >
            <Select disabled placeholder={intl.formatMessage({ id: 'pages.aiModels.form.modelType.readonly' })}>
              <Select.Option value="0">Chat</Select.Option>
              <Select.Option value="1">Embedding</Select.Option>
              <Select.Option value="2">Image Generation</Select.Option>
              <Select.Option value="3">Text to Speech</Select.Option>
            </Select>
          </Form.Item>
        </div>

        <div style={{ display: 'flex', gap: '16px' }}>
          <Form.Item
            name="Endpoint"
            label={<FormattedMessage id="pages.aiModels.form.endpoint" />}
            style={{ flex: 1 }}
          >
            <Input disabled placeholder="从模板自动获取" />
          </Form.Item>

          <Form.Item
            name="SupportsStreaming"
            label={<FormattedMessage id="pages.aiModels.form.supportsStreaming" />}
            style={{ flex: 1 }}
          >
            <Select disabled placeholder="从模板自动获取">
              <Select.Option value={true}>支持</Select.Option>
              <Select.Option value={false}>不支持</Select.Option>
            </Select>
          </Form.Item>
        </div>

        {/* 可配置参数 */}
        <div style={{ display: 'flex', gap: '16px' }}>
          <Form.Item
            name="MaxTokens"
            label={<FormattedMessage id="pages.aiModels.form.maxTokens" />}
            style={{ flex: 1 }}
          >
            <InputNumber
              min={1}
              max={100000}
              style={{ width: '100%' }}
              placeholder={intl.formatMessage({ id: 'pages.aiModels.form.maxTokens.placeholder' })}
            />
          </Form.Item>

          <Form.Item
            name="Temperature"
            label={<FormattedMessage id="pages.aiModels.form.temperature" />}
            style={{ flex: 1 }}
          >
            <InputNumber
              min={0}
              max={2}
              step={0.1}
              style={{ width: '100%' }}
              placeholder={intl.formatMessage({ id: 'pages.aiModels.form.temperature.placeholder' })}
            />
          </Form.Item>
        </div>

        <div style={{ display: 'flex', gap: '16px', alignItems: 'flex-end' }}>
          <Form.Item
            name="IsEnabled"
            label={<FormattedMessage id="pages.aiModels.form.isEnabled" />}
            valuePropName="checked"
            style={{ flex: 1 }}
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="IsDefault"
            label={<FormattedMessage id="pages.aiModels.form.isDefault" />}
            valuePropName="checked"
            tooltip={<FormattedMessage id="pages.aiModels.form.isDefault.tooltip" />}
            style={{ flex: 1 }}
          >
            <Switch />
          </Form.Item>
        </div>

      </Form>
    </Modal>
  );
};

export default ModelForm;
