/**
 * 增强的请求工具
 * 集成统一的响应处理和错误处理
 */

// import { request as umiRequest } from '@umijs/max';
import { getToken, getRefreshToken, setToken, setRefreshToken, logout, isTokenExpired } from './auth';
import { handleApiResponse, handleApiError } from './responseHandler';
import { ResponseHandleResult } from '@/types/response';

// 请求选项接口
export interface RequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  data?: any;
  headers?: Record<string, string>;
  showSuccessMessage?: boolean;
  showErrorNotification?: boolean;
  successMessage?: string;
  skipAuth?: boolean;
  timeout?: number;
}

// 增强的请求函数
export async function enhancedRequest<T = any>(
  url: string,
  options: RequestOptions = {}
): Promise<ResponseHandleResult<T>> {
  const {
    method = 'GET',
    data,
    headers = {},
    showSuccessMessage = false,
    showErrorNotification = true,
    successMessage,
    skipAuth = false,
    timeout = 30000,
  } = options;

  try {
    // 添加JWT令牌
    if (!skipAuth && !url.includes('/Auth/login') && !url.includes('/Auth/register')) {
      const token = getToken();
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }
    }

    // 发送请求
    const response = await fetch(url, {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers,
      },
      body: data ? JSON.stringify(data) : undefined,
      signal: AbortSignal.timeout(timeout),
    });

    // 处理401错误 - 尝试刷新令牌
    if (response.status === 401 && !skipAuth) {
      const refreshResult = await tryRefreshToken();
      if (refreshResult) {
        // 重新发送请求
        headers.Authorization = `Bearer ${refreshResult.token}`;
        const retryResponse = await fetch(url, {
          method,
          headers: {
            'Content-Type': 'application/json',
            ...headers,
          },
          body: data ? JSON.stringify(data) : undefined,
          signal: AbortSignal.timeout(timeout),
        });

        return handleApiResponse<T>(retryResponse, {
          showSuccessMessage,
          showErrorNotification,
          successMessage,
        });
      }
    }

    // 处理响应
    return handleApiResponse<T>(response, {
      showSuccessMessage,
      showErrorNotification,
      successMessage,
    });

  } catch (error: any) {
    // 处理网络错误或超时
    const apiError = handleApiError(error, showErrorNotification);
    return {
      success: false,
      error: apiError,
      message: apiError.message,
    };
  }
}

// 尝试刷新令牌
async function tryRefreshToken(): Promise<{ token: string; refreshToken: string } | null> {
  const refreshTokenValue = getRefreshToken();

  if (!refreshTokenValue || isTokenExpired(refreshTokenValue)) {
    logout();
    return null;
  }

  try {
    // 直接调用刷新令牌API，避免循环依赖
    const response = await fetch('/api/Auth/refresh-token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ RefreshToken: refreshTokenValue }),
    });

    if (response.ok) {
      const refreshResult = await response.json();
      if (refreshResult.token) {
        setToken(refreshResult.token);
        setRefreshToken(refreshResult.refreshToken);
        return refreshResult;
      }
    }
  } catch (error) {
    console.error('刷新令牌失败:', error);
    logout();
  }

  return null;
}

// 便捷的HTTP方法
export const httpRequest = {
  get: <T = any>(url: string, options: Omit<RequestOptions, 'method'> = {}) =>
    enhancedRequest<T>(url, { ...options, method: 'GET' }),

  post: <T = any>(url: string, data?: any, options: Omit<RequestOptions, 'method' | 'data'> = {}) =>
    enhancedRequest<T>(url, { ...options, method: 'POST', data }),

  put: <T = any>(url: string, data?: any, options: Omit<RequestOptions, 'method' | 'data'> = {}) =>
    enhancedRequest<T>(url, { ...options, method: 'PUT', data }),

  delete: <T = any>(url: string, options: Omit<RequestOptions, 'method'> = {}) =>
    enhancedRequest<T>(url, { ...options, method: 'DELETE' }),

  patch: <T = any>(url: string, data?: any, options: Omit<RequestOptions, 'method' | 'data'> = {}) =>
    enhancedRequest<T>(url, { ...options, method: 'PATCH', data }),
};

// 兼容原有的请求拦截器（用于UmiJS的request）
export const requestInterceptor = (url: string, options: any) => {
  const token = getToken();

  if (token && !url.includes('/Auth/login') && !url.includes('/Auth/register')) {
    const headers = options.headers || {};
    headers.Authorization = `Bearer ${token}`;
    options.headers = headers;
  }

  return { url, options };
};

export const responseInterceptor = async (response: Response) => {
  if (response.status === 401) {
    const refreshResult = await tryRefreshToken();
    if (!refreshResult) {
      logout();
    }
  }
  return response;
};

export const errorHandler = (error: any) => {
  handleApiError(error, true);
  throw error;
};
