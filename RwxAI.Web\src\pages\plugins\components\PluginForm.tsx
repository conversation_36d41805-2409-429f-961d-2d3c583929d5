import React, { useEffect, useState } from 'react';
import { Modal, Form, Input, Switch, message } from 'antd';
import { useIntl, FormattedMessage } from '@umijs/max';
import { createPlugin, updatePlugin } from '@/services/rwxai';

interface PluginFormProps {
  visible: boolean;
  onVisibleChange: (visible: boolean) => void;
  initialValues?: RwxAI.Plugin;
  onSuccess: () => void;
}

const PluginForm: React.FC<PluginFormProps> = ({
  visible,
  onVisibleChange,
  initialValues,
  onSuccess,
}) => {
  const intl = useIntl();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const isEdit = !!initialValues?.Id;

  useEffect(() => {
    if (visible) {
      if (initialValues) {
        form.setFieldsValue(initialValues);
      } else {
        form.resetFields();
      }
    }
  }, [visible, initialValues]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      if (isEdit) {
        await updatePlugin(initialValues!.Id, { ...initialValues, ...values });
        message.success(intl.formatMessage({ id: 'pages.plugins.form.update.success' }));
      } else {
        await createPlugin(values);
        message.success(intl.formatMessage({ id: 'pages.plugins.form.create.success' }));
      }

      onSuccess();
    } catch (error) {
      message.error(
        intl.formatMessage({
          id: isEdit ? 'pages.plugins.form.update.error' : 'pages.plugins.form.create.error',
        })
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={intl.formatMessage({
        id: isEdit ? 'pages.plugins.form.edit.title' : 'pages.plugins.form.create.title',
      })}
      open={visible}
      onCancel={() => onVisibleChange(false)}
      onOk={handleSubmit}
      confirmLoading={loading}
      width={600}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          IsEnabled: true,
          Status: 'Inactive',
        }}
      >
        <Form.Item
          name="Name"
          label={<FormattedMessage id="pages.plugins.form.name" />}
          rules={[{ required: true, message: intl.formatMessage({ id: 'pages.plugins.form.name.required' }) }]}
        >
          <Input placeholder={intl.formatMessage({ id: 'pages.plugins.form.name.placeholder' })} />
        </Form.Item>

        <Form.Item
          name="Description"
          label={<FormattedMessage id="pages.plugins.form.description" />}
        >
          <Input.TextArea
            rows={3}
            placeholder={intl.formatMessage({ id: 'pages.plugins.form.description.placeholder' })}
          />
        </Form.Item>

        <Form.Item
          name="Version"
          label={<FormattedMessage id="pages.plugins.form.version" />}
          rules={[{ required: true, message: intl.formatMessage({ id: 'pages.plugins.form.version.required' }) }]}
        >
          <Input placeholder={intl.formatMessage({ id: 'pages.plugins.form.version.placeholder' })} />
        </Form.Item>

        <Form.Item
          name="Author"
          label={<FormattedMessage id="pages.plugins.form.author" />}
        >
          <Input placeholder={intl.formatMessage({ id: 'pages.plugins.form.author.placeholder' })} />
        </Form.Item>

        <Form.Item
          name="AssemblyPath"
          label={<FormattedMessage id="pages.plugins.form.assemblyPath" />}
          rules={[{ required: true, message: intl.formatMessage({ id: 'pages.plugins.form.assemblyPath.required' }) }]}
          help={intl.formatMessage({ id: 'pages.plugins.form.assemblyPath.help' })}
        >
          <Input placeholder={intl.formatMessage({ id: 'pages.plugins.form.assemblyPath.placeholder' })} />
        </Form.Item>

        <Form.Item
          name="TypeName"
          label={<FormattedMessage id="pages.plugins.form.typeName" />}
          rules={[{ required: true, message: intl.formatMessage({ id: 'pages.plugins.form.typeName.required' }) }]}
          help={intl.formatMessage({ id: 'pages.plugins.form.typeName.help' })}
        >
          <Input placeholder={intl.formatMessage({ id: 'pages.plugins.form.typeName.placeholder' })} />
        </Form.Item>

        <Form.Item
          name="IsEnabled"
          label={<FormattedMessage id="pages.plugins.form.isEnabled" />}
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default PluginForm;
