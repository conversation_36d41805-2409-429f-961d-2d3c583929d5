{"version": 3, "sources": ["common-async.12178176932930876729.hot-update.js", "src/pages/chat/components/SessionForm.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'common',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='44807626438597387';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/.umi/plugin-openapi/openapi.tsx\":[\"vendors\",\"src/.umi/plugin-openapi/openapi.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Admin.tsx\":[\"p__Admin\"],\"src/pages/Welcome.tsx\":[\"p__Welcome\"],\"src/pages/ai-models/index.tsx\":[\"common\",\"src/pages/ai-models/index.tsx\"],\"src/pages/api-test/index.tsx\":[\"common\",\"src/pages/api-test/index.tsx\"],\"src/pages/apps/index.tsx\":[\"common\",\"p__apps__index\"],\"src/pages/auth-test/index.tsx\":[\"common\",\"src/pages/auth-test/index.tsx\"],\"src/pages/chat/chat-interface.tsx\":[\"vendors\",\"common\",\"src/pages/chat/chat-interface.tsx\"],\"src/pages/chat/index.tsx\":[\"common\",\"p__chat__index\"],\"src/pages/chat/session/[id].tsx\":[\"common\",\"p__chat__session__id\"],\"src/pages/knowledge/[id]/files.tsx\":[\"common\",\"p__knowledge__id__files\"],\"src/pages/knowledge/index.tsx\":[\"common\",\"p__knowledge__index\"],\"src/pages/plugins/index.tsx\":[\"common\",\"p__plugins__index\"],\"src/pages/response-demo/index.tsx\":[\"src/pages/response-demo/index.tsx\"],\"src/pages/table-list/index.tsx\":[\"src/pages/table-list/index.tsx\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"],\"src/pages/user/register/index.tsx\":[\"common\",\"p__user__register__index\"]});;\r\n  },\r\n);\r\n", "import React, { useEffect, useState } from 'react';\nimport { Modal, Form, Input, Select, InputNumber, Switch, message } from 'antd';\nimport { useIntl, FormattedMessage } from '@umijs/max';\nimport { createChatSession, updateChatSession, getAIModels } from '@/services/rwxai';\n\ninterface SessionFormProps {\n  visible: boolean;\n  onVisibleChange: (visible: boolean) => void;\n  initialValues?: RwxAI.ChatSession;\n  onSuccess: () => void;\n}\n\nconst SessionForm: React.FC<SessionFormProps> = ({\n  visible,\n  onVisibleChange,\n  initialValues,\n  onSuccess,\n}) => {\n  const intl = useIntl();\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [models, setModels] = useState<RwxAI.AIModel[]>([]);\n  const [selectedModel, setSelectedModel] = useState<RwxAI.AIModel | null>(null);\n\n  const isEdit = !!initialValues?.Id;\n\n  useEffect(() => {\n    if (visible) {\n      loadModels();\n      if (initialValues) {\n        form.setFieldsValue(initialValues);\n        // 如果是编辑模式，设置选中的模型\n        if (initialValues.ModelId) {\n          const model = models.find(m => m.Id === initialValues.ModelId);\n          setSelectedModel(model || null);\n        }\n      } else {\n        form.resetFields();\n        setSelectedModel(null);\n      }\n    }\n  }, [visible, initialValues, models]);\n\n  const loadModels = async () => {\n    try {\n      const response = await getAIModels();\n      console.log('AI Models API response:', response); // 调试日志\n\n      if (response.success) {\n        // 先显示所有启用的模型，暂时不过滤类型\n        const enabledModels = (response.data || []).filter(model => {\n          console.log('Model:', model); // 调试每个模型\n          return model.IsEnabled === true;\n        });\n\n        console.log('Enabled models:', enabledModels); // 调试日志\n        setModels(enabledModels);\n      } else {\n        console.error('Failed to load models:', response);\n        setModels([]);\n      }\n    } catch (error) {\n      console.error('Error loading models:', error);\n      setModels([]);\n    }\n  };\n\n  // 处理模型选择变化\n  const handleModelChange = (modelId: string) => {\n    const model = models.find(m => m.Id === modelId);\n    setSelectedModel(model || null);\n\n    if (model) {\n      // 自动填充模型的配置参数\n      form.setFieldsValue({\n        Temperature: model.Temperature || 0.7,\n        MaxTokens: model.MaxTokens || 2000,\n        SystemPrompt: model.SystemPrompt || '',\n      });\n    }\n  };\n\n  const handleSubmit = async () => {\n    try {\n      const values = await form.validateFields();\n      setLoading(true);\n\n      let response;\n      if (isEdit) {\n        response = await updateChatSession(initialValues!.Id, { ...initialValues, ...values });\n      } else {\n        response = await createChatSession(values);\n      }\n\n      if (response.success) {\n        onSuccess();\n      }\n      // 成功和错误消息会由统一响应处理系统自动显示\n    } catch (error) {\n      // 表单验证错误等\n      console.error('Form validation error:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Modal\n      title={intl.formatMessage({\n        id: isEdit ? 'pages.chat.form.edit.title' : 'pages.chat.form.create.title',\n      })}\n      open={visible}\n      onCancel={() => onVisibleChange(false)}\n      onOk={handleSubmit}\n      confirmLoading={loading}\n      width={600}\n    >\n      <Form\n        form={form}\n        layout=\"vertical\"\n        initialValues={{\n          Temperature: 0.7,\n          MaxTokens: 2000,\n        }}\n      >\n        <Form.Item\n          name=\"Name\"\n          label={<FormattedMessage id=\"pages.chat.form.name\" />}\n          rules={[{ required: true, message: intl.formatMessage({ id: 'pages.chat.form.name.required' }) }]}\n        >\n          <Input placeholder={intl.formatMessage({ id: 'pages.chat.form.name.placeholder' })} />\n        </Form.Item>\n\n        <Form.Item\n          name=\"ModelId\"\n          label={<FormattedMessage id=\"pages.chat.form.model\" />}\n          rules={[{ required: true, message: intl.formatMessage({ id: 'pages.chat.form.model.required' }) }]}\n        >\n          <Select\n            placeholder={intl.formatMessage({ id: 'pages.chat.form.model.placeholder' })}\n            onChange={handleModelChange}\n          >\n            {models.map((model) => (\n              <Select.Option key={model.Id} value={model.Id}>\n                {model.Name || model.DisplayName} ({model.Template?.ModelId || model.ModelId || '未知模型'})\n              </Select.Option>\n            ))}\n          </Select>\n        </Form.Item>\n\n        {/* 显示从模型自动获取的配置信息 */}\n        {selectedModel && (\n          <>\n            <Form.Item\n              name=\"SystemPrompt\"\n              label={<FormattedMessage id=\"pages.chat.form.systemPrompt\" />}\n              help=\"从选中的模型自动获取\"\n            >\n              <Input.TextArea\n                rows={3}\n                disabled\n                placeholder=\"从模型配置中自动获取\"\n              />\n            </Form.Item>\n\n            <div style={{ display: 'flex', gap: '16px' }}>\n              <Form.Item\n                name=\"Temperature\"\n                label={<FormattedMessage id=\"pages.chat.form.temperature\" />}\n                style={{ flex: 1 }}\n              >\n                <InputNumber\n                  disabled\n                  style={{ width: '100%' }}\n                  placeholder=\"从模型配置中自动获取\"\n                />\n              </Form.Item>\n\n              <Form.Item\n                name=\"MaxTokens\"\n                label={<FormattedMessage id=\"pages.chat.form.maxTokens\" />}\n                style={{ flex: 1 }}\n              >\n                <InputNumber\n                  disabled\n                  style={{ width: '100%' }}\n                  placeholder=\"从模型配置中自动获取\"\n                />\n              </Form.Item>\n            </div>\n          </>\n        )}\n\n\n      </Form>\n    </Modal>\n  );\n};\n\nexport default SessionForm;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,UACA;IACE,SAAS;;;;;;wCCoMb;;;2BAAA;;;;;;oFAvM2C;yCAC8B;wCAC/B;0CACwB;;;;;;;;;;YASlE,MAAM,cAA0C,CAAC,EAC/C,OAAO,EACP,eAAe,EACf,aAAa,EACb,SAAS,EACV;;gBACC,MAAM,OAAO,IAAA,YAAO;gBACpB,MAAM,CAAC,KAAK,GAAG,UAAI,CAAC,OAAO;gBAC3B,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;gBACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,eAAQ,EAAkB,EAAE;gBACxD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAuB;gBAEzE,MAAM,SAAS,CAAC,EAAC,0BAAA,oCAAA,cAAe,EAAE;gBAElC,IAAA,gBAAS,EAAC;oBACR,IAAI,SAAS;wBACX;wBACA,IAAI,eAAe;4BACjB,KAAK,cAAc,CAAC;4BACpB,kBAAkB;4BAClB,IAAI,cAAc,OAAO,EAAE;gCACzB,MAAM,QAAQ,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,cAAc,OAAO;gCAC7D,iBAAiB,SAAS;4BAC5B;wBACF,OAAO;4BACL,KAAK,WAAW;4BAChB,iBAAiB;wBACnB;oBACF;gBACF,GAAG;oBAAC;oBAAS;oBAAe;iBAAO;gBAEnC,MAAM,aAAa;oBACjB,IAAI;wBACF,MAAM,WAAW,MAAM,IAAA,kBAAW;wBAClC,QAAQ,GAAG,CAAC,2BAA2B,WAAW,OAAO;wBAEzD,IAAI,SAAS,OAAO,EAAE;4BACpB,qBAAqB;4BACrB,MAAM,gBAAgB,AAAC,CAAA,SAAS,IAAI,IAAI,EAAE,AAAD,EAAG,MAAM,CAAC,CAAA;gCACjD,QAAQ,GAAG,CAAC,UAAU,QAAQ,SAAS;gCACvC,OAAO,MAAM,SAAS,KAAK;4BAC7B;4BAEA,QAAQ,GAAG,CAAC,mBAAmB,gBAAgB,OAAO;4BACtD,UAAU;wBACZ,OAAO;4BACL,QAAQ,KAAK,CAAC,0BAA0B;4BACxC,UAAU,EAAE;wBACd;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,yBAAyB;wBACvC,UAAU,EAAE;oBACd;gBACF;gBAEA,WAAW;gBACX,MAAM,oBAAoB,CAAC;oBACzB,MAAM,QAAQ,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBACxC,iBAAiB,SAAS;oBAE1B,IAAI,OACF,cAAc;oBACd,KAAK,cAAc,CAAC;wBAClB,aAAa,MAAM,WAAW,IAAI;wBAClC,WAAW,MAAM,SAAS,IAAI;wBAC9B,cAAc,MAAM,YAAY,IAAI;oBACtC;gBAEJ;gBAEA,MAAM,eAAe;oBACnB,IAAI;wBACF,MAAM,SAAS,MAAM,KAAK,cAAc;wBACxC,WAAW;wBAEX,IAAI;wBACJ,IAAI,QACF,WAAW,MAAM,IAAA,wBAAiB,EAAC,cAAe,EAAE,EAAE;4BAAE,GAAG,aAAa;4BAAE,GAAG,MAAM;wBAAC;6BAEpF,WAAW,MAAM,IAAA,wBAAiB,EAAC;wBAGrC,IAAI,SAAS,OAAO,EAClB;oBAEF,wBAAwB;oBAC1B,EAAE,OAAO,OAAO;wBACd,UAAU;wBACV,QAAQ,KAAK,CAAC,0BAA0B;oBAC1C,SAAU;wBACR,WAAW;oBACb;gBACF;gBAEA,qBACE,2BAAC,WAAK;oBACJ,OAAO,KAAK,aAAa,CAAC;wBACxB,IAAI,SAAS,+BAA+B;oBAC9C;oBACA,MAAM;oBACN,UAAU,IAAM,gBAAgB;oBAChC,MAAM;oBACN,gBAAgB;oBAChB,OAAO;8BAEP,cAAA,2BAAC,UAAI;wBACH,MAAM;wBACN,QAAO;wBACP,eAAe;4BACb,aAAa;4BACb,WAAW;wBACb;;0CAEA,2BAAC,UAAI,CAAC,IAAI;gCACR,MAAK;gCACL,qBAAO,2BAAC,qBAAgB;oCAAC,IAAG;;;;;;gCAC5B,OAAO;oCAAC;wCAAE,UAAU;wCAAM,SAAS,KAAK,aAAa,CAAC;4CAAE,IAAI;wCAAgC;oCAAG;iCAAE;0CAEjG,cAAA,2BAAC,WAAK;oCAAC,aAAa,KAAK,aAAa,CAAC;wCAAE,IAAI;oCAAmC;;;;;;;;;;;0CAGlF,2BAAC,UAAI,CAAC,IAAI;gCACR,MAAK;gCACL,qBAAO,2BAAC,qBAAgB;oCAAC,IAAG;;;;;;gCAC5B,OAAO;oCAAC;wCAAE,UAAU;wCAAM,SAAS,KAAK,aAAa,CAAC;4CAAE,IAAI;wCAAiC;oCAAG;iCAAE;0CAElG,cAAA,2BAAC,YAAM;oCACL,aAAa,KAAK,aAAa,CAAC;wCAAE,IAAI;oCAAoC;oCAC1E,UAAU;8CAET,OAAO,GAAG,CAAC,CAAC;4CAE2B;6DADtC,2BAAC,YAAM,CAAC,MAAM;4CAAgB,OAAO,MAAM,EAAE;;gDAC1C,MAAM,IAAI,IAAI,MAAM,WAAW;gDAAC;gDAAG,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,OAAO,KAAI,MAAM,OAAO,IAAI;gDAAO;;2CADrE,MAAM,EAAE;;;;;;;;;;;;;;;;4BAQjC,+BACC;;kDACE,2BAAC,UAAI,CAAC,IAAI;wCACR,MAAK;wCACL,qBAAO,2BAAC,qBAAgB;4CAAC,IAAG;;;;;;wCAC5B,MAAK;kDAEL,cAAA,2BAAC,WAAK,CAAC,QAAQ;4CACb,MAAM;4CACN,QAAQ;4CACR,aAAY;;;;;;;;;;;kDAIhB,2BAAC;wCAAI,OAAO;4CAAE,SAAS;4CAAQ,KAAK;wCAAO;;0DACzC,2BAAC,UAAI,CAAC,IAAI;gDACR,MAAK;gDACL,qBAAO,2BAAC,qBAAgB;oDAAC,IAAG;;;;;;gDAC5B,OAAO;oDAAE,MAAM;gDAAE;0DAEjB,cAAA,2BAAC,iBAAW;oDACV,QAAQ;oDACR,OAAO;wDAAE,OAAO;oDAAO;oDACvB,aAAY;;;;;;;;;;;0DAIhB,2BAAC,UAAI,CAAC,IAAI;gDACR,MAAK;gDACL,qBAAO,2BAAC,qBAAgB;oDAAC,IAAG;;;;;;gDAC5B,OAAO;oDAAE,MAAM;gDAAE;0DAEjB,cAAA,2BAAC,iBAAW;oDACV,QAAQ;oDACR,OAAO;wDAAE,OAAO;oDAAO;oDACvB,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAW9B;eAzLM;;oBAMS,YAAO;oBACL,UAAI,CAAC;;;iBAPhB;gBA2LN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;IDpMD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,uCAAsC;YAAC;YAAU;SAAsC;QAAC,qBAAoB;YAAC;SAAS;QAAC,uBAAsB;YAAC;SAAW;QAAC,yBAAwB;YAAC;SAAa;QAAC,iCAAgC;YAAC;YAAS;SAAgC;QAAC,gCAA+B;YAAC;YAAS;SAA+B;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,iCAAgC;YAAC;YAAS;SAAgC;QAAC,qCAAoC;YAAC;YAAU;YAAS;SAAoC;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,mCAAkC;YAAC;YAAS;SAAuB;QAAC,sCAAqC;YAAC;YAAS;SAA0B;QAAC,iCAAgC;YAAC;YAAS;SAAsB;QAAC,+BAA8B;YAAC;YAAS;SAAoB;QAAC,qCAAoC;YAAC;SAAoC;QAAC,kCAAiC;YAAC;SAAiC;QAAC,kCAAiC;YAAC;YAAS;SAAwB;QAAC,qCAAoC;YAAC;YAAS;SAA2B;IAAA;;AAC9zC"}