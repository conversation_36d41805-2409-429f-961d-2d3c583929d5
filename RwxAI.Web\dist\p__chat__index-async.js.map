{"version": 3, "sources": ["src/pages/chat/index.tsx"], "sourcesContent": ["import React, { useRef, useState } from 'react';\r\nimport { PageContainer } from '@ant-design/pro-components';\r\nimport { ProTable, ActionType, ProColumns } from '@ant-design/pro-table';\r\nimport { Button, Space, Tag, message, Modal, Switch } from 'antd';\r\nimport { PlusOutlined, EditOutlined, DeleteOutlined, MessageOutlined, EyeOutlined } from '@ant-design/icons';\r\nimport { useIntl, FormattedMessage, history } from '@umijs/max';\r\nimport { getChatSessions, deleteChatSession } from '@/services/rwxai';\r\nimport { createSimpleProTableRequest, defaultPaginationConfig } from '@/utils/pageDataHandler';\r\nimport SessionForm from './components/SessionForm';\r\n\r\nconst ChatSessionsPage: React.FC = () => {\r\n  const intl = useIntl();\r\n  const actionRef = useRef<ActionType>();\r\n  const [createModalVisible, setCreateModalVisible] = useState(false);\r\n  const [editModalVisible, setEditModalVisible] = useState(false);\r\n  const [currentRecord, setCurrentRecord] = useState<RwxAI.ChatSession>();\r\n\r\n  const handleDelete = async (record: RwxAI.ChatSession) => {\r\n    Modal.confirm({\r\n      title: intl.formatMessage({ id: 'pages.chat.delete.confirm.title' }),\r\n      content: intl.formatMessage({ id: 'pages.chat.delete.confirm.content' }),\r\n      onOk: async () => {\r\n        try {\r\n          await deleteChatSession(record.Id);\r\n          message.success(intl.formatMessage({ id: 'pages.chat.delete.success' }));\r\n          actionRef.current?.reload();\r\n        } catch (error) {\r\n          message.error(intl.formatMessage({ id: 'pages.chat.delete.error' }));\r\n        }\r\n      },\r\n    });\r\n  };\r\n\r\n  const handleChat = (record: RwxAI.ChatSession) => {\r\n    history.push(`/chat/session/${record.Id}`);\r\n  };\r\n\r\n  const columns: ProColumns<RwxAI.ChatSession>[] = [\r\n    {\r\n      title: <FormattedMessage id=\"pages.chat.table.name\" />,\r\n      dataIndex: 'Name',\r\n      key: 'Name',\r\n      ellipsis: true,\r\n    },\r\n    {\r\n      title: <FormattedMessage id=\"pages.chat.table.model\" />,\r\n      dataIndex: ['Model', 'Name'],\r\n      key: 'Model',\r\n      ellipsis: true,\r\n      hideInSearch: true,\r\n      render: (_, record) => {\r\n        // 优先使用 Model 对象中的信息\r\n        if (record.Model?.Name) {\r\n          return record.Model.Name;\r\n        }\r\n        if (record.Model?.DisplayName) {\r\n          return record.Model.DisplayName;\r\n        }\r\n        \r\n        // 如果没有 Model 对象，尝试使用 ModelId\r\n        if (record.ModelId) {\r\n          return record.ModelId;\r\n        }\r\n        \r\n        // 最后的默认值\r\n        return '未配置模型';\r\n      }\r\n    },\r\n    {\r\n      title: <FormattedMessage id=\"pages.chat.table.status\" />,\r\n      dataIndex: 'IsActive',\r\n      key: 'IsActive',\r\n      render: (_, record) => (\r\n        <Tag color={record.IsActive ? 'green' : 'default'}>\r\n          {record.IsActive ? \r\n            intl.formatMessage({ id: 'pages.chat.status.active' }) : \r\n            intl.formatMessage({ id: 'pages.chat.status.inactive' })\r\n          }\r\n        </Tag>\r\n      ),\r\n    },\r\n    {\r\n      title: <FormattedMessage id=\"pages.chat.table.messageCount\" />,\r\n      key: 'messageCount',\r\n      hideInSearch: true,\r\n      render: (_, record) => record.Messages?.length || 0,\r\n    },\r\n    {\r\n      title: <FormattedMessage id=\"pages.chat.table.temperature\" />,\r\n      dataIndex: 'Temperature',\r\n      key: 'Temperature',\r\n      hideInSearch: true,\r\n    },\r\n    {\r\n      title: <FormattedMessage id=\"pages.chat.table.maxTokens\" />,\r\n      dataIndex: 'MaxTokens',\r\n      key: 'MaxTokens',\r\n      hideInSearch: true,\r\n    },\r\n    {\r\n      title: <FormattedMessage id=\"pages.chat.table.createdTime\" />,\r\n      dataIndex: 'CreatedTime',\r\n      key: 'CreatedTime',\r\n      valueType: 'dateTime',\r\n      width: 180,\r\n      hideInSearch: true,\r\n    },\r\n    {\r\n      title: <FormattedMessage id=\"pages.chat.table.actions\" />,\r\n      key: 'actions',\r\n      width: 200,\r\n      render: (_, record) => (\r\n        <Space>\r\n          <Button\r\n            type=\"link\"\r\n            size=\"small\"\r\n            icon={<MessageOutlined />}\r\n            onClick={() => handleChat(record)}\r\n          >\r\n            <FormattedMessage id=\"pages.chat.actions.chat\" />\r\n          </Button>\r\n          <Button\r\n            type=\"link\"\r\n            size=\"small\"\r\n            icon={<EditOutlined />}\r\n            onClick={() => {\r\n              setCurrentRecord(record);\r\n              setEditModalVisible(true);\r\n            }}\r\n          >\r\n            <FormattedMessage id=\"pages.chat.actions.edit\" />\r\n          </Button>\r\n          <Button\r\n            type=\"link\"\r\n            size=\"small\"\r\n            danger\r\n            icon={<DeleteOutlined />}\r\n            onClick={() => handleDelete(record)}\r\n          >\r\n            <FormattedMessage id=\"pages.chat.actions.delete\" />\r\n          </Button>\r\n        </Space>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <PageContainer>\r\n      <ProTable<RwxAI.ChatSession>\r\n        headerTitle={intl.formatMessage({ id: 'pages.chat.title' })}\r\n        actionRef={actionRef}\r\n        rowKey=\"Id\"\r\n        search={{\r\n          labelWidth: 120,\r\n        }}\r\n        toolBarRender={() => [\r\n          <Button\r\n            type=\"primary\"\r\n            key=\"primary\"\r\n            icon={<PlusOutlined />}\r\n            onClick={() => setCreateModalVisible(true)}\r\n          >\r\n            <FormattedMessage id=\"pages.chat.actions.create\" />\r\n          </Button>,\r\n        ]}\r\n        request={createSimpleProTableRequest(getChatSessions)}\r\n        columns={columns}\r\n      />\r\n\r\n      <SessionForm\r\n        visible={createModalVisible}\r\n        onVisibleChange={setCreateModalVisible}\r\n        onSuccess={() => {\r\n          actionRef.current?.reload();\r\n          setCreateModalVisible(false);\r\n        }}\r\n      />\r\n\r\n      <SessionForm\r\n        visible={editModalVisible}\r\n        onVisibleChange={setEditModalVisible}\r\n        initialValues={currentRecord}\r\n        onSuccess={() => {\r\n          actionRef.current?.reload();\r\n          setEditModalVisible(false);\r\n        }}\r\n      />\r\n    </PageContainer>\r\n  );\r\n};\r\n\r\nexport default ChatSessionsPage;\r\n"], "names": [], "mappings": ";;;;;;;4BA+LA;;;eAAA;;;;;;;wEA/LwC;sCACV;iCACmB;6BACU;8BAC8B;4BACtC;8BACA;wCACkB;6EAC7C;;;;;;;;;;AAExB,MAAM,mBAA6B;;IACjC,MAAM,OAAO,IAAA,YAAO;IACpB,MAAM,YAAY,IAAA,aAAM;IACxB,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,IAAA,eAAQ,EAAC;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,eAAQ,EAAC;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ;IAElD,MAAM,eAAe,OAAO;QAC1B,WAAK,CAAC,OAAO,CAAC;YACZ,OAAO,KAAK,aAAa,CAAC;gBAAE,IAAI;YAAkC;YAClE,SAAS,KAAK,aAAa,CAAC;gBAAE,IAAI;YAAoC;YACtE,MAAM;gBACJ,IAAI;wBAGF;oBAFA,MAAM,IAAA,wBAAiB,EAAC,OAAO,EAAE;oBACjC,aAAO,CAAC,OAAO,CAAC,KAAK,aAAa,CAAC;wBAAE,IAAI;oBAA4B;qBACrE,qBAAA,UAAU,OAAO,cAAjB,iCAAA,mBAAmB,MAAM;gBAC3B,EAAE,OAAO,OAAO;oBACd,aAAO,CAAC,KAAK,CAAC,KAAK,aAAa,CAAC;wBAAE,IAAI;oBAA0B;gBACnE;YACF;QACF;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,YAAO,CAAC,IAAI,CAAC,CAAC,cAAc,EAAE,OAAO,EAAE,CAAC,CAAC;IAC3C;IAEA,MAAM,UAA2C;QAC/C;YACE,qBAAO,2BAAC,qBAAgB;gBAAC,IAAG;;;;;;YAC5B,WAAW;YACX,KAAK;YACL,UAAU;QACZ;QACA;YACE,qBAAO,2BAAC,qBAAgB;gBAAC,IAAG;;;;;;YAC5B,WAAW;gBAAC;gBAAS;aAAO;YAC5B,KAAK;YACL,UAAU;YACV,cAAc;YACd,QAAQ,CAAC,GAAG;oBAEN,eAGA;gBAJJ,oBAAoB;gBACpB,KAAI,gBAAA,OAAO,KAAK,cAAZ,oCAAA,cAAc,IAAI,EACpB,OAAO,OAAO,KAAK,CAAC,IAAI;gBAE1B,KAAI,iBAAA,OAAO,KAAK,cAAZ,qCAAA,eAAc,WAAW,EAC3B,OAAO,OAAO,KAAK,CAAC,WAAW;gBAGjC,6BAA6B;gBAC7B,IAAI,OAAO,OAAO,EAChB,OAAO,OAAO,OAAO;gBAGvB,SAAS;gBACT,OAAO;YACT;QACF;QACA;YACE,qBAAO,2BAAC,qBAAgB;gBAAC,IAAG;;;;;;YAC5B,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,GAAG,uBACV,2BAAC,SAAG;oBAAC,OAAO,OAAO,QAAQ,GAAG,UAAU;8BACrC,OAAO,QAAQ,GACd,KAAK,aAAa,CAAC;wBAAE,IAAI;oBAA2B,KACpD,KAAK,aAAa,CAAC;wBAAE,IAAI;oBAA6B;;;;;;QAI9D;QACA;YACE,qBAAO,2BAAC,qBAAgB;gBAAC,IAAG;;;;;;YAC5B,KAAK;YACL,cAAc;YACd,QAAQ,CAAC,GAAG;oBAAW;uBAAA,EAAA,mBAAA,OAAO,QAAQ,cAAf,uCAAA,iBAAiB,MAAM,KAAI;;QACpD;QACA;YACE,qBAAO,2BAAC,qBAAgB;gBAAC,IAAG;;;;;;YAC5B,WAAW;YACX,KAAK;YACL,cAAc;QAChB;QACA;YACE,qBAAO,2BAAC,qBAAgB;gBAAC,IAAG;;;;;;YAC5B,WAAW;YACX,KAAK;YACL,cAAc;QAChB;QACA;YACE,qBAAO,2BAAC,qBAAgB;gBAAC,IAAG;;;;;;YAC5B,WAAW;YACX,KAAK;YACL,WAAW;YACX,OAAO;YACP,cAAc;QAChB;QACA;YACE,qBAAO,2BAAC,qBAAgB;gBAAC,IAAG;;;;;;YAC5B,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,GAAG,uBACV,2BAAC,WAAK;;sCACJ,2BAAC,YAAM;4BACL,MAAK;4BACL,MAAK;4BACL,oBAAM,2BAAC,sBAAe;;;;;4BACtB,SAAS,IAAM,WAAW;sCAE1B,cAAA,2BAAC,qBAAgB;gCAAC,IAAG;;;;;;;;;;;sCAEvB,2BAAC,YAAM;4BACL,MAAK;4BACL,MAAK;4BACL,oBAAM,2BAAC,mBAAY;;;;;4BACnB,SAAS;gCACP,iBAAiB;gCACjB,oBAAoB;4BACtB;sCAEA,cAAA,2BAAC,qBAAgB;gCAAC,IAAG;;;;;;;;;;;sCAEvB,2BAAC,YAAM;4BACL,MAAK;4BACL,MAAK;4BACL,MAAM;4BACN,oBAAM,2BAAC,qBAAc;;;;;4BACrB,SAAS,IAAM,aAAa;sCAE5B,cAAA,2BAAC,qBAAgB;gCAAC,IAAG;;;;;;;;;;;;;;;;;QAI7B;KACD;IAED,qBACE,2BAAC,4BAAa;;0BACZ,2BAAC,kBAAQ;gBACP,aAAa,KAAK,aAAa,CAAC;oBAAE,IAAI;gBAAmB;gBACzD,WAAW;gBACX,QAAO;gBACP,QAAQ;oBACN,YAAY;gBACd;gBACA,eAAe,IAAM;sCACnB,2BAAC,YAAM;4BACL,MAAK;4BAEL,oBAAM,2BAAC,mBAAY;;;;;4BACnB,SAAS,IAAM,sBAAsB;sCAErC,cAAA,2BAAC,qBAAgB;gCAAC,IAAG;;;;;;2BAJjB;;;;;qBAMP;gBACD,SAAS,IAAA,4CAA2B,EAAC,sBAAe;gBACpD,SAAS;;;;;;0BAGX,2BAAC,oBAAW;gBACV,SAAS;gBACT,iBAAiB;gBACjB,WAAW;wBACT;qBAAA,qBAAA,UAAU,OAAO,cAAjB,iCAAA,mBAAmB,MAAM;oBACzB,sBAAsB;gBACxB;;;;;;0BAGF,2BAAC,oBAAW;gBACV,SAAS;gBACT,iBAAiB;gBACjB,eAAe;gBACf,WAAW;wBACT;qBAAA,qBAAA,UAAU,OAAO,cAAjB,iCAAA,mBAAmB,MAAM;oBACzB,oBAAoB;gBACtB;;;;;;;;;;;;AAIR;GAnLM;;QACS,YAAO;;;KADhB;IAqLN,WAAe"}