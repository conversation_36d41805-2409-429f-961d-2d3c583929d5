{"version": 3, "sources": ["src/pages/chat/session/[id].tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { PageContainer } from '@ant-design/pro-components';\nimport { Card, Input, Button, List, Avatar, Typography, Space, message, Spin } from 'antd';\nimport { SendOutlined, UserOutlined, RobotOutlined } from '@ant-design/icons';\nimport { useParams, useIntl, FormattedMessage } from '@umijs/max';\nimport { getChatSessionById, sendMessage } from '@/services/rwxai';\n\nconst { TextArea } = Input;\nconst { Text } = Typography;\n\nconst ChatSessionPage: React.FC = () => {\n  const intl = useIntl();\n  const { id } = useParams<{ id: string }>();\n  const [session, setSession] = useState<RwxAI.ChatSession>();\n  const [messages, setMessages] = useState<RwxAI.ChatMessage[]>([]);\n  const [inputValue, setInputValue] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [sessionLoading, setSessionLoading] = useState(true);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    if (id) {\n      loadSession();\n    }\n  }, [id]);\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  const loadSession = async () => {\n    try {\n      setSessionLoading(true);\n      const data = await getChatSessionById(id!);\n      setSession(data);\n      setMessages(data.Messages || []);\n    } catch (error) {\n      message.error(intl.formatMessage({ id: 'pages.chat.session.load.error' }));\n    } finally {\n      setSessionLoading(false);\n    }\n  };\n\n  const handleSendMessage = async () => {\n    if (!inputValue.trim() || !session) return;\n\n    const userMessage: RwxAI.ChatMessage = {\n      Id: `temp-${Date.now()}`,\n      Role: 'user',\n      Content: inputValue,\n      CreatedTime: new Date().toISOString(),\n      SessionId: session.Id,\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    setInputValue('');\n    setLoading(true);\n\n    try {\n      const response = await sendMessage(session.Id, {\n        Content: inputValue,\n        ModelId: session.ModelId,\n        Temperature: session.Temperature,\n        MaxTokens: session.MaxTokens,\n      });\n\n      setMessages(prev => [...prev, response]);\n    } catch (error) {\n      message.error(intl.formatMessage({ id: 'pages.chat.session.send.error' }));\n      // 移除临时用户消息\n      setMessages(prev => prev.filter(msg => msg.Id !== userMessage.Id));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  const renderMessage = (message: RwxAI.ChatMessage) => {\n    const isUser = message.Role === 'user';\n    return (\n      <List.Item\n        key={message.Id}\n        style={{\n          padding: '12px 0',\n          justifyContent: isUser ? 'flex-end' : 'flex-start',\n        }}\n      >\n        <div\n          style={{\n            display: 'flex',\n            flexDirection: isUser ? 'row-reverse' : 'row',\n            alignItems: 'flex-start',\n            maxWidth: '80%',\n          }}\n        >\n          <Avatar\n            icon={isUser ? <UserOutlined /> : <RobotOutlined />}\n            style={{\n              backgroundColor: isUser ? '#1890ff' : '#52c41a',\n              margin: isUser ? '0 0 0 8px' : '0 8px 0 0',\n            }}\n          />\n          <div\n            style={{\n              backgroundColor: isUser ? '#e6f7ff' : '#f6ffed',\n              padding: '8px 12px',\n              borderRadius: '8px',\n              border: `1px solid ${isUser ? '#91d5ff' : '#b7eb8f'}`,\n            }}\n          >\n            <Text>{message.Content}</Text>\n            <div style={{ marginTop: '4px', fontSize: '12px', color: '#999' }}>\n              {message.CreatedTime ? new Date(message.CreatedTime).toLocaleTimeString() : ''}\n            </div>\n          </div>\n        </div>\n      </List.Item>\n    );\n  };\n\n  if (sessionLoading) {\n    return (\n      <PageContainer>\n        <div style={{ textAlign: 'center', padding: '50px' }}>\n          <Spin size=\"large\" />\n        </div>\n      </PageContainer>\n    );\n  }\n\n  return (\n    <PageContainer\n      title={session?.Name}\n      subTitle={session?.Model?.Name}\n    >\n      <Card\n        style={{ height: 'calc(100vh - 200px)', display: 'flex', flexDirection: 'column' }}\n        bodyStyle={{ flex: 1, display: 'flex', flexDirection: 'column', padding: 0 }}\n      >\n        <div\n          style={{\n            flex: 1,\n            overflow: 'auto',\n            padding: '16px',\n            backgroundColor: '#fafafa',\n          }}\n        >\n          <List\n            dataSource={messages}\n            renderItem={renderMessage}\n            style={{ backgroundColor: 'transparent' }}\n          />\n          <div ref={messagesEndRef} />\n        </div>\n        \n        <div style={{ padding: '16px', borderTop: '1px solid #f0f0f0' }}>\n          <Space.Compact style={{ width: '100%' }}>\n            <TextArea\n              value={inputValue}\n              onChange={(e) => setInputValue(e.target.value)}\n              onKeyPress={handleKeyPress}\n              placeholder={intl.formatMessage({ id: 'pages.chat.session.input.placeholder' })}\n              autoSize={{ minRows: 1, maxRows: 4 }}\n              disabled={loading}\n            />\n            <Button\n              type=\"primary\"\n              icon={<SendOutlined />}\n              onClick={handleSendMessage}\n              loading={loading}\n              disabled={!inputValue.trim()}\n            >\n              <FormattedMessage id=\"pages.chat.session.send\" />\n            </Button>\n          </Space.Compact>\n        </div>\n      </Card>\n    </PageContainer>\n  );\n};\n\nexport default ChatSessionPage;\n"], "names": [], "mappings": ";;;;;;;4BA+LA;;;eAAA;;;;;;wEA/LmD;sCACrB;6BACsD;8BAC1B;4BACL;8BACL;;;;;;;;;;AAEhD,MAAM,EAAE,QAAQ,EAAE,GAAG,WAAK;AAC1B,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAU;AAE3B,MAAM,kBAA4B;QAqIlB;;IApId,MAAM,OAAO,IAAA,YAAO;IACpB,MAAM,EAAE,EAAE,EAAE,GAAG,IAAA,cAAS;IACxB,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ;IACtC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAsB,EAAE;IAChE,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAC;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,eAAQ,EAAC;IACrD,MAAM,iBAAiB,IAAA,aAAM,EAAiB;IAE9C,IAAA,gBAAS,EAAC;QACR,IAAI,IACF;IAEJ,GAAG;QAAC;KAAG;IAEP,IAAA,gBAAS,EAAC;QACR;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,iBAAiB;YACrB;SAAA,0BAAA,eAAe,OAAO,cAAtB,sCAAA,wBAAwB,cAAc,CAAC;YAAE,UAAU;QAAS;IAC9D;IAEA,MAAM,cAAc;QAClB,IAAI;YACF,kBAAkB;YAClB,MAAM,OAAO,MAAM,IAAA,yBAAkB,EAAC;YACtC,WAAW;YACX,YAAY,KAAK,QAAQ,IAAI,EAAE;QACjC,EAAE,OAAO,OAAO;YACd,aAAO,CAAC,KAAK,CAAC,KAAK,aAAa,CAAC;gBAAE,IAAI;YAAgC;QACzE,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,WAAW,IAAI,MAAM,CAAC,SAAS;QAEpC,MAAM,cAAiC;YACrC,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,CAAC;YACxB,MAAM;YACN,SAAS;YACT,aAAa,IAAI,OAAO,WAAW;YACnC,WAAW,QAAQ,EAAE;QACvB;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,cAAc;QACd,WAAW;QAEX,IAAI;YACF,MAAM,WAAW,MAAM,IAAA,kBAAW,EAAC,QAAQ,EAAE,EAAE;gBAC7C,SAAS;gBACT,SAAS,QAAQ,OAAO;gBACxB,aAAa,QAAQ,WAAW;gBAChC,WAAW,QAAQ,SAAS;YAC9B;YAEA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAS;QACzC,EAAE,OAAO,OAAO;YACd,aAAO,CAAC,KAAK,CAAC,KAAK,aAAa,CAAC;gBAAE,IAAI;YAAgC;YACvE,WAAW;YACX,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,YAAY,EAAE;QAClE,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,SAAS,QAAQ,IAAI,KAAK;QAChC,qBACE,2BAAC,UAAI,CAAC,IAAI;YAER,OAAO;gBACL,SAAS;gBACT,gBAAgB,SAAS,aAAa;YACxC;sBAEA,cAAA,2BAAC;gBACC,OAAO;oBACL,SAAS;oBACT,eAAe,SAAS,gBAAgB;oBACxC,YAAY;oBACZ,UAAU;gBACZ;;kCAEA,2BAAC,YAAM;wBACL,MAAM,uBAAS,2BAAC,mBAAY;;;;mDAAM,2BAAC,oBAAa;;;;;wBAChD,OAAO;4BACL,iBAAiB,SAAS,YAAY;4BACtC,QAAQ,SAAS,cAAc;wBACjC;;;;;;kCAEF,2BAAC;wBACC,OAAO;4BACL,iBAAiB,SAAS,YAAY;4BACtC,SAAS;4BACT,cAAc;4BACd,QAAQ,CAAC,UAAU,EAAE,SAAS,YAAY,UAAU,CAAC;wBACvD;;0CAEA,2BAAC;0CAAM,QAAQ,OAAO;;;;;;0CACtB,2BAAC;gCAAI,OAAO;oCAAE,WAAW;oCAAO,UAAU;oCAAQ,OAAO;gCAAO;0CAC7D,QAAQ,WAAW,GAAG,IAAI,KAAK,QAAQ,WAAW,EAAE,kBAAkB,KAAK;;;;;;;;;;;;;;;;;;WA/B7E,QAAQ,EAAE;;;;;IAqCrB;IAEA,IAAI,gBACF,qBACE,2BAAC,4BAAa;kBACZ,cAAA,2BAAC;YAAI,OAAO;gBAAE,WAAW;gBAAU,SAAS;YAAO;sBACjD,cAAA,2BAAC,UAAI;gBAAC,MAAK;;;;;;;;;;;;;;;;IAMnB,qBACE,2BAAC,4BAAa;QACZ,KAAK,EAAE,oBAAA,8BAAA,QAAS,IAAI;QACpB,QAAQ,EAAE,oBAAA,+BAAA,iBAAA,QAAS,KAAK,cAAd,qCAAA,eAAgB,IAAI;kBAE9B,cAAA,2BAAC,UAAI;YACH,OAAO;gBAAE,QAAQ;gBAAuB,SAAS;gBAAQ,eAAe;YAAS;YACjF,WAAW;gBAAE,MAAM;gBAAG,SAAS;gBAAQ,eAAe;gBAAU,SAAS;YAAE;;8BAE3E,2BAAC;oBACC,OAAO;wBACL,MAAM;wBACN,UAAU;wBACV,SAAS;wBACT,iBAAiB;oBACnB;;sCAEA,2BAAC,UAAI;4BACH,YAAY;4BACZ,YAAY;4BACZ,OAAO;gCAAE,iBAAiB;4BAAc;;;;;;sCAE1C,2BAAC;4BAAI,KAAK;;;;;;;;;;;;8BAGZ,2BAAC;oBAAI,OAAO;wBAAE,SAAS;wBAAQ,WAAW;oBAAoB;8BAC5D,cAAA,2BAAC,WAAK,CAAC,OAAO;wBAAC,OAAO;4BAAE,OAAO;wBAAO;;0CACpC,2BAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,YAAY;gCACZ,aAAa,KAAK,aAAa,CAAC;oCAAE,IAAI;gCAAuC;gCAC7E,UAAU;oCAAE,SAAS;oCAAG,SAAS;gCAAE;gCACnC,UAAU;;;;;;0CAEZ,2BAAC,YAAM;gCACL,MAAK;gCACL,oBAAM,2BAAC,mBAAY;;;;;gCACnB,SAAS;gCACT,SAAS;gCACT,UAAU,CAAC,WAAW,IAAI;0CAE1B,cAAA,2BAAC,qBAAgB;oCAAC,IAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnC;GAnLM;;QACS,YAAO;QACL,cAAS;;;KAFpB;IAqLN,WAAe"}