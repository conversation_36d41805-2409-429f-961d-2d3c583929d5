export default {
  // 用户认证
  'pages.login.username.placeholder': '用户名',
  'pages.login.username.required': '请输入用户名!',
  'pages.login.password.placeholder': '密码',
  'pages.login.password.required': '请输入密码！',
  'pages.login.success': '登录成功！',
  'pages.login.failure': '登录失败，请重试！',
  'pages.register.submit': '注册',
  'pages.register.success': '注册成功！',
  'pages.register.failure': '注册失败，请重试！',
  'pages.register.username.placeholder': '用户名',
  'pages.register.username.required': '请输入用户名!',
  'pages.register.email.placeholder': '邮箱',
  'pages.register.email.required': '请输入邮箱!',
  'pages.register.email.invalid': '邮箱格式错误!',
  'pages.register.firstName.placeholder': '名',
  'pages.register.lastName.placeholder': '姓',
  'pages.register.password.placeholder': '密码',
  'pages.register.password.required': '请输入密码！',
  'pages.register.password.min': '密码长度不能少于6位！',
  'pages.register.loginAccount': '已有账户？去登录',
  // 聊天会话管理
  'pages.chat.title': '聊天会话管理',
  'pages.chat.table.name': '会话名称',
  'pages.chat.table.model': '使用模型',
  'pages.chat.table.status': '状态',
  'pages.chat.table.messageCount': '消息数量',
  'pages.chat.table.temperature': '温度',
  'pages.chat.table.maxTokens': '最大令牌数',
  'pages.chat.table.createdTime': '创建时间',
  'pages.chat.table.actions': '操作',
  'pages.chat.actions.create': '新建会话',
  'pages.chat.actions.chat': '开始聊天',
  'pages.chat.actions.edit': '编辑',
  'pages.chat.actions.delete': '删除',
  'pages.chat.status.active': '活跃',
  'pages.chat.status.inactive': '非活跃',
  'pages.chat.delete.confirm.title': '确认删除',
  'pages.chat.delete.confirm.content': '确定要删除这个聊天会话吗？',
  'pages.chat.delete.success': '删除成功',
  'pages.chat.delete.error': '删除失败',
  'pages.chat.form.create.title': '新建聊天会话',
  'pages.chat.form.edit.title': '编辑聊天会话',
  'pages.chat.form.name': '会话名称',
  'pages.chat.form.name.required': '请输入会话名称',
  'pages.chat.form.name.placeholder': '请输入会话名称',
  'pages.chat.form.model': '选择模型',
  'pages.chat.form.model.required': '请选择模型',
  'pages.chat.form.model.placeholder': '请选择聊天模型',
  'pages.chat.form.systemPrompt': '系统提示词',
  'pages.chat.form.systemPrompt.placeholder': '请输入系统提示词',
  'pages.chat.form.systemPrompt.help': '系统提示词将影响AI的回答风格和行为',
  'pages.chat.form.temperature': '温度',
  'pages.chat.form.temperature.placeholder': '请输入温度值',
  'pages.chat.form.temperature.help': '控制回答的随机性，0-2之间',
  'pages.chat.form.maxTokens': '最大令牌数',
  'pages.chat.form.maxTokens.placeholder': '请输入最大令牌数',
  'pages.chat.form.maxTokens.help': '单次回答的最大长度',
  'pages.chat.form.isActive': '激活状态',
  'pages.chat.form.create.success': '创建成功',
  'pages.chat.form.create.error': '创建失败',
  'pages.chat.form.update.success': '更新成功',
  'pages.chat.form.update.error': '更新失败',
  'pages.chat.form.loadModels.error': '加载模型失败',

  // 聊天界面
  'pages.chat.session.load.error': '加载会话失败',
  'pages.chat.session.send.error': '发送消息失败',
  'pages.chat.session.input.placeholder': '请输入您的消息...',
  'pages.chat.session.send': '发送',

  // 知识库管理
  'pages.knowledge.title': '知识库管理',
  'pages.knowledge.table.name': '知识库名称',
  'pages.knowledge.table.description': '描述',
  'pages.knowledge.table.chunkSize': '分块大小',
  'pages.knowledge.table.chunkOverlap': '分块重叠',
  'pages.knowledge.table.createdTime': '创建时间',
  'pages.knowledge.table.actions': '操作',
  'pages.knowledge.actions.create': '新建知识库',
  'pages.knowledge.actions.view': '查看',
  'pages.knowledge.actions.files': '文件管理',
  'pages.knowledge.actions.edit': '编辑',
  'pages.knowledge.actions.delete': '删除',
  'pages.knowledge.delete.confirm.title': '确认删除',
  'pages.knowledge.delete.confirm.content': '确定要删除这个知识库吗？',
  'pages.knowledge.delete.success': '删除成功',
  'pages.knowledge.delete.error': '删除失败',
  'pages.knowledge.form.create.title': '新建知识库',
  'pages.knowledge.form.edit.title': '编辑知识库',
  'pages.knowledge.form.name': '知识库名称',
  'pages.knowledge.form.name.required': '请输入知识库名称',
  'pages.knowledge.form.name.placeholder': '请输入知识库名称',
  'pages.knowledge.form.description': '知识库描述',
  'pages.knowledge.form.description.placeholder': '请输入知识库描述',
  'pages.knowledge.form.embeddingModel': '嵌入模型',
  'pages.knowledge.form.embeddingModel.required': '请选择嵌入模型',
  'pages.knowledge.form.embeddingModel.placeholder': '请选择嵌入模型',
  'pages.knowledge.form.chunkSize': '分块大小',
  'pages.knowledge.form.chunkSize.required': '请输入分块大小',
  'pages.knowledge.form.chunkSize.placeholder': '请输入分块大小',
  'pages.knowledge.form.chunkSize.help': '文档分块的字符数量',
  'pages.knowledge.form.chunkOverlap': '分块重叠',
  'pages.knowledge.form.chunkOverlap.required': '请输入分块重叠',
  'pages.knowledge.form.chunkOverlap.placeholder': '请输入分块重叠',
  'pages.knowledge.form.chunkOverlap.help': '相邻分块之间的重叠字符数',
  'pages.knowledge.form.create.success': '创建成功',
  'pages.knowledge.form.create.error': '创建失败',
  'pages.knowledge.form.update.success': '更新成功',
  'pages.knowledge.form.update.error': '更新失败',
  'pages.knowledge.form.loadModels.error': '加载模型失败',
  'pages.knowledge.detail.title': '知识库详情',
  'pages.knowledge.detail.name': '知识库名称',
  'pages.knowledge.detail.description': '描述',
  'pages.knowledge.detail.chunkSize': '分块大小',
  'pages.knowledge.detail.chunkOverlap': '分块重叠',
  'pages.knowledge.detail.embeddingModelId': '嵌入模型ID',
  'pages.knowledge.detail.createdTime': '创建时间',
  'pages.knowledge.detail.updatedTime': '更新时间',
  'pages.knowledge.detail.metadataJson': '元数据',

  // 知识库文件管理
  'pages.knowledge.files.title': '知识库文件管理',
  'pages.knowledge.files.subtitle': '文件管理',
  'pages.knowledge.files.table.fileName': '文件名',
  'pages.knowledge.files.table.contentType': '文件类型',
  'pages.knowledge.files.table.fileSize': '文件大小',
  'pages.knowledge.files.table.status': '处理状态',
  'pages.knowledge.files.table.processedTime': '处理时间',
  'pages.knowledge.files.table.createdTime': '上传时间',
  'pages.knowledge.files.table.actions': '操作',
  'pages.knowledge.files.actions.upload': '上传文件',
  'pages.knowledge.files.actions.process': '处理',
  'pages.knowledge.files.actions.delete': '删除',
  'pages.knowledge.files.status.processed': '已处理',
  'pages.knowledge.files.status.pending': '待处理',
  'pages.knowledge.files.delete.confirm.title': '确认删除',
  'pages.knowledge.files.delete.confirm.content': '确定要删除这个文件吗？',
  'pages.knowledge.files.delete.success': '删除成功',
  'pages.knowledge.files.delete.error': '删除失败',
  'pages.knowledge.files.process.success': '处理成功',
  'pages.knowledge.files.process.error': '处理失败',
  'pages.knowledge.files.upload.title': '上传文件',
  'pages.knowledge.files.upload.success': '上传成功',
  'pages.knowledge.files.upload.error': '上传失败',
  'pages.knowledge.files.upload.dragText': '点击或拖拽文件到此区域上传',
  'pages.knowledge.files.upload.hint': '支持单个文件上传，支持常见文档格式',
  'pages.knowledge.files.loadKnowledge.error': '加载知识库信息失败',

  // 插件管理
  'pages.plugins.title': '插件管理',
  'pages.plugins.table.name': '插件名称',
  'pages.plugins.table.description': '插件描述',
  'pages.plugins.table.version': '版本',
  'pages.plugins.table.author': '作者',
  'pages.plugins.table.status': '状态',
  'pages.plugins.table.enabled': '启用状态',
  'pages.plugins.table.functions': '功能数量',
  'pages.plugins.table.createdTime': '创建时间',
  'pages.plugins.table.actions': '操作',
  'pages.plugins.actions.create': '新建插件',
  'pages.plugins.actions.view': '查看',
  'pages.plugins.actions.functions': '功能管理',
  'pages.plugins.actions.edit': '编辑',
  'pages.plugins.actions.delete': '删除',
  'pages.plugins.status.active': '活跃',
  'pages.plugins.status.inactive': '非活跃',
  'pages.plugins.status.error': '错误',
  'pages.plugins.delete.confirm.title': '确认删除',
  'pages.plugins.delete.confirm.content': '确定要删除这个插件吗？',
  'pages.plugins.delete.success': '删除成功',
  'pages.plugins.delete.error': '删除失败',
  'pages.plugins.status.update.success': '状态更新成功',
  'pages.plugins.status.update.error': '状态更新失败',
  'pages.plugins.form.create.title': '新建插件',
  'pages.plugins.form.edit.title': '编辑插件',
  'pages.plugins.form.name': '插件名称',
  'pages.plugins.form.name.required': '请输入插件名称',
  'pages.plugins.form.name.placeholder': '请输入插件名称',
  'pages.plugins.form.description': '插件描述',
  'pages.plugins.form.description.placeholder': '请输入插件描述',
  'pages.plugins.form.version': '版本',
  'pages.plugins.form.version.required': '请输入版本号',
  'pages.plugins.form.version.placeholder': '请输入版本号',
  'pages.plugins.form.author': '作者',
  'pages.plugins.form.author.placeholder': '请输入作者',
  'pages.plugins.form.assemblyPath': '程序集路径',
  'pages.plugins.form.assemblyPath.required': '请输入程序集路径',
  'pages.plugins.form.assemblyPath.placeholder': '请输入程序集路径',
  'pages.plugins.form.assemblyPath.help': '插件DLL文件的完整路径',
  'pages.plugins.form.typeName': '类型名称',
  'pages.plugins.form.typeName.required': '请输入类型名称',
  'pages.plugins.form.typeName.placeholder': '请输入类型名称',
  'pages.plugins.form.typeName.help': '插件主类的完整类型名称',
  'pages.plugins.form.isEnabled': '启用状态',
  'pages.plugins.form.create.success': '创建成功',
  'pages.plugins.form.create.error': '创建失败',
  'pages.plugins.form.update.success': '更新成功',
  'pages.plugins.form.update.error': '更新失败',
  'pages.plugins.detail.title': '插件详情',
  'pages.plugins.detail.name': '插件名称',
  'pages.plugins.detail.description': '插件描述',
  'pages.plugins.detail.version': '版本',
  'pages.plugins.detail.author': '作者',
  'pages.plugins.detail.status': '状态',
  'pages.plugins.detail.enabled': '启用状态',
  'pages.plugins.detail.assemblyPath': '程序集路径',
  'pages.plugins.detail.typeName': '类型名称',
  'pages.plugins.detail.functions': '功能数量',
  'pages.plugins.detail.createdTime': '创建时间',
  'pages.plugins.detail.updatedTime': '更新时间',
};
