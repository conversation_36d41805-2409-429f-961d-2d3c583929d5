{"version": 3, "sources": ["common-async.7343740033512506034.hot-update.js"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'common',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='11456163715654709';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/.umi/plugin-openapi/openapi.tsx\":[\"vendors\",\"src/.umi/plugin-openapi/openapi.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Admin.tsx\":[\"p__Admin\"],\"src/pages/Welcome.tsx\":[\"p__Welcome\"],\"src/pages/ai-models/index.tsx\":[\"common\",\"src/pages/ai-models/index.tsx\"],\"src/pages/api-test/index.tsx\":[\"common\",\"src/pages/api-test/index.tsx\"],\"src/pages/apps/index.tsx\":[\"common\",\"p__apps__index\"],\"src/pages/auth-test/index.tsx\":[\"common\",\"src/pages/auth-test/index.tsx\"],\"src/pages/chat/chat-interface.tsx\":[\"vendors\",\"common\",\"src/pages/chat/chat-interface.tsx\"],\"src/pages/chat/index.tsx\":[\"common\",\"p__chat__index\"],\"src/pages/chat/session/[id].tsx\":[\"common\",\"p__chat__session__id\"],\"src/pages/knowledge/[id]/files.tsx\":[\"common\",\"p__knowledge__id__files\"],\"src/pages/knowledge/index.tsx\":[\"common\",\"p__knowledge__index\"],\"src/pages/plugins/index.tsx\":[\"common\",\"p__plugins__index\"],\"src/pages/response-demo/index.tsx\":[\"src/pages/response-demo/index.tsx\"],\"src/pages/table-list/index.tsx\":[\"src/pages/table-list/index.tsx\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"],\"src/pages/user/register/index.tsx\":[\"common\",\"p__user__register__index\"]});;\r\n  },\r\n);\r\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,UACA;IACE,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAAC;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,uCAAsC;YAAC;YAAU;SAAsC;QAAC,qBAAoB;YAAC;SAAS;QAAC,uBAAsB;YAAC;SAAW;QAAC,yBAAwB;YAAC;SAAa;QAAC,iCAAgC;YAAC;YAAS;SAAgC;QAAC,gCAA+B;YAAC;YAAS;SAA+B;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,iCAAgC;YAAC;YAAS;SAAgC;QAAC,qCAAoC;YAAC;YAAU;YAAS;SAAoC;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,mCAAkC;YAAC;YAAS;SAAuB;QAAC,sCAAqC;YAAC;YAAS;SAA0B;QAAC,iCAAgC;YAAC;YAAS;SAAsB;QAAC,+BAA8B;YAAC;YAAS;SAAoB;QAAC,qCAAoC;YAAC;SAAoC;QAAC,kCAAiC;YAAC;SAAiC;QAAC,kCAAiC;YAAC;YAAS;SAAwB;QAAC,qCAAoC;YAAC;YAAS;SAA2B;IAAA;;AAC9zC"}