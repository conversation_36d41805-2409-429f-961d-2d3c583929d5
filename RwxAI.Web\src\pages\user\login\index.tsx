import {
  LockOutlined,
  UserOutlined,
  RobotOutlined,
  SafetyCertificateOutlined,
} from '@ant-design/icons';
import {
  LoginForm,
  ProFormCheckbox,
  ProFormText,
} from '@ant-design/pro-components';
import {
  FormattedMessage,
  Helmet,
  SelectLang,
  useIntl,
  useModel,
} from '@umijs/max';
import { Alert, App, Card, Typography } from 'antd';
import { createStyles } from 'antd-style';
import React, { useState } from 'react';
import { flushSync } from 'react-dom';
import { login } from '@/services/rwxai';
import { saveLoginInfo } from '@/utils/auth';
import Settings from '../../../../config/defaultSettings';

const { Title, Paragraph } = Typography;

const useStyles = createStyles(({ token }) => {
  return {
    container: {
      display: 'flex',
      height: '100vh',
      overflow: 'hidden',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      position: 'relative',
    },
    leftSection: {
      flex: 1,
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      padding: '0 80px',
      color: 'white',
      position: 'relative',
      '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: 'rgba(0, 0, 0, 0.1)',
        backdropFilter: 'blur(10px)',
      },
    },
    rightSection: {
      width: '420px',
      minWidth: '380px',
      maxWidth: '450px',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      padding: '30px 25px',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      backdropFilter: 'blur(20px)',
      boxShadow: '-10px 0 30px rgba(0, 0, 0, 0.1)',
      '@media (max-width: 768px)': {
        width: '100%',
        minWidth: 'auto',
        maxWidth: 'none',
        padding: '20px',
      },
    },
    brandSection: {
      textAlign: 'center',
      marginBottom: '60px',
      position: 'relative',
      zIndex: 1,
    },
    brandTitle: {
      fontSize: '48px',
      fontWeight: 'bold',
      marginBottom: '16px',
      background: 'linear-gradient(45deg, #fff, #f0f0f0)',
      backgroundClip: 'text',
      WebkitBackgroundClip: 'text',
      WebkitTextFillColor: 'transparent',
      textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)',
    },
    brandSubtitle: {
      fontSize: '18px',
      opacity: 0.9,
      marginBottom: '40px',
    },
    featureList: {
      listStyle: 'none',
      padding: 0,
      margin: 0,
      '& li': {
        display: 'flex',
        alignItems: 'center',
        marginBottom: '20px',
        fontSize: '16px',
        '& .anticon': {
          marginRight: '12px',
          fontSize: '20px',
          color: '#fff',
        },
      },
    },
    loginCard: {
      width: '100%',
      maxWidth: '380px',
      border: 'none',
      borderRadius: '12px',
      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
      overflow: 'hidden',
      '& .ant-card-body': {
        padding: '0',
      },
    },
    loginHeader: {
      textAlign: 'center',
      marginBottom: '32px',
    },
    logo: {
      width: '64px',
      height: '64px',
      marginBottom: '16px',
    },
    loginTitle: {
      fontSize: '28px',
      fontWeight: 'bold',
      color: token.colorText,
      marginBottom: '8px',
    },
    loginSubtitle: {
      color: token.colorTextSecondary,
      fontSize: '14px',
    },
    lang: {
      position: 'fixed',
      top: 16,
      right: 16,
      zIndex: 1000,
      width: 42,
      height: 42,
      lineHeight: '42px',
      borderRadius: token.borderRadius,
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      backdropFilter: 'blur(10px)',
      ':hover': {
        backgroundColor: 'rgba(255, 255, 255, 0.2)',
      },
    },
    formItem: {
      marginBottom: '24px',
      '& .ant-input-affix-wrapper': {
        height: '48px',
        borderRadius: '8px',
        border: '1px solid #e0e0e0',
        '&:hover': {
          borderColor: token.colorPrimary,
        },
        '&:focus-within': {
          borderColor: token.colorPrimary,
          boxShadow: `0 0 0 2px ${token.colorPrimary}20`,
        },
      },
    },
    submitButton: {
      height: '48px',
      borderRadius: '8px',
      fontSize: '16px',
      fontWeight: 'bold',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      border: 'none',
      '&:hover': {
        background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
      },
    },
  };
});

const BrandSection = () => {
  const { styles } = useStyles();
  const intl = useIntl();

  return (
    <div className={styles.brandSection}>
      <Title className={styles.brandTitle}>RwxAI</Title>
      <Paragraph className={styles.brandSubtitle}>
        {intl.formatMessage({
          id: 'pages.login.brand.subtitle',
          defaultMessage: '智能AI助手平台，让AI为您的工作赋能',
        })}
      </Paragraph>
      <ul className={styles.featureList}>
        <li>
          <RobotOutlined />
          {intl.formatMessage({
            id: 'pages.login.feature.ai',
            defaultMessage: '强大的AI模型集成',
          })}
        </li>
        <li>
          <SafetyCertificateOutlined />
          {intl.formatMessage({
            id: 'pages.login.feature.security',
            defaultMessage: '企业级安全保障',
          })}
        </li>
        <li>
          <UserOutlined />
          {intl.formatMessage({
            id: 'pages.login.feature.easy',
            defaultMessage: '简单易用的界面',
          })}
        </li>
      </ul>
    </div>
  );
};

const Lang = () => {
  const { styles } = useStyles();

  return (
    <div className={styles.lang} data-lang>
      {SelectLang && <SelectLang />}
    </div>
  );
};

const LoginMessage: React.FC<{
  content: string;
}> = ({ content }) => {
  return (
    <Alert
      style={{
        marginBottom: 24,
      }}
      message={content}
      type="error"
      showIcon
    />
  );
};

const Login: React.FC = () => {
  const [userLoginState, setUserLoginState] = useState<API.LoginResult>({});
  const { initialState, setInitialState } = useModel('@@initialState');
  const { styles } = useStyles();
  const { message } = App.useApp();
  const intl = useIntl();

  const fetchUserInfo = async () => {
    const userInfo = await initialState?.fetchUserInfo?.();
    if (userInfo) {
      flushSync(() => {
        setInitialState((s) => ({
          ...s,
          currentUser: userInfo,
        }));
      });
    }
  };

  const handleSubmit = async (values: API.LoginParams) => {
    try {
      // 调用RwxAI登录接口
      const loginData = {
        Username: values.username,
        Password: values.password,
      };

      const response = await login(loginData);

      if (response.success && response.data?.token) {
        // 保存JWT令牌和用户信息
        saveLoginInfo(response.data);

        const defaultLoginSuccessMessage = intl.formatMessage({
          id: 'pages.login.success',
          defaultMessage: '登录成功！',
        });
        message.success(defaultLoginSuccessMessage);

        // 刷新用户信息
        await fetchUserInfo();

        // 跳转到目标页面
        const urlParams = new URL(window.location.href).searchParams;
        window.location.href = urlParams.get('redirect') || '/';
        return;
      }

      // 登录失败
      setUserLoginState({ status: 'error', type: 'account' });
    } catch (error: any) {
      const defaultLoginFailureMessage = intl.formatMessage({
        id: 'pages.login.failure',
        defaultMessage: '登录失败，请重试！',
      });
      console.error('登录错误:', error);
      message.error(error.message || defaultLoginFailureMessage);
      setUserLoginState({ status: 'error', type: 'account' });
    }
  };
  const { status } = userLoginState;

  return (
    <div className={styles.container}>
      <Helmet>
        <title>
          {intl.formatMessage({
            id: 'menu.login',
            defaultMessage: '登录页',
          })}
          {Settings.title && ` - ${Settings.title}`}
        </title>
      </Helmet>
      <Lang />

      {/* 左侧品牌展示区域 */}
      <div className={styles.leftSection}>
        <BrandSection />
      </div>

      {/* 右侧登录表单区域 */}
      <div className={styles.rightSection}>
        <Card className={styles.loginCard}>
          <div className={styles.loginHeader}>
            <img alt="logo" src="/logo.svg" className={styles.logo} />
            <Title level={2} className={styles.loginTitle}>
              {intl.formatMessage({
                id: 'pages.login.title',
                defaultMessage: '欢迎登录',
              })}
            </Title>
            <div className={styles.loginSubtitle}>
              {intl.formatMessage({
                id: 'pages.login.subtitle',
                defaultMessage: '请输入您的账号和密码',
              })}
            </div>
          </div>

          <LoginForm
            submitter={{
              searchConfig: {
                submitText: intl.formatMessage({
                  id: 'pages.login.submit',
                  defaultMessage: '登录',
                }),
              },
              submitButtonProps: {
                className: styles.submitButton,
                size: 'large',
              },
            }}
            onFinish={async (values) => {
              await handleSubmit(values as API.LoginParams);
            }}
          >
            {status === 'error' && (
              <LoginMessage
                content={intl.formatMessage({
                  id: 'pages.login.accountLogin.errorMessage',
                  defaultMessage: '用户名或密码错误，请重试',
                })}
              />
            )}

            <div className={styles.formItem}>
              <ProFormText
                name="username"
                fieldProps={{
                  size: 'large',
                  prefix: <UserOutlined style={{ color: '#999' }} />,
                }}
                placeholder={intl.formatMessage({
                  id: 'pages.login.username.placeholder',
                  defaultMessage: '请输入用户名',
                })}
                rules={[
                  {
                    required: true,
                    message: (
                      <FormattedMessage
                        id="pages.login.username.required"
                        defaultMessage="请输入用户名!"
                      />
                    ),
                  },
                ]}
              />
            </div>

            <div className={styles.formItem}>
              <ProFormText.Password
                name="password"
                fieldProps={{
                  size: 'large',
                  prefix: <LockOutlined style={{ color: '#999' }} />,
                }}
                placeholder={intl.formatMessage({
                  id: 'pages.login.password.placeholder',
                  defaultMessage: '请输入密码',
                })}
                rules={[
                  {
                    required: true,
                    message: (
                      <FormattedMessage
                        id="pages.login.password.required"
                        defaultMessage="请输入密码！"
                      />
                    ),
                  },
                ]}
              />
            </div>

            <div
              style={{
                marginBottom: 24,
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <ProFormCheckbox noStyle name="autoLogin">
                <FormattedMessage
                  id="pages.login.rememberMe"
                  defaultMessage="记住我"
                />
              </ProFormCheckbox>
              <a
                style={{
                  color: '#667eea',
                  textDecoration: 'none',
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.textDecoration = 'underline';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.textDecoration = 'none';
                }}
              >
                <FormattedMessage
                  id="pages.login.forgotPassword"
                  defaultMessage="忘记密码？"
                />
              </a>
            </div>
          </LoginForm>
        </Card>
      </div>
    </div>
  );
};

export default Login;
