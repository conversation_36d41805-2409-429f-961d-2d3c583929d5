import React, { useRef, useState } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import { ProTable, ActionType, ProColumns } from '@ant-design/pro-table';
import { Button, Space, message, Modal } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, FileOutlined, EyeOutlined } from '@ant-design/icons';
import { useIntl, FormattedMessage, history } from '@umijs/max';
import { getKnowledgeBases, deleteKnowledgeBase } from '@/services/rwxai';
import { createSimpleProTableRequest, defaultPaginationConfig } from '@/utils/pageDataHandler';
import KnowledgeForm from './components/KnowledgeForm';
import KnowledgeDetail from './components/KnowledgeDetail';

const KnowledgePage: React.FC = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<RwxAI.Knowledge>();

  const handleDelete = async (record: RwxAI.Knowledge) => {
    Modal.confirm({
      title: intl.formatMessage({ id: 'pages.knowledge.delete.confirm.title' }),
      content: intl.formatMessage({ id: 'pages.knowledge.delete.confirm.content' }),
      onOk: async () => {
        try {
          await deleteKnowledgeBase(record.Id);
          message.success(intl.formatMessage({ id: 'pages.knowledge.delete.success' }));
          actionRef.current?.reload();
        } catch (error) {
          message.error(intl.formatMessage({ id: 'pages.knowledge.delete.error' }));
        }
      },
    });
  };

  const handleManageFiles = (record: RwxAI.Knowledge) => {
    history.push(`/knowledge/${record.Id}/files`);
  };

  const columns: ProColumns<RwxAI.Knowledge>[] = [
    {
      title: <FormattedMessage id="pages.knowledge.table.name" />,
      dataIndex: 'Name',
      key: 'Name',
      ellipsis: true,
    },
    {
      title: <FormattedMessage id="pages.knowledge.table.description" />,
      dataIndex: 'Description',
      key: 'Description',
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: <FormattedMessage id="pages.knowledge.table.chunkSize" />,
      dataIndex: 'ChunkSize',
      key: 'ChunkSize',
      hideInSearch: true,
      width: 120,
    },
    {
      title: <FormattedMessage id="pages.knowledge.table.chunkOverlap" />,
      dataIndex: 'ChunkOverlap',
      key: 'ChunkOverlap',
      hideInSearch: true,
      width: 120,
    },
    {
      title: <FormattedMessage id="pages.knowledge.table.createdTime" />,
      dataIndex: 'CreatedTime',
      key: 'CreatedTime',
      valueType: 'dateTime',
      width: 180,
      hideInSearch: true,
    },
    {
      title: <FormattedMessage id="pages.knowledge.table.actions" />,
      key: 'actions',
      width: 250,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => {
              setCurrentRecord(record);
              setDetailModalVisible(true);
            }}
          >
            <FormattedMessage id="pages.knowledge.actions.view" />
          </Button>
          <Button
            type="link"
            size="small"
            icon={<FileOutlined />}
            onClick={() => handleManageFiles(record)}
          >
            <FormattedMessage id="pages.knowledge.actions.files" />
          </Button>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => {
              setCurrentRecord(record);
              setEditModalVisible(true);
            }}
          >
            <FormattedMessage id="pages.knowledge.actions.edit" />
          </Button>
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          >
            <FormattedMessage id="pages.knowledge.actions.delete" />
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <PageContainer>
      <ProTable<RwxAI.Knowledge>
        headerTitle={intl.formatMessage({ id: 'pages.knowledge.title' })}
        actionRef={actionRef}
        rowKey="Id"
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            icon={<PlusOutlined />}
            onClick={() => setCreateModalVisible(true)}
          >
            <FormattedMessage id="pages.knowledge.actions.create" />
          </Button>,
        ]}
        request={createSimpleProTableRequest(getKnowledgeBases)}
        columns={columns}
      />

      <KnowledgeForm
        visible={createModalVisible}
        onVisibleChange={setCreateModalVisible}
        onSuccess={() => {
          actionRef.current?.reload();
          setCreateModalVisible(false);
        }}
      />

      <KnowledgeForm
        visible={editModalVisible}
        onVisibleChange={setEditModalVisible}
        initialValues={currentRecord}
        onSuccess={() => {
          actionRef.current?.reload();
          setEditModalVisible(false);
        }}
      />

      <KnowledgeDetail
        visible={detailModalVisible}
        onVisibleChange={setDetailModalVisible}
        data={currentRecord}
      />
    </PageContainer>
  );
};

export default KnowledgePage;
