import React, { useRef } from 'react';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import type { ProColumns, ActionType } from '@ant-design/pro-components';
import { Button, Tag } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { useIntl, FormattedMessage } from '@umijs/max';

interface TableListItem {
  id: string;
  name: string;
  status: string;
  createdAt: string;
  updatedAt: string;
}

const TableList: React.FC = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();

  const columns: ProColumns<TableListItem>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (_, record) => (
        <Tag color={record.status === 'active' ? 'green' : 'red'}>
          {record.status === 'active' ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      valueType: 'dateTime',
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      valueType: 'dateTime',
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      render: () => [
        <Button key="edit" type="link" size="small">
          编辑
        </Button>,
        <Button key="delete" type="link" size="small" danger>
          删除
        </Button>,
      ],
    },
  ];

  // 模拟数据
  const mockData: TableListItem[] = [
    {
      id: '1',
      name: '示例项目1',
      status: 'active',
      createdAt: '2024-01-01 10:00:00',
      updatedAt: '2024-01-01 10:00:00',
    },
    {
      id: '2',
      name: '示例项目2',
      status: 'inactive',
      createdAt: '2024-01-02 10:00:00',
      updatedAt: '2024-01-02 10:00:00',
    },
  ];

  return (
    <PageContainer
      title="表格列表"
      subTitle="标准的表格列表页面"
    >
      <ProTable<TableListItem>
        headerTitle="表格列表"
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            icon={<PlusOutlined />}
          >
            新建
          </Button>,
        ]}
        request={async () => {
          // 模拟API请求
          return {
            data: mockData,
            success: true,
            total: mockData.length,
          };
        }}
        columns={columns}
      />
    </PageContainer>
  );
};

export default TableList;
