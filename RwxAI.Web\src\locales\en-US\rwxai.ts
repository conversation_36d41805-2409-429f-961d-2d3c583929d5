export default {
  // User Authentication
  'pages.login.username.placeholder': 'Username',
  'pages.login.username.required': 'Please enter username!',
  'pages.login.password.placeholder': 'Password',
  'pages.login.password.required': 'Please enter password!',
  'pages.login.success': 'Login successful!',
  'pages.login.failure': 'Login failed, please try again!',
  'pages.register.submit': 'Register',
  'pages.register.success': 'Registration successful!',
  'pages.register.failure': 'Registration failed, please try again!',
  'pages.register.username.placeholder': 'Username',
  'pages.register.username.required': 'Please enter username!',
  'pages.register.email.placeholder': 'Email',
  'pages.register.email.required': 'Please enter email!',
  'pages.register.email.invalid': 'Invalid email format!',
  'pages.register.firstName.placeholder': 'First Name',
  'pages.register.lastName.placeholder': 'Last Name',
  'pages.register.password.placeholder': 'Password',
  'pages.register.password.required': 'Please enter password!',
  'pages.register.password.min': 'Password must be at least 6 characters!',
  'pages.register.loginAccount': 'Already have an account? Sign in',
  // AI Models Management
  'pages.aiModels.title': 'AI Models Management',
  'pages.aiModels.table.name': 'Model Name',
  'pages.aiModels.table.modelKey': 'Model Key',
  'pages.aiModels.table.modelType': 'Model Type',
  'pages.aiModels.table.provider': 'Provider',
  'pages.aiModels.table.status': 'Status',
  'pages.aiModels.table.maxTokens': 'Max Tokens',
  'pages.aiModels.table.temperature': 'Temperature',
  'pages.aiModels.table.createdTime': 'Created Time',
  'pages.aiModels.table.actions': 'Actions',
  'pages.aiModels.actions.create': 'Create Model',
  'pages.aiModels.actions.view': 'View',
  'pages.aiModels.actions.edit': 'Edit',
  'pages.aiModels.actions.delete': 'Delete',
  'pages.aiModels.delete.confirm.title': 'Confirm Delete',
  'pages.aiModels.delete.confirm.content': 'Are you sure to delete this AI model?',
  'pages.aiModels.delete.success': 'Delete successfully',
  'pages.aiModels.delete.error': 'Delete failed',
  'pages.aiModels.form.create.title': 'Create AI Model',
  'pages.aiModels.form.edit.title': 'Edit AI Model',
  'pages.aiModels.form.name': 'Model Name',
  'pages.aiModels.form.name.required': 'Please enter model name',
  'pages.aiModels.form.name.placeholder': 'Please enter model name',
  'pages.aiModels.form.description': 'Description',
  'pages.aiModels.form.description.placeholder': 'Please enter description',
  'pages.aiModels.form.modelKey': 'Model Key',
  'pages.aiModels.form.modelKey.required': 'Please enter model key',
  'pages.aiModels.form.modelKey.placeholder': 'Please enter model key',
  'pages.aiModels.form.modelType': 'Model Type',
  'pages.aiModels.form.modelType.required': 'Please select model type',
  'pages.aiModels.form.modelType.placeholder': 'Please select model type',
  'pages.aiModels.form.provider': 'Provider',
  'pages.aiModels.form.provider.required': 'Please select provider',
  'pages.aiModels.form.provider.placeholder': 'Please select provider',
  'pages.aiModels.form.maxTokens': 'Max Tokens',
  'pages.aiModels.form.maxTokens.placeholder': 'Please enter max tokens',
  'pages.aiModels.form.temperature': 'Temperature',
  'pages.aiModels.form.temperature.placeholder': 'Please enter temperature',
  'pages.aiModels.form.topP': 'Top P',
  'pages.aiModels.form.topP.placeholder': 'Please enter Top P value',
  'pages.aiModels.form.frequencyPenalty': 'Frequency Penalty',
  'pages.aiModels.form.frequencyPenalty.placeholder': 'Please enter frequency penalty',
  'pages.aiModels.form.presencePenalty': 'Presence Penalty',
  'pages.aiModels.form.presencePenalty.placeholder': 'Please enter presence penalty',
  'pages.aiModels.form.isEnabled': 'Enabled',
  'pages.aiModels.form.create.success': 'Create successfully',
  'pages.aiModels.form.create.error': 'Create failed',
  'pages.aiModels.form.update.success': 'Update successfully',
  'pages.aiModels.form.update.error': 'Update failed',
  'pages.aiModels.form.loadProviders.error': 'Load providers failed',
  'pages.aiModels.detail.title': 'AI Model Details',
  'pages.aiModels.detail.name': 'Model Name',
  'pages.aiModels.detail.description': 'Description',
  'pages.aiModels.detail.modelKey': 'Model Key',
  'pages.aiModels.detail.modelType': 'Model Type',
  'pages.aiModels.detail.provider': 'Provider',
  'pages.aiModels.detail.status': 'Status',
  'pages.aiModels.detail.maxTokens': 'Max Tokens',
  'pages.aiModels.detail.temperature': 'Temperature',
  'pages.aiModels.detail.topP': 'Top P',
  'pages.aiModels.detail.frequencyPenalty': 'Frequency Penalty',
  'pages.aiModels.detail.presencePenalty': 'Presence Penalty',
  'pages.aiModels.detail.createdTime': 'Created Time',
  'pages.aiModels.detail.updatedTime': 'Updated Time',
  'pages.aiModels.detail.configJson': 'Configuration',

  // Applications Management
  'pages.apps.title': 'Applications Management',
  'pages.apps.table.name': 'App Name',
  'pages.apps.table.description': 'Description',
  'pages.apps.table.status': 'Status',
  'pages.apps.table.apiCalls': 'API Calls',
  'pages.apps.table.rateLimit': 'Rate Limit',
  'pages.apps.table.createdTime': 'Created Time',
  'pages.apps.table.actions': 'Actions',
  'pages.apps.actions.create': 'Create App',
  'pages.apps.actions.view': 'View',
  'pages.apps.actions.edit': 'Edit',
  'pages.apps.actions.delete': 'Delete',
  'pages.apps.actions.regenerateKeys': 'Regenerate Keys',
  'pages.apps.actions.resetApiCalls': 'Reset API Calls',
  'pages.apps.delete.confirm.title': 'Confirm Delete',
  'pages.apps.delete.confirm.content': 'Are you sure to delete this application?',
  'pages.apps.delete.success': 'Delete successfully',
  'pages.apps.delete.error': 'Delete failed',
  'pages.apps.status.update.success': 'Status updated successfully',
  'pages.apps.status.update.error': 'Status update failed',
  'pages.apps.regenerateKeys.confirm.title': 'Confirm Regenerate Keys',
  'pages.apps.regenerateKeys.confirm.content': 'Old keys will be invalid after regeneration, continue?',
  'pages.apps.regenerateKeys.success': 'Keys regenerated successfully',
  'pages.apps.regenerateKeys.error': 'Keys regeneration failed',
  'pages.apps.resetApiCalls.confirm.title': 'Confirm Reset API Calls',
  'pages.apps.resetApiCalls.confirm.content': 'Are you sure to reset API call count?',
  'pages.apps.resetApiCalls.success': 'API calls reset successfully',
  'pages.apps.resetApiCalls.error': 'API calls reset failed',
  'pages.apps.form.create.title': 'Create Application',
  'pages.apps.form.edit.title': 'Edit Application',
  'pages.apps.form.name': 'App Name',
  'pages.apps.form.name.required': 'Please enter app name',
  'pages.apps.form.name.placeholder': 'Please enter app name',
  'pages.apps.form.description': 'Description',
  'pages.apps.form.description.placeholder': 'Please enter description',
  'pages.apps.form.maxApiCalls': 'Max API Calls',
  'pages.apps.form.maxApiCalls.placeholder': 'Please enter max API calls',
  'pages.apps.form.maxApiCalls.help': 'Leave empty for unlimited',
  'pages.apps.form.rateLimit': 'Rate Limit (per minute)',
  'pages.apps.form.rateLimit.placeholder': 'Please enter rate limit',
  'pages.apps.form.rateLimit.help': 'Maximum requests per minute',
  'pages.apps.form.allowedOrigins': 'Allowed Origins',
  'pages.apps.form.allowedOrigins.placeholder': 'Please enter allowed origins, one per line',
  'pages.apps.form.allowedOrigins.help': 'CORS configuration, one domain per line',
  'pages.apps.form.isEnabled': 'Enabled',
  'pages.apps.form.create.success': 'Create successfully',
  'pages.apps.form.create.error': 'Create failed',
  'pages.apps.form.update.success': 'Update successfully',
  'pages.apps.form.update.error': 'Update failed',
  'pages.apps.detail.title': 'Application Details',
  'pages.apps.detail.name': 'App Name',
  'pages.apps.detail.description': 'Description',
  'pages.apps.detail.apiKey': 'API Key',
  'pages.apps.detail.apiSecret': 'API Secret',
  'pages.apps.detail.status': 'Status',
  'pages.apps.detail.apiCalls': 'API Calls',
  'pages.apps.detail.rateLimit': 'Rate Limit',
  'pages.apps.detail.allowedOrigins': 'Allowed Origins',
  'pages.apps.detail.createdTime': 'Created Time',
  'pages.apps.detail.updatedTime': 'Updated Time',
  'pages.apps.detail.configJson': 'Configuration',
  'pages.apps.detail.copy.apiKey.success': 'API Key copied successfully',
  'pages.apps.detail.copy.apiSecret.success': 'API Secret copied successfully',

  // Chat Sessions Management
  'pages.chat.title': 'Chat Sessions Management',
  'pages.chat.table.name': 'Session Name',
  'pages.chat.table.model': 'Model',
  'pages.chat.table.status': 'Status',
  'pages.chat.table.messageCount': 'Message Count',
  'pages.chat.table.temperature': 'Temperature',
  'pages.chat.table.maxTokens': 'Max Tokens',
  'pages.chat.table.createdTime': 'Created Time',
  'pages.chat.table.actions': 'Actions',
  'pages.chat.actions.create': 'Create Session',
  'pages.chat.actions.chat': 'Start Chat',
  'pages.chat.actions.edit': 'Edit',
  'pages.chat.actions.delete': 'Delete',
  'pages.chat.status.active': 'Active',
  'pages.chat.status.inactive': 'Inactive',
  'pages.chat.delete.confirm.title': 'Confirm Delete',
  'pages.chat.delete.confirm.content': 'Are you sure to delete this chat session?',
  'pages.chat.delete.success': 'Delete successfully',
  'pages.chat.delete.error': 'Delete failed',
  'pages.chat.form.create.title': 'Create Chat Session',
  'pages.chat.form.edit.title': 'Edit Chat Session',
  'pages.chat.form.name': 'Session Name',
  'pages.chat.form.name.required': 'Please enter session name',
  'pages.chat.form.name.placeholder': 'Please enter session name',
  'pages.chat.form.model': 'Select Model',
  'pages.chat.form.model.required': 'Please select model',
  'pages.chat.form.model.placeholder': 'Please select chat model',
  'pages.chat.form.systemPrompt': 'System Prompt',
  'pages.chat.form.systemPrompt.placeholder': 'Please enter system prompt',
  'pages.chat.form.systemPrompt.help': 'System prompt affects AI response style and behavior',
  'pages.chat.form.temperature': 'Temperature',
  'pages.chat.form.temperature.placeholder': 'Please enter temperature',
  'pages.chat.form.temperature.help': 'Controls randomness of responses, between 0-2',
  'pages.chat.form.maxTokens': 'Max Tokens',
  'pages.chat.form.maxTokens.placeholder': 'Please enter max tokens',
  'pages.chat.form.maxTokens.help': 'Maximum length of single response',
  'pages.chat.form.isActive': 'Active Status',
  'pages.chat.form.create.success': 'Create successfully',
  'pages.chat.form.create.error': 'Create failed',
  'pages.chat.form.update.success': 'Update successfully',
  'pages.chat.form.update.error': 'Update failed',
  'pages.chat.form.loadModels.error': 'Load models failed',

  // Chat Interface
  'pages.chat.session.load.error': 'Load session failed',
  'pages.chat.session.send.error': 'Send message failed',
  'pages.chat.session.input.placeholder': 'Please enter your message...',
  'pages.chat.session.send': 'Send',

  // Knowledge Base Management
  'pages.knowledge.title': 'Knowledge Base Management',
  'pages.knowledge.table.name': 'Knowledge Base Name',
  'pages.knowledge.table.description': 'Description',
  'pages.knowledge.table.chunkSize': 'Chunk Size',
  'pages.knowledge.table.chunkOverlap': 'Chunk Overlap',
  'pages.knowledge.table.createdTime': 'Created Time',
  'pages.knowledge.table.actions': 'Actions',
  'pages.knowledge.actions.create': 'Create Knowledge Base',
  'pages.knowledge.actions.view': 'View',
  'pages.knowledge.actions.files': 'File Management',
  'pages.knowledge.actions.edit': 'Edit',
  'pages.knowledge.actions.delete': 'Delete',
  'pages.knowledge.delete.confirm.title': 'Confirm Delete',
  'pages.knowledge.delete.confirm.content': 'Are you sure to delete this knowledge base?',
  'pages.knowledge.delete.success': 'Delete successfully',
  'pages.knowledge.delete.error': 'Delete failed',
  'pages.knowledge.form.create.title': 'Create Knowledge Base',
  'pages.knowledge.form.edit.title': 'Edit Knowledge Base',
  'pages.knowledge.form.name': 'Knowledge Base Name',
  'pages.knowledge.form.name.required': 'Please enter knowledge base name',
  'pages.knowledge.form.name.placeholder': 'Please enter knowledge base name',
  'pages.knowledge.form.description': 'Description',
  'pages.knowledge.form.description.placeholder': 'Please enter description',
  'pages.knowledge.form.embeddingModel': 'Embedding Model',
  'pages.knowledge.form.embeddingModel.required': 'Please select embedding model',
  'pages.knowledge.form.embeddingModel.placeholder': 'Please select embedding model',
  'pages.knowledge.form.chunkSize': 'Chunk Size',
  'pages.knowledge.form.chunkSize.required': 'Please enter chunk size',
  'pages.knowledge.form.chunkSize.placeholder': 'Please enter chunk size',
  'pages.knowledge.form.chunkSize.help': 'Number of characters per document chunk',
  'pages.knowledge.form.chunkOverlap': 'Chunk Overlap',
  'pages.knowledge.form.chunkOverlap.required': 'Please enter chunk overlap',
  'pages.knowledge.form.chunkOverlap.placeholder': 'Please enter chunk overlap',
  'pages.knowledge.form.chunkOverlap.help': 'Number of overlapping characters between adjacent chunks',
  'pages.knowledge.form.create.success': 'Create successfully',
  'pages.knowledge.form.create.error': 'Create failed',
  'pages.knowledge.form.update.success': 'Update successfully',
  'pages.knowledge.form.update.error': 'Update failed',
  'pages.knowledge.form.loadModels.error': 'Load models failed',
  'pages.knowledge.detail.title': 'Knowledge Base Details',
  'pages.knowledge.detail.name': 'Knowledge Base Name',
  'pages.knowledge.detail.description': 'Description',
  'pages.knowledge.detail.chunkSize': 'Chunk Size',
  'pages.knowledge.detail.chunkOverlap': 'Chunk Overlap',
  'pages.knowledge.detail.embeddingModelId': 'Embedding Model ID',
  'pages.knowledge.detail.createdTime': 'Created Time',
  'pages.knowledge.detail.updatedTime': 'Updated Time',
  'pages.knowledge.detail.metadataJson': 'Metadata',

  // Knowledge Base Files Management
  'pages.knowledge.files.title': 'Knowledge Base Files Management',
  'pages.knowledge.files.subtitle': 'File Management',
  'pages.knowledge.files.table.fileName': 'File Name',
  'pages.knowledge.files.table.contentType': 'File Type',
  'pages.knowledge.files.table.fileSize': 'File Size',
  'pages.knowledge.files.table.status': 'Processing Status',
  'pages.knowledge.files.table.processedTime': 'Processed Time',
  'pages.knowledge.files.table.createdTime': 'Upload Time',
  'pages.knowledge.files.table.actions': 'Actions',
  'pages.knowledge.files.actions.upload': 'Upload File',
  'pages.knowledge.files.actions.process': 'Process',
  'pages.knowledge.files.actions.delete': 'Delete',
  'pages.knowledge.files.status.processed': 'Processed',
  'pages.knowledge.files.status.pending': 'Pending',
  'pages.knowledge.files.delete.confirm.title': 'Confirm Delete',
  'pages.knowledge.files.delete.confirm.content': 'Are you sure to delete this file?',
  'pages.knowledge.files.delete.success': 'Delete successfully',
  'pages.knowledge.files.delete.error': 'Delete failed',
  'pages.knowledge.files.process.success': 'Process successfully',
  'pages.knowledge.files.process.error': 'Process failed',
  'pages.knowledge.files.upload.title': 'Upload File',
  'pages.knowledge.files.upload.success': 'Upload successfully',
  'pages.knowledge.files.upload.error': 'Upload failed',
  'pages.knowledge.files.upload.dragText': 'Click or drag file to this area to upload',
  'pages.knowledge.files.upload.hint': 'Support single file upload, common document formats supported',
  'pages.knowledge.files.loadKnowledge.error': 'Load knowledge base information failed',

  // Plugins Management
  'pages.plugins.title': 'Plugins Management',
  'pages.plugins.table.name': 'Plugin Name',
  'pages.plugins.table.description': 'Description',
  'pages.plugins.table.version': 'Version',
  'pages.plugins.table.author': 'Author',
  'pages.plugins.table.status': 'Status',
  'pages.plugins.table.enabled': 'Enabled',
  'pages.plugins.table.functions': 'Function Count',
  'pages.plugins.table.createdTime': 'Created Time',
  'pages.plugins.table.actions': 'Actions',
  'pages.plugins.actions.create': 'Create Plugin',
  'pages.plugins.actions.view': 'View',
  'pages.plugins.actions.functions': 'Function Management',
  'pages.plugins.actions.edit': 'Edit',
  'pages.plugins.actions.delete': 'Delete',
  'pages.plugins.status.active': 'Active',
  'pages.plugins.status.inactive': 'Inactive',
  'pages.plugins.status.error': 'Error',
  'pages.plugins.delete.confirm.title': 'Confirm Delete',
  'pages.plugins.delete.confirm.content': 'Are you sure to delete this plugin?',
  'pages.plugins.delete.success': 'Delete successfully',
  'pages.plugins.delete.error': 'Delete failed',
  'pages.plugins.status.update.success': 'Status updated successfully',
  'pages.plugins.status.update.error': 'Status update failed',
  'pages.plugins.form.create.title': 'Create Plugin',
  'pages.plugins.form.edit.title': 'Edit Plugin',
  'pages.plugins.form.name': 'Plugin Name',
  'pages.plugins.form.name.required': 'Please enter plugin name',
  'pages.plugins.form.name.placeholder': 'Please enter plugin name',
  'pages.plugins.form.description': 'Description',
  'pages.plugins.form.description.placeholder': 'Please enter description',
  'pages.plugins.form.version': 'Version',
  'pages.plugins.form.version.required': 'Please enter version',
  'pages.plugins.form.version.placeholder': 'Please enter version',
  'pages.plugins.form.author': 'Author',
  'pages.plugins.form.author.placeholder': 'Please enter author',
  'pages.plugins.form.assemblyPath': 'Assembly Path',
  'pages.plugins.form.assemblyPath.required': 'Please enter assembly path',
  'pages.plugins.form.assemblyPath.placeholder': 'Please enter assembly path',
  'pages.plugins.form.assemblyPath.help': 'Full path to plugin DLL file',
  'pages.plugins.form.typeName': 'Type Name',
  'pages.plugins.form.typeName.required': 'Please enter type name',
  'pages.plugins.form.typeName.placeholder': 'Please enter type name',
  'pages.plugins.form.typeName.help': 'Full type name of plugin main class',
  'pages.plugins.form.isEnabled': 'Enabled',
  'pages.plugins.form.create.success': 'Create successfully',
  'pages.plugins.form.create.error': 'Create failed',
  'pages.plugins.form.update.success': 'Update successfully',
  'pages.plugins.form.update.error': 'Update failed',
  'pages.plugins.detail.title': 'Plugin Details',
  'pages.plugins.detail.name': 'Plugin Name',
  'pages.plugins.detail.description': 'Description',
  'pages.plugins.detail.version': 'Version',
  'pages.plugins.detail.author': 'Author',
  'pages.plugins.detail.status': 'Status',
  'pages.plugins.detail.enabled': 'Enabled',
  'pages.plugins.detail.assemblyPath': 'Assembly Path',
  'pages.plugins.detail.typeName': 'Type Name',
  'pages.plugins.detail.functions': 'Function Count',
  'pages.plugins.detail.createdTime': 'Created Time',
  'pages.plugins.detail.updatedTime': 'Updated Time',
};
