declare namespace RwxAI {
  // 基础实体接口
  interface BaseEntity {
    Id: string;
    CreatedTime: string;
    UpdatedTime: string;
    IsDeleted: boolean;
  }

  // AI模型相关类型
  interface AIModel extends BaseEntity {
    // 模型模板ID
    ModelTemplateId?: string;

    // 模型名称（来自模板）
    Name?: string;

    // 模型ID
    ModelId?: string;

    // 模型服务端点
    Endpoint?: string;

    // 提供商代码
    ProviderCode?: string;

    // 显示名称（来自模板）
    DisplayName?: string;

    // 模型描述
    Description?: string;

    // API密钥
    ApiKey?: string;

    // 是否启用
    IsEnabled?: boolean;

    // 是否为默认模型
    IsDefault?: boolean;

    // 最大令牌数
    MaxTokens?: number;

    // 温度参数
    Temperature?: number;

    // 频率惩罚
    FrequencyPenalty?: number;

    // 存在惩罚
    PresencePenalty?: number;

    // 系统提示词
    SystemPrompt?: string;

    // 提示词
    Prompts?: string;

    // 模板信息
    Template?: AIModelTemplate;
  }

  // AI模型创建请求类型
  interface CreateAIModelRequest {
    modelTemplateId: string;
    name?: string;
    modelId?: string;
    endpoint?: string;
    providerCode?: string;
    displayName?: string;
    description?: string;
    apiKey?: string;
    isEnabled?: boolean;
    isDefault?: boolean;
    maxTokens?: number;
    temperature?: number;
    frequencyPenalty?: number;
    presencePenalty?: number;
    systemPrompt?: string;
    prompts?: string;
  }

  // AI模型更新请求类型
  interface UpdateAIModelRequest {
    modelTemplateId?: string;
    name?: string;
    modelId?: string;
    endpoint?: string;
    providerCode?: string;
    displayName?: string;
    description?: string;
    apiKey?: string;
    isEnabled?: boolean;
    isDefault?: boolean;
    maxTokens?: number;
    temperature?: number;
    frequencyPenalty?: number;
    presencePenalty?: number;
    systemPrompt?: string;
    prompts?: string;
  }

  // 测试连接结果类型
  interface TestConnectionResult {
    success: boolean;
    message: string;
    responseTime?: number;
    details?: any;
  }

  interface AIModelTemplate extends BaseEntity {
    // 模型名称
    Name?: string;

    // 显示名称
    DisplayName?: string;

    // 模型ID（API调用时使用的标识符）
    ModelId?: string;

    // 服务端点URL
    Endpoint?: string;

    // 模型类型
    Type?: string;

    // 模型描述
    Description?: string;

    // 最大上下文长度
    MaxContextLength?: number;

    // 最大输出长度
    MaxOutputLength?: number;

    // 是否支持流式输出
    SupportsStreaming?: boolean;

    // 是否支持函数调用
    SupportsFunctionCalling?: boolean;

    // 是否支持视觉理解
    SupportsVision?: boolean;

    // 备注
    Notes?: string;

    // AI提供商信息
    Provider?: AIProvider;
  }

  interface AIProvider extends BaseEntity {
    // 提供商名称
    Name?: string;

    // 显示名称
    DisplayName?: string;

    // 提供商代码
    Code?: string;

    // 提供商描述
    Description?: string;

    // 官方网站
    Website?: string;

    // 图标URL
    IconUrl?: string;
  }

  // 应用相关类型
  interface App extends BaseEntity {
    Name?: string;
    Description?: string;
    ApiKey?: string;
    ApiSecret?: string;
    IsEnabled?: boolean;
    UserId?: string;
    ApiCallCount?: number;
    MaxApiCalls?: number;
    RateLimitPerMinute?: number;
    AllowedOrigins?: string;
    ConfigJson?: string;
  }

  interface AppStatusUpdateRequest {
    IsEnabled: boolean;
  }

  // 聊天相关类型
  interface ChatSession extends BaseEntity {
    Name?: string;
    UserId?: string;
    ModelId?: string;
    SystemPrompt?: string;
    Temperature?: number;
    MaxTokens?: number;
    IsActive?: boolean;
    Messages?: ChatMessage[];
    Model?: AIModel;
  }

  interface ChatMessage extends BaseEntity {
    SessionId?: string;
    Role?: string;
    Content?: string;
    TokenCount?: number;
    ModelId?: string;
    Attachments?: MessageAttachment[];
    Session?: ChatSession;
    Model?: AIModel;
  }

  interface ChatMessageRequest {
    Content?: string;
    ModelId?: string;
    Temperature?: number;
    MaxTokens?: number;
    Attachments?: MessageAttachment[];
  }

  interface MessageAttachment extends BaseEntity {
    ChatMessageId: string;
    FileName?: string;
    ContentType?: string;
    FilePath?: string;
    FileSize: number;
  }

  // 知识库相关类型
  interface Knowledge extends BaseEntity {
    Name?: string;
    Description?: string;
    EmbeddingModelId?: string;
    ChunkSize: number;
    ChunkOverlap: number;
    Title?: string;
    Content?: string;
    ContentType?: string;
    MetadataJson?: string;
    KnowledgeBaseId?: string;
    VectorId?: string;
  }

  interface KnowledgeFile extends BaseEntity {
    KnowledgeId: string;
    FileName?: string;
    FilePath?: string;
    ContentType?: string;
    FileSize: number;
    IsProcessed: boolean;
    ProcessedTime?: string;
  }

  // 插件相关类型
  interface Plugin extends BaseEntity {
    Name?: string;
    Description?: string;
    Version?: string;
    Author?: string;
    AssemblyPath?: string;
    TypeName?: string;
    Status?: string;
    IsEnabled: boolean;
    Functions?: PluginFunction[];
  }

  interface PluginFunction extends BaseEntity {
    PluginId: string;
    Name?: string;
    Description?: string;
    MethodName?: string;
    ReturnType?: string;
    Parameters?: PluginParameterInfo[];
  }

  interface PluginParameterInfo {
    Name?: string;
    Description?: string;
    Type?: string;
    IsRequired: boolean;
    DefaultValue?: string;
    Order: number;
  }

  // 认证相关类型
  interface LoginRequest {
    Username?: string;
    Password?: string;
  }

  interface RegisterRequest {
    Username?: string;
    Email?: string;
    Password?: string;
    FirstName?: string;
    LastName?: string;
  }

  interface RefreshTokenRequest {
    RefreshToken?: string;
  }

  interface ChangePasswordRequest {
    CurrentPassword?: string;
    NewPassword?: string;
    ConfirmPassword?: string;
  }

  // 枚举类型
  enum ModelTypeEnum {
    Chat = 0,
    Embedding = 1,
    ImageGeneration = 2,
    TextToSpeech = 3
  }

  // 错误响应类型
  interface ProblemDetails {
    type?: string;
    title?: string;
    status?: number;
    detail?: string;
    instance?: string;
  }

  // API响应类型
  interface ApiResponse<T = any> {
    data?: T;
    success?: boolean;
    message?: string;
    errorCode?: string;
  }

  // 分页相关类型
  interface PagedRequest {
    page?: number;
    pageSize?: number;
    keyword?: string;
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
  }

  interface PagedResponse<T> {
    items: T[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  }

  // 新的后台分页响应格式
  interface BackendPagedResponse<T> {
    Items: T[];
    TotalCount: number;
    PageNumber: number;
    PageSize: number;
    TotalPages: number;
    HasPreviousPage: boolean;
    HasNextPage: boolean;
  }
}
