// @ts-nocheck
// This file is generated by <PERSON><PERSON> automatically
// DO NOT CHANGE IT MANUALLY!
import React from 'react';

export async function getRoutes() {
  const routes = {"1":{"path":"/user","layout":false,"id":"1"},"2":{"name":"login","path":"/user/login","parentId":"1","id":"2"},"3":{"name":"register","path":"/user/register","parentId":"1","id":"3"},"4":{"path":"/welcome","name":"welcome","icon":"smile","parentId":"ant-design-pro-layout","id":"4"},"5":{"path":"/ai-models","name":"ai-models","icon":"robot","parentId":"ant-design-pro-layout","id":"5"},"6":{"path":"/apps","name":"apps","icon":"appstore","parentId":"ant-design-pro-layout","id":"6"},"7":{"path":"/chat","name":"chat","icon":"message","parentId":"ant-design-pro-layout","id":"7"},"8":{"path":"/chat","redirect":"/chat/sessions","parentId":"7","id":"8"},"9":{"path":"/chat/sessions","name":"sessions","parentId":"7","id":"9"},"10":{"path":"/chat/session/:id","name":"session-detail","hideInMenu":true,"parentId":"7","id":"10"},"11":{"path":"/knowledge","name":"knowledge","icon":"book","parentId":"ant-design-pro-layout","id":"11"},"12":{"path":"/knowledge","parentId":"11","id":"12"},"13":{"path":"/knowledge/:id/files","name":"knowledge-files","hideInMenu":true,"parentId":"11","id":"13"},"14":{"path":"/plugins","name":"plugins","icon":"api","parentId":"ant-design-pro-layout","id":"14"},"15":{"path":"/auth-test","name":"auth-test","icon":"safety","parentId":"ant-design-pro-layout","id":"15"},"16":{"path":"/response-demo","name":"response-demo","icon":"experiment","parentId":"ant-design-pro-layout","id":"16"},"17":{"path":"/api-test","name":"api-test","icon":"api","parentId":"ant-design-pro-layout","id":"17"},"18":{"path":"/admin","name":"admin","icon":"crown","access":"canAdmin","parentId":"ant-design-pro-layout","id":"18"},"19":{"path":"/admin","redirect":"/admin/sub-page","parentId":"18","id":"19"},"20":{"path":"/admin/sub-page","name":"sub-page","parentId":"18","id":"20"},"21":{"name":"list.table-list","icon":"table","path":"/list","parentId":"ant-design-pro-layout","id":"21"},"22":{"path":"/","redirect":"/welcome","parentId":"ant-design-pro-layout","id":"22"},"23":{"path":"*","layout":false,"id":"23"},"ant-design-pro-layout":{"id":"ant-design-pro-layout","path":"/","isLayout":true}} as const;
  return {
    routes,
    routeComponents: {
'1': React.lazy(() => import('./EmptyRoute')),
'2': React.lazy(() => import(/* webpackChunkName: "p__user__login__index" */'@/pages/user/login/index.tsx')),
'3': React.lazy(() => import(/* webpackChunkName: "p__user__register__index" */'@/pages/user/register/index.tsx')),
'4': React.lazy(() => import(/* webpackChunkName: "p__Welcome" */'@/pages/Welcome.tsx')),
'5': React.lazy(() => import(/* webpackChunkName: "p__ai-models__index" */'@/pages/ai-models/index.tsx')),
'6': React.lazy(() => import(/* webpackChunkName: "p__apps__index" */'@/pages/apps/index.tsx')),
'7': React.lazy(() => import('./EmptyRoute')),
'8': React.lazy(() => import('./EmptyRoute')),
'9': React.lazy(() => import(/* webpackChunkName: "p__chat__index" */'@/pages/chat/index.tsx')),
'10': React.lazy(() => import(/* webpackChunkName: "p__chat__session__id" */'@/pages/chat/session/[id].tsx')),
'11': React.lazy(() => import('./EmptyRoute')),
'12': React.lazy(() => import(/* webpackChunkName: "p__knowledge__index" */'@/pages/knowledge/index.tsx')),
'13': React.lazy(() => import(/* webpackChunkName: "p__knowledge__id__files" */'@/pages/knowledge/[id]/files.tsx')),
'14': React.lazy(() => import(/* webpackChunkName: "p__plugins__index" */'@/pages/plugins/index.tsx')),
'15': React.lazy(() => import(/* webpackChunkName: "p__auth-test__index" */'@/pages/auth-test/index.tsx')),
'16': React.lazy(() => import(/* webpackChunkName: "p__response-demo__index" */'@/pages/response-demo/index.tsx')),
'17': React.lazy(() => import(/* webpackChunkName: "p__api-test__index" */'@/pages/api-test/index.tsx')),
'18': React.lazy(() => import('./EmptyRoute')),
'19': React.lazy(() => import('./EmptyRoute')),
'20': React.lazy(() => import(/* webpackChunkName: "p__Admin" */'@/pages/Admin.tsx')),
'21': React.lazy(() => import(/* webpackChunkName: "p__table-list__index" */'@/pages/table-list/index.tsx')),
'22': React.lazy(() => import('./EmptyRoute')),
'23': React.lazy(() => import(/* webpackChunkName: "p__404" */'@/pages/404.tsx')),
'ant-design-pro-layout': React.lazy(() => import(/* webpackChunkName: "t__plugin-layout__Layout" */'D:/rwx-ai/RwxAI.Web/src/.umi-production/plugin-layout/Layout.tsx')),
},
  };
}
