/**
 * 统一的分页数据处理工具
 * 用于处理后台返回的分页格式数据
 */

import { ResponseHandleResult } from '@/types/response';

/**
 * API函数类型定义
 */
export type PagedApiFunction<T> = (params?: any) => Promise<ResponseHandleResult<BackendPagedResponse<T>>>;
export type LegacyApiFunction<T> = (params?: any) => Promise<ResponseHandleResult<BackendPagedResponse<T> | T[]>>;
export type SimpleApiFunction<T> = () => Promise<ResponseHandleResult<T[]>>;

/**
 * ProTable请求参数接口
 */
export interface ProTableParams {
  current?: number;
  pageSize?: number;
  [key: string]: any;
}

/**
 * ProTable期望的返回格式
 */
export interface ProTableResult<T> {
  data: T[];
  success: boolean;
  total?: number;
}

/**
 * 后台分页响应格式
 */
export interface BackendPagedResponse<T> {
  Items: T[];
  TotalCount: number;
  PageNumber: number;
  PageSize: number;
  TotalPages: number;
  HasPreviousPage: boolean;
  HasNextPage: boolean;
}

/**
 * 统一处理后台分页响应数据
 * @param response 后台API响应
 * @returns ProTable期望的数据格式
 */
export function handlePagedResponse<T>(
  response: ResponseHandleResult<BackendPagedResponse<T>>
): ProTableResult<T> {
  if (!response.success || !response.data) {
    return {
      data: [],
      success: response.success,
      total: 0,
    };
  }

  const data = response.data;

  // 处理分页格式
  if (isPagedResponse<T>(data)) {
    return {
      data: data.Items,
      success: true,
      total: data.TotalCount,
    };
  }

  // 如果不是标准分页格式，返回空数据
  return {
    data: [],
    success: false,
    total: 0,
  };
}

/**
 * 兼容处理后台分页响应数据（支持旧格式）
 * @param response 后台API响应
 * @returns ProTable期望的数据格式
 */
export function handleLegacyPagedResponse<T>(
  response: ResponseHandleResult<BackendPagedResponse<T> | T[]>
): ProTableResult<T> {
  if (!response.success || !response.data) {
    return {
      data: [],
      success: response.success,
      total: 0,
    };
  }

  const data = response.data as any;

  // 检查是否是新的分页格式
  if (isPagedResponse<T>(data)) {
    return {
      data: data.Items,
      success: true,
      total: data.TotalCount,
    };
  }

  // 兼容旧格式（直接返回数组）
  if (Array.isArray(data)) {
    return {
      data: data,
      success: true,
      total: data.length,
    };
  }

  // 其他情况返回空数据
  return {
    data: [],
    success: false,
    total: 0,
  };
}

/**
 * 构建分页查询参数（集成smartQuery功能）
 * @param params ProTable传入的参数
 * @param fieldMapping 字段映射关系
 * @param extra 额外参数
 * @returns 后台期望的查询参数
 */
export function buildPagedQuery(
  params: ProTableParams,
  fieldMapping: Record<string, string> = {},
  extra?: Record<string, any>
): Record<string, any> {
  const query: Record<string, any> = {};

  // 添加分页参数
  query.PageNumber = params.current ?? 1;
  query.PageSize = params.pageSize ?? 20;

  // 添加关键词搜索
  if (params.keyWord) {
    query.SearchKeyword = params.keyWord as string;
  }

  // 字段映射：将列的 dataIndex 对应值映射到后端字段
  if (fieldMapping) {
    Object.keys(fieldMapping).forEach((from) => {
      const to = fieldMapping[from];
      const value = (params as any)[from];
      if (value !== undefined && value !== null && value !== '') {
        query[to] = value;
      }
    });
  }

  // 添加额外参数
  if (extra) {
    Object.assign(query, extra);
  }

  return query;
}

/**
 * 将查询对象转换为查询字符串
 * @param obj 查询对象
 * @returns 查询字符串
 */
export function toQueryString(obj: Record<string, any>): string {
  const qs = new URLSearchParams();
  Object.entries(obj).forEach(([k, v]) => {
    if (v === undefined || v === null || v === '') return;
    if (v instanceof Date) {
      qs.append(k, v.toISOString());
    } else {
      qs.append(k, String(v));
    }
  });
  return qs.toString();
}

/**
 * 创建标准的ProTable request函数（新分页格式）
 * @param apiFunction 后台API函数
 * @param fieldMapping 字段映射关系
 * @param extra 额外参数
 * @returns ProTable request函数
 */
export function createProTableRequest<T>(
  apiFunction: PagedApiFunction<T>,
  fieldMapping: Record<string, string> = {},
  extra?: Record<string, any>
) {
  return async (params: ProTableParams): Promise<ProTableResult<T>> => {
    const query = buildPagedQuery(params, fieldMapping, extra);
    const response = await apiFunction(query);
    return handlePagedResponse(response);
  };
}

/**
 * 创建兼容的ProTable request函数（支持新旧格式）
 * @param apiFunction 后台API函数
 * @param fieldMapping 字段映射关系
 * @param extra 额外参数
 * @returns ProTable request函数
 */
export function createLegacyProTableRequest<T>(
  apiFunction: LegacyApiFunction<T>,
  fieldMapping: Record<string, string> = {},
  extra?: Record<string, any>
) {
  return async (params: ProTableParams): Promise<ProTableResult<T>> => {
    const query = buildPagedQuery(params, fieldMapping, extra);
    const response = await apiFunction(query);
    return handleLegacyPagedResponse(response);
  };
}

/**
 * 创建简单的ProTable request函数（不带分页参数）
 * @param apiFunction 后台API函数
 * @returns ProTable request函数
 */
export function createSimpleProTableRequest<T>(
  apiFunction: SimpleApiFunction<T>
) {
  return async (): Promise<ProTableResult<T>> => {
    const response = await apiFunction();
    return {
      data: response.success ? (response.data || []) : [],
      success: response.success,
      total: response.data?.length || 0,
    };
  };
}

/**
 * 默认的分页配置
 */
export const defaultPaginationConfig = {
  showSizeChanger: true,
  pageSize: 20,
  pageSizeOptions: ['10', '20', '50', '100'],
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) => 
    `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
};

/**
 * 检查响应是否为分页格式
 * @param data 响应数据
 * @returns 是否为分页格式
 */
export function isPagedResponse<T>(data: any): data is BackendPagedResponse<T> {
  return data && 
         typeof data === 'object' && 
         Array.isArray(data.Items) && 
         typeof data.TotalCount === 'number' &&
         typeof data.PageNumber === 'number' &&
         typeof data.PageSize === 'number';
}

/**
 * 提取分页信息
 * @param data 分页响应数据
 * @returns 分页信息
 */
export function extractPaginationInfo<T>(data: BackendPagedResponse<T>) {
  return {
    current: data.PageNumber,
    pageSize: data.PageSize,
    total: data.TotalCount,
    totalPages: data.TotalPages,
    hasPrevious: data.HasPreviousPage,
    hasNext: data.HasNextPage,
  };
}
