{"name": "umi", "version": "4.4.12", "description": "umi", "homepage": "https://github.com/umijs/umi/tree/master/packages/umi#readme", "bugs": "https://github.com/umijs/umi/issues", "repository": {"type": "git", "url": "https://github.com/umijs/umi"}, "license": "MIT", "main": "dist/index.js", "types": "index.d.ts", "bin": {"umi": "bin/umi.js"}, "files": ["bin", "client", "dist", "eslint.js", "index.d.ts", "index.esm.js", "plugin-utils.d.ts", "plugin-utils.js", "plugin.js", "prettier.js", "stylelint.js", "test-setup.js", "test.d.ts", "test.js"], "dependencies": {"@babel/runtime": "7.23.6", "prettier-plugin-organize-imports": "^3.2.2", "prettier-plugin-packagejson": "2.4.3", "@umijs/bundler-utils": "4.4.12", "@umijs/bundler-webpack": "4.4.12", "@umijs/core": "4.4.12", "@umijs/lint": "4.4.12", "@umijs/renderer-react": "4.4.12", "@umijs/test": "4.4.12", "@umijs/utils": "4.4.12", "@umijs/preset-umi": "4.4.12", "@umijs/server": "4.4.12"}, "engines": {"node": ">=14"}, "publishConfig": {"access": "public"}, "authors": ["chen<PERSON> <<EMAIL>> (https://github.com/sorrycc)"], "scripts": {"build": "umi-scripts father build", "build:deps": "umi-scripts bundleDeps", "dev": "umi-scripts father dev", "test": "umi-scripts jest-turbo"}}