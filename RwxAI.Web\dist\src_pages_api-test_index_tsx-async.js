((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] || []).push([
        ['src/pages/api-test/index.tsx'],
{ "src/pages/api-test/index.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _rwxai = __mako_require__("src/services/rwxai/index.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Text, Paragraph } = _antd.Typography;
const ApiTestPage = ()=>{
    _s();
    const [testResults, setTestResults] = (0, _react.useState)([]);
    const [loading, setLoading] = (0, _react.useState)({});
    const addTestResult = (result)=>{
        setTestResults((prev)=>[
                result,
                ...prev.slice(0, 19)
            ]); // 保留最近20条记录
    };
    const testApi = async (apiName, apiFunction)=>{
        setLoading((prev)=>({
                ...prev,
                [apiName]: true
            }));
        const startTime = new Date().toLocaleTimeString();
        try {
            const response = await apiFunction();
            if (response.success) {
                var _response_data;
                addTestResult({
                    api: apiName,
                    status: 'success',
                    message: `✅ 成功 - ${((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.length) || 0} 条记录`,
                    data: response.data,
                    timestamp: startTime
                });
            } else addTestResult({
                api: apiName,
                status: 'error',
                message: `❌ 失败 - ${response.message}`,
                timestamp: startTime
            });
        } catch (error) {
            addTestResult({
                api: apiName,
                status: 'error',
                message: `❌ 异常 - ${error.message}`,
                timestamp: startTime
            });
        } finally{
            setLoading((prev)=>({
                    ...prev,
                    [apiName]: false
                }));
        }
    };
    const testAllApis = async ()=>{
        const apis = [
            {
                name: 'AI模型列表',
                func: _rwxai.getAIModels
            },
            {
                name: '应用列表',
                func: _rwxai.getApps
            },
            {
                name: '聊天会话列表',
                func: _rwxai.getChatSessions
            },
            {
                name: '知识库列表',
                func: _rwxai.getKnowledgeBases
            },
            {
                name: '插件列表',
                func: _rwxai.getPlugins
            }
        ];
        for (const api of apis){
            await testApi(api.name, api.func);
            // 添加小延迟避免请求过于频繁
            await new Promise((resolve)=>setTimeout(resolve, 500));
        }
    };
    const clearResults = ()=>{
        setTestResults([]);
    };
    const getStatusColor = (status)=>{
        switch(status){
            case 'success':
                return 'green';
            case 'error':
                return 'red';
            case 'loading':
                return 'blue';
            default:
                return 'default';
        }
    };
    const apiTests = [
        {
            category: 'AI模型管理',
            tests: [
                {
                    name: 'AI模型列表',
                    func: _rwxai.getAIModels,
                    desc: '获取所有AI模型'
                }
            ]
        },
        {
            category: '应用管理',
            tests: [
                {
                    name: '应用列表',
                    func: _rwxai.getApps,
                    desc: '获取所有应用'
                }
            ]
        },
        {
            category: '聊天管理',
            tests: [
                {
                    name: '聊天会话列表',
                    func: _rwxai.getChatSessions,
                    desc: '获取所有聊天会话'
                }
            ]
        },
        {
            category: '知识库管理',
            tests: [
                {
                    name: '知识库列表',
                    func: _rwxai.getKnowledgeBases,
                    desc: '获取所有知识库'
                }
            ]
        },
        {
            category: '插件管理',
            tests: [
                {
                    name: '插件列表',
                    func: _rwxai.getPlugins,
                    desc: '获取所有插件'
                }
            ]
        }
    ];
    const tabItems = [
        {
            key: 'test',
            label: 'API测试',
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                direction: "vertical",
                size: "large",
                style: {
                    width: '100%'
                },
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                        title: "批量测试",
                        size: "small",
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    type: "primary",
                                    onClick: testAllApis,
                                    loading: Object.values(loading).some(Boolean),
                                    children: "测试所有API"
                                }, void 0, false, {
                                    fileName: "src/pages/api-test/index.tsx",
                                    lineNumber: 136,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    onClick: clearResults,
                                    children: "清空结果"
                                }, void 0, false, {
                                    fileName: "src/pages/api-test/index.tsx",
                                    lineNumber: 139,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/api-test/index.tsx",
                            lineNumber: 135,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/api-test/index.tsx",
                        lineNumber: 134,
                        columnNumber: 11
                    }, this),
                    apiTests.map((category)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            title: category.category,
                            size: "small",
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                wrap: true,
                                children: category.tests.map((test)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        onClick: ()=>testApi(test.name, test.func),
                                        loading: loading[test.name],
                                        title: test.desc,
                                        children: test.name
                                    }, test.name, false, {
                                        fileName: "src/pages/api-test/index.tsx",
                                        lineNumber: 149,
                                        columnNumber: 19
                                    }, this))
                            }, void 0, false, {
                                fileName: "src/pages/api-test/index.tsx",
                                lineNumber: 147,
                                columnNumber: 15
                            }, this)
                        }, category.category, false, {
                            fileName: "src/pages/api-test/index.tsx",
                            lineNumber: 146,
                            columnNumber: 13
                        }, this))
                ]
            }, void 0, true, {
                fileName: "src/pages/api-test/index.tsx",
                lineNumber: 133,
                columnNumber: 9
            }, this)
        },
        {
            key: 'results',
            label: `测试结果 (${testResults.length})`,
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                title: "测试结果",
                size: "small",
                children: testResults.length === 0 ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                    type: "secondary",
                    children: "暂无测试结果"
                }, void 0, false, {
                    fileName: "src/pages/api-test/index.tsx",
                    lineNumber: 170,
                    columnNumber: 13
                }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List, {
                    dataSource: testResults,
                    renderItem: (item)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item.Meta, {
                                title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            strong: true,
                                            children: item.api
                                        }, void 0, false, {
                                            fileName: "src/pages/api-test/index.tsx",
                                            lineNumber: 179,
                                            columnNumber: 25
                                        }, void 0),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                            color: getStatusColor(item.status),
                                            children: item.status.toUpperCase()
                                        }, void 0, false, {
                                            fileName: "src/pages/api-test/index.tsx",
                                            lineNumber: 180,
                                            columnNumber: 25
                                        }, void 0),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            type: "secondary",
                                            children: item.timestamp
                                        }, void 0, false, {
                                            fileName: "src/pages/api-test/index.tsx",
                                            lineNumber: 183,
                                            columnNumber: 25
                                        }, void 0)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/api-test/index.tsx",
                                    lineNumber: 178,
                                    columnNumber: 23
                                }, void 0),
                                description: item.message
                            }, void 0, false, {
                                fileName: "src/pages/api-test/index.tsx",
                                lineNumber: 176,
                                columnNumber: 19
                            }, void 0)
                        }, void 0, false, {
                            fileName: "src/pages/api-test/index.tsx",
                            lineNumber: 175,
                            columnNumber: 17
                        }, void 0)
                }, void 0, false, {
                    fileName: "src/pages/api-test/index.tsx",
                    lineNumber: 172,
                    columnNumber: 13
                }, this)
            }, void 0, false, {
                fileName: "src/pages/api-test/index.tsx",
                lineNumber: 168,
                columnNumber: 9
            }, this)
        },
        {
            key: 'info',
            label: '系统信息',
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                direction: "vertical",
                size: "large",
                style: {
                    width: '100%'
                },
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                        title: "迁移状态",
                        size: "small",
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                    level: 5,
                                    children: "✅ 已完成迁移的API服务："
                                }, void 0, false, {
                                    fileName: "src/pages/api-test/index.tsx",
                                    lineNumber: 202,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("ul", {
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                            children: "✅ AI模型管理 (aiModels.ts) - 支持统一响应处理"
                                        }, void 0, false, {
                                            fileName: "src/pages/api-test/index.tsx",
                                            lineNumber: 204,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                            children: "✅ 应用管理 (apps.ts) - 支持统一响应处理"
                                        }, void 0, false, {
                                            fileName: "src/pages/api-test/index.tsx",
                                            lineNumber: 205,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                            children: "✅ 用户认证 (auth.ts) - 支持统一响应处理"
                                        }, void 0, false, {
                                            fileName: "src/pages/api-test/index.tsx",
                                            lineNumber: 206,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                            children: "✅ 聊天管理 (chat.ts) - 支持统一响应处理"
                                        }, void 0, false, {
                                            fileName: "src/pages/api-test/index.tsx",
                                            lineNumber: 207,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                            children: "✅ 知识库管理 (knowledge.ts) - 支持统一响应处理"
                                        }, void 0, false, {
                                            fileName: "src/pages/api-test/index.tsx",
                                            lineNumber: 208,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                            children: "✅ 插件管理 (plugins.ts) - 支持统一响应处理"
                                        }, void 0, false, {
                                            fileName: "src/pages/api-test/index.tsx",
                                            lineNumber: 209,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/api-test/index.tsx",
                                    lineNumber: 203,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/api-test/index.tsx",
                            lineNumber: 201,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/api-test/index.tsx",
                        lineNumber: 200,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                        title: "响应处理特性",
                        size: "small",
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                    level: 5,
                                    children: "🎯 统一响应处理系统特性："
                                }, void 0, false, {
                                    fileName: "src/pages/api-test/index.tsx",
                                    lineNumber: 216,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("ul", {
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                            children: "✅ 自动处理所有HTTP状态码 (200, 400, 401, 403, 404, 422, 500等)"
                                        }, void 0, false, {
                                            fileName: "src/pages/api-test/index.tsx",
                                            lineNumber: 218,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                            children: "✅ 用户友好的错误提示"
                                        }, void 0, false, {
                                            fileName: "src/pages/api-test/index.tsx",
                                            lineNumber: 219,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                            children: "✅ 自动JWT认证和令牌刷新"
                                        }, void 0, false, {
                                            fileName: "src/pages/api-test/index.tsx",
                                            lineNumber: 220,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                            children: "✅ 可配置的成功/失败消息"
                                        }, void 0, false, {
                                            fileName: "src/pages/api-test/index.tsx",
                                            lineNumber: 221,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                            children: "✅ 统一的响应格式"
                                        }, void 0, false, {
                                            fileName: "src/pages/api-test/index.tsx",
                                            lineNumber: 222,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                            children: "✅ 智能的通知显示策略"
                                        }, void 0, false, {
                                            fileName: "src/pages/api-test/index.tsx",
                                            lineNumber: 223,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                            children: "✅ 详细的验证错误信息"
                                        }, void 0, false, {
                                            fileName: "src/pages/api-test/index.tsx",
                                            lineNumber: 224,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                            children: "✅ 网络错误自动处理"
                                        }, void 0, false, {
                                            fileName: "src/pages/api-test/index.tsx",
                                            lineNumber: 225,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/api-test/index.tsx",
                                    lineNumber: 217,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/api-test/index.tsx",
                            lineNumber: 215,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/api-test/index.tsx",
                        lineNumber: 214,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                        title: "测试说明",
                        size: "small",
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                    level: 5,
                                    children: "📝 如何使用："
                                }, void 0, false, {
                                    fileName: "src/pages/api-test/index.tsx",
                                    lineNumber: 232,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("ol", {
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                            children: "确保已登录并有有效的JWT令牌"
                                        }, void 0, false, {
                                            fileName: "src/pages/api-test/index.tsx",
                                            lineNumber: 234,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                            children: '点击"测试所有API"进行批量测试'
                                        }, void 0, false, {
                                            fileName: "src/pages/api-test/index.tsx",
                                            lineNumber: 235,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                            children: "或单独测试各个API接口"
                                        }, void 0, false, {
                                            fileName: "src/pages/api-test/index.tsx",
                                            lineNumber: 236,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                            children: '查看"测试结果"标签页了解详细结果'
                                        }, void 0, false, {
                                            fileName: "src/pages/api-test/index.tsx",
                                            lineNumber: 237,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                            children: "观察错误处理和成功提示的效果"
                                        }, void 0, false, {
                                            fileName: "src/pages/api-test/index.tsx",
                                            lineNumber: 238,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/api-test/index.tsx",
                                    lineNumber: 233,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/api-test/index.tsx",
                            lineNumber: 231,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/api-test/index.tsx",
                        lineNumber: 230,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/api-test/index.tsx",
                lineNumber: 199,
                columnNumber: 9
            }, this)
        }
    ];
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        title: "API统一响应处理测试",
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                message: "API迁移完成",
                description: "所有API服务已成功迁移到统一响应处理系统，支持完整的HTTP状态码处理和用户友好的错误提示。",
                type: "success",
                showIcon: true,
                style: {
                    marginBottom: 24
                }
            }, void 0, false, {
                fileName: "src/pages/api-test/index.tsx",
                lineNumber: 249,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tabs, {
                items: tabItems
            }, void 0, false, {
                fileName: "src/pages/api-test/index.tsx",
                lineNumber: 257,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/api-test/index.tsx",
        lineNumber: 248,
        columnNumber: 5
    }, this);
};
_s(ApiTestPage, "SZq/I4dNIiMeHyVejiVoNhXobY0=");
_c = ApiTestPage;
var _default = ApiTestPage;
var _c;
$RefreshReg$(_c, "ApiTestPage");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=src_pages_api-test_index_tsx-async.js.map