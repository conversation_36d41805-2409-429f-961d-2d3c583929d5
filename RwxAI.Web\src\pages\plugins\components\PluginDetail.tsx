import React from 'react';
import { Modal, Descriptions, Tag, Switch } from 'antd';
import { useIntl, FormattedMessage } from '@umijs/max';

interface PluginDetailProps {
  visible: boolean;
  onVisibleChange: (visible: boolean) => void;
  data?: RwxAI.Plugin;
}

const PluginDetail: React.FC<PluginDetailProps> = ({
  visible,
  onVisibleChange,
  data,
}) => {
  const intl = useIntl();

  const getStatusTag = (status?: string) => {
    const statusMap: Record<string, { color: string; text: string }> = {
      'Active': { color: 'green', text: intl.formatMessage({ id: 'pages.plugins.status.active' }) },
      'Inactive': { color: 'default', text: intl.formatMessage({ id: 'pages.plugins.status.inactive' }) },
      'Error': { color: 'red', text: intl.formatMessage({ id: 'pages.plugins.status.error' }) },
    };
    const statusInfo = statusMap[status || 'Inactive'];
    return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
  };

  return (
    <Modal
      title={intl.formatMessage({ id: 'pages.plugins.detail.title' })}
      open={visible}
      onCancel={() => onVisibleChange(false)}
      footer={null}
      width={800}
    >
      {data && (
        <Descriptions column={2} bordered>
          <Descriptions.Item
            label={<FormattedMessage id="pages.plugins.detail.name" />}
            span={2}
          >
            {data.Name}
          </Descriptions.Item>
          
          <Descriptions.Item
            label={<FormattedMessage id="pages.plugins.detail.description" />}
            span={2}
          >
            {data.Description || '-'}
          </Descriptions.Item>
          
          <Descriptions.Item
            label={<FormattedMessage id="pages.plugins.detail.version" />}
          >
            {data.Version}
          </Descriptions.Item>
          
          <Descriptions.Item
            label={<FormattedMessage id="pages.plugins.detail.author" />}
          >
            {data.Author || '-'}
          </Descriptions.Item>
          
          <Descriptions.Item
            label={<FormattedMessage id="pages.plugins.detail.status" />}
          >
            {getStatusTag(data.Status)}
          </Descriptions.Item>
          
          <Descriptions.Item
            label={<FormattedMessage id="pages.plugins.detail.enabled" />}
          >
            <Switch checked={data.IsEnabled} disabled size="small" />
          </Descriptions.Item>
          
          <Descriptions.Item
            label={<FormattedMessage id="pages.plugins.detail.assemblyPath" />}
            span={2}
          >
            <code>{data.AssemblyPath}</code>
          </Descriptions.Item>
          
          <Descriptions.Item
            label={<FormattedMessage id="pages.plugins.detail.typeName" />}
            span={2}
          >
            <code>{data.TypeName}</code>
          </Descriptions.Item>
          
          <Descriptions.Item
            label={<FormattedMessage id="pages.plugins.detail.functions" />}
          >
            {data.Functions?.length || 0}
          </Descriptions.Item>
          
          <Descriptions.Item
            label={<FormattedMessage id="pages.plugins.detail.createdTime" />}
          >
            {data.CreatedTime ? new Date(data.CreatedTime).toLocaleString() : '-'}
          </Descriptions.Item>
          
          <Descriptions.Item
            label={<FormattedMessage id="pages.plugins.detail.updatedTime" />}
            span={2}
          >
            {data.UpdatedTime ? new Date(data.UpdatedTime).toLocaleString() : '-'}
          </Descriptions.Item>
        </Descriptions>
      )}
    </Modal>
  );
};

export default PluginDetail;
