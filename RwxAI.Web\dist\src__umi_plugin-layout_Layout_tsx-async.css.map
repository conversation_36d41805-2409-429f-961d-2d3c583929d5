{"version": 3, "sources": ["src/.umi/plugin-layout/Layout.css"], "sourcesContent": ["@media screen and (max-width: 480px) {\n  /* 在小屏幕的时候可以有更好的体验 */\n  .umi-plugin-layout-container {\n    width: 100% !important;\n  }\n  .umi-plugin-layout-container > * {\n    border-radius: 0 !important;\n  }\n}\n.umi-plugin-layout-menu .anticon {\n  margin-right: 8px;\n}\n.umi-plugin-layout-menu .ant-dropdown-menu-item {\n  min-width: 160px;\n}\n.umi-plugin-layout-right {\n  display: flex !important;\n  float: right;\n  height: 100%;\n  margin-left: auto;\n  overflow: hidden;\n}\n.umi-plugin-layout-right .umi-plugin-layout-action {\n  display: flex;\n  align-items: center;\n  height: 100%;\n  padding: 0 12px;\n  cursor: pointer;\n  transition: all 0.3s;\n}\n.umi-plugin-layout-right .umi-plugin-layout-action > i {\n  color: rgba(255, 255, 255, 0.85);\n  vertical-align: middle;\n}\n.umi-plugin-layout-right .umi-plugin-layout-action:hover {\n  background: rgba(0, 0, 0, 0.025);\n}\n.umi-plugin-layout-right .umi-plugin-layout-action.opened {\n  background: rgba(0, 0, 0, 0.025);\n}\n.umi-plugin-layout-right .umi-plugin-layout-search {\n  padding: 0 12px;\n}\n.umi-plugin-layout-right .umi-plugin-layout-search:hover {\n  background: transparent;\n}\n.umi-plugin-layout-name {\n  margin-left: 8px;\n}\n.umi-plugin-layout-name.umi-plugin-layout-hide-avatar-img {\n  margin-left: 0;\n}\n"], "names": [], "mappings": "AAAA,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;EAEpC,CAAC,2BAA2B,CAAC,CAAC;IAC5B,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,SAAS;EACxB,CAAC;EACD,CAAC,2BAA2B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,aAAa,EAAE,CAAC,CAAC,CAAC,SAAS;EAC7B,CAAC;AACH,CAAC;AACD,CAAC,sBAAsB,CAAC,CAAC,OAAO,CAAC,CAAC;EAChC,YAAY,EAAE,CAAC,EAAE;AACnB,CAAC;AACD,CAAC,sBAAsB,CAAC,CAAC,sBAAsB,CAAC,CAAC;EAC/C,SAAS,EAAE,GAAG,EAAE;AAClB,CAAC;AACD,CAAC,uBAAuB,CAAC,CAAC;EACxB,OAAO,EAAE,IAAI,CAAC,CAAC,SAAS;EACxB,KAAK,EAAE,KAAK;EACZ,MAAM,EAAE,GAAG,CAAC;EACZ,WAAW,EAAE,IAAI;EACjB,QAAQ,EAAE,MAAM;AAClB,CAAC;AACD,CAAC,uBAAuB,CAAC,CAAC,wBAAwB,CAAC,CAAC;EAClD,OAAO,EAAE,IAAI;EACb,WAAW,EAAE,MAAM;EACnB,MAAM,EAAE,GAAG,CAAC;EACZ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;EACf,MAAM,EAAE,OAAO;EACf,UAAU,EAAE,GAAG,CAAC,GAAG,CAAC;AACtB,CAAC;AACD,CAAC,uBAAuB,CAAC,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtD,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI;EAC/B,cAAc,EAAE,MAAM;AACxB,CAAC;AACD,CAAC,uBAAuB,CAAC,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;EACxD,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK;AACjC,CAAC;AACD,CAAC,uBAAuB,CAAC,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC;EACzD,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK;AACjC,CAAC;AACD,CAAC,uBAAuB,CAAC,CAAC,wBAAwB,CAAC,CAAC;EAClD,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;AACjB,CAAC;AACD,CAAC,uBAAuB,CAAC,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;EACxD,UAAU,EAAE,WAAW;AACzB,CAAC;AACD,CAAC,sBAAsB,CAAC,CAAC;EACvB,WAAW,EAAE,CAAC,EAAE;AAClB,CAAC;AACD,CAAC,sBAAsB,CAAC,iCAAiC,CAAC,CAAC;EACzD,WAAW,EAAE,CAAC;AAChB,CAAC"}