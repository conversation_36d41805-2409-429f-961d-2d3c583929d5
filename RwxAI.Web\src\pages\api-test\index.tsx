import React, { useState } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import { Card, Button, Space, Typography, Alert, Tabs, List, Tag } from 'antd';
import { 
  getAIModels, 
  getApps, 
  getChatSessions, 
  getKnowledgeBases, 
  getPlugins 
} from '@/services/rwxai';

const { Title, Text, Paragraph } = Typography;

interface TestResult {
  api: string;
  status: 'success' | 'error' | 'loading';
  message: string;
  data?: any;
  timestamp: string;
}

const ApiTestPage: React.FC = () => {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [loading, setLoading] = useState<Record<string, boolean>>({});

  const addTestResult = (result: TestResult) => {
    setTestResults(prev => [result, ...prev.slice(0, 19)]); // 保留最近20条记录
  };

  const testApi = async (apiName: string, apiFunction: () => Promise<any>) => {
    setLoading(prev => ({ ...prev, [apiName]: true }));
    
    const startTime = new Date().toLocaleTimeString();
    
    try {
      const response = await apiFunction();
      
      if (response.success) {
        addTestResult({
          api: apiName,
          status: 'success',
          message: `✅ 成功 - ${response.data?.length || 0} 条记录`,
          data: response.data,
          timestamp: startTime,
        });
      } else {
        addTestResult({
          api: apiName,
          status: 'error',
          message: `❌ 失败 - ${response.message}`,
          timestamp: startTime,
        });
      }
    } catch (error: any) {
      addTestResult({
        api: apiName,
        status: 'error',
        message: `❌ 异常 - ${error.message}`,
        timestamp: startTime,
      });
    } finally {
      setLoading(prev => ({ ...prev, [apiName]: false }));
    }
  };

  const testAllApis = async () => {
    const apis = [
      { name: 'AI模型列表', func: getAIModels },
      { name: '应用列表', func: getApps },
      { name: '聊天会话列表', func: getChatSessions },
      { name: '知识库列表', func: getKnowledgeBases },
      { name: '插件列表', func: getPlugins },
    ];

    for (const api of apis) {
      await testApi(api.name, api.func);
      // 添加小延迟避免请求过于频繁
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'green';
      case 'error': return 'red';
      case 'loading': return 'blue';
      default: return 'default';
    }
  };

  const apiTests = [
    {
      category: 'AI模型管理',
      tests: [
        { name: 'AI模型列表', func: getAIModels, desc: '获取所有AI模型' },
      ]
    },
    {
      category: '应用管理',
      tests: [
        { name: '应用列表', func: getApps, desc: '获取所有应用' },
      ]
    },
    {
      category: '聊天管理',
      tests: [
        { name: '聊天会话列表', func: getChatSessions, desc: '获取所有聊天会话' },
      ]
    },
    {
      category: '知识库管理',
      tests: [
        { name: '知识库列表', func: getKnowledgeBases, desc: '获取所有知识库' },
      ]
    },
    {
      category: '插件管理',
      tests: [
        { name: '插件列表', func: getPlugins, desc: '获取所有插件' },
      ]
    },
  ];

  const tabItems = [
    {
      key: 'test',
      label: 'API测试',
      children: (
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <Card title="批量测试" size="small">
            <Space>
              <Button type="primary" onClick={testAllApis} loading={Object.values(loading).some(Boolean)}>
                测试所有API
              </Button>
              <Button onClick={clearResults}>
                清空结果
              </Button>
            </Space>
          </Card>

          {apiTests.map((category) => (
            <Card key={category.category} title={category.category} size="small">
              <Space wrap>
                {category.tests.map((test) => (
                  <Button
                    key={test.name}
                    onClick={() => testApi(test.name, test.func)}
                    loading={loading[test.name]}
                    title={test.desc}
                  >
                    {test.name}
                  </Button>
                ))}
              </Space>
            </Card>
          ))}
        </Space>
      ),
    },
    {
      key: 'results',
      label: `测试结果 (${testResults.length})`,
      children: (
        <Card title="测试结果" size="small">
          {testResults.length === 0 ? (
            <Text type="secondary">暂无测试结果</Text>
          ) : (
            <List
              dataSource={testResults}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    title={
                      <Space>
                        <Text strong>{item.api}</Text>
                        <Tag color={getStatusColor(item.status)}>
                          {item.status.toUpperCase()}
                        </Tag>
                        <Text type="secondary">{item.timestamp}</Text>
                      </Space>
                    }
                    description={item.message}
                  />
                </List.Item>
              )}
            />
          )}
        </Card>
      ),
    },
    {
      key: 'info',
      label: '系统信息',
      children: (
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <Card title="迁移状态" size="small">
            <Paragraph>
              <Title level={5}>✅ 已完成迁移的API服务：</Title>
              <ul>
                <li>✅ AI模型管理 (aiModels.ts) - 支持统一响应处理</li>
                <li>✅ 应用管理 (apps.ts) - 支持统一响应处理</li>
                <li>✅ 用户认证 (auth.ts) - 支持统一响应处理</li>
                <li>✅ 聊天管理 (chat.ts) - 支持统一响应处理</li>
                <li>✅ 知识库管理 (knowledge.ts) - 支持统一响应处理</li>
                <li>✅ 插件管理 (plugins.ts) - 支持统一响应处理</li>
              </ul>
            </Paragraph>
          </Card>

          <Card title="响应处理特性" size="small">
            <Paragraph>
              <Title level={5}>🎯 统一响应处理系统特性：</Title>
              <ul>
                <li>✅ 自动处理所有HTTP状态码 (200, 400, 401, 403, 404, 422, 500等)</li>
                <li>✅ 用户友好的错误提示</li>
                <li>✅ 自动JWT认证和令牌刷新</li>
                <li>✅ 可配置的成功/失败消息</li>
                <li>✅ 统一的响应格式</li>
                <li>✅ 智能的通知显示策略</li>
                <li>✅ 详细的验证错误信息</li>
                <li>✅ 网络错误自动处理</li>
              </ul>
            </Paragraph>
          </Card>

          <Card title="测试说明" size="small">
            <Paragraph>
              <Title level={5}>📝 如何使用：</Title>
              <ol>
                <li>确保已登录并有有效的JWT令牌</li>
                <li>点击"测试所有API"进行批量测试</li>
                <li>或单独测试各个API接口</li>
                <li>查看"测试结果"标签页了解详细结果</li>
                <li>观察错误处理和成功提示的效果</li>
              </ol>
            </Paragraph>
          </Card>
        </Space>
      ),
    },
  ];

  return (
    <PageContainer title="API统一响应处理测试">
      <Alert
        message="API迁移完成"
        description="所有API服务已成功迁移到统一响应处理系统，支持完整的HTTP状态码处理和用户友好的错误提示。"
        type="success"
        showIcon
        style={{ marginBottom: 24 }}
      />
      
      <Tabs items={tabItems} />
    </PageContainer>
  );
};

export default ApiTestPage;
