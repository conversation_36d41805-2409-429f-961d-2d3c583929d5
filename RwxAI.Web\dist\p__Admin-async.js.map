{"version": 3, "sources": ["src/pages/Admin.tsx"], "sourcesContent": ["import { HeartTwoTone, SmileTwoTone } from '@ant-design/icons';\r\nimport { PageContainer } from '@ant-design/pro-components';\r\nimport { useIntl } from '@umijs/max';\r\nimport { Alert, Card, Typography } from 'antd';\r\nimport React from 'react';\r\n\r\nconst Admin: React.FC = () => {\r\n  const intl = useIntl();\r\n  return (\r\n    <PageContainer\r\n      content={intl.formatMessage({\r\n        id: 'pages.admin.subPage.title',\r\n        defaultMessage: 'This page can only be viewed by admin',\r\n      })}\r\n    >\r\n      <Card>\r\n        <Alert\r\n          message={intl.formatMessage({\r\n            id: 'pages.welcome.alertMessage',\r\n            defaultMessage:\r\n              'Faster and stronger heavy-duty components have been released.',\r\n          })}\r\n          type=\"success\"\r\n          showIcon\r\n          banner\r\n          style={{\r\n            margin: -12,\r\n            marginBottom: 48,\r\n          }}\r\n        />\r\n        <Typography.Title level={2} style={{ textAlign: 'center' }}>\r\n          <SmileTwoTone /> Ant Design Pro{' '}\r\n          <HeartTwoTone twoToneColor=\"#eb2f96\" /> You\r\n        </Typography.Title>\r\n      </Card>\r\n      <p style={{ textAlign: 'center', marginTop: 24 }}>\r\n        Want to add more pages? Please refer to{' '}\r\n        <a\r\n          href=\"https://pro.ant.design/docs/block-cn\"\r\n          target=\"_blank\"\r\n          rel=\"noopener noreferrer\"\r\n        >\r\n          use block\r\n        </a>\r\n        。\r\n      </p>\r\n    </PageContainer>\r\n  );\r\n};\r\n\r\nexport default Admin;\r\n"], "names": [], "mappings": ";;;;;;;4BAkDA;;;eAAA;;;;;;;8BAlD2C;sCACb;4BACN;6BACgB;uEACtB;;;;;;;;;;AAElB,MAAM,QAAkB;;IACtB,MAAM,OAAO,IAAA,YAAO;IACpB,qBACE,2BAAC,4BAAa;QACZ,SAAS,KAAK,aAAa,CAAC;YAC1B,IAAI;YACJ,gBAAgB;QAClB;;0BAEA,2BAAC,UAAI;;kCACH,2BAAC,WAAK;wBACJ,SAAS,KAAK,aAAa,CAAC;4BAC1B,IAAI;4BACJ,gBACE;wBACJ;wBACA,MAAK;wBACL,QAAQ;wBACR,MAAM;wBACN,OAAO;4BACL,QAAQ;4BACR,cAAc;wBAChB;;;;;;kCAEF,2BAAC,gBAAU,CAAC,KAAK;wBAAC,OAAO;wBAAG,OAAO;4BAAE,WAAW;wBAAS;;0CACvD,2BAAC,mBAAY;;;;;4BAAG;4BAAgB;0CAChC,2BAAC,mBAAY;gCAAC,cAAa;;;;;;4BAAY;;;;;;;;;;;;;0BAG3C,2BAAC;gBAAE,OAAO;oBAAE,WAAW;oBAAU,WAAW;gBAAG;;oBAAG;oBACR;kCACxC,2BAAC;wBACC,MAAK;wBACL,QAAO;wBACP,KAAI;kCACL;;;;;;oBAEG;;;;;;;;;;;;;AAKZ;GA1CM;;QACS,YAAO;;;KADhB;IA4CN,WAAe"}