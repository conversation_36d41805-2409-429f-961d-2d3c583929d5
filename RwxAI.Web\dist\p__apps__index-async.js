((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] || []).push([
        ['p__apps__index'],
{ "src/pages/apps/components/AppDetail.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Text } = _antd.Typography;
const AppDetail = ({ visible, onVisibleChange, data })=>{
    _s();
    const intl = (0, _max.useIntl)();
    const [showApiKey, setShowApiKey] = (0, _react.useState)(false);
    const [showApiSecret, setShowApiSecret] = (0, _react.useState)(false);
    const copyToClipboard = (text, type)=>{
        navigator.clipboard.writeText(text).then(()=>{
            _antd.message.success(intl.formatMessage({
                id: `pages.apps.detail.copy.${type}.success`
            }));
        });
    };
    const maskString = (str)=>{
        if (!str) return '-';
        if (str.length <= 8) return '*'.repeat(str.length);
        return str.substring(0, 4) + '*'.repeat(str.length - 8) + str.substring(str.length - 4);
    };
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
        title: intl.formatMessage({
            id: 'pages.apps.detail.title'
        }),
        open: visible,
        onCancel: ()=>onVisibleChange(false),
        footer: null,
        width: 800,
        children: data && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions, {
            column: 2,
            bordered: true,
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.apps.detail.name"
                    }, void 0, false, {
                        fileName: "src/pages/apps/components/AppDetail.tsx",
                        lineNumber: 46,
                        columnNumber: 20
                    }, void 0),
                    span: 2,
                    children: data.Name
                }, void 0, false, {
                    fileName: "src/pages/apps/components/AppDetail.tsx",
                    lineNumber: 45,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.apps.detail.description"
                    }, void 0, false, {
                        fileName: "src/pages/apps/components/AppDetail.tsx",
                        lineNumber: 53,
                        columnNumber: 20
                    }, void 0),
                    span: 2,
                    children: data.Description || '-'
                }, void 0, false, {
                    fileName: "src/pages/apps/components/AppDetail.tsx",
                    lineNumber: 52,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.apps.detail.apiKey"
                    }, void 0, false, {
                        fileName: "src/pages/apps/components/AppDetail.tsx",
                        lineNumber: 60,
                        columnNumber: 20
                    }, void 0),
                    span: 2,
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                code: true,
                                copyable: false,
                                children: showApiKey ? data.ApiKey : maskString(data.ApiKey)
                            }, void 0, false, {
                                fileName: "src/pages/apps/components/AppDetail.tsx",
                                lineNumber: 64,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "link",
                                size: "small",
                                icon: showApiKey ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EyeInvisibleOutlined, {}, void 0, false, {
                                    fileName: "src/pages/apps/components/AppDetail.tsx",
                                    lineNumber: 70,
                                    columnNumber: 36
                                }, void 0) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EyeOutlined, {}, void 0, false, {
                                    fileName: "src/pages/apps/components/AppDetail.tsx",
                                    lineNumber: 70,
                                    columnNumber: 63
                                }, void 0),
                                onClick: ()=>setShowApiKey(!showApiKey)
                            }, void 0, false, {
                                fileName: "src/pages/apps/components/AppDetail.tsx",
                                lineNumber: 67,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "link",
                                size: "small",
                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CopyOutlined, {}, void 0, false, {
                                    fileName: "src/pages/apps/components/AppDetail.tsx",
                                    lineNumber: 76,
                                    columnNumber: 23
                                }, void 0),
                                onClick: ()=>copyToClipboard(data.ApiKey || '', 'apiKey')
                            }, void 0, false, {
                                fileName: "src/pages/apps/components/AppDetail.tsx",
                                lineNumber: 73,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/apps/components/AppDetail.tsx",
                        lineNumber: 63,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/apps/components/AppDetail.tsx",
                    lineNumber: 59,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.apps.detail.apiSecret"
                    }, void 0, false, {
                        fileName: "src/pages/apps/components/AppDetail.tsx",
                        lineNumber: 83,
                        columnNumber: 20
                    }, void 0),
                    span: 2,
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                code: true,
                                copyable: false,
                                children: showApiSecret ? data.ApiSecret : maskString(data.ApiSecret)
                            }, void 0, false, {
                                fileName: "src/pages/apps/components/AppDetail.tsx",
                                lineNumber: 87,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "link",
                                size: "small",
                                icon: showApiSecret ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EyeInvisibleOutlined, {}, void 0, false, {
                                    fileName: "src/pages/apps/components/AppDetail.tsx",
                                    lineNumber: 93,
                                    columnNumber: 39
                                }, void 0) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EyeOutlined, {}, void 0, false, {
                                    fileName: "src/pages/apps/components/AppDetail.tsx",
                                    lineNumber: 93,
                                    columnNumber: 66
                                }, void 0),
                                onClick: ()=>setShowApiSecret(!showApiSecret)
                            }, void 0, false, {
                                fileName: "src/pages/apps/components/AppDetail.tsx",
                                lineNumber: 90,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "link",
                                size: "small",
                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CopyOutlined, {}, void 0, false, {
                                    fileName: "src/pages/apps/components/AppDetail.tsx",
                                    lineNumber: 99,
                                    columnNumber: 23
                                }, void 0),
                                onClick: ()=>copyToClipboard(data.ApiSecret || '', 'apiSecret')
                            }, void 0, false, {
                                fileName: "src/pages/apps/components/AppDetail.tsx",
                                lineNumber: 96,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/apps/components/AppDetail.tsx",
                        lineNumber: 86,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/apps/components/AppDetail.tsx",
                    lineNumber: 82,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.apps.detail.status"
                    }, void 0, false, {
                        fileName: "src/pages/apps/components/AppDetail.tsx",
                        lineNumber: 106,
                        columnNumber: 20
                    }, void 0),
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Switch, {
                        checked: data.IsEnabled,
                        disabled: true,
                        size: "small"
                    }, void 0, false, {
                        fileName: "src/pages/apps/components/AppDetail.tsx",
                        lineNumber: 108,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/apps/components/AppDetail.tsx",
                    lineNumber: 105,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.apps.detail.apiCalls"
                    }, void 0, false, {
                        fileName: "src/pages/apps/components/AppDetail.tsx",
                        lineNumber: 112,
                        columnNumber: 20
                    }, void 0),
                    children: [
                        data.ApiCallCount || 0,
                        " / ",
                        data.MaxApiCalls || '∞'
                    ]
                }, void 0, true, {
                    fileName: "src/pages/apps/components/AppDetail.tsx",
                    lineNumber: 111,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.apps.detail.rateLimit"
                    }, void 0, false, {
                        fileName: "src/pages/apps/components/AppDetail.tsx",
                        lineNumber: 118,
                        columnNumber: 20
                    }, void 0),
                    children: data.RateLimitPerMinute ? `${data.RateLimitPerMinute}/min` : '-'
                }, void 0, false, {
                    fileName: "src/pages/apps/components/AppDetail.tsx",
                    lineNumber: 117,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.apps.detail.allowedOrigins"
                    }, void 0, false, {
                        fileName: "src/pages/apps/components/AppDetail.tsx",
                        lineNumber: 124,
                        columnNumber: 20
                    }, void 0),
                    children: data.AllowedOrigins || '-'
                }, void 0, false, {
                    fileName: "src/pages/apps/components/AppDetail.tsx",
                    lineNumber: 123,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.apps.detail.createdTime"
                    }, void 0, false, {
                        fileName: "src/pages/apps/components/AppDetail.tsx",
                        lineNumber: 130,
                        columnNumber: 20
                    }, void 0),
                    children: data.CreatedTime ? new Date(data.CreatedTime).toLocaleString() : '-'
                }, void 0, false, {
                    fileName: "src/pages/apps/components/AppDetail.tsx",
                    lineNumber: 129,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.apps.detail.updatedTime"
                    }, void 0, false, {
                        fileName: "src/pages/apps/components/AppDetail.tsx",
                        lineNumber: 136,
                        columnNumber: 20
                    }, void 0),
                    children: data.UpdatedTime ? new Date(data.UpdatedTime).toLocaleString() : '-'
                }, void 0, false, {
                    fileName: "src/pages/apps/components/AppDetail.tsx",
                    lineNumber: 135,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.apps.detail.configJson"
                    }, void 0, false, {
                        fileName: "src/pages/apps/components/AppDetail.tsx",
                        lineNumber: 142,
                        columnNumber: 20
                    }, void 0),
                    span: 2,
                    children: data.ConfigJson ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("pre", {
                        style: {
                            whiteSpace: 'pre-wrap',
                            fontSize: '12px'
                        },
                        children: JSON.stringify(JSON.parse(data.ConfigJson), null, 2)
                    }, void 0, false, {
                        fileName: "src/pages/apps/components/AppDetail.tsx",
                        lineNumber: 146,
                        columnNumber: 15
                    }, this) : '-'
                }, void 0, false, {
                    fileName: "src/pages/apps/components/AppDetail.tsx",
                    lineNumber: 141,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true, {
            fileName: "src/pages/apps/components/AppDetail.tsx",
            lineNumber: 44,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "src/pages/apps/components/AppDetail.tsx",
        lineNumber: 36,
        columnNumber: 5
    }, this);
};
_s(AppDetail, "BcLSaundbIU9czF1Y0E01tHA1OM=", false, function() {
    return [
        _max.useIntl
    ];
});
_c = AppDetail;
var _default = AppDetail;
var _c;
$RefreshReg$(_c, "AppDetail");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/apps/components/AppForm.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _rwxai = __mako_require__("src/services/rwxai/index.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const AppForm = ({ visible, onVisibleChange, initialValues, onSuccess })=>{
    _s();
    const intl = (0, _max.useIntl)();
    const [form] = _antd.Form.useForm();
    const [loading, setLoading] = (0, _react.useState)(false);
    const isEdit = !!(initialValues === null || initialValues === void 0 ? void 0 : initialValues.Id);
    (0, _react.useEffect)(()=>{
        if (visible) {
            if (initialValues) form.setFieldsValue(initialValues);
            else form.resetFields();
        }
    }, [
        visible,
        initialValues
    ]);
    const handleSubmit = async ()=>{
        try {
            const values = await form.validateFields();
            setLoading(true);
            let response;
            if (isEdit) response = await (0, _rwxai.updateApp)(initialValues.Id, {
                ...initialValues,
                ...values
            });
            else response = await (0, _rwxai.createApp)(values);
            if (response.success) onSuccess();
        // 成功和错误消息会由统一响应处理系统自动显示
        } catch (error) {
            // 表单验证错误等
            console.error('Form validation error:', error);
        } finally{
            setLoading(false);
        }
    };
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
        title: intl.formatMessage({
            id: isEdit ? 'pages.apps.form.edit.title' : 'pages.apps.form.create.title'
        }),
        open: visible,
        onCancel: ()=>onVisibleChange(false),
        onOk: handleSubmit,
        confirmLoading: loading,
        width: 600,
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
            form: form,
            layout: "vertical",
            initialValues: {
                IsEnabled: true,
                RateLimitPerMinute: 60
            },
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    name: "Name",
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.apps.form.name"
                    }, void 0, false, {
                        fileName: "src/pages/apps/components/AppForm.tsx",
                        lineNumber: 80,
                        columnNumber: 18
                    }, void 0),
                    rules: [
                        {
                            required: true,
                            message: intl.formatMessage({
                                id: 'pages.apps.form.name.required'
                            })
                        }
                    ],
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                        placeholder: intl.formatMessage({
                            id: 'pages.apps.form.name.placeholder'
                        })
                    }, void 0, false, {
                        fileName: "src/pages/apps/components/AppForm.tsx",
                        lineNumber: 83,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/apps/components/AppForm.tsx",
                    lineNumber: 78,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    name: "Description",
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.apps.form.description"
                    }, void 0, false, {
                        fileName: "src/pages/apps/components/AppForm.tsx",
                        lineNumber: 88,
                        columnNumber: 18
                    }, void 0),
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input.TextArea, {
                        rows: 3,
                        placeholder: intl.formatMessage({
                            id: 'pages.apps.form.description.placeholder'
                        })
                    }, void 0, false, {
                        fileName: "src/pages/apps/components/AppForm.tsx",
                        lineNumber: 90,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/apps/components/AppForm.tsx",
                    lineNumber: 86,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    name: "MaxApiCalls",
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.apps.form.maxApiCalls"
                    }, void 0, false, {
                        fileName: "src/pages/apps/components/AppForm.tsx",
                        lineNumber: 98,
                        columnNumber: 18
                    }, void 0),
                    help: intl.formatMessage({
                        id: 'pages.apps.form.maxApiCalls.help'
                    }),
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.InputNumber, {
                        min: 0,
                        style: {
                            width: '100%'
                        },
                        placeholder: intl.formatMessage({
                            id: 'pages.apps.form.maxApiCalls.placeholder'
                        })
                    }, void 0, false, {
                        fileName: "src/pages/apps/components/AppForm.tsx",
                        lineNumber: 101,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/apps/components/AppForm.tsx",
                    lineNumber: 96,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    name: "RateLimitPerMinute",
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.apps.form.rateLimit"
                    }, void 0, false, {
                        fileName: "src/pages/apps/components/AppForm.tsx",
                        lineNumber: 110,
                        columnNumber: 18
                    }, void 0),
                    help: intl.formatMessage({
                        id: 'pages.apps.form.rateLimit.help'
                    }),
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.InputNumber, {
                        min: 1,
                        max: 10000,
                        style: {
                            width: '100%'
                        },
                        placeholder: intl.formatMessage({
                            id: 'pages.apps.form.rateLimit.placeholder'
                        })
                    }, void 0, false, {
                        fileName: "src/pages/apps/components/AppForm.tsx",
                        lineNumber: 113,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/apps/components/AppForm.tsx",
                    lineNumber: 108,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    name: "AllowedOrigins",
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.apps.form.allowedOrigins"
                    }, void 0, false, {
                        fileName: "src/pages/apps/components/AppForm.tsx",
                        lineNumber: 123,
                        columnNumber: 18
                    }, void 0),
                    help: intl.formatMessage({
                        id: 'pages.apps.form.allowedOrigins.help'
                    }),
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input.TextArea, {
                        rows: 2,
                        placeholder: intl.formatMessage({
                            id: 'pages.apps.form.allowedOrigins.placeholder'
                        })
                    }, void 0, false, {
                        fileName: "src/pages/apps/components/AppForm.tsx",
                        lineNumber: 126,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/apps/components/AppForm.tsx",
                    lineNumber: 121,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    name: "IsEnabled",
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.apps.form.isEnabled"
                    }, void 0, false, {
                        fileName: "src/pages/apps/components/AppForm.tsx",
                        lineNumber: 134,
                        columnNumber: 18
                    }, void 0),
                    valuePropName: "checked",
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Switch, {}, void 0, false, {
                        fileName: "src/pages/apps/components/AppForm.tsx",
                        lineNumber: 137,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/apps/components/AppForm.tsx",
                    lineNumber: 132,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "src/pages/apps/components/AppForm.tsx",
            lineNumber: 70,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "src/pages/apps/components/AppForm.tsx",
        lineNumber: 60,
        columnNumber: 5
    }, this);
};
_s(AppForm, "G9fecnUMjs1JWq19zn6BW1ErtLA=", false, function() {
    return [
        _max.useIntl,
        _antd.Form.useForm
    ];
});
_c = AppForm;
var _default = AppForm;
var _c;
$RefreshReg$(_c, "AppForm");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/apps/index.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
var _protable = __mako_require__("node_modules/@ant-design/pro-table/es/index.js");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _rwxai = __mako_require__("src/services/rwxai/index.ts");
var _pageDataHandler = __mako_require__("src/utils/pageDataHandler.ts");
var _AppForm = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/apps/components/AppForm.tsx"));
var _AppDetail = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/apps/components/AppDetail.tsx"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const AppsPage = ()=>{
    _s();
    const intl = (0, _max.useIntl)();
    const actionRef = (0, _react.useRef)();
    const [createModalVisible, setCreateModalVisible] = (0, _react.useState)(false);
    const [editModalVisible, setEditModalVisible] = (0, _react.useState)(false);
    const [detailModalVisible, setDetailModalVisible] = (0, _react.useState)(false);
    const [currentRecord, setCurrentRecord] = (0, _react.useState)();
    const handleDelete = async (record)=>{
        _antd.Modal.confirm({
            title: intl.formatMessage({
                id: 'pages.apps.delete.confirm.title'
            }),
            content: intl.formatMessage({
                id: 'pages.apps.delete.confirm.content'
            }),
            onOk: async ()=>{
                try {
                    var _actionRef_current;
                    await (0, _rwxai.deleteApp)(record.Id);
                    _antd.message.success(intl.formatMessage({
                        id: 'pages.apps.delete.success'
                    }));
                    (_actionRef_current = actionRef.current) === null || _actionRef_current === void 0 || _actionRef_current.reload();
                } catch (error) {
                    _antd.message.error(intl.formatMessage({
                        id: 'pages.apps.delete.error'
                    }));
                }
            }
        });
    };
    const handleStatusChange = async (record, checked)=>{
        try {
            var _actionRef_current;
            await (0, _rwxai.updateAppStatus)(record.Id, {
                IsEnabled: checked
            });
            _antd.message.success(intl.formatMessage({
                id: 'pages.apps.status.update.success'
            }));
            (_actionRef_current = actionRef.current) === null || _actionRef_current === void 0 || _actionRef_current.reload();
        } catch (error) {
            _antd.message.error(intl.formatMessage({
                id: 'pages.apps.status.update.error'
            }));
        }
    };
    const handleRegenerateKeys = async (record)=>{
        _antd.Modal.confirm({
            title: intl.formatMessage({
                id: 'pages.apps.regenerateKeys.confirm.title'
            }),
            content: intl.formatMessage({
                id: 'pages.apps.regenerateKeys.confirm.content'
            }),
            onOk: async ()=>{
                try {
                    var _actionRef_current;
                    await (0, _rwxai.regenerateApiKeys)(record.Id);
                    _antd.message.success(intl.formatMessage({
                        id: 'pages.apps.regenerateKeys.success'
                    }));
                    (_actionRef_current = actionRef.current) === null || _actionRef_current === void 0 || _actionRef_current.reload();
                } catch (error) {
                    _antd.message.error(intl.formatMessage({
                        id: 'pages.apps.regenerateKeys.error'
                    }));
                }
            }
        });
    };
    const handleResetApiCalls = async (record)=>{
        _antd.Modal.confirm({
            title: intl.formatMessage({
                id: 'pages.apps.resetApiCalls.confirm.title'
            }),
            content: intl.formatMessage({
                id: 'pages.apps.resetApiCalls.confirm.content'
            }),
            onOk: async ()=>{
                try {
                    var _actionRef_current;
                    await (0, _rwxai.resetApiCalls)(record.Id);
                    _antd.message.success(intl.formatMessage({
                        id: 'pages.apps.resetApiCalls.success'
                    }));
                    (_actionRef_current = actionRef.current) === null || _actionRef_current === void 0 || _actionRef_current.reload();
                } catch (error) {
                    _antd.message.error(intl.formatMessage({
                        id: 'pages.apps.resetApiCalls.error'
                    }));
                }
            }
        });
    };
    const columns = [
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.apps.table.name"
            }, void 0, false, {
                fileName: "src/pages/apps/index.tsx",
                lineNumber: 80,
                columnNumber: 14
            }, this),
            dataIndex: 'Name',
            key: 'Name',
            ellipsis: true
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.apps.table.description"
            }, void 0, false, {
                fileName: "src/pages/apps/index.tsx",
                lineNumber: 86,
                columnNumber: 14
            }, this),
            dataIndex: 'Description',
            key: 'Description',
            ellipsis: true,
            hideInSearch: true
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.apps.table.status"
            }, void 0, false, {
                fileName: "src/pages/apps/index.tsx",
                lineNumber: 93,
                columnNumber: 14
            }, this),
            dataIndex: 'IsEnabled',
            key: 'IsEnabled',
            render: (_, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Switch, {
                    checked: record.IsEnabled,
                    size: "small",
                    onChange: (checked)=>handleStatusChange(record, checked)
                }, void 0, false, {
                    fileName: "src/pages/apps/index.tsx",
                    lineNumber: 97,
                    columnNumber: 9
                }, this)
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.apps.table.apiCalls"
            }, void 0, false, {
                fileName: "src/pages/apps/index.tsx",
                lineNumber: 105,
                columnNumber: 14
            }, this),
            key: 'apiCalls',
            hideInSearch: true,
            render: (_, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                    children: [
                        record.ApiCallCount || 0,
                        " / ",
                        record.MaxApiCalls || '∞'
                    ]
                }, void 0, true, {
                    fileName: "src/pages/apps/index.tsx",
                    lineNumber: 109,
                    columnNumber: 9
                }, this)
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.apps.table.rateLimit"
            }, void 0, false, {
                fileName: "src/pages/apps/index.tsx",
                lineNumber: 115,
                columnNumber: 14
            }, this),
            dataIndex: 'RateLimitPerMinute',
            key: 'RateLimitPerMinute',
            hideInSearch: true,
            render: (value)=>value ? `${value}/min` : '-'
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.apps.table.createdTime"
            }, void 0, false, {
                fileName: "src/pages/apps/index.tsx",
                lineNumber: 122,
                columnNumber: 14
            }, this),
            dataIndex: 'CreatedTime',
            key: 'CreatedTime',
            valueType: 'dateTime',
            width: 180,
            hideInSearch: true
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.apps.table.actions"
            }, void 0, false, {
                fileName: "src/pages/apps/index.tsx",
                lineNumber: 130,
                columnNumber: 14
            }, this),
            key: 'actions',
            width: 280,
            render: (_, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                            title: intl.formatMessage({
                                id: 'pages.apps.actions.view'
                            }),
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "link",
                                size: "small",
                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EyeOutlined, {}, void 0, false, {
                                    fileName: "src/pages/apps/index.tsx",
                                    lineNumber: 139,
                                    columnNumber: 21
                                }, void 0),
                                onClick: ()=>{
                                    setCurrentRecord(record);
                                    setDetailModalVisible(true);
                                }
                            }, void 0, false, {
                                fileName: "src/pages/apps/index.tsx",
                                lineNumber: 136,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/apps/index.tsx",
                            lineNumber: 135,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                            title: intl.formatMessage({
                                id: 'pages.apps.actions.edit'
                            }),
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "link",
                                size: "small",
                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                                    fileName: "src/pages/apps/index.tsx",
                                    lineNumber: 150,
                                    columnNumber: 21
                                }, void 0),
                                onClick: ()=>{
                                    setCurrentRecord(record);
                                    setEditModalVisible(true);
                                }
                            }, void 0, false, {
                                fileName: "src/pages/apps/index.tsx",
                                lineNumber: 147,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/apps/index.tsx",
                            lineNumber: 146,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                            title: intl.formatMessage({
                                id: 'pages.apps.actions.regenerateKeys'
                            }),
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "link",
                                size: "small",
                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.KeyOutlined, {}, void 0, false, {
                                    fileName: "src/pages/apps/index.tsx",
                                    lineNumber: 161,
                                    columnNumber: 21
                                }, void 0),
                                onClick: ()=>handleRegenerateKeys(record)
                            }, void 0, false, {
                                fileName: "src/pages/apps/index.tsx",
                                lineNumber: 158,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/apps/index.tsx",
                            lineNumber: 157,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                            title: intl.formatMessage({
                                id: 'pages.apps.actions.resetApiCalls'
                            }),
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "link",
                                size: "small",
                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ReloadOutlined, {}, void 0, false, {
                                    fileName: "src/pages/apps/index.tsx",
                                    lineNumber: 169,
                                    columnNumber: 21
                                }, void 0),
                                onClick: ()=>handleResetApiCalls(record)
                            }, void 0, false, {
                                fileName: "src/pages/apps/index.tsx",
                                lineNumber: 166,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/apps/index.tsx",
                            lineNumber: 165,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                            title: intl.formatMessage({
                                id: 'pages.apps.actions.delete'
                            }),
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "link",
                                size: "small",
                                danger: true,
                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                    fileName: "src/pages/apps/index.tsx",
                                    lineNumber: 178,
                                    columnNumber: 21
                                }, void 0),
                                onClick: ()=>handleDelete(record)
                            }, void 0, false, {
                                fileName: "src/pages/apps/index.tsx",
                                lineNumber: 174,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/apps/index.tsx",
                            lineNumber: 173,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/apps/index.tsx",
                    lineNumber: 134,
                    columnNumber: 9
                }, this)
        }
    ];
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_protable.ProTable, {
                headerTitle: intl.formatMessage({
                    id: 'pages.apps.title'
                }),
                actionRef: actionRef,
                rowKey: "Id",
                search: {
                    labelWidth: 120
                },
                toolBarRender: ()=>[
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "primary",
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PlusOutlined, {}, void 0, false, {
                                fileName: "src/pages/apps/index.tsx",
                                lineNumber: 200,
                                columnNumber: 19
                            }, void 0),
                            onClick: ()=>setCreateModalVisible(true),
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                id: "pages.apps.actions.create"
                            }, void 0, false, {
                                fileName: "src/pages/apps/index.tsx",
                                lineNumber: 203,
                                columnNumber: 13
                            }, void 0)
                        }, "primary", false, {
                            fileName: "src/pages/apps/index.tsx",
                            lineNumber: 197,
                            columnNumber: 11
                        }, void 0)
                    ],
                request: (0, _pageDataHandler.createSimpleProTableRequest)(_rwxai.getMyApps),
                columns: columns
            }, void 0, false, {
                fileName: "src/pages/apps/index.tsx",
                lineNumber: 189,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_AppForm.default, {
                visible: createModalVisible,
                onVisibleChange: setCreateModalVisible,
                onSuccess: ()=>{
                    var _actionRef_current;
                    (_actionRef_current = actionRef.current) === null || _actionRef_current === void 0 || _actionRef_current.reload();
                    setCreateModalVisible(false);
                }
            }, void 0, false, {
                fileName: "src/pages/apps/index.tsx",
                lineNumber: 210,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_AppForm.default, {
                visible: editModalVisible,
                onVisibleChange: setEditModalVisible,
                initialValues: currentRecord,
                onSuccess: ()=>{
                    var _actionRef_current;
                    (_actionRef_current = actionRef.current) === null || _actionRef_current === void 0 || _actionRef_current.reload();
                    setEditModalVisible(false);
                }
            }, void 0, false, {
                fileName: "src/pages/apps/index.tsx",
                lineNumber: 219,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_AppDetail.default, {
                visible: detailModalVisible,
                onVisibleChange: setDetailModalVisible,
                data: currentRecord
            }, void 0, false, {
                fileName: "src/pages/apps/index.tsx",
                lineNumber: 229,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/apps/index.tsx",
        lineNumber: 188,
        columnNumber: 5
    }, this);
};
_s(AppsPage, "hTbNuNc6vHQfYm20aQVEQo9um4U=", false, function() {
    return [
        _max.useIntl
    ];
});
_c = AppsPage;
var _default = AppsPage;
var _c;
$RefreshReg$(_c, "AppsPage");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=p__apps__index-async.js.map