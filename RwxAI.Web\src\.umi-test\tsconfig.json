{"compilerOptions": {"target": "esnext", "module": "esnext", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "moduleResolution": "bundler", "importHelpers": true, "noEmit": true, "jsx": "react-jsx", "esModuleInterop": true, "sourceMap": true, "baseUrl": "../../", "strict": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "paths": {"@/*": ["src/*"], "@@/*": ["src/.umi/*"], "@umijs/max": ["../../node_modules/umi"], "@umijs/max/typings": ["src/.umi/typings"]}}, "include": ["../../.umirc.ts", "../../.umirc.*.ts", "../../**/*.d.ts", "../../**/*.ts", "../../**/*.tsx"]}