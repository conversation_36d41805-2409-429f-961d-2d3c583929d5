{"version": 3, "sources": ["src_pages_ai-models_index_tsx-async.11515147630694169085.hot-update.js", "src/pages/ai-models/components/ModelForm.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/ai-models/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='16629148152863461991';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/.umi/plugin-openapi/openapi.tsx\":[\"vendors\",\"src/.umi/plugin-openapi/openapi.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Admin.tsx\":[\"p__Admin\"],\"src/pages/Welcome.tsx\":[\"p__Welcome\"],\"src/pages/ai-models/index.tsx\":[\"common\",\"src/pages/ai-models/index.tsx\"],\"src/pages/api-test/index.tsx\":[\"common\",\"src/pages/api-test/index.tsx\"],\"src/pages/apps/index.tsx\":[\"common\",\"p__apps__index\"],\"src/pages/auth-test/index.tsx\":[\"common\",\"src/pages/auth-test/index.tsx\"],\"src/pages/chat/chat-interface.tsx\":[\"vendors\",\"common\",\"src/pages/chat/chat-interface.tsx\"],\"src/pages/chat/index.tsx\":[\"common\",\"p__chat__index\"],\"src/pages/chat/session/[id].tsx\":[\"common\",\"p__chat__session__id\"],\"src/pages/knowledge/[id]/files.tsx\":[\"common\",\"p__knowledge__id__files\"],\"src/pages/knowledge/index.tsx\":[\"common\",\"p__knowledge__index\"],\"src/pages/plugins/index.tsx\":[\"common\",\"p__plugins__index\"],\"src/pages/response-demo/index.tsx\":[\"src/pages/response-demo/index.tsx\"],\"src/pages/table-list/index.tsx\":[\"src/pages/table-list/index.tsx\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"],\"src/pages/user/register/index.tsx\":[\"common\",\"p__user__register__index\"]});;\r\n  },\r\n);\r\n", "import React, { useEffect, useState } from 'react';\r\nimport { Modal, Form, Input, Select, InputNumber, Switch } from 'antd';\r\nimport { useIntl, FormattedMessage } from '@umijs/max';\r\nimport {\r\n  createAIModel,\r\n  updateAIModel,\r\n  getEnabledAIProviders,\r\n  getAIModelTemplatesByProviderAndType\r\n} from '@/services/rwxai';\r\n\r\ninterface ModelFormProps {\r\n  visible: boolean;\r\n  onVisibleChange: (visible: boolean) => void;\r\n  initialValues?: RwxAI.AIModel;\r\n  onSuccess: () => void;\r\n}\r\n\r\nconst ModelForm: React.FC<ModelFormProps> = ({\r\n  visible,\r\n  onVisibleChange,\r\n  initialValues,\r\n  onSuccess,\r\n}) => {\r\n  const intl = useIntl();\r\n  const [form] = Form.useForm();\r\n  const [loading, setLoading] = useState(false);\r\n  const [providers, setProviders] = useState<RwxAI.AIProvider[]>([]);\r\n  const [modelTemplates, setModelTemplates] = useState<RwxAI.AIModelTemplate[]>([]);\r\n  const [loadingTemplates, setLoadingTemplates] = useState(false);\r\n\r\n  const isEdit = !!initialValues?.Id;\r\n\r\n  useEffect(() => {\r\n    if (visible) {\r\n      initializeForm();\r\n    }\r\n  }, [visible, initialValues]);\r\n\r\n  const initializeForm = async () => {\r\n    // 先加载提供商数据\r\n    await loadProviders();\r\n\r\n    if (initialValues) {\r\n      // 映射后端数据结构到表单字段\r\n      const formData = {\r\n        // 基本字段\r\n        Name: initialValues.Name,\r\n\r\n        // 模型ID\r\n        ModelId: initialValues.ModelId,\r\n\r\n        // 模型服务端点\r\n        Endpoint: initialValues.Endpoint,\r\n\r\n        // 提供商代码\r\n        ProviderCode: initialValues.ProviderCode,\r\n\r\n        DisplayName: initialValues.DisplayName,\r\n        Description: initialValues.Description,\r\n        ApiKey: initialValues.ApiKey,\r\n        IsEnabled: initialValues.IsEnabled,\r\n        IsDefault: initialValues.IsDefault,\r\n\r\n        // 配置参数\r\n        MaxTokens: initialValues.MaxTokens,\r\n        Temperature: initialValues.Temperature,\r\n\r\n        // 关联字段\r\n        TemplateId: initialValues.ModelTemplateId,\r\n        ProviderId: initialValues.Template?.Provider?.Id, // 添加提供商ID\r\n\r\n        // 只读字段，从模板中获取\r\n        ModelType: initialValues.Template?.Type,\r\n        SupportsStreaming: initialValues.Template?.SupportsStreaming,\r\n      };\r\n\r\n      form.setFieldsValue(formData);\r\n\r\n      // 如果有提供商ID，加载对应的模型模板\r\n      const providerId = initialValues.Template?.Provider?.Id;\r\n      if (providerId) {\r\n        await loadModelTemplates(providerId);\r\n      }\r\n    } else {\r\n      form.resetFields();\r\n      setModelTemplates([]);\r\n    }\r\n  };\r\n\r\n  // 监听表单字段变化\r\n  const handleFormValuesChange = (changedValues: any) => {\r\n    // 当提供商发生变化时，重新加载模型模板\r\n    if (changedValues.ProviderId !== undefined) {\r\n      const providerId = changedValues.ProviderId;\r\n\r\n      loadModelTemplates(providerId);\r\n\r\n      // 清空模型模板选择\r\n      form.setFieldValue('TemplateId', undefined);\r\n\r\n      // 清空相关字段，因为要重新选择模板\r\n      form.setFieldsValue({\r\n        ModelId: undefined,\r\n        ModelType: undefined,\r\n        Endpoint: undefined,\r\n        SupportsStreaming: undefined,\r\n        Name: undefined,\r\n        Description: undefined,\r\n        DisplayName: undefined,\r\n        ProviderCode: undefined,\r\n      });\r\n    }\r\n\r\n    // 当选择模型模板时，自动填充相关字段\r\n    if (changedValues.TemplateId !== undefined) {\r\n      const selectedTemplate = modelTemplates.find(template =>\r\n        template.Id === changedValues.TemplateId\r\n      );\r\n      if (selectedTemplate) {\r\n        // 自动填充模板的所有信息\r\n        const updates: any = {\r\n          ModelType: selectedTemplate.Type,\r\n          Name: selectedTemplate.Name,\r\n          ModelId: selectedTemplate.ModelId,\r\n          Endpoint: selectedTemplate.Endpoint,\r\n          Description: selectedTemplate.Description,\r\n          DisplayName: selectedTemplate.DisplayName,\r\n          SupportsStreaming: selectedTemplate.SupportsStreaming,\r\n          ProviderCode: selectedTemplate.Provider?.Code,\r\n        };\r\n\r\n        // 填充可选的配置参数\r\n        if (selectedTemplate.MaxOutputLength !== undefined) {\r\n          updates.MaxTokens = selectedTemplate.MaxOutputLength;\r\n        }\r\n\r\n        form.setFieldsValue(updates);\r\n      }\r\n    }\r\n  };\r\n\r\n  const loadProviders = async () => {\r\n    try {\r\n      const response = await getEnabledAIProviders();\r\n      if (response.success) {\r\n        setProviders(response.data || []);\r\n      } else {\r\n        setProviders([]);\r\n      }\r\n    } catch (error) {\r\n      setProviders([]);\r\n    }\r\n    // 错误消息会由统一响应处理系统自动显示\r\n  };\r\n\r\n  // 根据提供商加载模型模板\r\n  const loadModelTemplates = async (providerId?: string) => {\r\n    if (!providerId) {\r\n      setModelTemplates([]);\r\n      return;\r\n    }\r\n\r\n    setLoadingTemplates(true);\r\n    try {\r\n      // 根据提供商获取所有模板\r\n      const response = await getAIModelTemplatesByProviderAndType({\r\n        providerId,\r\n        modelType: undefined, // 不限制模型类型，获取该提供商的所有模板\r\n      });\r\n\r\n      if (response.success) {\r\n        setModelTemplates(response.data || []);\r\n      } else {\r\n        setModelTemplates([]);\r\n      }\r\n    } catch (error) {\r\n      setModelTemplates([]);\r\n    } finally {\r\n      setLoadingTemplates(false);\r\n    }\r\n  };\r\n\r\n  const handleSubmit = async () => {\r\n    try {\r\n      const values = await form.validateFields();\r\n      setLoading(true);\r\n\r\n      // 构建提交数据，只保存模板ID和必要字段\r\n      const submitData: any = {\r\n        modelTemplateId: values.TemplateId, // 只保存模板ID        \r\n        name: values.Name,\r\n        modelId: values.ModelId,\r\n        endpoint: values.Endpoint,\r\n        providerCode: values.ProviderCode,\r\n        displayName: values.DisplayName,\r\n        description: values.Description,\r\n        apiKey: values.ApiKey,\r\n        isEnabled: values.IsEnabled,\r\n        isDefault: values.IsDefault,\r\n        // 可选的自定义配置参数\r\n        maxTokens: values.MaxTokens,\r\n        temperature: values.Temperature,\r\n        frequencyPenalty: values.FrequencyPenalty,\r\n        presencePenalty: values.PresencePenalty,\r\n        systemPrompt: values.SystemPrompt,\r\n      };\r\n\r\n      let response;\r\n      if (isEdit) {\r\n        response = await updateAIModel(initialValues!.Id, { ...initialValues, ...submitData });\r\n      } else {\r\n        response = await createAIModel(submitData);\r\n      }\r\n\r\n      if (response.success) {\r\n        onSuccess();\r\n      }\r\n      // 成功和错误消息会由统一响应处理系统自动显示\r\n    } catch (error) {\r\n      // 表单验证错误等\r\n      console.error('Form validation error:', error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Modal\r\n      title={intl.formatMessage({\r\n        id: isEdit ? 'pages.aiModels.form.edit.title' : 'pages.aiModels.form.create.title',\r\n      })}\r\n      open={visible}\r\n      onCancel={() => onVisibleChange(false)}\r\n      onOk={handleSubmit}\r\n      confirmLoading={loading}\r\n      width={900}\r\n      style={{ top: 50 }}\r\n      styles={{\r\n        body: { maxHeight: '70vh', overflowY: 'auto', padding: '24px' }\r\n      }}\r\n    >\r\n      <Form\r\n        form={form}\r\n        layout=\"vertical\"\r\n        onValuesChange={handleFormValuesChange}\r\n        initialValues={{\r\n          IsEnabled: true,\r\n          Temperature: 0.7,\r\n        }}\r\n      >\r\n        {/* 第一步：选择AI模型提供商和模板 */}\r\n        <div style={{ display: 'flex', gap: '16px' }}>\r\n          <Form.Item\r\n            name=\"ProviderId\"\r\n            label={<FormattedMessage id=\"pages.aiModels.form.provider\" />}\r\n            rules={[{ required: true, message: intl.formatMessage({ id: 'pages.aiModels.form.provider.required' }) }]}\r\n            tooltip={<FormattedMessage id=\"pages.aiModels.form.provider.tooltip\" />}\r\n            style={{ flex: 1 }}\r\n          >\r\n            <Select placeholder={intl.formatMessage({ id: 'pages.aiModels.form.provider.placeholder' })}>\r\n              {providers.map((provider) => (\r\n                <Select.Option key={provider.Id} value={provider.Id}>\r\n                  {provider.DisplayName || provider.Name}\r\n                </Select.Option>\r\n              ))}\r\n            </Select>\r\n          </Form.Item>\r\n\r\n          <Form.Item\r\n            name=\"TemplateId\"\r\n            label={<FormattedMessage id=\"pages.aiModels.form.template\" />}\r\n            rules={[{ required: true, message: intl.formatMessage({ id: 'pages.aiModels.form.template.required' }) }]}\r\n            tooltip={<FormattedMessage id=\"pages.aiModels.form.template.tooltip\" />}\r\n            style={{ flex: 1 }}\r\n          >\r\n            <Select\r\n              placeholder={intl.formatMessage({ id: 'pages.aiModels.form.template.placeholder' })}\r\n              loading={loadingTemplates}\r\n              allowClear\r\n              showSearch\r\n              optionFilterProp=\"children\"\r\n              filterOption={(input, option) =>\r\n                String(option?.children || '').toLowerCase().includes(input.toLowerCase())\r\n              }\r\n            >\r\n              {modelTemplates.map((template) => (\r\n                <Select.Option key={template.Id} value={template.Id}>\r\n                  {template.DisplayName || template.Name} ({template.ModelId})\r\n                </Select.Option>\r\n              ))}\r\n            </Select>\r\n          </Form.Item>\r\n        </div>\r\n\r\n        {/* 基本信息（从模板自动填充） */}\r\n        <div style={{ display: 'flex', gap: '16px' }}>\r\n          <Form.Item\r\n            name=\"Name\"\r\n            label={<FormattedMessage id=\"pages.aiModels.form.name\" />}\r\n            rules={[{ required: true, message: intl.formatMessage({ id: 'pages.aiModels.form.name.required' }) }]}\r\n            style={{ flex: 1 }}\r\n          >\r\n            <Input placeholder={intl.formatMessage({ id: 'pages.aiModels.form.name.placeholder' })} />\r\n          </Form.Item>\r\n\r\n          <Form.Item\r\n            name=\"DisplayName\"\r\n            label={<FormattedMessage id=\"pages.aiModels.form.displayName\" />}\r\n            style={{ flex: 1 }}\r\n          >\r\n            <Input placeholder={intl.formatMessage({ id: 'pages.aiModels.form.displayName.placeholder' })} />\r\n          </Form.Item>\r\n        </div>\r\n\r\n        <Form.Item\r\n          name=\"Description\"\r\n          label={<FormattedMessage id=\"pages.aiModels.form.description\" />}\r\n        >\r\n          <Input.TextArea\r\n            rows={2}\r\n            placeholder={intl.formatMessage({ id: 'pages.aiModels.form.description.placeholder' })}\r\n          />\r\n        </Form.Item>\r\n\r\n        <Form.Item\r\n          name=\"SystemPrompt\"\r\n          label={<FormattedMessage id=\"pages.aiModels.form.systemPrompt\" />}\r\n          tooltip={<FormattedMessage id=\"pages.aiModels.form.systemPrompt.tooltip\" />}\r\n        >\r\n          <Input.TextArea\r\n            rows={3}\r\n            placeholder={intl.formatMessage({ id: 'pages.aiModels.form.systemPrompt.placeholder' })}\r\n          />\r\n        </Form.Item>\r\n\r\n        <Form.Item\r\n          name=\"ApiKey\"\r\n          label={<FormattedMessage id=\"pages.aiModels.form.apiKey\" />}\r\n          tooltip={<FormattedMessage id=\"pages.aiModels.form.apiKey.tooltip\" />}\r\n        >\r\n          <Input.Password placeholder={intl.formatMessage({ id: 'pages.aiModels.form.apiKey.placeholder' })} />\r\n        </Form.Item>\r\n\r\n        {/* 只读字段，显示从模板获取的信息 */}\r\n        <div style={{ display: 'flex', gap: '16px' }}>\r\n          <Form.Item\r\n            name=\"ModelId\"\r\n            label={<FormattedMessage id=\"pages.aiModels.form.modelId\" />}\r\n            style={{ flex: 1 }}\r\n          >\r\n            <Input disabled placeholder={intl.formatMessage({ id: 'pages.aiModels.form.modelId.readonly' })} />\r\n          </Form.Item>\r\n\r\n          <Form.Item\r\n            name=\"ModelType\"\r\n            label={<FormattedMessage id=\"pages.aiModels.form.modelType\" />}\r\n            style={{ flex: 1 }}\r\n          >\r\n            <Select disabled placeholder={intl.formatMessage({ id: 'pages.aiModels.form.modelType.readonly' })}>\r\n              <Select.Option value=\"0\">Chat</Select.Option>\r\n              <Select.Option value=\"1\">Embedding</Select.Option>\r\n              <Select.Option value=\"2\">Image Generation</Select.Option>\r\n              <Select.Option value=\"3\">Text to Speech</Select.Option>\r\n            </Select>\r\n          </Form.Item>\r\n        </div>\r\n\r\n        <div style={{ display: 'flex', gap: '16px' }}>\r\n          <Form.Item\r\n            name=\"Endpoint\"\r\n            label={<FormattedMessage id=\"pages.aiModels.form.endpoint\" />}\r\n            style={{ flex: 1 }}\r\n          >\r\n            <Input disabled placeholder=\"从模板自动获取\" />\r\n          </Form.Item>\r\n\r\n          <Form.Item\r\n            name=\"SupportsStreaming\"\r\n            label={<FormattedMessage id=\"pages.aiModels.form.supportsStreaming\" />}\r\n            style={{ flex: 1 }}\r\n          >\r\n            <Select disabled placeholder=\"从模板自动获取\">\r\n              <Select.Option value={true}>支持</Select.Option>\r\n              <Select.Option value={false}>不支持</Select.Option>\r\n            </Select>\r\n          </Form.Item>\r\n        </div>\r\n\r\n        {/* 可配置参数 */}\r\n        <div style={{ display: 'flex', gap: '16px' }}>\r\n          <Form.Item\r\n            name=\"MaxTokens\"\r\n            label={<FormattedMessage id=\"pages.aiModels.form.maxTokens\" />}\r\n            style={{ flex: 1 }}\r\n          >\r\n            <InputNumber\r\n              min={1}\r\n              max={100000}\r\n              style={{ width: '100%' }}\r\n              placeholder={intl.formatMessage({ id: 'pages.aiModels.form.maxTokens.placeholder' })}\r\n            />\r\n          </Form.Item>\r\n\r\n          <Form.Item\r\n            name=\"Temperature\"\r\n            label={<FormattedMessage id=\"pages.aiModels.form.temperature\" />}\r\n            style={{ flex: 1 }}\r\n          >\r\n            <InputNumber\r\n              min={0}\r\n              max={2}\r\n              step={0.1}\r\n              style={{ width: '100%' }}\r\n              placeholder={intl.formatMessage({ id: 'pages.aiModels.form.temperature.placeholder' })}\r\n            />\r\n          </Form.Item>\r\n        </div>\r\n\r\n        <div style={{ display: 'flex', gap: '16px', alignItems: 'flex-end' }}>\r\n          <Form.Item\r\n            name=\"IsEnabled\"\r\n            label={<FormattedMessage id=\"pages.aiModels.form.isEnabled\" />}\r\n            valuePropName=\"checked\"\r\n            style={{ flex: 1 }}\r\n          >\r\n            <Switch />\r\n          </Form.Item>\r\n\r\n          <Form.Item\r\n            name=\"IsDefault\"\r\n            label={<FormattedMessage id=\"pages.aiModels.form.isDefault\" />}\r\n            valuePropName=\"checked\"\r\n            tooltip={<FormattedMessage id=\"pages.aiModels.form.isDefault.tooltip\" />}\r\n            style={{ flex: 1 }}\r\n          >\r\n            <Switch />\r\n          </Form.Item>\r\n        </div>\r\n\r\n      </Form>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport default ModelForm;\r\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,iCACA;IACE,SAAS;;;;;;wCCybb;;;2BAAA;;;;;;oFA5b2C;yCACqB;wCACtB;0CAMnC;;;;;;;;;;YASP,MAAM,YAAsC,CAAC,EAC3C,OAAO,EACP,eAAe,EACf,aAAa,EACb,SAAS,EACV;;gBACC,MAAM,OAAO,IAAA,YAAO;gBACpB,MAAM,CAAC,KAAK,GAAG,UAAI,CAAC,OAAO;gBAC3B,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;gBACvC,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAqB,EAAE;gBACjE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,eAAQ,EAA0B,EAAE;gBAChF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,eAAQ,EAAC;gBAEzD,MAAM,SAAS,CAAC,EAAC,0BAAA,oCAAA,cAAe,EAAE;gBAElC,IAAA,gBAAS,EAAC;oBACR,IAAI,SACF;gBAEJ,GAAG;oBAAC;oBAAS;iBAAc;gBAE3B,MAAM,iBAAiB;oBACrB,WAAW;oBACX,MAAM;oBAEN,IAAI,eAAe;4BA2BH,kCAAA,yBAGD,0BACQ,0BAMF,mCAAA;wBApCnB,gBAAgB;wBAChB,MAAM,WAAW;4BACf,OAAO;4BACP,MAAM,cAAc,IAAI;4BAExB,OAAO;4BACP,SAAS,cAAc,OAAO;4BAE9B,SAAS;4BACT,UAAU,cAAc,QAAQ;4BAEhC,QAAQ;4BACR,cAAc,cAAc,YAAY;4BAExC,aAAa,cAAc,WAAW;4BACtC,aAAa,cAAc,WAAW;4BACtC,QAAQ,cAAc,MAAM;4BAC5B,WAAW,cAAc,SAAS;4BAClC,WAAW,cAAc,SAAS;4BAElC,OAAO;4BACP,WAAW,cAAc,SAAS;4BAClC,aAAa,cAAc,WAAW;4BAEtC,OAAO;4BACP,YAAY,cAAc,eAAe;4BACzC,UAAU,GAAE,0BAAA,cAAc,QAAQ,cAAtB,+CAAA,mCAAA,wBAAwB,QAAQ,cAAhC,uDAAA,iCAAkC,EAAE;4BAEhD,cAAc;4BACd,SAAS,GAAE,2BAAA,cAAc,QAAQ,cAAtB,+CAAA,yBAAwB,IAAI;4BACvC,iBAAiB,GAAE,2BAAA,cAAc,QAAQ,cAAtB,+CAAA,yBAAwB,iBAAiB;wBAC9D;wBAEA,KAAK,cAAc,CAAC;wBAEpB,qBAAqB;wBACrB,MAAM,cAAa,2BAAA,cAAc,QAAQ,cAAtB,gDAAA,oCAAA,yBAAwB,QAAQ,cAAhC,wDAAA,kCAAkC,EAAE;wBACvD,IAAI,YACF,MAAM,mBAAmB;oBAE7B,OAAO;wBACL,KAAK,WAAW;wBAChB,kBAAkB,EAAE;oBACtB;gBACF;gBAEA,WAAW;gBACX,MAAM,yBAAyB,CAAC;oBAC9B,qBAAqB;oBACrB,IAAI,cAAc,UAAU,KAAK,WAAW;wBAC1C,MAAM,aAAa,cAAc,UAAU;wBAE3C,mBAAmB;wBAEnB,WAAW;wBACX,KAAK,aAAa,CAAC,cAAc;wBAEjC,mBAAmB;wBACnB,KAAK,cAAc,CAAC;4BAClB,SAAS;4BACT,WAAW;4BACX,UAAU;4BACV,mBAAmB;4BACnB,MAAM;4BACN,aAAa;4BACb,aAAa;4BACb,cAAc;wBAChB;oBACF;oBAEA,oBAAoB;oBACpB,IAAI,cAAc,UAAU,KAAK,WAAW;wBAC1C,MAAM,mBAAmB,eAAe,IAAI,CAAC,CAAA,WAC3C,SAAS,EAAE,KAAK,cAAc,UAAU;wBAE1C,IAAI,kBAAkB;gCAUJ;4BAThB,cAAc;4BACd,MAAM,UAAe;gCACnB,WAAW,iBAAiB,IAAI;gCAChC,MAAM,iBAAiB,IAAI;gCAC3B,SAAS,iBAAiB,OAAO;gCACjC,UAAU,iBAAiB,QAAQ;gCACnC,aAAa,iBAAiB,WAAW;gCACzC,aAAa,iBAAiB,WAAW;gCACzC,mBAAmB,iBAAiB,iBAAiB;gCACrD,YAAY,GAAE,6BAAA,iBAAiB,QAAQ,cAAzB,iDAAA,2BAA2B,IAAI;4BAC/C;4BAEA,YAAY;4BACZ,IAAI,iBAAiB,eAAe,KAAK,WACvC,QAAQ,SAAS,GAAG,iBAAiB,eAAe;4BAGtD,KAAK,cAAc,CAAC;wBACtB;oBACF;gBACF;gBAEA,MAAM,gBAAgB;oBACpB,IAAI;wBACF,MAAM,WAAW,MAAM,IAAA,4BAAqB;wBAC5C,IAAI,SAAS,OAAO,EAClB,aAAa,SAAS,IAAI,IAAI,EAAE;6BAEhC,aAAa,EAAE;oBAEnB,EAAE,OAAO,OAAO;wBACd,aAAa,EAAE;oBACjB;gBACA,qBAAqB;gBACvB;gBAEA,cAAc;gBACd,MAAM,qBAAqB,OAAO;oBAChC,IAAI,CAAC,YAAY;wBACf,kBAAkB,EAAE;wBACpB;oBACF;oBAEA,oBAAoB;oBACpB,IAAI;wBACF,cAAc;wBACd,MAAM,WAAW,MAAM,IAAA,2CAAoC,EAAC;4BAC1D;4BACA,WAAW;wBACb;wBAEA,IAAI,SAAS,OAAO,EAClB,kBAAkB,SAAS,IAAI,IAAI,EAAE;6BAErC,kBAAkB,EAAE;oBAExB,EAAE,OAAO,OAAO;wBACd,kBAAkB,EAAE;oBACtB,SAAU;wBACR,oBAAoB;oBACtB;gBACF;gBAEA,MAAM,eAAe;oBACnB,IAAI;wBACF,MAAM,SAAS,MAAM,KAAK,cAAc;wBACxC,WAAW;wBAEX,sBAAsB;wBACtB,MAAM,aAAkB;4BACtB,iBAAiB,OAAO,UAAU;4BAClC,MAAM,OAAO,IAAI;4BACjB,SAAS,OAAO,OAAO;4BACvB,UAAU,OAAO,QAAQ;4BACzB,cAAc,OAAO,YAAY;4BACjC,aAAa,OAAO,WAAW;4BAC/B,aAAa,OAAO,WAAW;4BAC/B,QAAQ,OAAO,MAAM;4BACrB,WAAW,OAAO,SAAS;4BAC3B,WAAW,OAAO,SAAS;4BAC3B,aAAa;4BACb,WAAW,OAAO,SAAS;4BAC3B,aAAa,OAAO,WAAW;4BAC/B,kBAAkB,OAAO,gBAAgB;4BACzC,iBAAiB,OAAO,eAAe;4BACvC,cAAc,OAAO,YAAY;wBACnC;wBAEA,IAAI;wBACJ,IAAI,QACF,WAAW,MAAM,IAAA,oBAAa,EAAC,cAAe,EAAE,EAAE;4BAAE,GAAG,aAAa;4BAAE,GAAG,UAAU;wBAAC;6BAEpF,WAAW,MAAM,IAAA,oBAAa,EAAC;wBAGjC,IAAI,SAAS,OAAO,EAClB;oBAEF,wBAAwB;oBAC1B,EAAE,OAAO,OAAO;wBACd,UAAU;wBACV,QAAQ,KAAK,CAAC,0BAA0B;oBAC1C,SAAU;wBACR,WAAW;oBACb;gBACF;gBAEA,qBACE,2BAAC,WAAK;oBACJ,OAAO,KAAK,aAAa,CAAC;wBACxB,IAAI,SAAS,mCAAmC;oBAClD;oBACA,MAAM;oBACN,UAAU,IAAM,gBAAgB;oBAChC,MAAM;oBACN,gBAAgB;oBAChB,OAAO;oBACP,OAAO;wBAAE,KAAK;oBAAG;oBACjB,QAAQ;wBACN,MAAM;4BAAE,WAAW;4BAAQ,WAAW;4BAAQ,SAAS;wBAAO;oBAChE;8BAEA,cAAA,2BAAC,UAAI;wBACH,MAAM;wBACN,QAAO;wBACP,gBAAgB;wBAChB,eAAe;4BACb,WAAW;4BACX,aAAa;wBACf;;0CAGA,2BAAC;gCAAI,OAAO;oCAAE,SAAS;oCAAQ,KAAK;gCAAO;;kDACzC,2BAAC,UAAI,CAAC,IAAI;wCACR,MAAK;wCACL,qBAAO,2BAAC,qBAAgB;4CAAC,IAAG;;;;;;wCAC5B,OAAO;4CAAC;gDAAE,UAAU;gDAAM,SAAS,KAAK,aAAa,CAAC;oDAAE,IAAI;gDAAwC;4CAAG;yCAAE;wCACzG,uBAAS,2BAAC,qBAAgB;4CAAC,IAAG;;;;;;wCAC9B,OAAO;4CAAE,MAAM;wCAAE;kDAEjB,cAAA,2BAAC,YAAM;4CAAC,aAAa,KAAK,aAAa,CAAC;gDAAE,IAAI;4CAA2C;sDACtF,UAAU,GAAG,CAAC,CAAC,yBACd,2BAAC,YAAM,CAAC,MAAM;oDAAmB,OAAO,SAAS,EAAE;8DAChD,SAAS,WAAW,IAAI,SAAS,IAAI;mDADpB,SAAS,EAAE;;;;;;;;;;;;;;;kDAOrC,2BAAC,UAAI,CAAC,IAAI;wCACR,MAAK;wCACL,qBAAO,2BAAC,qBAAgB;4CAAC,IAAG;;;;;;wCAC5B,OAAO;4CAAC;gDAAE,UAAU;gDAAM,SAAS,KAAK,aAAa,CAAC;oDAAE,IAAI;gDAAwC;4CAAG;yCAAE;wCACzG,uBAAS,2BAAC,qBAAgB;4CAAC,IAAG;;;;;;wCAC9B,OAAO;4CAAE,MAAM;wCAAE;kDAEjB,cAAA,2BAAC,YAAM;4CACL,aAAa,KAAK,aAAa,CAAC;gDAAE,IAAI;4CAA2C;4CACjF,SAAS;4CACT,UAAU;4CACV,UAAU;4CACV,kBAAiB;4CACjB,cAAc,CAAC,OAAO,SACpB,OAAO,CAAA,mBAAA,6BAAA,OAAQ,QAAQ,KAAI,IAAI,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW;sDAGxE,eAAe,GAAG,CAAC,CAAC,yBACnB,2BAAC,YAAM,CAAC,MAAM;oDAAmB,OAAO,SAAS,EAAE;;wDAChD,SAAS,WAAW,IAAI,SAAS,IAAI;wDAAC;wDAAG,SAAS,OAAO;wDAAC;;mDADzC,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;0CASvC,2BAAC;gCAAI,OAAO;oCAAE,SAAS;oCAAQ,KAAK;gCAAO;;kDACzC,2BAAC,UAAI,CAAC,IAAI;wCACR,MAAK;wCACL,qBAAO,2BAAC,qBAAgB;4CAAC,IAAG;;;;;;wCAC5B,OAAO;4CAAC;gDAAE,UAAU;gDAAM,SAAS,KAAK,aAAa,CAAC;oDAAE,IAAI;gDAAoC;4CAAG;yCAAE;wCACrG,OAAO;4CAAE,MAAM;wCAAE;kDAEjB,cAAA,2BAAC,WAAK;4CAAC,aAAa,KAAK,aAAa,CAAC;gDAAE,IAAI;4CAAuC;;;;;;;;;;;kDAGtF,2BAAC,UAAI,CAAC,IAAI;wCACR,MAAK;wCACL,qBAAO,2BAAC,qBAAgB;4CAAC,IAAG;;;;;;wCAC5B,OAAO;4CAAE,MAAM;wCAAE;kDAEjB,cAAA,2BAAC,WAAK;4CAAC,aAAa,KAAK,aAAa,CAAC;gDAAE,IAAI;4CAA8C;;;;;;;;;;;;;;;;;0CAI/F,2BAAC,UAAI,CAAC,IAAI;gCACR,MAAK;gCACL,qBAAO,2BAAC,qBAAgB;oCAAC,IAAG;;;;;;0CAE5B,cAAA,2BAAC,WAAK,CAAC,QAAQ;oCACb,MAAM;oCACN,aAAa,KAAK,aAAa,CAAC;wCAAE,IAAI;oCAA8C;;;;;;;;;;;0CAIxF,2BAAC,UAAI,CAAC,IAAI;gCACR,MAAK;gCACL,qBAAO,2BAAC,qBAAgB;oCAAC,IAAG;;;;;;gCAC5B,uBAAS,2BAAC,qBAAgB;oCAAC,IAAG;;;;;;0CAE9B,cAAA,2BAAC,WAAK,CAAC,QAAQ;oCACb,MAAM;oCACN,aAAa,KAAK,aAAa,CAAC;wCAAE,IAAI;oCAA+C;;;;;;;;;;;0CAIzF,2BAAC,UAAI,CAAC,IAAI;gCACR,MAAK;gCACL,qBAAO,2BAAC,qBAAgB;oCAAC,IAAG;;;;;;gCAC5B,uBAAS,2BAAC,qBAAgB;oCAAC,IAAG;;;;;;0CAE9B,cAAA,2BAAC,WAAK,CAAC,QAAQ;oCAAC,aAAa,KAAK,aAAa,CAAC;wCAAE,IAAI;oCAAyC;;;;;;;;;;;0CAIjG,2BAAC;gCAAI,OAAO;oCAAE,SAAS;oCAAQ,KAAK;gCAAO;;kDACzC,2BAAC,UAAI,CAAC,IAAI;wCACR,MAAK;wCACL,qBAAO,2BAAC,qBAAgB;4CAAC,IAAG;;;;;;wCAC5B,OAAO;4CAAE,MAAM;wCAAE;kDAEjB,cAAA,2BAAC,WAAK;4CAAC,QAAQ;4CAAC,aAAa,KAAK,aAAa,CAAC;gDAAE,IAAI;4CAAuC;;;;;;;;;;;kDAG/F,2BAAC,UAAI,CAAC,IAAI;wCACR,MAAK;wCACL,qBAAO,2BAAC,qBAAgB;4CAAC,IAAG;;;;;;wCAC5B,OAAO;4CAAE,MAAM;wCAAE;kDAEjB,cAAA,2BAAC,YAAM;4CAAC,QAAQ;4CAAC,aAAa,KAAK,aAAa,CAAC;gDAAE,IAAI;4CAAyC;;8DAC9F,2BAAC,YAAM,CAAC,MAAM;oDAAC,OAAM;8DAAI;;;;;;8DACzB,2BAAC,YAAM,CAAC,MAAM;oDAAC,OAAM;8DAAI;;;;;;8DACzB,2BAAC,YAAM,CAAC,MAAM;oDAAC,OAAM;8DAAI;;;;;;8DACzB,2BAAC,YAAM,CAAC,MAAM;oDAAC,OAAM;8DAAI;;;;;;;;;;;;;;;;;;;;;;;0CAK/B,2BAAC;gCAAI,OAAO;oCAAE,SAAS;oCAAQ,KAAK;gCAAO;;kDACzC,2BAAC,UAAI,CAAC,IAAI;wCACR,MAAK;wCACL,qBAAO,2BAAC,qBAAgB;4CAAC,IAAG;;;;;;wCAC5B,OAAO;4CAAE,MAAM;wCAAE;kDAEjB,cAAA,2BAAC,WAAK;4CAAC,QAAQ;4CAAC,aAAY;;;;;;;;;;;kDAG9B,2BAAC,UAAI,CAAC,IAAI;wCACR,MAAK;wCACL,qBAAO,2BAAC,qBAAgB;4CAAC,IAAG;;;;;;wCAC5B,OAAO;4CAAE,MAAM;wCAAE;kDAEjB,cAAA,2BAAC,YAAM;4CAAC,QAAQ;4CAAC,aAAY;;8DAC3B,2BAAC,YAAM,CAAC,MAAM;oDAAC,OAAO;8DAAM;;;;;;8DAC5B,2BAAC,YAAM,CAAC,MAAM;oDAAC,OAAO;8DAAO;;;;;;;;;;;;;;;;;;;;;;;0CAMnC,2BAAC;gCAAI,OAAO;oCAAE,SAAS;oCAAQ,KAAK;gCAAO;;kDACzC,2BAAC,UAAI,CAAC,IAAI;wCACR,MAAK;wCACL,qBAAO,2BAAC,qBAAgB;4CAAC,IAAG;;;;;;wCAC5B,OAAO;4CAAE,MAAM;wCAAE;kDAEjB,cAAA,2BAAC,iBAAW;4CACV,KAAK;4CACL,KAAK;4CACL,OAAO;gDAAE,OAAO;4CAAO;4CACvB,aAAa,KAAK,aAAa,CAAC;gDAAE,IAAI;4CAA4C;;;;;;;;;;;kDAItF,2BAAC,UAAI,CAAC,IAAI;wCACR,MAAK;wCACL,qBAAO,2BAAC,qBAAgB;4CAAC,IAAG;;;;;;wCAC5B,OAAO;4CAAE,MAAM;wCAAE;kDAEjB,cAAA,2BAAC,iBAAW;4CACV,KAAK;4CACL,KAAK;4CACL,MAAM;4CACN,OAAO;gDAAE,OAAO;4CAAO;4CACvB,aAAa,KAAK,aAAa,CAAC;gDAAE,IAAI;4CAA8C;;;;;;;;;;;;;;;;;0CAK1F,2BAAC;gCAAI,OAAO;oCAAE,SAAS;oCAAQ,KAAK;oCAAQ,YAAY;gCAAW;;kDACjE,2BAAC,UAAI,CAAC,IAAI;wCACR,MAAK;wCACL,qBAAO,2BAAC,qBAAgB;4CAAC,IAAG;;;;;;wCAC5B,eAAc;wCACd,OAAO;4CAAE,MAAM;wCAAE;kDAEjB,cAAA,2BAAC,YAAM;;;;;;;;;;kDAGT,2BAAC,UAAI,CAAC,IAAI;wCACR,MAAK;wCACL,qBAAO,2BAAC,qBAAgB;4CAAC,IAAG;;;;;;wCAC5B,eAAc;wCACd,uBAAS,2BAAC,qBAAgB;4CAAC,IAAG;;;;;;wCAC9B,OAAO;4CAAE,MAAM;wCAAE;kDAEjB,cAAA,2BAAC,YAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOnB;eAzaM;;oBAMS,YAAO;oBACL,UAAI,CAAC;;;iBAPhB;gBA2aN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;IDzbD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,uCAAsC;YAAC;YAAU;SAAsC;QAAC,qBAAoB;YAAC;SAAS;QAAC,uBAAsB;YAAC;SAAW;QAAC,yBAAwB;YAAC;SAAa;QAAC,iCAAgC;YAAC;YAAS;SAAgC;QAAC,gCAA+B;YAAC;YAAS;SAA+B;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,iCAAgC;YAAC;YAAS;SAAgC;QAAC,qCAAoC;YAAC;YAAU;YAAS;SAAoC;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,mCAAkC;YAAC;YAAS;SAAuB;QAAC,sCAAqC;YAAC;YAAS;SAA0B;QAAC,iCAAgC;YAAC;YAAS;SAAsB;QAAC,+BAA8B;YAAC;YAAS;SAAoB;QAAC,qCAAoC;YAAC;SAAoC;QAAC,kCAAiC;YAAC;SAAiC;QAAC,kCAAiC;YAAC;YAAS;SAAwB;QAAC,qCAAoC;YAAC;YAAS;SAA2B;IAAA;;AAC9zC"}