import React, { useState } from 'react';
import { Modal } from 'antd';
import { useIntl, FormattedMessage } from '@umijs/max';
import ChatInterface from './components/ChatInterface';
import SessionForm from './components/SessionForm';

const ChatInterfacePage: React.FC = () => {
  const intl = useIntl();
  const [sessionFormVisible, setSessionFormVisible] = useState(false);
  const [editingSession, setEditingSession] = useState<RwxAI.ChatSession | null>(null);

  const handleCreateSession = () => {
    setEditingSession(null);
    setSessionFormVisible(true);
  };

  const handleEditSession = (session: RwxAI.ChatSession) => {
    setEditingSession(session);
    setSessionFormVisible(true);
  };

  const handleSessionFormClose = () => {
    setSessionFormVisible(false);
    setEditingSession(null);
  };

  const handleSessionFormSuccess = () => {
    setSessionFormVisible(false);
    setEditingSession(null);
    // 这里可以触发聊天界面的刷新
    window.location.reload();
  };

  return (
    <div style={{ height: '100vh', overflow: 'hidden' }}>
      <ChatInterface
        onCreateSession={handleCreateSession}
        onEditSession={handleEditSession}
      />
      
      <Modal
        title={editingSession ? '编辑会话' : '创建新会话'}
        open={sessionFormVisible}
        onCancel={handleSessionFormClose}
        footer={null}
        width={600}
      >
        <SessionForm
          visible={sessionFormVisible}
          onVisibleChange={setSessionFormVisible}
          onSuccess={handleSessionFormSuccess}
          initialValues={editingSession}
        />
      </Modal>
    </div>
  );
};

export default ChatInterfacePage;
