import React, { useState, useRef } from 'react';
import { Modal, Button, Space } from 'antd';
import { useIntl, FormattedMessage } from '@umijs/max';
import ChatInterface from './components/ChatInterface';
import SessionForm from './components/SessionForm';

const ChatInterfacePage: React.FC = () => {
  const intl = useIntl();
  const [sessionFormVisible, setSessionFormVisible] = useState(false);
  const [editingSession, setEditingSession] = useState<RwxAI.ChatSession | null>(null);
  const [formLoading, setFormLoading] = useState(false);
  const formRef = useRef<any>(null);

  const handleCreateSession = () => {
    setEditingSession(null);
    setSessionFormVisible(true);
  };

  const handleEditSession = (session: RwxAI.ChatSession) => {
    setEditingSession(session);
    setSessionFormVisible(true);
  };

  const handleSessionFormClose = () => {
    setSessionFormVisible(false);
    setEditingSession(null);
    setFormLoading(false);
  };

  const handleSessionFormSuccess = () => {
    setSessionFormVisible(false);
    setEditingSession(null);
    setFormLoading(false);
    // 这里可以触发聊天界面的刷新
    window.location.reload();
  };

  const handleFormSubmit = () => {
    if (formRef.current) {
      setFormLoading(true);
      formRef.current.submit();
    }
  };

  return (
    <div style={{ height: '100vh', overflow: 'hidden' }}>
      <ChatInterface
        onCreateSession={handleCreateSession}
        onEditSession={handleEditSession}
      />
      
      <Modal
        title={editingSession ? '编辑会话' : '创建新会话'}
        open={sessionFormVisible}
        onCancel={handleSessionFormClose}
        onOk={handleFormSubmit}
        confirmLoading={formLoading}
        width={600}
        footer={[
          <Button key="cancel" onClick={handleSessionFormClose}>
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={formLoading}
            onClick={handleFormSubmit}
          >
            {editingSession ? '更新' : '创建'}
          </Button>,
        ]}
      >
        <SessionForm
          ref={formRef}
          visible={sessionFormVisible}
          onVisibleChange={setSessionFormVisible}
          onSuccess={handleSessionFormSuccess}
          initialValues={editingSession}
        />
      </Modal>
    </div>
  );
};

export default ChatInterfacePage;
