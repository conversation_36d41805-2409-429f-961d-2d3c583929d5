{"version": 3, "sources": ["src/pages/api-test/index.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { PageContainer } from '@ant-design/pro-components';\nimport { Card, Button, Space, Typography, Alert, Tabs, List, Tag } from 'antd';\nimport { \n  getAIModels, \n  getApps, \n  getChatSessions, \n  getKnowledgeBases, \n  getPlugins \n} from '@/services/rwxai';\n\nconst { Title, Text, Paragraph } = Typography;\n\ninterface TestResult {\n  api: string;\n  status: 'success' | 'error' | 'loading';\n  message: string;\n  data?: any;\n  timestamp: string;\n}\n\nconst ApiTestPage: React.FC = () => {\n  const [testResults, setTestResults] = useState<TestResult[]>([]);\n  const [loading, setLoading] = useState<Record<string, boolean>>({});\n\n  const addTestResult = (result: TestResult) => {\n    setTestResults(prev => [result, ...prev.slice(0, 19)]); // 保留最近20条记录\n  };\n\n  const testApi = async (apiName: string, apiFunction: () => Promise<any>) => {\n    setLoading(prev => ({ ...prev, [apiName]: true }));\n    \n    const startTime = new Date().toLocaleTimeString();\n    \n    try {\n      const response = await apiFunction();\n      \n      if (response.success) {\n        addTestResult({\n          api: apiName,\n          status: 'success',\n          message: `✅ 成功 - ${response.data?.length || 0} 条记录`,\n          data: response.data,\n          timestamp: startTime,\n        });\n      } else {\n        addTestResult({\n          api: apiName,\n          status: 'error',\n          message: `❌ 失败 - ${response.message}`,\n          timestamp: startTime,\n        });\n      }\n    } catch (error: any) {\n      addTestResult({\n        api: apiName,\n        status: 'error',\n        message: `❌ 异常 - ${error.message}`,\n        timestamp: startTime,\n      });\n    } finally {\n      setLoading(prev => ({ ...prev, [apiName]: false }));\n    }\n  };\n\n  const testAllApis = async () => {\n    const apis = [\n      { name: 'AI模型列表', func: getAIModels },\n      { name: '应用列表', func: getApps },\n      { name: '聊天会话列表', func: getChatSessions },\n      { name: '知识库列表', func: getKnowledgeBases },\n      { name: '插件列表', func: getPlugins },\n    ];\n\n    for (const api of apis) {\n      await testApi(api.name, api.func);\n      // 添加小延迟避免请求过于频繁\n      await new Promise(resolve => setTimeout(resolve, 500));\n    }\n  };\n\n  const clearResults = () => {\n    setTestResults([]);\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'success': return 'green';\n      case 'error': return 'red';\n      case 'loading': return 'blue';\n      default: return 'default';\n    }\n  };\n\n  const apiTests = [\n    {\n      category: 'AI模型管理',\n      tests: [\n        { name: 'AI模型列表', func: getAIModels, desc: '获取所有AI模型' },\n      ]\n    },\n    {\n      category: '应用管理',\n      tests: [\n        { name: '应用列表', func: getApps, desc: '获取所有应用' },\n      ]\n    },\n    {\n      category: '聊天管理',\n      tests: [\n        { name: '聊天会话列表', func: getChatSessions, desc: '获取所有聊天会话' },\n      ]\n    },\n    {\n      category: '知识库管理',\n      tests: [\n        { name: '知识库列表', func: getKnowledgeBases, desc: '获取所有知识库' },\n      ]\n    },\n    {\n      category: '插件管理',\n      tests: [\n        { name: '插件列表', func: getPlugins, desc: '获取所有插件' },\n      ]\n    },\n  ];\n\n  const tabItems = [\n    {\n      key: 'test',\n      label: 'API测试',\n      children: (\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          <Card title=\"批量测试\" size=\"small\">\n            <Space>\n              <Button type=\"primary\" onClick={testAllApis} loading={Object.values(loading).some(Boolean)}>\n                测试所有API\n              </Button>\n              <Button onClick={clearResults}>\n                清空结果\n              </Button>\n            </Space>\n          </Card>\n\n          {apiTests.map((category) => (\n            <Card key={category.category} title={category.category} size=\"small\">\n              <Space wrap>\n                {category.tests.map((test) => (\n                  <Button\n                    key={test.name}\n                    onClick={() => testApi(test.name, test.func)}\n                    loading={loading[test.name]}\n                    title={test.desc}\n                  >\n                    {test.name}\n                  </Button>\n                ))}\n              </Space>\n            </Card>\n          ))}\n        </Space>\n      ),\n    },\n    {\n      key: 'results',\n      label: `测试结果 (${testResults.length})`,\n      children: (\n        <Card title=\"测试结果\" size=\"small\">\n          {testResults.length === 0 ? (\n            <Text type=\"secondary\">暂无测试结果</Text>\n          ) : (\n            <List\n              dataSource={testResults}\n              renderItem={(item) => (\n                <List.Item>\n                  <List.Item.Meta\n                    title={\n                      <Space>\n                        <Text strong>{item.api}</Text>\n                        <Tag color={getStatusColor(item.status)}>\n                          {item.status.toUpperCase()}\n                        </Tag>\n                        <Text type=\"secondary\">{item.timestamp}</Text>\n                      </Space>\n                    }\n                    description={item.message}\n                  />\n                </List.Item>\n              )}\n            />\n          )}\n        </Card>\n      ),\n    },\n    {\n      key: 'info',\n      label: '系统信息',\n      children: (\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          <Card title=\"迁移状态\" size=\"small\">\n            <Paragraph>\n              <Title level={5}>✅ 已完成迁移的API服务：</Title>\n              <ul>\n                <li>✅ AI模型管理 (aiModels.ts) - 支持统一响应处理</li>\n                <li>✅ 应用管理 (apps.ts) - 支持统一响应处理</li>\n                <li>✅ 用户认证 (auth.ts) - 支持统一响应处理</li>\n                <li>✅ 聊天管理 (chat.ts) - 支持统一响应处理</li>\n                <li>✅ 知识库管理 (knowledge.ts) - 支持统一响应处理</li>\n                <li>✅ 插件管理 (plugins.ts) - 支持统一响应处理</li>\n              </ul>\n            </Paragraph>\n          </Card>\n\n          <Card title=\"响应处理特性\" size=\"small\">\n            <Paragraph>\n              <Title level={5}>🎯 统一响应处理系统特性：</Title>\n              <ul>\n                <li>✅ 自动处理所有HTTP状态码 (200, 400, 401, 403, 404, 422, 500等)</li>\n                <li>✅ 用户友好的错误提示</li>\n                <li>✅ 自动JWT认证和令牌刷新</li>\n                <li>✅ 可配置的成功/失败消息</li>\n                <li>✅ 统一的响应格式</li>\n                <li>✅ 智能的通知显示策略</li>\n                <li>✅ 详细的验证错误信息</li>\n                <li>✅ 网络错误自动处理</li>\n              </ul>\n            </Paragraph>\n          </Card>\n\n          <Card title=\"测试说明\" size=\"small\">\n            <Paragraph>\n              <Title level={5}>📝 如何使用：</Title>\n              <ol>\n                <li>确保已登录并有有效的JWT令牌</li>\n                <li>点击\"测试所有API\"进行批量测试</li>\n                <li>或单独测试各个API接口</li>\n                <li>查看\"测试结果\"标签页了解详细结果</li>\n                <li>观察错误处理和成功提示的效果</li>\n              </ol>\n            </Paragraph>\n          </Card>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <PageContainer title=\"API统一响应处理测试\">\n      <Alert\n        message=\"API迁移完成\"\n        description=\"所有API服务已成功迁移到统一响应处理系统，支持完整的HTTP状态码处理和用户友好的错误提示。\"\n        type=\"success\"\n        showIcon\n        style={{ marginBottom: 24 }}\n      />\n      \n      <Tabs items={tabItems} />\n    </PageContainer>\n  );\n};\n\nexport default ApiTestPage;\n"], "names": [], "mappings": ";;;;;;;4BAqQA;;;eAAA;;;;;;wEArQgC;sCACF;6BAC0C;8BAOjE;;;;;;;;;;AAEP,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,gBAAU;AAU7C,MAAM,cAAwB;;IAC5B,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,eAAQ,EAAe,EAAE;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAA0B,CAAC;IAEjE,MAAM,gBAAgB,CAAC;QACrB,eAAe,CAAA,OAAQ;gBAAC;mBAAW,KAAK,KAAK,CAAC,GAAG;aAAI,GAAG,YAAY;IACtE;IAEA,MAAM,UAAU,OAAO,SAAiB;QACtC,WAAW,CAAA,OAAS,CAAA;gBAAE,GAAG,IAAI;gBAAE,CAAC,QAAQ,EAAE;YAAK,CAAA;QAE/C,MAAM,YAAY,IAAI,OAAO,kBAAkB;QAE/C,IAAI;YACF,MAAM,WAAW,MAAM;YAEvB,IAAI,SAAS,OAAO,EAAE;oBAIC;gBAHrB,cAAc;oBACZ,KAAK;oBACL,QAAQ;oBACR,SAAS,CAAC,OAAO,EAAE,EAAA,iBAAA,SAAS,IAAI,cAAb,qCAAA,eAAe,MAAM,KAAI,EAAE,IAAI,CAAC;oBACnD,MAAM,SAAS,IAAI;oBACnB,WAAW;gBACb;YACF,OACE,cAAc;gBACZ,KAAK;gBACL,QAAQ;gBACR,SAAS,CAAC,OAAO,EAAE,SAAS,OAAO,CAAC,CAAC;gBACrC,WAAW;YACb;QAEJ,EAAE,OAAO,OAAY;YACnB,cAAc;gBACZ,KAAK;gBACL,QAAQ;gBACR,SAAS,CAAC,OAAO,EAAE,MAAM,OAAO,CAAC,CAAC;gBAClC,WAAW;YACb;QACF,SAAU;YACR,WAAW,CAAA,OAAS,CAAA;oBAAE,GAAG,IAAI;oBAAE,CAAC,QAAQ,EAAE;gBAAM,CAAA;QAClD;IACF;IAEA,MAAM,cAAc;QAClB,MAAM,OAAO;YACX;gBAAE,MAAM;gBAAU,MAAM,kBAAW;YAAC;YACpC;gBAAE,MAAM;gBAAQ,MAAM,cAAO;YAAC;YAC9B;gBAAE,MAAM;gBAAU,MAAM,sBAAe;YAAC;YACxC;gBAAE,MAAM;gBAAS,MAAM,wBAAiB;YAAC;YACzC;gBAAE,MAAM;gBAAQ,MAAM,iBAAU;YAAC;SAClC;QAED,KAAK,MAAM,OAAO,KAAM;YACtB,MAAM,QAAQ,IAAI,IAAI,EAAE,IAAI,IAAI;YAChC,gBAAgB;YAChB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACnD;IACF;IAEA,MAAM,eAAe;QACnB,eAAe,EAAE;IACnB;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,WAAW;QACf;YACE,UAAU;YACV,OAAO;gBACL;oBAAE,MAAM;oBAAU,MAAM,kBAAW;oBAAE,MAAM;gBAAW;aACvD;QACH;QACA;YACE,UAAU;YACV,OAAO;gBACL;oBAAE,MAAM;oBAAQ,MAAM,cAAO;oBAAE,MAAM;gBAAS;aAC/C;QACH;QACA;YACE,UAAU;YACV,OAAO;gBACL;oBAAE,MAAM;oBAAU,MAAM,sBAAe;oBAAE,MAAM;gBAAW;aAC3D;QACH;QACA;YACE,UAAU;YACV,OAAO;gBACL;oBAAE,MAAM;oBAAS,MAAM,wBAAiB;oBAAE,MAAM;gBAAU;aAC3D;QACH;QACA;YACE,UAAU;YACV,OAAO;gBACL;oBAAE,MAAM;oBAAQ,MAAM,iBAAU;oBAAE,MAAM;gBAAS;aAClD;QACH;KACD;IAED,MAAM,WAAW;QACf;YACE,KAAK;YACL,OAAO;YACP,wBACE,2BAAC,WAAK;gBAAC,WAAU;gBAAW,MAAK;gBAAQ,OAAO;oBAAE,OAAO;gBAAO;;kCAC9D,2BAAC,UAAI;wBAAC,OAAM;wBAAO,MAAK;kCACtB,cAAA,2BAAC,WAAK;;8CACJ,2BAAC,YAAM;oCAAC,MAAK;oCAAU,SAAS;oCAAa,SAAS,OAAO,MAAM,CAAC,SAAS,IAAI,CAAC;8CAAU;;;;;;8CAG5F,2BAAC,YAAM;oCAAC,SAAS;8CAAc;;;;;;;;;;;;;;;;;oBAMlC,SAAS,GAAG,CAAC,CAAC,yBACb,2BAAC,UAAI;4BAAyB,OAAO,SAAS,QAAQ;4BAAE,MAAK;sCAC3D,cAAA,2BAAC,WAAK;gCAAC,IAAI;0CACR,SAAS,KAAK,CAAC,GAAG,CAAC,CAAC,qBACnB,2BAAC,YAAM;wCAEL,SAAS,IAAM,QAAQ,KAAK,IAAI,EAAE,KAAK,IAAI;wCAC3C,SAAS,OAAO,CAAC,KAAK,IAAI,CAAC;wCAC3B,OAAO,KAAK,IAAI;kDAEf,KAAK,IAAI;uCALL,KAAK,IAAI;;;;;;;;;;2BAJX,SAAS,QAAQ;;;;;;;;;;;QAiBpC;QACA;YACE,KAAK;YACL,OAAO,CAAC,MAAM,EAAE,YAAY,MAAM,CAAC,CAAC,CAAC;YACrC,wBACE,2BAAC,UAAI;gBAAC,OAAM;gBAAO,MAAK;0BACrB,YAAY,MAAM,KAAK,kBACtB,2BAAC;oBAAK,MAAK;8BAAY;;;;;yCAEvB,2BAAC,UAAI;oBACH,YAAY;oBACZ,YAAY,CAAC,qBACX,2BAAC,UAAI,CAAC,IAAI;sCACR,cAAA,2BAAC,UAAI,CAAC,IAAI,CAAC,IAAI;gCACb,qBACE,2BAAC,WAAK;;sDACJ,2BAAC;4CAAK,MAAM;sDAAE,KAAK,GAAG;;;;;;sDACtB,2BAAC,SAAG;4CAAC,OAAO,eAAe,KAAK,MAAM;sDACnC,KAAK,MAAM,CAAC,WAAW;;;;;;sDAE1B,2BAAC;4CAAK,MAAK;sDAAa,KAAK,SAAS;;;;;;;;;;;;gCAG1C,aAAa,KAAK,OAAO;;;;;;;;;;;;;;;;;;;;;QAQzC;QACA;YACE,KAAK;YACL,OAAO;YACP,wBACE,2BAAC,WAAK;gBAAC,WAAU;gBAAW,MAAK;gBAAQ,OAAO;oBAAE,OAAO;gBAAO;;kCAC9D,2BAAC,UAAI;wBAAC,OAAM;wBAAO,MAAK;kCACtB,cAAA,2BAAC;;8CACC,2BAAC;oCAAM,OAAO;8CAAG;;;;;;8CACjB,2BAAC;;sDACC,2BAAC;sDAAG;;;;;;sDACJ,2BAAC;sDAAG;;;;;;sDACJ,2BAAC;sDAAG;;;;;;sDACJ,2BAAC;sDAAG;;;;;;sDACJ,2BAAC;sDAAG;;;;;;sDACJ,2BAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;kCAKV,2BAAC,UAAI;wBAAC,OAAM;wBAAS,MAAK;kCACxB,cAAA,2BAAC;;8CACC,2BAAC;oCAAM,OAAO;8CAAG;;;;;;8CACjB,2BAAC;;sDACC,2BAAC;sDAAG;;;;;;sDACJ,2BAAC;sDAAG;;;;;;sDACJ,2BAAC;sDAAG;;;;;;sDACJ,2BAAC;sDAAG;;;;;;sDACJ,2BAAC;sDAAG;;;;;;sDACJ,2BAAC;sDAAG;;;;;;sDACJ,2BAAC;sDAAG;;;;;;sDACJ,2BAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;kCAKV,2BAAC,UAAI;wBAAC,OAAM;wBAAO,MAAK;kCACtB,cAAA,2BAAC;;8CACC,2BAAC;oCAAM,OAAO;8CAAG;;;;;;8CACjB,2BAAC;;sDACC,2BAAC;sDAAG;;;;;;sDACJ,2BAAC;sDAAG;;;;;;sDACJ,2BAAC;sDAAG;;;;;;sDACJ,2BAAC;sDAAG;;;;;;sDACJ,2BAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAMhB;KACD;IAED,qBACE,2BAAC,4BAAa;QAAC,OAAM;;0BACnB,2BAAC,WAAK;gBACJ,SAAQ;gBACR,aAAY;gBACZ,MAAK;gBACL,QAAQ;gBACR,OAAO;oBAAE,cAAc;gBAAG;;;;;;0BAG5B,2BAAC,UAAI;gBAAC,OAAO;;;;;;;;;;;;AAGnB;GA9OM;KAAA;IAgPN,WAAe"}