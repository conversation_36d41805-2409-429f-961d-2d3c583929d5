globalThis.makoModuleHotUpdate('src/.umi/umi.ts?hmr', {
    modules: {
        "src/locales/zh-CN/pages.ts": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _default = {
                // Common actions
                'pages.common.close': '关闭',
                'pages.common.edit': '编辑',
                'pages.common.cancel': '取消',
                'pages.common.confirm': '确认',
                'pages.common.save': '保存',
                'pages.common.delete': '删除',
                'pages.common.view': '查看',
                'pages.common.create': '新建',
                'pages.common.search': '搜索',
                'pages.common.reset': '重置',
                'pages.common.export': '导出',
                'pages.layouts.userLayout.title': 'Ant Design 是西湖区最具影响力的 Web 设计规范',
                'pages.login.accountLogin.tab': '账户密码登录',
                'pages.login.accountLogin.errorMessage': '错误的用户名和密码(admin/ant.design)',
                'pages.login.failure': '登录失败，请重试！',
                'pages.login.success': '登录成功！',
                'pages.login.username.placeholder': '用户名: admin or user',
                'pages.login.username.required': '用户名是必填项！',
                'pages.login.password.placeholder': '密码: ant.design',
                'pages.login.password.required': '密码是必填项！',
                'pages.login.phoneLogin.tab': '手机号登录',
                'pages.login.phoneLogin.errorMessage': '验证码错误',
                'pages.login.phoneNumber.placeholder': '请输入手机号！',
                'pages.login.phoneNumber.required': '手机号是必填项！',
                'pages.login.phoneNumber.invalid': '不合法的手机号！',
                'pages.login.captcha.placeholder': '请输入验证码！',
                'pages.login.captcha.required': '验证码是必填项！',
                'pages.login.phoneLogin.getVerificationCode': '获取验证码',
                'pages.getCaptchaSecondText': '秒后重新获取',
                'pages.login.title': '欢迎登录',
                'pages.login.subtitle': '请输入您的账号和密码',
                'pages.login.brand.subtitle': '智能AI助手平台，让AI为您的工作赋能',
                'pages.login.feature.ai': '强大的AI模型集成',
                'pages.login.feature.security': '企业级安全保障',
                'pages.login.feature.easy': '简单易用的界面',
                'pages.login.accountLogin.errorMessage': '用户名或密码错误，请重试',
                'pages.login.username.placeholder': '请输入用户名',
                'pages.login.username.required': '请输入用户名!',
                'pages.login.password.placeholder': '请输入密码',
                'pages.login.password.required': '请输入密码！',
                'pages.login.failure': '登录失败，请重试！',
                'pages.login.success': '登录成功！',
                'pages.login.rememberMe': '记住我',
                'pages.login.forgotPassword': '忘记密码？',
                'pages.login.submit': '登录',
                'pages.login.loginWith': '其他登录方式 :',
                'pages.login.registerAccount': '注册账户',
                'pages.welcome.link': '欢迎使用',
                'pages.welcome.alertMessage': '更快更强的重型组件，已经发布。',
                'pages.404.subTitle': '抱歉，您访问的页面不存在。',
                'pages.404.buttonText': '返回首页',
                'pages.admin.subPage.title': ' 这个页面只有 admin 权限才能查看',
                'pages.admin.subPage.alertMessage': 'umi ui 现已发布，欢迎使用 npm run ui 启动体验。',
                'pages.searchTable.createForm.newRule': '新建规则',
                'pages.searchTable.updateForm.ruleConfig': '规则配置',
                'pages.searchTable.updateForm.basicConfig': '基本信息',
                'pages.searchTable.updateForm.ruleName.nameLabel': '规则名称',
                'pages.searchTable.updateForm.ruleName.nameRules': '请输入规则名称！',
                'pages.searchTable.updateForm.ruleDesc.descLabel': '规则描述',
                'pages.searchTable.updateForm.ruleDesc.descPlaceholder': '请输入至少五个字符',
                'pages.searchTable.updateForm.ruleDesc.descRules': '请输入至少五个字符的规则描述！',
                'pages.searchTable.updateForm.ruleProps.title': '配置规则属性',
                'pages.searchTable.updateForm.object': '监控对象',
                'pages.searchTable.updateForm.ruleProps.templateLabel': '规则模板',
                'pages.searchTable.updateForm.ruleProps.typeLabel': '规则类型',
                'pages.searchTable.updateForm.schedulingPeriod.title': '设定调度周期',
                'pages.searchTable.updateForm.schedulingPeriod.timeLabel': '开始时间',
                'pages.searchTable.updateForm.schedulingPeriod.timeRules': '请选择开始时间！',
                'pages.searchTable.titleDesc': '描述',
                'pages.searchTable.ruleName': '规则名称为必填项',
                'pages.searchTable.titleCallNo': '服务调用次数',
                'pages.searchTable.titleStatus': '状态',
                'pages.searchTable.nameStatus.default': '关闭',
                'pages.searchTable.nameStatus.running': '运行中',
                'pages.searchTable.nameStatus.online': '已上线',
                'pages.searchTable.nameStatus.abnormal': '异常',
                'pages.searchTable.titleUpdatedAt': '上次调度时间',
                'pages.searchTable.exception': '请输入异常原因！',
                'pages.searchTable.titleOption': '操作',
                'pages.searchTable.config': '配置',
                'pages.searchTable.subscribeAlert': '订阅警报',
                'pages.searchTable.title': '查询表格',
                'pages.searchTable.new': '新建',
                'pages.searchTable.chosen': '已选择',
                'pages.searchTable.item': '项',
                'pages.searchTable.totalServiceCalls': '服务调用次数总计',
                'pages.searchTable.tenThousand': '万',
                'pages.searchTable.batchDeletion': '批量删除',
                'pages.searchTable.batchApproval': '批量审批',
                // AI模型管理
                'pages.aiModels.title': 'AI模型管理',
                'pages.aiModels.table.name': '模型名称',
                'pages.aiModels.table.modelId': '模型标识',
                'pages.aiModels.table.modelType': '模型类型',
                'pages.aiModels.table.provider': '服务提供商',
                'pages.aiModels.table.status': '状态',
                'pages.aiModels.table.maxTokens': '最大令牌数',
                'pages.aiModels.table.temperature': '温度',
                'pages.aiModels.table.createdTime': '创建时间',
                'pages.aiModels.table.actions': '操作',
                // 使用公共 actions，移除 pages.aiModels.actions.* 重复定义
                'pages.aiModels.delete.confirm.title': '确认删除',
                'pages.aiModels.delete.confirm.content': '确定要删除这个AI模型吗？',
                'pages.aiModels.delete.success': '删除成功',
                'pages.aiModels.delete.error': '删除失败',
                'pages.aiModels.form.create.title': '新建AI模型',
                'pages.aiModels.form.edit.title': '编辑AI模型',
                'pages.aiModels.form.name': '模型名称',
                'pages.aiModels.form.name.required': '请输入模型名称',
                'pages.aiModels.form.name.placeholder': '请输入模型名称',
                'pages.aiModels.form.description': '模型描述',
                'pages.aiModels.form.description.placeholder': '请输入模型描述',
                'pages.aiModels.form.modelId': '模型标识',
                'pages.aiModels.form.modelId.required': '请输入模型标识',
                'pages.aiModels.form.modelId.placeholder': '请输入模型标识',
                'pages.aiModels.form.modelType': '模型类型',
                'pages.aiModels.form.modelType.required': '请选择模型类型',
                'pages.aiModels.form.modelType.placeholder': '请选择模型类型',
                'pages.aiModels.form.provider': '服务提供商',
                'pages.aiModels.form.provider.required': '请选择服务提供商',
                'pages.aiModels.form.provider.placeholder': '请选择服务提供商',
                'pages.aiModels.form.provider.tooltip': '首先选择AI服务提供商，然后选择对应的模型模板',
                'pages.aiModels.form.template': '模型模板',
                'pages.aiModels.form.template.required': '请选择模型模板',
                'pages.aiModels.form.template.placeholder': '请选择模型模板',
                'pages.aiModels.form.template.tooltip': '选择模型模板将自动填充模型配置信息',
                'pages.aiModels.form.displayName': '显示名称',
                'pages.aiModels.form.displayName.placeholder': '请输入显示名称',
                'pages.aiModels.form.apiKey': 'API密钥',
                'pages.aiModels.form.apiKey.placeholder': '请输入API密钥',
                'pages.aiModels.form.apiKey.tooltip': '用于调用AI服务的API密钥',
                'pages.aiModels.form.isDefault': '设为默认',
                'pages.aiModels.form.isDefault.tooltip': '是否设置为默认模型',
                'pages.aiModels.form.modelId.readonly': '模型标识（从模板自动获取）',
                'pages.aiModels.form.modelType.readonly': '模型类型（从模板自动获取）',
                'pages.aiModels.form.edit.title': '编辑AI模型',
                'pages.aiModels.form.create.title': '新建AI模型',
                'pages.aiModels.detail.title': 'AI模型详情',
                'pages.aiModels.detail.displayName': '显示名称',
                'pages.aiModels.detail.apiKey': 'API密钥',
                'pages.aiModels.detail.isDefault': '默认模型',
                'pages.aiModels.form.maxTokens': '最大令牌数',
                'pages.aiModels.form.maxTokens.placeholder': '请输入最大令牌数',
                'pages.aiModels.form.temperature': '温度',
                'pages.aiModels.form.temperature.placeholder': '请输入温度值',
                'pages.aiModels.form.topP': 'Top P',
                'pages.aiModels.form.topP.placeholder': '请输入Top P值',
                'pages.aiModels.form.frequencyPenalty': '频率惩罚',
                'pages.aiModels.form.frequencyPenalty.placeholder': '请输入频率惩罚值',
                'pages.aiModels.form.presencePenalty': '存在惩罚',
                'pages.aiModels.form.presencePenalty.placeholder': '请输入存在惩罚值',
                'pages.aiModels.form.isEnabled': '启用状态',
                'pages.aiModels.form.systemPrompt': '系统提示词',
                'pages.aiModels.form.systemPrompt.placeholder': '请输入系统提示词',
                'pages.aiModels.form.systemPrompt.tooltip': '系统提示词将影响AI的回答风格和行为',
                'pages.aiModels.form.create.success': '创建成功',
                'pages.aiModels.form.create.error': '创建失败',
                'pages.aiModels.form.update.success': '更新成功',
                'pages.aiModels.form.update.error': '更新失败',
                'pages.aiModels.form.loadProviders.error': '加载服务提供商失败',
                'pages.aiModels.detail.title': 'AI模型详情',
                'pages.aiModels.detail.name': '模型名称',
                'pages.aiModels.detail.description': '模型描述',
                'pages.aiModels.detail.modelKey': '模型标识',
                'pages.aiModels.detail.modelType': '模型类型',
                'pages.aiModels.detail.provider': '服务提供商',
                'pages.aiModels.detail.status': '状态',
                'pages.aiModels.detail.maxTokens': '最大令牌数',
                'pages.aiModels.detail.temperature': '温度',
                'pages.aiModels.detail.topP': 'Top P',
                'pages.aiModels.detail.frequencyPenalty': '频率惩罚',
                'pages.aiModels.detail.presencePenalty': '存在惩罚',
                'pages.aiModels.detail.createdTime': '创建时间',
                'pages.aiModels.detail.updatedTime': '更新时间',
                'pages.aiModels.detail.configJson': '配置信息',
                // AI模型详情页面新增翻译
                'pages.aiModels.detail.templateInfo': '模板信息',
                'pages.aiModels.detail.basicInfo': '基本信息',
                'pages.aiModels.detail.configParams': '配置参数',
                'pages.aiModels.detail.otherInfo': '其他信息',
                'pages.aiModels.detail.endpoint': '服务端点',
                'pages.aiModels.detail.supportsStreaming': '支持流式输出',
                'pages.aiModels.detail.supportsFunctionCalling': '支持函数调用',
                'pages.aiModels.detail.supportsVision': '支持视觉理解',
                'pages.aiModels.detail.maxContextLength': '最大上下文长度',
                'pages.aiModels.detail.maxOutputLength': '最大输出长度',
                'pages.aiModels.detail.notes': '备注',
                // AI模型表单新增翻译
                'pages.aiModels.form.endpoint': '服务端点',
                'pages.aiModels.form.supportsStreaming': '支持流式输出',
                'pages.aiModels.form.supportsFunctionCalling': '支持函数调用',
                'pages.aiModels.form.supportsVision': '支持视觉理解',
                'pages.aiModels.form.maxContextLength': '最大上下文长度',
                'pages.aiModels.form.maxOutputLength': '最大输出长度',
                'pages.aiModels.form.notes': '备注',
                // 应用管理
                'pages.apps.title': '应用管理',
                'pages.apps.table.name': '应用名称',
                'pages.apps.table.description': '应用描述',
                'pages.apps.table.status': '状态',
                'pages.apps.table.apiCalls': 'API调用次数',
                'pages.apps.table.rateLimit': '速率限制',
                'pages.apps.table.createdTime': '创建时间',
                'pages.apps.table.actions': '操作',
                'pages.apps.actions.create': '新建应用',
                'pages.apps.actions.view': '查看',
                'pages.apps.actions.edit': '编辑',
                'pages.apps.actions.delete': '删除',
                'pages.apps.actions.regenerateKeys': '重新生成密钥',
                'pages.apps.actions.resetApiCalls': '重置调用次数',
                'pages.apps.delete.confirm.title': '确认删除',
                'pages.apps.delete.confirm.content': '确定要删除这个应用吗？',
                'pages.apps.delete.success': '删除成功',
                'pages.apps.delete.error': '删除失败',
                'pages.apps.status.update.success': '状态更新成功',
                'pages.apps.status.update.error': '状态更新失败',
                'pages.apps.regenerateKeys.confirm.title': '确认重新生成密钥',
                'pages.apps.regenerateKeys.confirm.content': '重新生成密钥后，旧密钥将失效，确定继续吗？',
                'pages.apps.regenerateKeys.success': '密钥重新生成成功',
                'pages.apps.regenerateKeys.error': '密钥重新生成失败',
                'pages.apps.resetApiCalls.confirm.title': '确认重置调用次数',
                'pages.apps.resetApiCalls.confirm.content': '确定要重置API调用次数吗？',
                'pages.apps.resetApiCalls.success': '调用次数重置成功',
                'pages.apps.resetApiCalls.error': '调用次数重置失败',
                'pages.apps.form.create.title': '新建应用',
                'pages.apps.form.edit.title': '编辑应用',
                'pages.apps.form.name': '应用名称',
                'pages.apps.form.name.required': '请输入应用名称',
                'pages.apps.form.name.placeholder': '请输入应用名称',
                'pages.apps.form.description': '应用描述',
                'pages.apps.form.description.placeholder': '请输入应用描述',
                'pages.apps.form.maxApiCalls': '最大API调用次数',
                'pages.apps.form.maxApiCalls.placeholder': '请输入最大API调用次数',
                'pages.apps.form.maxApiCalls.help': '留空表示无限制',
                'pages.apps.form.rateLimit': '速率限制（次/分钟）',
                'pages.apps.form.rateLimit.placeholder': '请输入速率限制',
                'pages.apps.form.rateLimit.help': '每分钟最大请求次数',
                'pages.apps.form.allowedOrigins': '允许的来源',
                'pages.apps.form.allowedOrigins.placeholder': '请输入允许的来源，多个用换行分隔',
                'pages.apps.form.allowedOrigins.help': 'CORS配置，每行一个域名',
                'pages.apps.form.isEnabled': '启用状态',
                'pages.apps.form.create.success': '创建成功',
                'pages.apps.form.create.error': '创建失败',
                'pages.apps.form.update.success': '更新成功',
                'pages.apps.form.update.error': '更新失败',
                'pages.apps.detail.title': '应用详情',
                'pages.apps.detail.name': '应用名称',
                'pages.apps.detail.description': '应用描述',
                'pages.apps.detail.apiKey': 'API密钥',
                'pages.apps.detail.apiSecret': 'API密钥',
                'pages.apps.detail.status': '状态',
                'pages.apps.detail.apiCalls': 'API调用次数',
                'pages.apps.detail.rateLimit': '速率限制',
                'pages.apps.detail.allowedOrigins': '允许的来源',
                'pages.apps.detail.createdTime': '创建时间',
                'pages.apps.detail.updatedTime': '更新时间',
                'pages.apps.detail.configJson': '配置信息',
                'pages.apps.detail.copy.apiKey.success': 'API密钥复制成功',
                'pages.apps.detail.copy.apiSecret.success': 'API密钥复制成功'
            };
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '2315245009422849245';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/.umi/plugin-openapi/openapi.tsx": [
            "vendors",
            "src/.umi/plugin-openapi/openapi.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Admin.tsx": [
            "p__Admin"
        ],
        "src/pages/Welcome.tsx": [
            "p__Welcome"
        ],
        "src/pages/ai-models/index.tsx": [
            "common",
            "src/pages/ai-models/index.tsx"
        ],
        "src/pages/api-test/index.tsx": [
            "common",
            "src/pages/api-test/index.tsx"
        ],
        "src/pages/apps/index.tsx": [
            "common",
            "p__apps__index"
        ],
        "src/pages/auth-test/index.tsx": [
            "common",
            "src/pages/auth-test/index.tsx"
        ],
        "src/pages/chat/chat-interface.tsx": [
            "vendors",
            "common",
            "src/pages/chat/chat-interface.tsx"
        ],
        "src/pages/chat/index.tsx": [
            "common",
            "p__chat__index"
        ],
        "src/pages/chat/session/[id].tsx": [
            "common",
            "p__chat__session__id"
        ],
        "src/pages/knowledge/[id]/files.tsx": [
            "common",
            "p__knowledge__id__files"
        ],
        "src/pages/knowledge/index.tsx": [
            "common",
            "p__knowledge__index"
        ],
        "src/pages/plugins/index.tsx": [
            "common",
            "p__plugins__index"
        ],
        "src/pages/response-demo/index.tsx": [
            "src/pages/response-demo/index.tsx"
        ],
        "src/pages/table-list/index.tsx": [
            "src/pages/table-list/index.tsx"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ],
        "src/pages/user/register/index.tsx": [
            "common",
            "p__user__register__index"
        ]
    });
    ;
});

//# sourceMappingURL=umi.5760159428415107075.hot-update.js.map