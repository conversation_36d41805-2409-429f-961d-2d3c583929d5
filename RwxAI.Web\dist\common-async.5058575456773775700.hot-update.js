globalThis.makoModuleHotUpdate('common', {
    modules: {
        "src/pages/chat/components/SessionForm.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _rwxai = __mako_require__("src/services/rwxai/index.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const SessionForm = ({ visible, onVisibleChange, initialValues, onSuccess })=>{
                _s();
                const intl = (0, _max.useIntl)();
                const [form] = _antd.Form.useForm();
                const [loading, setLoading] = (0, _react.useState)(false);
                const [models, setModels] = (0, _react.useState)([]);
                const [selectedModel, setSelectedModel] = (0, _react.useState)(null);
                const isEdit = !!(initialValues === null || initialValues === void 0 ? void 0 : initialValues.Id);
                (0, _react.useEffect)(()=>{
                    if (visible) {
                        loadModels();
                        if (initialValues) form.setFieldsValue(initialValues);
                        else form.resetFields();
                    }
                }, [
                    visible,
                    initialValues
                ]);
                const loadModels = async ()=>{
                    const response = await (0, _rwxai.getAIModels)();
                    if (response.success) {
                        // 只显示聊天类型的模型
                        const chatModels = (response.data || []).filter((model)=>{
                            var _model_Template;
                            return ((_model_Template = model.Template) === null || _model_Template === void 0 ? void 0 : _model_Template.Type) === 0 && model.IsEnabled;
                        });
                        setModels(chatModels);
                    } else setModels([]);
                // 错误消息会由统一响应处理系统自动显示
                };
                // 处理模型选择变化
                const handleModelChange = (modelId)=>{
                    const model = models.find((m)=>m.Id === modelId);
                    setSelectedModel(model || null);
                    if (model) // 自动填充模型的配置参数
                    form.setFieldsValue({
                        Temperature: model.Temperature || 0.7,
                        MaxTokens: model.MaxTokens || 2000,
                        SystemPrompt: model.SystemPrompt || ''
                    });
                };
                const handleSubmit = async ()=>{
                    try {
                        const values = await form.validateFields();
                        setLoading(true);
                        let response;
                        if (isEdit) response = await (0, _rwxai.updateChatSession)(initialValues.Id, {
                            ...initialValues,
                            ...values
                        });
                        else response = await (0, _rwxai.createChatSession)(values);
                        if (response.success) onSuccess();
                    // 成功和错误消息会由统一响应处理系统自动显示
                    } catch (error) {
                        // 表单验证错误等
                        console.error('Form validation error:', error);
                    } finally{
                        setLoading(false);
                    }
                };
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                    title: intl.formatMessage({
                        id: isEdit ? 'pages.chat.form.edit.title' : 'pages.chat.form.create.title'
                    }),
                    open: visible,
                    onCancel: ()=>onVisibleChange(false),
                    onOk: handleSubmit,
                    confirmLoading: loading,
                    width: 600,
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                        form: form,
                        layout: "vertical",
                        initialValues: {
                            IsActive: true,
                            Temperature: 0.7,
                            MaxTokens: 2000
                        },
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                name: "Name",
                                label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                    id: "pages.chat.form.name"
                                }, void 0, false, {
                                    fileName: "src/pages/chat/components/SessionForm.tsx",
                                    lineNumber: 111,
                                    columnNumber: 18
                                }, void 0),
                                rules: [
                                    {
                                        required: true,
                                        message: intl.formatMessage({
                                            id: 'pages.chat.form.name.required'
                                        })
                                    }
                                ],
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                    placeholder: intl.formatMessage({
                                        id: 'pages.chat.form.name.placeholder'
                                    })
                                }, void 0, false, {
                                    fileName: "src/pages/chat/components/SessionForm.tsx",
                                    lineNumber: 114,
                                    columnNumber: 11
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/chat/components/SessionForm.tsx",
                                lineNumber: 109,
                                columnNumber: 9
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                name: "ModelId",
                                label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                    id: "pages.chat.form.model"
                                }, void 0, false, {
                                    fileName: "src/pages/chat/components/SessionForm.tsx",
                                    lineNumber: 119,
                                    columnNumber: 18
                                }, void 0),
                                rules: [
                                    {
                                        required: true,
                                        message: intl.formatMessage({
                                            id: 'pages.chat.form.model.required'
                                        })
                                    }
                                ],
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select, {
                                    placeholder: intl.formatMessage({
                                        id: 'pages.chat.form.model.placeholder'
                                    }),
                                    onChange: handleModelChange,
                                    children: models.map((model)=>{
                                        var _model_Template;
                                        return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                            value: model.Id,
                                            children: [
                                                model.Name,
                                                " (",
                                                (_model_Template = model.Template) === null || _model_Template === void 0 ? void 0 : _model_Template.ModelId,
                                                ")"
                                            ]
                                        }, model.Id, true, {
                                            fileName: "src/pages/chat/components/SessionForm.tsx",
                                            lineNumber: 127,
                                            columnNumber: 15
                                        }, this);
                                    })
                                }, void 0, false, {
                                    fileName: "src/pages/chat/components/SessionForm.tsx",
                                    lineNumber: 122,
                                    columnNumber: 11
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/chat/components/SessionForm.tsx",
                                lineNumber: 117,
                                columnNumber: 9
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                name: "SystemPrompt",
                                label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                    id: "pages.chat.form.systemPrompt"
                                }, void 0, false, {
                                    fileName: "src/pages/chat/components/SessionForm.tsx",
                                    lineNumber: 136,
                                    columnNumber: 18
                                }, void 0),
                                help: intl.formatMessage({
                                    id: 'pages.chat.form.systemPrompt.help'
                                }),
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input.TextArea, {
                                    rows: 4,
                                    placeholder: intl.formatMessage({
                                        id: 'pages.chat.form.systemPrompt.placeholder'
                                    })
                                }, void 0, false, {
                                    fileName: "src/pages/chat/components/SessionForm.tsx",
                                    lineNumber: 139,
                                    columnNumber: 11
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/chat/components/SessionForm.tsx",
                                lineNumber: 134,
                                columnNumber: 9
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                name: "Temperature",
                                label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                    id: "pages.chat.form.temperature"
                                }, void 0, false, {
                                    fileName: "src/pages/chat/components/SessionForm.tsx",
                                    lineNumber: 147,
                                    columnNumber: 18
                                }, void 0),
                                help: intl.formatMessage({
                                    id: 'pages.chat.form.temperature.help'
                                }),
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.InputNumber, {
                                    min: 0,
                                    max: 2,
                                    step: 0.1,
                                    style: {
                                        width: '100%'
                                    },
                                    placeholder: intl.formatMessage({
                                        id: 'pages.chat.form.temperature.placeholder'
                                    })
                                }, void 0, false, {
                                    fileName: "src/pages/chat/components/SessionForm.tsx",
                                    lineNumber: 150,
                                    columnNumber: 11
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/chat/components/SessionForm.tsx",
                                lineNumber: 145,
                                columnNumber: 9
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                name: "MaxTokens",
                                label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                    id: "pages.chat.form.maxTokens"
                                }, void 0, false, {
                                    fileName: "src/pages/chat/components/SessionForm.tsx",
                                    lineNumber: 161,
                                    columnNumber: 18
                                }, void 0),
                                help: intl.formatMessage({
                                    id: 'pages.chat.form.maxTokens.help'
                                }),
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.InputNumber, {
                                    min: 1,
                                    max: 100000,
                                    style: {
                                        width: '100%'
                                    },
                                    placeholder: intl.formatMessage({
                                        id: 'pages.chat.form.maxTokens.placeholder'
                                    })
                                }, void 0, false, {
                                    fileName: "src/pages/chat/components/SessionForm.tsx",
                                    lineNumber: 164,
                                    columnNumber: 11
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/chat/components/SessionForm.tsx",
                                lineNumber: 159,
                                columnNumber: 9
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                name: "IsActive",
                                label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                    id: "pages.chat.form.isActive"
                                }, void 0, false, {
                                    fileName: "src/pages/chat/components/SessionForm.tsx",
                                    lineNumber: 174,
                                    columnNumber: 18
                                }, void 0),
                                valuePropName: "checked",
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Switch, {}, void 0, false, {
                                    fileName: "src/pages/chat/components/SessionForm.tsx",
                                    lineNumber: 177,
                                    columnNumber: 11
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/chat/components/SessionForm.tsx",
                                lineNumber: 172,
                                columnNumber: 9
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/chat/components/SessionForm.tsx",
                        lineNumber: 100,
                        columnNumber: 7
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/chat/components/SessionForm.tsx",
                    lineNumber: 90,
                    columnNumber: 5
                }, this);
            };
            _s(SessionForm, "IGErTDGogsOg+DvDQlsWPasSz8A=", false, function() {
                return [
                    _max.useIntl,
                    _antd.Form.useForm
                ];
            });
            _c = SessionForm;
            var _default = SessionForm;
            var _c;
            $RefreshReg$(_c, "SessionForm");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '9488064620083710597';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/.umi/plugin-openapi/openapi.tsx": [
            "vendors",
            "src/.umi/plugin-openapi/openapi.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Admin.tsx": [
            "p__Admin"
        ],
        "src/pages/Welcome.tsx": [
            "p__Welcome"
        ],
        "src/pages/ai-models/index.tsx": [
            "common",
            "src/pages/ai-models/index.tsx"
        ],
        "src/pages/api-test/index.tsx": [
            "common",
            "src/pages/api-test/index.tsx"
        ],
        "src/pages/apps/index.tsx": [
            "common",
            "p__apps__index"
        ],
        "src/pages/auth-test/index.tsx": [
            "common",
            "src/pages/auth-test/index.tsx"
        ],
        "src/pages/chat/chat-interface.tsx": [
            "vendors",
            "common",
            "src/pages/chat/chat-interface.tsx"
        ],
        "src/pages/chat/index.tsx": [
            "common",
            "p__chat__index"
        ],
        "src/pages/chat/session/[id].tsx": [
            "common",
            "p__chat__session__id"
        ],
        "src/pages/knowledge/[id]/files.tsx": [
            "common",
            "p__knowledge__id__files"
        ],
        "src/pages/knowledge/index.tsx": [
            "common",
            "p__knowledge__index"
        ],
        "src/pages/plugins/index.tsx": [
            "common",
            "p__plugins__index"
        ],
        "src/pages/response-demo/index.tsx": [
            "src/pages/response-demo/index.tsx"
        ],
        "src/pages/table-list/index.tsx": [
            "src/pages/table-list/index.tsx"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ],
        "src/pages/user/register/index.tsx": [
            "common",
            "p__user__register__index"
        ]
    });
    ;
});

//# sourceMappingURL=common-async.5058575456773775700.hot-update.js.map