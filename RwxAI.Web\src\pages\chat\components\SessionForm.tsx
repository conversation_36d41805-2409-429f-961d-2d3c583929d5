import React, { useEffect, useState } from 'react';
import { Modal, Form, Input, Select, InputNumber, Switch, message } from 'antd';
import { useIntl, FormattedMessage } from '@umijs/max';
import { createChatSession, updateChatSession, getAIModels } from '@/services/rwxai';

interface SessionFormProps {
  visible: boolean;
  onVisibleChange: (visible: boolean) => void;
  initialValues?: RwxAI.ChatSession;
  onSuccess: () => void;
}

const SessionForm: React.FC<SessionFormProps> = ({
  visible,
  onVisibleChange,
  initialValues,
  onSuccess,
}) => {
  const intl = useIntl();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [models, setModels] = useState<RwxAI.AIModel[]>([]);

  const isEdit = !!initialValues?.Id;

  useEffect(() => {
    if (visible) {
      loadModels();
      if (initialValues) {
        form.setFieldsValue(initialValues);
      } else {
        form.resetFields();
      }
    }
  }, [visible, initialValues]);

  const loadModels = async () => {
    const response = await getAIModels();
    if (response.success) {
      // 只显示聊天类型的模型
      const chatModels = (response.data || []).filter(model => model.ModelType === 0 && model.IsEnabled);
      setModels(chatModels);
    } else {
      setModels([]);
    }
    // 错误消息会由统一响应处理系统自动显示
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      let response;
      if (isEdit) {
        response = await updateChatSession(initialValues!.Id, { ...initialValues, ...values });
      } else {
        response = await createChatSession(values);
      }

      if (response.success) {
        onSuccess();
      }
      // 成功和错误消息会由统一响应处理系统自动显示
    } catch (error) {
      // 表单验证错误等
      console.error('Form validation error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={intl.formatMessage({
        id: isEdit ? 'pages.chat.form.edit.title' : 'pages.chat.form.create.title',
      })}
      open={visible}
      onCancel={() => onVisibleChange(false)}
      onOk={handleSubmit}
      confirmLoading={loading}
      width={600}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          IsActive: true,
          Temperature: 0.7,
          MaxTokens: 2000,
        }}
      >
        <Form.Item
          name="Name"
          label={<FormattedMessage id="pages.chat.form.name" />}
          rules={[{ required: true, message: intl.formatMessage({ id: 'pages.chat.form.name.required' }) }]}
        >
          <Input placeholder={intl.formatMessage({ id: 'pages.chat.form.name.placeholder' })} />
        </Form.Item>

        <Form.Item
          name="ModelId"
          label={<FormattedMessage id="pages.chat.form.model" />}
          rules={[{ required: true, message: intl.formatMessage({ id: 'pages.chat.form.model.required' }) }]}
        >
          <Select placeholder={intl.formatMessage({ id: 'pages.chat.form.model.placeholder' })}>
            {models.map((model) => (
              <Select.Option key={model.Id} value={model.Id}>
                {model.Name} ({model.ModelKey})
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="SystemPrompt"
          label={<FormattedMessage id="pages.chat.form.systemPrompt" />}
          help={intl.formatMessage({ id: 'pages.chat.form.systemPrompt.help' })}
        >
          <Input.TextArea
            rows={4}
            placeholder={intl.formatMessage({ id: 'pages.chat.form.systemPrompt.placeholder' })}
          />
        </Form.Item>

        <Form.Item
          name="Temperature"
          label={<FormattedMessage id="pages.chat.form.temperature" />}
          help={intl.formatMessage({ id: 'pages.chat.form.temperature.help' })}
        >
          <InputNumber
            min={0}
            max={2}
            step={0.1}
            style={{ width: '100%' }}
            placeholder={intl.formatMessage({ id: 'pages.chat.form.temperature.placeholder' })}
          />
        </Form.Item>

        <Form.Item
          name="MaxTokens"
          label={<FormattedMessage id="pages.chat.form.maxTokens" />}
          help={intl.formatMessage({ id: 'pages.chat.form.maxTokens.help' })}
        >
          <InputNumber
            min={1}
            max={100000}
            style={{ width: '100%' }}
            placeholder={intl.formatMessage({ id: 'pages.chat.form.maxTokens.placeholder' })}
          />
        </Form.Item>

        <Form.Item
          name="IsActive"
          label={<FormattedMessage id="pages.chat.form.isActive" />}
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default SessionForm;
