import React, { useEffect, useState, forwardRef, useImperativeHandle } from 'react';
import { Form, Input, Select, InputNumber, message } from 'antd';
import { useIntl, FormattedMessage } from '@umijs/max';
import { createChatSession, updateChatSession, getAllAIModels } from '@/services/rwxai';

interface SessionFormProps {
  visible: boolean;
  onVisibleChange: (visible: boolean) => void;
  initialValues?: RwxAI.ChatSession;
  onSuccess: (newSession?: RwxAI.ChatSession) => void;
}

const SessionForm = forwardRef<any, SessionFormProps>(({
  visible,
  onVisibleChange,
  initialValues,
  onSuccess,
}, ref) => {
  const intl = useIntl();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [models, setModels] = useState<RwxAI.AIModel[]>([]);
  const [selectedModel, setSelectedModel] = useState<RwxAI.AIModel | null>(null);

  const isEdit = !!initialValues?.Id;

  // 暴露submit方法给父组件
  useImperativeHandle(ref, () => ({
    submit: () => {
      form.submit();
    }
  }));

  // 加载模型列表 - 只在visible变化时触发
  useEffect(() => {
    if (visible) {
      loadModels();
    }
  }, [visible]);

  // 处理初始值设置 - 在visible、initialValues或models变化时触发
  useEffect(() => {
    if (visible) {
      if (initialValues) {
        form.setFieldsValue(initialValues);
        // 如果是编辑模式且有模型数据，设置选中的模型
        if (initialValues.ModelId && models.length > 0) {
          const model = models.find(m => m.Id === initialValues.ModelId);
          setSelectedModel(model || null);
        }
      } else {
        form.resetFields();
        setSelectedModel(null);
      }
    }
  }, [visible, initialValues, models]);

  const loadModels = async () => {
    try {
      const response = await getAllAIModels();
      console.log('AI Models API response:', response); // 调试日志

      if (response.success) {
        // 显示所有启用的聊天类型模型
        const enabledModels = (response.data || []).filter(model => {
          console.log('Model:', model); // 调试每个模型
          // 检查模型是否启用且为聊天类型
          const isEnabled = model.IsEnabled === true;
          const isChatModel = model.Template?.Type === "Chat" || model.Template?.Type === 0;
          return isEnabled && isChatModel;
        });

        console.log('Enabled chat models:', enabledModels); // 调试日志
        setModels(enabledModels);
      } else {
        console.error('Failed to load models:', response);
        setModels([]);
      }
    } catch (error) {
      console.error('Error loading models:', error);
      setModels([]);
    }
  };

  // 处理模型选择变化
  const handleModelChange = (modelId: string) => {
    const model = models.find(m => m.Id === modelId);
    setSelectedModel(model || null);

    if (model) {
      // 自动填充模型的配置参数
      form.setFieldsValue({
        Temperature: model.Temperature || 0.7,
        MaxTokens: model.MaxTokens || 2000,
        SystemPrompt: model.SystemPrompt || '',
      });
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);

      let response;
      if (isEdit) {
        response = await updateChatSession(initialValues!.Id, { ...initialValues, ...values });
      } else {
        response = await createChatSession(values);
      }

      if (response.success) {
        onSuccess(response.data);
      }
      // 成功和错误消息会由统一响应处理系统自动显示
    } catch (error) {
      // 表单验证错误等
      console.error('Form validation error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Form
      form={form}
      layout="vertical"
      initialValues={{
        Temperature: 0.7,
        MaxTokens: 2000,
      }}
      onFinish={handleSubmit}
    >
        <Form.Item
          name="Name"
          label={<FormattedMessage id="pages.chat.form.name" />}
          rules={[{ required: true, message: intl.formatMessage({ id: 'pages.chat.form.name.required' }) }]}
        >
          <Input placeholder={intl.formatMessage({ id: 'pages.chat.form.name.placeholder' })} />
        </Form.Item>

        <Form.Item
          name="ModelId"
          label={<FormattedMessage id="pages.chat.form.model" />}
          rules={[{ required: true, message: intl.formatMessage({ id: 'pages.chat.form.model.required' }) }]}
        >
          <Select
            placeholder={intl.formatMessage({ id: 'pages.chat.form.model.placeholder' })}
            onChange={handleModelChange}
          >
            {models.map((model) => (
              <Select.Option key={model.Id} value={model.Id}>
                {model.Name || model.DisplayName} ({model.Template?.ModelId || model.ModelId || '未知模型'})
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        {/* 显示从模型自动获取的配置信息 */}
        {selectedModel && (
          <>
            <Form.Item
              name="SystemPrompt"
              label={<FormattedMessage id="pages.chat.form.systemPrompt" />}
              help="从选中的模型自动获取"
            >
              <Input.TextArea
                rows={3}
                disabled
                placeholder="从模型配置中自动获取"
              />
            </Form.Item>

            <div style={{ display: 'flex', gap: '16px' }}>
              <Form.Item
                name="Temperature"
                label={<FormattedMessage id="pages.chat.form.temperature" />}
                style={{ flex: 1 }}
              >
                <InputNumber
                  disabled
                  style={{ width: '100%' }}
                  placeholder="从模型配置中自动获取"
                />
              </Form.Item>

              <Form.Item
                name="MaxTokens"
                label={<FormattedMessage id="pages.chat.form.maxTokens" />}
                style={{ flex: 1 }}
              >
                <InputNumber
                  disabled
                  style={{ width: '100%' }}
                  placeholder="从模型配置中自动获取"
                />
              </Form.Item>
            </div>
          </>
        )}

    </Form>
  );
});

SessionForm.displayName = 'SessionForm';

export default SessionForm;
