((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] || []).push([
        ['p__knowledge__id__files'],
{ "src/pages/knowledge/[id]/files.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
var _protable = __mako_require__("node_modules/@ant-design/pro-table/es/index.js");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _rwxai = __mako_require__("src/services/rwxai/index.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const KnowledgeFilesPage = ()=>{
    _s();
    const intl = (0, _max.useIntl)();
    const { id } = (0, _max.useParams)();
    const actionRef = (0, _react.useRef)();
    const [knowledgeBase, setKnowledgeBase] = (0, _react.useState)();
    const [uploadModalVisible, setUploadModalVisible] = (0, _react.useState)(false);
    const [uploading, setUploading] = (0, _react.useState)(false);
    (0, _react.useEffect)(()=>{
        if (id) loadKnowledgeBase();
    }, [
        id
    ]);
    const loadKnowledgeBase = async ()=>{
        try {
            const data = await (0, _rwxai.getKnowledgeBaseById)(id);
            setKnowledgeBase(data);
        } catch (error) {
            _antd.message.error(intl.formatMessage({
                id: 'pages.knowledge.files.loadKnowledge.error'
            }));
        }
    };
    const handleDelete = async (record)=>{
        _antd.Modal.confirm({
            title: intl.formatMessage({
                id: 'pages.knowledge.files.delete.confirm.title'
            }),
            content: intl.formatMessage({
                id: 'pages.knowledge.files.delete.confirm.content'
            }),
            onOk: async ()=>{
                try {
                    var _actionRef_current;
                    await (0, _rwxai.deleteKnowledgeFile)(record.Id);
                    _antd.message.success(intl.formatMessage({
                        id: 'pages.knowledge.files.delete.success'
                    }));
                    (_actionRef_current = actionRef.current) === null || _actionRef_current === void 0 || _actionRef_current.reload();
                } catch (error) {
                    _antd.message.error(intl.formatMessage({
                        id: 'pages.knowledge.files.delete.error'
                    }));
                }
            }
        });
    };
    const handleProcess = async (record)=>{
        try {
            var _actionRef_current;
            await (0, _rwxai.processKnowledgeFile)(record.Id);
            _antd.message.success(intl.formatMessage({
                id: 'pages.knowledge.files.process.success'
            }));
            (_actionRef_current = actionRef.current) === null || _actionRef_current === void 0 || _actionRef_current.reload();
        } catch (error) {
            _antd.message.error(intl.formatMessage({
                id: 'pages.knowledge.files.process.error'
            }));
        }
    };
    const handleUpload = async (file)=>{
        try {
            var _actionRef_current;
            setUploading(true);
            await (0, _rwxai.uploadKnowledgeFile)(id, file);
            _antd.message.success(intl.formatMessage({
                id: 'pages.knowledge.files.upload.success'
            }));
            setUploadModalVisible(false);
            (_actionRef_current = actionRef.current) === null || _actionRef_current === void 0 || _actionRef_current.reload();
        } catch (error) {
            _antd.message.error(intl.formatMessage({
                id: 'pages.knowledge.files.upload.error'
            }));
        } finally{
            setUploading(false);
        }
    };
    const formatFileSize = (bytes)=>{
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = [
            'Bytes',
            'KB',
            'MB',
            'GB'
        ];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };
    const columns = [
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.knowledge.files.table.fileName"
            }, void 0, false, {
                fileName: "src/pages/knowledge/[id]/files.tsx",
                lineNumber: 82,
                columnNumber: 14
            }, this),
            dataIndex: 'FileName',
            key: 'FileName',
            ellipsis: true,
            render: (_, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.FileOutlined, {}, void 0, false, {
                            fileName: "src/pages/knowledge/[id]/files.tsx",
                            lineNumber: 88,
                            columnNumber: 11
                        }, this),
                        record.FileName
                    ]
                }, void 0, true, {
                    fileName: "src/pages/knowledge/[id]/files.tsx",
                    lineNumber: 87,
                    columnNumber: 9
                }, this)
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.knowledge.files.table.contentType"
            }, void 0, false, {
                fileName: "src/pages/knowledge/[id]/files.tsx",
                lineNumber: 94,
                columnNumber: 14
            }, this),
            dataIndex: 'ContentType',
            key: 'ContentType',
            hideInSearch: true
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.knowledge.files.table.fileSize"
            }, void 0, false, {
                fileName: "src/pages/knowledge/[id]/files.tsx",
                lineNumber: 100,
                columnNumber: 14
            }, this),
            dataIndex: 'FileSize',
            key: 'FileSize',
            hideInSearch: true,
            render: (_, record)=>formatFileSize(record.FileSize)
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.knowledge.files.table.status"
            }, void 0, false, {
                fileName: "src/pages/knowledge/[id]/files.tsx",
                lineNumber: 107,
                columnNumber: 14
            }, this),
            dataIndex: 'IsProcessed',
            key: 'IsProcessed',
            render: (_, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                    color: record.IsProcessed ? 'green' : 'orange',
                    children: record.IsProcessed ? intl.formatMessage({
                        id: 'pages.knowledge.files.status.processed'
                    }) : intl.formatMessage({
                        id: 'pages.knowledge.files.status.pending'
                    })
                }, void 0, false, {
                    fileName: "src/pages/knowledge/[id]/files.tsx",
                    lineNumber: 111,
                    columnNumber: 9
                }, this)
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.knowledge.files.table.processedTime"
            }, void 0, false, {
                fileName: "src/pages/knowledge/[id]/files.tsx",
                lineNumber: 120,
                columnNumber: 14
            }, this),
            dataIndex: 'ProcessedTime',
            key: 'ProcessedTime',
            valueType: 'dateTime',
            hideInSearch: true,
            render: (_, record)=>record.ProcessedTime ? new Date(record.ProcessedTime).toLocaleString() : '-'
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.knowledge.files.table.createdTime"
            }, void 0, false, {
                fileName: "src/pages/knowledge/[id]/files.tsx",
                lineNumber: 128,
                columnNumber: 14
            }, this),
            dataIndex: 'CreatedTime',
            key: 'CreatedTime',
            valueType: 'dateTime',
            width: 180,
            hideInSearch: true
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.knowledge.files.table.actions"
            }, void 0, false, {
                fileName: "src/pages/knowledge/[id]/files.tsx",
                lineNumber: 136,
                columnNumber: 14
            }, this),
            key: 'actions',
            width: 200,
            render: (_, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    children: [
                        !record.IsProcessed && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "link",
                            size: "small",
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PlayCircleOutlined, {}, void 0, false, {
                                fileName: "src/pages/knowledge/[id]/files.tsx",
                                lineNumber: 145,
                                columnNumber: 21
                            }, void 0),
                            onClick: ()=>handleProcess(record),
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                id: "pages.knowledge.files.actions.process"
                            }, void 0, false, {
                                fileName: "src/pages/knowledge/[id]/files.tsx",
                                lineNumber: 148,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/knowledge/[id]/files.tsx",
                            lineNumber: 142,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "link",
                            size: "small",
                            danger: true,
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                fileName: "src/pages/knowledge/[id]/files.tsx",
                                lineNumber: 155,
                                columnNumber: 19
                            }, void 0),
                            onClick: ()=>handleDelete(record),
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                id: "pages.knowledge.files.actions.delete"
                            }, void 0, false, {
                                fileName: "src/pages/knowledge/[id]/files.tsx",
                                lineNumber: 158,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/knowledge/[id]/files.tsx",
                            lineNumber: 151,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/knowledge/[id]/files.tsx",
                    lineNumber: 140,
                    columnNumber: 9
                }, this)
        }
    ];
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        title: knowledgeBase === null || knowledgeBase === void 0 ? void 0 : knowledgeBase.Name,
        subTitle: intl.formatMessage({
            id: 'pages.knowledge.files.subtitle'
        }),
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_protable.ProTable, {
                headerTitle: intl.formatMessage({
                    id: 'pages.knowledge.files.title'
                }),
                actionRef: actionRef,
                rowKey: "Id",
                search: {
                    labelWidth: 120
                },
                toolBarRender: ()=>[
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "primary",
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UploadOutlined, {}, void 0, false, {
                                fileName: "src/pages/knowledge/[id]/files.tsx",
                                lineNumber: 181,
                                columnNumber: 19
                            }, void 0),
                            onClick: ()=>setUploadModalVisible(true),
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                id: "pages.knowledge.files.actions.upload"
                            }, void 0, false, {
                                fileName: "src/pages/knowledge/[id]/files.tsx",
                                lineNumber: 184,
                                columnNumber: 13
                            }, void 0)
                        }, "primary", false, {
                            fileName: "src/pages/knowledge/[id]/files.tsx",
                            lineNumber: 178,
                            columnNumber: 11
                        }, void 0)
                    ],
                request: async ()=>{
                    try {
                        const data = await (0, _rwxai.getKnowledgeFiles)(id);
                        return {
                            data: data || [],
                            success: true
                        };
                    } catch (error) {
                        return {
                            data: [],
                            success: false
                        };
                    }
                },
                columns: columns
            }, void 0, false, {
                fileName: "src/pages/knowledge/[id]/files.tsx",
                lineNumber: 170,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                title: intl.formatMessage({
                    id: 'pages.knowledge.files.upload.title'
                }),
                open: uploadModalVisible,
                onCancel: ()=>setUploadModalVisible(false),
                footer: null,
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Upload.Dragger, {
                        name: "file",
                        multiple: false,
                        beforeUpload: (file)=>{
                            handleUpload(file);
                            return false;
                        },
                        disabled: uploading,
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("p", {
                                className: "ant-upload-drag-icon",
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UploadOutlined, {}, void 0, false, {
                                    fileName: "src/pages/knowledge/[id]/files.tsx",
                                    lineNumber: 220,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/knowledge/[id]/files.tsx",
                                lineNumber: 219,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("p", {
                                className: "ant-upload-text",
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                    id: "pages.knowledge.files.upload.dragText"
                                }, void 0, false, {
                                    fileName: "src/pages/knowledge/[id]/files.tsx",
                                    lineNumber: 223,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/knowledge/[id]/files.tsx",
                                lineNumber: 222,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("p", {
                                className: "ant-upload-hint",
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                    id: "pages.knowledge.files.upload.hint"
                                }, void 0, false, {
                                    fileName: "src/pages/knowledge/[id]/files.tsx",
                                    lineNumber: 226,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/knowledge/[id]/files.tsx",
                                lineNumber: 225,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/knowledge/[id]/files.tsx",
                        lineNumber: 210,
                        columnNumber: 9
                    }, this),
                    uploading && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            marginTop: 16
                        },
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Progress, {
                            percent: 100,
                            status: "active"
                        }, void 0, false, {
                            fileName: "src/pages/knowledge/[id]/files.tsx",
                            lineNumber: 231,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/knowledge/[id]/files.tsx",
                        lineNumber: 230,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/knowledge/[id]/files.tsx",
                lineNumber: 204,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/knowledge/[id]/files.tsx",
        lineNumber: 166,
        columnNumber: 5
    }, this);
};
_s(KnowledgeFilesPage, "df+fVRlvn7A+fknpdSiXZT+cy64=", false, function() {
    return [
        _max.useIntl,
        _max.useParams
    ];
});
_c = KnowledgeFilesPage;
var _default = KnowledgeFilesPage;
var _c;
$RefreshReg$(_c, "KnowledgeFilesPage");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=p__knowledge__id__files-async.js.map