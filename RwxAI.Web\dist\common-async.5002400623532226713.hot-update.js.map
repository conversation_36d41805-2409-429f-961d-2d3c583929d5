{"version": 3, "sources": ["common-async.5002400623532226713.hot-update.js", "src/pages/chat/components/SessionForm.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'common',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='5058575456773775700';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/.umi/plugin-openapi/openapi.tsx\":[\"vendors\",\"src/.umi/plugin-openapi/openapi.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Admin.tsx\":[\"p__Admin\"],\"src/pages/Welcome.tsx\":[\"p__Welcome\"],\"src/pages/ai-models/index.tsx\":[\"common\",\"src/pages/ai-models/index.tsx\"],\"src/pages/api-test/index.tsx\":[\"common\",\"src/pages/api-test/index.tsx\"],\"src/pages/apps/index.tsx\":[\"common\",\"p__apps__index\"],\"src/pages/auth-test/index.tsx\":[\"common\",\"src/pages/auth-test/index.tsx\"],\"src/pages/chat/chat-interface.tsx\":[\"vendors\",\"common\",\"src/pages/chat/chat-interface.tsx\"],\"src/pages/chat/index.tsx\":[\"common\",\"p__chat__index\"],\"src/pages/chat/session/[id].tsx\":[\"common\",\"p__chat__session__id\"],\"src/pages/knowledge/[id]/files.tsx\":[\"common\",\"p__knowledge__id__files\"],\"src/pages/knowledge/index.tsx\":[\"common\",\"p__knowledge__index\"],\"src/pages/plugins/index.tsx\":[\"common\",\"p__plugins__index\"],\"src/pages/response-demo/index.tsx\":[\"src/pages/response-demo/index.tsx\"],\"src/pages/table-list/index.tsx\":[\"src/pages/table-list/index.tsx\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"],\"src/pages/user/register/index.tsx\":[\"common\",\"p__user__register__index\"]});;\r\n  },\r\n);\r\n", "import React, { useEffect, useState } from 'react';\nimport { Modal, Form, Input, Select, InputNumber, Switch, message } from 'antd';\nimport { useIntl, FormattedMessage } from '@umijs/max';\nimport { createChatSession, updateChatSession, getAIModels } from '@/services/rwxai';\n\ninterface SessionFormProps {\n  visible: boolean;\n  onVisibleChange: (visible: boolean) => void;\n  initialValues?: RwxAI.ChatSession;\n  onSuccess: () => void;\n}\n\nconst SessionForm: React.FC<SessionFormProps> = ({\n  visible,\n  onVisibleChange,\n  initialValues,\n  onSuccess,\n}) => {\n  const intl = useIntl();\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [models, setModels] = useState<RwxAI.AIModel[]>([]);\n  const [selectedModel, setSelectedModel] = useState<RwxAI.AIModel | null>(null);\n\n  const isEdit = !!initialValues?.Id;\n\n  useEffect(() => {\n    if (visible) {\n      loadModels();\n      if (initialValues) {\n        form.setFieldsValue(initialValues);\n      } else {\n        form.resetFields();\n      }\n    }\n  }, [visible, initialValues]);\n\n  const loadModels = async () => {\n    const response = await getAIModels();\n    if (response.success) {\n      // 只显示聊天类型的模型\n      const chatModels = (response.data || []).filter(model => model.Template?.Type === 0 && model.IsEnabled);\n      setModels(chatModels);\n    } else {\n      setModels([]);\n    }\n    // 错误消息会由统一响应处理系统自动显示\n  };\n\n  // 处理模型选择变化\n  const handleModelChange = (modelId: string) => {\n    const model = models.find(m => m.Id === modelId);\n    setSelectedModel(model || null);\n\n    if (model) {\n      // 自动填充模型的配置参数\n      form.setFieldsValue({\n        Temperature: model.Temperature || 0.7,\n        MaxTokens: model.MaxTokens || 2000,\n        SystemPrompt: model.SystemPrompt || '',\n      });\n    }\n  };\n\n  const handleSubmit = async () => {\n    try {\n      const values = await form.validateFields();\n      setLoading(true);\n\n      let response;\n      if (isEdit) {\n        response = await updateChatSession(initialValues!.Id, { ...initialValues, ...values });\n      } else {\n        response = await createChatSession(values);\n      }\n\n      if (response.success) {\n        onSuccess();\n      }\n      // 成功和错误消息会由统一响应处理系统自动显示\n    } catch (error) {\n      // 表单验证错误等\n      console.error('Form validation error:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Modal\n      title={intl.formatMessage({\n        id: isEdit ? 'pages.chat.form.edit.title' : 'pages.chat.form.create.title',\n      })}\n      open={visible}\n      onCancel={() => onVisibleChange(false)}\n      onOk={handleSubmit}\n      confirmLoading={loading}\n      width={600}\n    >\n      <Form\n        form={form}\n        layout=\"vertical\"\n        initialValues={{\n          IsActive: true,\n          Temperature: 0.7,\n          MaxTokens: 2000,\n        }}\n      >\n        <Form.Item\n          name=\"Name\"\n          label={<FormattedMessage id=\"pages.chat.form.name\" />}\n          rules={[{ required: true, message: intl.formatMessage({ id: 'pages.chat.form.name.required' }) }]}\n        >\n          <Input placeholder={intl.formatMessage({ id: 'pages.chat.form.name.placeholder' })} />\n        </Form.Item>\n\n        <Form.Item\n          name=\"ModelId\"\n          label={<FormattedMessage id=\"pages.chat.form.model\" />}\n          rules={[{ required: true, message: intl.formatMessage({ id: 'pages.chat.form.model.required' }) }]}\n        >\n          <Select placeholder={intl.formatMessage({ id: 'pages.chat.form.model.placeholder' })}>\n            {models.map((model) => (\n              <Select.Option key={model.Id} value={model.Id}>\n                {model.Name} ({model.ModelKey})\n              </Select.Option>\n            ))}\n          </Select>\n        </Form.Item>\n\n        <Form.Item\n          name=\"SystemPrompt\"\n          label={<FormattedMessage id=\"pages.chat.form.systemPrompt\" />}\n          help={intl.formatMessage({ id: 'pages.chat.form.systemPrompt.help' })}\n        >\n          <Input.TextArea\n            rows={4}\n            placeholder={intl.formatMessage({ id: 'pages.chat.form.systemPrompt.placeholder' })}\n          />\n        </Form.Item>\n\n        <Form.Item\n          name=\"Temperature\"\n          label={<FormattedMessage id=\"pages.chat.form.temperature\" />}\n          help={intl.formatMessage({ id: 'pages.chat.form.temperature.help' })}\n        >\n          <InputNumber\n            min={0}\n            max={2}\n            step={0.1}\n            style={{ width: '100%' }}\n            placeholder={intl.formatMessage({ id: 'pages.chat.form.temperature.placeholder' })}\n          />\n        </Form.Item>\n\n        <Form.Item\n          name=\"MaxTokens\"\n          label={<FormattedMessage id=\"pages.chat.form.maxTokens\" />}\n          help={intl.formatMessage({ id: 'pages.chat.form.maxTokens.help' })}\n        >\n          <InputNumber\n            min={1}\n            max={100000}\n            style={{ width: '100%' }}\n            placeholder={intl.formatMessage({ id: 'pages.chat.form.maxTokens.placeholder' })}\n          />\n        </Form.Item>\n\n        <Form.Item\n          name=\"IsActive\"\n          label={<FormattedMessage id=\"pages.chat.form.isActive\" />}\n          valuePropName=\"checked\"\n        >\n          <Switch />\n        </Form.Item>\n      </Form>\n    </Modal>\n  );\n};\n\nexport default SessionForm;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,UACA;IACE,SAAS;;;;;;wCCiLb;;;2BAAA;;;;;;oFApL2C;yCAC8B;wCAC/B;0CACwB;;;;;;;;;;YASlE,MAAM,cAA0C,CAAC,EAC/C,OAAO,EACP,eAAe,EACf,aAAa,EACb,SAAS,EACV;;gBACC,MAAM,OAAO,IAAA,YAAO;gBACpB,MAAM,CAAC,KAAK,GAAG,UAAI,CAAC,OAAO;gBAC3B,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;gBACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,eAAQ,EAAkB,EAAE;gBACxD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAuB;gBAEzE,MAAM,SAAS,CAAC,EAAC,0BAAA,oCAAA,cAAe,EAAE;gBAElC,IAAA,gBAAS,EAAC;oBACR,IAAI,SAAS;wBACX;wBACA,IAAI,eACF,KAAK,cAAc,CAAC;6BAEpB,KAAK,WAAW;oBAEpB;gBACF,GAAG;oBAAC;oBAAS;iBAAc;gBAE3B,MAAM,aAAa;oBACjB,MAAM,WAAW,MAAM,IAAA,kBAAW;oBAClC,IAAI,SAAS,OAAO,EAAE;wBACpB,aAAa;wBACb,MAAM,aAAa,AAAC,CAAA,SAAS,IAAI,IAAI,EAAE,AAAD,EAAG,MAAM,CAAC,CAAA;gCAAS;mCAAA,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,IAAI,MAAK,KAAK,MAAM,SAAS;;wBACtG,UAAU;oBACZ,OACE,UAAU,EAAE;gBAEd,qBAAqB;gBACvB;gBAiBA,MAAM,eAAe;oBACnB,IAAI;wBACF,MAAM,SAAS,MAAM,KAAK,cAAc;wBACxC,WAAW;wBAEX,IAAI;wBACJ,IAAI,QACF,WAAW,MAAM,IAAA,wBAAiB,EAAC,cAAe,EAAE,EAAE;4BAAE,GAAG,aAAa;4BAAE,GAAG,MAAM;wBAAC;6BAEpF,WAAW,MAAM,IAAA,wBAAiB,EAAC;wBAGrC,IAAI,SAAS,OAAO,EAClB;oBAEF,wBAAwB;oBAC1B,EAAE,OAAO,OAAO;wBACd,UAAU;wBACV,QAAQ,KAAK,CAAC,0BAA0B;oBAC1C,SAAU;wBACR,WAAW;oBACb;gBACF;gBAEA,qBACE,2BAAC,WAAK;oBACJ,OAAO,KAAK,aAAa,CAAC;wBACxB,IAAI,SAAS,+BAA+B;oBAC9C;oBACA,MAAM;oBACN,UAAU,IAAM,gBAAgB;oBAChC,MAAM;oBACN,gBAAgB;oBAChB,OAAO;8BAEP,cAAA,2BAAC,UAAI;wBACH,MAAM;wBACN,QAAO;wBACP,eAAe;4BACb,UAAU;4BACV,aAAa;4BACb,WAAW;wBACb;;0CAEA,2BAAC,UAAI,CAAC,IAAI;gCACR,MAAK;gCACL,qBAAO,2BAAC,qBAAgB;oCAAC,IAAG;;;;;;gCAC5B,OAAO;oCAAC;wCAAE,UAAU;wCAAM,SAAS,KAAK,aAAa,CAAC;4CAAE,IAAI;wCAAgC;oCAAG;iCAAE;0CAEjG,cAAA,2BAAC,WAAK;oCAAC,aAAa,KAAK,aAAa,CAAC;wCAAE,IAAI;oCAAmC;;;;;;;;;;;0CAGlF,2BAAC,UAAI,CAAC,IAAI;gCACR,MAAK;gCACL,qBAAO,2BAAC,qBAAgB;oCAAC,IAAG;;;;;;gCAC5B,OAAO;oCAAC;wCAAE,UAAU;wCAAM,SAAS,KAAK,aAAa,CAAC;4CAAE,IAAI;wCAAiC;oCAAG;iCAAE;0CAElG,cAAA,2BAAC,YAAM;oCAAC,aAAa,KAAK,aAAa,CAAC;wCAAE,IAAI;oCAAoC;8CAC/E,OAAO,GAAG,CAAC,CAAC,sBACX,2BAAC,YAAM,CAAC,MAAM;4CAAgB,OAAO,MAAM,EAAE;;gDAC1C,MAAM,IAAI;gDAAC;gDAAG,MAAM,QAAQ;gDAAC;;2CADZ,MAAM,EAAE;;;;;;;;;;;;;;;0CAOlC,2BAAC,UAAI,CAAC,IAAI;gCACR,MAAK;gCACL,qBAAO,2BAAC,qBAAgB;oCAAC,IAAG;;;;;;gCAC5B,MAAM,KAAK,aAAa,CAAC;oCAAE,IAAI;gCAAoC;0CAEnE,cAAA,2BAAC,WAAK,CAAC,QAAQ;oCACb,MAAM;oCACN,aAAa,KAAK,aAAa,CAAC;wCAAE,IAAI;oCAA2C;;;;;;;;;;;0CAIrF,2BAAC,UAAI,CAAC,IAAI;gCACR,MAAK;gCACL,qBAAO,2BAAC,qBAAgB;oCAAC,IAAG;;;;;;gCAC5B,MAAM,KAAK,aAAa,CAAC;oCAAE,IAAI;gCAAmC;0CAElE,cAAA,2BAAC,iBAAW;oCACV,KAAK;oCACL,KAAK;oCACL,MAAM;oCACN,OAAO;wCAAE,OAAO;oCAAO;oCACvB,aAAa,KAAK,aAAa,CAAC;wCAAE,IAAI;oCAA0C;;;;;;;;;;;0CAIpF,2BAAC,UAAI,CAAC,IAAI;gCACR,MAAK;gCACL,qBAAO,2BAAC,qBAAgB;oCAAC,IAAG;;;;;;gCAC5B,MAAM,KAAK,aAAa,CAAC;oCAAE,IAAI;gCAAiC;0CAEhE,cAAA,2BAAC,iBAAW;oCACV,KAAK;oCACL,KAAK;oCACL,OAAO;wCAAE,OAAO;oCAAO;oCACvB,aAAa,KAAK,aAAa,CAAC;wCAAE,IAAI;oCAAwC;;;;;;;;;;;0CAIlF,2BAAC,UAAI,CAAC,IAAI;gCACR,MAAK;gCACL,qBAAO,2BAAC,qBAAgB;oCAAC,IAAG;;;;;;gCAC5B,eAAc;0CAEd,cAAA,2BAAC,YAAM;;;;;;;;;;;;;;;;;;;;;YAKjB;eAtKM;;oBAMS,YAAO;oBACL,UAAI,CAAC;;;iBAPhB;gBAwKN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;IDjLD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,uCAAsC;YAAC;YAAU;SAAsC;QAAC,qBAAoB;YAAC;SAAS;QAAC,uBAAsB;YAAC;SAAW;QAAC,yBAAwB;YAAC;SAAa;QAAC,iCAAgC;YAAC;YAAS;SAAgC;QAAC,gCAA+B;YAAC;YAAS;SAA+B;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,iCAAgC;YAAC;YAAS;SAAgC;QAAC,qCAAoC;YAAC;YAAU;YAAS;SAAoC;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,mCAAkC;YAAC;YAAS;SAAuB;QAAC,sCAAqC;YAAC;YAAS;SAA0B;QAAC,iCAAgC;YAAC;YAAS;SAAsB;QAAC,+BAA8B;YAAC;YAAS;SAAoB;QAAC,qCAAoC;YAAC;SAAoC;QAAC,kCAAiC;YAAC;SAAiC;QAAC,kCAAiC;YAAC;YAAS;SAAwB;QAAC,qCAAoC;YAAC;YAAS;SAA2B;IAAA;;AAC9zC"}