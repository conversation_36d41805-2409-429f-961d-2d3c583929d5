import React, { useRef, useState } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import { ProTable, ActionType, ProColumns } from '@ant-design/pro-table';
import { Button, Space, Tag, Modal, Switch } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, ExperimentOutlined } from '@ant-design/icons';
import { useIntl, FormattedMessage } from '@umijs/max';
import { getAIModels, deleteAIModel } from '@/services/rwxai';
import { createProTableRequest, defaultPaginationConfig } from '@/utils/pageDataHandler';
import ModelForm from './components/ModelForm';
import ModelDetail from './components/ModelDetail';
import DataFormatTest from './components/DataFormatTest';

const AIModelsPage: React.FC = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>(null);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [testModalVisible, setTestModalVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<RwxAI.AIModel>();

  const handleDelete = async (record: RwxAI.AIModel) => {
    Modal.confirm({
      title: intl.formatMessage({ id: 'pages.aiModels.delete.confirm.title' }),
      content: intl.formatMessage({ id: 'pages.aiModels.delete.confirm.content' }),
      onOk: async () => {
        const response = await deleteAIModel(record.Id);
        if (response.success) {
          actionRef.current?.reload();
        }
        // 成功和错误消息会由统一响应处理系统自动显示
      },
    });
  };
  const getModelTypeTag = (type?: string | number) => {
    // 允许后端返回字符串枚举或数字枚举
    const normalized = typeof type === 'number'
      ? ({ 0: 'Chat', 1: 'Embedding', 2: 'TextToImage', 3: 'ImageToText' } as Record<number, string>)[type]
      : type;
    const typeMap: Record<string, { text: string; color: string }> = {
      Chat: { text: '对话模型', color: 'blue' },
      Embedding: { text: '嵌入模型', color: 'green' },
      TextToImage: { text: '文本生成图像模型', color: 'purple' },
      ImageToText: { text: '图像描述模型', color: 'orange' },
    };
    const info = normalized ? typeMap[normalized] : undefined;
    if (!info) return <Tag>-</Tag>;
    return <Tag color={info.color}>{info.text}</Tag>;
  };
  const columns: ProColumns<RwxAI.AIModel>[] = [
    {
      title: <FormattedMessage id="pages.aiModels.table.name" />,
      dataIndex: 'Name',
      key: 'Name',
      ellipsis: true,
      render: (_, record) => {
        // 优先显示 DisplayName，如果没有则显示 Name
        return record.DisplayName || record.Name || '-';
      },
    },
    {
      title: <FormattedMessage id="pages.aiModels.table.modelId" />,
      dataIndex: 'ModelId',
      key: 'ModelId',
      ellipsis: true,
    },
    {
      title: '端点',
      dataIndex: 'Endpoint',
      key: 'Endpoint',
      ellipsis: true,
      hideInSearch: true,
      width: 200,
      render: (text) => text || '-',
    },
    {
      title: <FormattedMessage id="pages.aiModels.table.modelType" />,
      dataIndex: 'ModelType',
      key: 'ModelType',
      render: (_, record) => {
        return getModelTypeTag((record as any).Template?.Type);
      },
    },
    {
      title: <FormattedMessage id="pages.aiModels.table.provider" />,
      dataIndex: 'Provider',
      key: 'Provider',
      ellipsis: true,
      render: (_, record) => {
        // 优先显示 DisplayName，如果没有则显示 Name
        const providerName = record.Template?.Provider?.DisplayName ||
          record.Template?.Provider?.Name ||
          '-';
        return providerName;
      },
    },
    {
      title: <FormattedMessage id="pages.aiModels.table.status" />,
      dataIndex: 'IsEnabled',
      key: 'IsEnabled',
      render: (_, record) => (
        <Switch
          checked={record.IsEnabled}
          size="small"
          disabled
        />
      ),
    },
    {
      title: '默认模型',
      dataIndex: 'IsDefault',
      key: 'IsDefault',
      hideInSearch: true,
      width: 100,
      render: (_, record) => (
        <Switch
          checked={record.IsDefault}
          size="small"
          disabled
        />
      ),
    },
    {
      title: <FormattedMessage id="pages.aiModels.table.maxTokens" />,
      dataIndex: 'MaxTokens',
      key: 'MaxTokens',
      hideInSearch: true,
      width: 120,
    },
    {
      title: <FormattedMessage id="pages.aiModels.table.temperature" />,
      dataIndex: 'Temperature',
      key: 'Temperature',
      hideInSearch: true,
      width: 120,
    },
    {
      title: <FormattedMessage id="pages.aiModels.table.createdTime" />,
      dataIndex: 'CreatedTime',
      key: 'CreatedTime',
      hideInSearch: true,
      valueType: 'dateTime',
      width: 180,
    },
    {
      title: <FormattedMessage id="pages.aiModels.table.actions" />,
      key: 'actions',
      hideInSearch: true,
      width: 200,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => {
              setCurrentRecord(record);
              setDetailModalVisible(true);
            }}
          >
            <FormattedMessage id="pages.common.view" />
          </Button>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => {
              setCurrentRecord(record);
              setEditModalVisible(true);
            }}
          >
            <FormattedMessage id="pages.common.edit" />
          </Button>
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          >
            <FormattedMessage id="pages.common.delete" />
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <PageContainer>
      <ProTable<RwxAI.AIModel>
        headerTitle={intl.formatMessage({ id: 'pages.aiModels.title' })}
        actionRef={actionRef}
        rowKey="Id"
        pagination={defaultPaginationConfig}
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => [
          <Button
            key="test"
            icon={<ExperimentOutlined />}
            onClick={() => setTestModalVisible(true)}
          >
            数据格式测试
          </Button>,
          <Button
            type="primary"
            key="primary"
            icon={<PlusOutlined />}
            onClick={() => setCreateModalVisible(true)}
          >
            <FormattedMessage id="pages.common.create" />
          </Button>,
        ]}
        request={createProTableRequest(getAIModels, {
          Name: 'Name',
          ModelId: 'ModelId',
          ProviderCode: 'ProviderCode',
          IsEnabled: 'IsEnabled',
        })}
        columns={columns}
      />

      <ModelForm
        visible={createModalVisible}
        onVisibleChange={setCreateModalVisible}
        onSuccess={() => {
          actionRef.current?.reload();
          setCreateModalVisible(false);
        }}
      />

      <ModelForm
        visible={editModalVisible}
        onVisibleChange={setEditModalVisible}
        initialValues={currentRecord}
        onSuccess={() => {
          actionRef.current?.reload();
          setEditModalVisible(false);
        }}
      />

      <ModelDetail
        visible={detailModalVisible}
        onVisibleChange={setDetailModalVisible}
        onEdit={() => {
          setEditModalVisible(true);
          setDetailModalVisible(false);
        }}
        data={currentRecord}
      />

      <Modal
        title="后台数据格式测试"
        open={testModalVisible}
        onCancel={() => setTestModalVisible(false)}
        footer={null}
        width={1200}
        style={{ top: 20 }}
      >
        <DataFormatTest />
      </Modal>
    </PageContainer>
  );
};

export default AIModelsPage;
