import React from 'react';
import { Modal, Descriptions } from 'antd';
import { useIntl, FormattedMessage } from '@umijs/max';

interface KnowledgeDetailProps {
  visible: boolean;
  onVisibleChange: (visible: boolean) => void;
  data?: RwxAI.Knowledge;
}

const KnowledgeDetail: React.FC<KnowledgeDetailProps> = ({
  visible,
  onVisibleChange,
  data,
}) => {
  const intl = useIntl();

  return (
    <Modal
      title={intl.formatMessage({ id: 'pages.knowledge.detail.title' })}
      open={visible}
      onCancel={() => onVisibleChange(false)}
      footer={null}
      width={800}
    >
      {data && (
        <Descriptions column={2} bordered>
          <Descriptions.Item
            label={<FormattedMessage id="pages.knowledge.detail.name" />}
            span={2}
          >
            {data.Name}
          </Descriptions.Item>
          
          <Descriptions.Item
            label={<FormattedMessage id="pages.knowledge.detail.description" />}
            span={2}
          >
            {data.Description || '-'}
          </Descriptions.Item>
          
          <Descriptions.Item
            label={<FormattedMessage id="pages.knowledge.detail.chunkSize" />}
          >
            {data.ChunkSize}
          </Descriptions.Item>
          
          <Descriptions.Item
            label={<FormattedMessage id="pages.knowledge.detail.chunkOverlap" />}
          >
            {data.ChunkOverlap}
          </Descriptions.Item>
          
          <Descriptions.Item
            label={<FormattedMessage id="pages.knowledge.detail.embeddingModelId" />}
            span={2}
          >
            {data.EmbeddingModelId || '-'}
          </Descriptions.Item>
          
          <Descriptions.Item
            label={<FormattedMessage id="pages.knowledge.detail.createdTime" />}
          >
            {data.CreatedTime ? new Date(data.CreatedTime).toLocaleString() : '-'}
          </Descriptions.Item>
          
          <Descriptions.Item
            label={<FormattedMessage id="pages.knowledge.detail.updatedTime" />}
          >
            {data.UpdatedTime ? new Date(data.UpdatedTime).toLocaleString() : '-'}
          </Descriptions.Item>
          
          <Descriptions.Item
            label={<FormattedMessage id="pages.knowledge.detail.metadataJson" />}
            span={2}
          >
            {data.MetadataJson ? (
              <pre style={{ whiteSpace: 'pre-wrap', fontSize: '12px' }}>
                {JSON.stringify(JSON.parse(data.MetadataJson), null, 2)}
              </pre>
            ) : (
              '-'
            )}
          </Descriptions.Item>
        </Descriptions>
      )}
    </Modal>
  );
};

export default KnowledgeDetail;
