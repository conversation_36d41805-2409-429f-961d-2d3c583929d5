import { httpRequest } from '@/utils/request';
import { getApiPrefix } from '@/utils/api';

const API_PREFIX: string = getApiPrefix();

/** 获取当前的用户 GET /api/currentUser */
export async function currentUser(options?: { [key: string]: any }) {
  return httpRequest.get<{
    success?: boolean;
    data?: API.CurrentUser;
  }>(`${API_PREFIX}/currentUser`, {
    ...(options || {}),
  });
}

/** 退出登录接口 POST /api/login/outLogin */
export async function outLogin(options?: { [key: string]: any }) {
  return httpRequest.post<Record<string, any>>(`${API_PREFIX}/login/outLogin`, {
    ...(options || {}),
  });
}

/** 登录接口 POST /api/login/account */
export async function login(body: API.LoginParams, options?: { [key: string]: any }) {
  return httpRequest.post<API.LoginResult>(`${API_PREFIX}/login/account`, {
    data: body,
    ...(options || {}),
  });
}

/** 发送验证码 POST /api/login/captcha */
export async function getFakeCaptcha(
  params: {
    /** 手机号 */
    phone?: string;
  },
  options?: { [key: string]: any },
) {
  return httpRequest.get<API.FakeCaptcha>(`${API_PREFIX}/login/captcha`, {
    params,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /api/notices */
export async function getNotices(options?: { [key: string]: any }) {
  return httpRequest.get<API.NoticeIconList>(`${API_PREFIX}/notices`, {
    ...(options || {}),
  });
}

/** 获取规则列表 GET /api/rule */
export async function rule(
  params: {
    /** 当前的页码 */
    current?: number;
    /** 页面的容量 */
    pageSize?: number;
  },
  options?: { [key: string]: any },
) {
  return httpRequest.get<API.RuleList>(`${API_PREFIX}/rule`, {
    params,
    ...(options || {}),
  });
}

/** 新建规则 PUT /api/rule */
export async function updateRule(options?: { [key: string]: any }) {
  return httpRequest.put<API.RuleListItem>(`${API_PREFIX}/rule`, {
    ...(options || {}),
  });
}

/** 新建规则 POST /api/rule */
export async function addRule(options?: { [key: string]: any }) {
  return httpRequest.post<API.RuleListItem>(`${API_PREFIX}/rule`, {
    ...(options || {}),
  });
}

/** 删除规则 DELETE /api/rule */
export async function removeRule(options?: { [key: string]: any }) {
  return httpRequest.delete<Record<string, any>>(`${API_PREFIX}/rule`, {
    ...(options || {}),
  });
}
