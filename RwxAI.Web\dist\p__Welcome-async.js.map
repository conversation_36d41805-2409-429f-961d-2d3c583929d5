{"version": 3, "sources": ["src/pages/Welcome.tsx"], "sourcesContent": ["import { PageContainer } from '@ant-design/pro-components';\r\nimport { useModel } from '@umijs/max';\r\nimport { Card, theme } from 'antd';\r\nimport React from 'react';\r\n\r\n/**\r\n * 每个单独的卡片，为了复用样式抽成了组件\r\n * @param param0\r\n * @returns\r\n */\r\nconst InfoCard: React.FC<{\r\n  title: string;\r\n  index: number;\r\n  desc: string;\r\n  href: string;\r\n}> = ({ title, href, index, desc }) => {\r\n  const { useToken } = theme;\r\n\r\n  const { token } = useToken();\r\n\r\n  return (\r\n    <div\r\n      style={{\r\n        backgroundColor: token.colorBgContainer,\r\n        boxShadow: token.boxShadow,\r\n        borderRadius: '8px',\r\n        fontSize: '14px',\r\n        color: token.colorTextSecondary,\r\n        lineHeight: '22px',\r\n        padding: '16px 19px',\r\n        minWidth: '220px',\r\n        flex: 1,\r\n      }}\r\n    >\r\n      <div\r\n        style={{\r\n          display: 'flex',\r\n          gap: '4px',\r\n          alignItems: 'center',\r\n        }}\r\n      >\r\n        <div\r\n          style={{\r\n            width: 48,\r\n            height: 48,\r\n            lineHeight: '22px',\r\n            backgroundSize: '100%',\r\n            textAlign: 'center',\r\n            padding: '8px 16px 16px 12px',\r\n            color: '#FFF',\r\n            fontWeight: 'bold',\r\n            backgroundImage:\r\n              \"url('https://gw.alipayobjects.com/zos/bmw-prod/daaf8d50-8e6d-4251-905d-676a24ddfa12.svg')\",\r\n          }}\r\n        >\r\n          {index}\r\n        </div>\r\n        <div\r\n          style={{\r\n            fontSize: '16px',\r\n            color: token.colorText,\r\n            paddingBottom: 8,\r\n          }}\r\n        >\r\n          {title}\r\n        </div>\r\n      </div>\r\n      <div\r\n        style={{\r\n          fontSize: '14px',\r\n          color: token.colorTextSecondary,\r\n          textAlign: 'justify',\r\n          lineHeight: '22px',\r\n          marginBottom: 8,\r\n        }}\r\n      >\r\n        {desc}\r\n      </div>\r\n      <a href={href} target=\"_blank\" rel=\"noreferrer\">\r\n        了解更多 {'>'}\r\n      </a>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst Welcome: React.FC = () => {\r\n  const { token } = theme.useToken();\r\n  const { initialState } = useModel('@@initialState');\r\n  return (\r\n    <PageContainer>\r\n      <Card\r\n        style={{\r\n          borderRadius: 8,\r\n        }}\r\n        styles={{\r\n          body: {\r\n            backgroundImage:\r\n              initialState?.settings?.navTheme === 'realDark'\r\n                ? 'background-image: linear-gradient(75deg, #1A1B1F 0%, #191C1F 100%)'\r\n                : 'background-image: linear-gradient(75deg, #FBFDFF 0%, #F5F7FF 100%)',\r\n          },\r\n        }}\r\n      >\r\n        <div\r\n          style={{\r\n            backgroundPosition: '100% -30%',\r\n            backgroundRepeat: 'no-repeat',\r\n            backgroundSize: '274px auto',\r\n            backgroundImage:\r\n              \"url('https://gw.alipayobjects.com/mdn/rms_a9745b/afts/img/A*BuFmQqsB2iAAAAAAAAAAAAAAARQnAQ')\",\r\n          }}\r\n        >\r\n          <div\r\n            style={{\r\n              fontSize: '20px',\r\n              color: token.colorTextHeading,\r\n            }}\r\n          >\r\n            欢迎使用 Ant Design Pro\r\n          </div>\r\n          <p\r\n            style={{\r\n              fontSize: '14px',\r\n              color: token.colorTextSecondary,\r\n              lineHeight: '22px',\r\n              marginTop: 16,\r\n              marginBottom: 32,\r\n              width: '65%',\r\n            }}\r\n          >\r\n            Ant Design Pro 是一个整合了 umi，Ant Design 和 ProComponents\r\n            的脚手架方案。致力于在设计规范和基础组件的基础上，继续向上构建，提炼出典型模板/业务组件/配套设计资源，进一步提升企业级中后台产品设计研发过程中的『用户』和『设计者』的体验。\r\n          </p>\r\n          <div\r\n            style={{\r\n              display: 'flex',\r\n              flexWrap: 'wrap',\r\n              gap: 16,\r\n            }}\r\n          >\r\n            <InfoCard\r\n              index={1}\r\n              href=\"https://umijs.org/docs/introduce/introduce\"\r\n              title=\"了解 umi\"\r\n              desc=\"umi 是一个可扩展的企业级前端应用框架,umi 以路由为基础的，同时支持配置式路由和约定式路由，保证路由的功能完备，并以此进行功能扩展。\"\r\n            />\r\n            <InfoCard\r\n              index={2}\r\n              title=\"了解 ant design\"\r\n              href=\"https://ant.design\"\r\n              desc=\"antd 是基于 Ant Design 设计体系的 React UI 组件库，主要用于研发企业级中后台产品。\"\r\n            />\r\n            <InfoCard\r\n              index={3}\r\n              title=\"了解 Pro Components\"\r\n              href=\"https://procomponents.ant.design\"\r\n              desc=\"ProComponents 是一个基于 Ant Design 做了更高抽象的模板组件，以 一个组件就是一个页面为开发理念，为中后台开发带来更好的体验。\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </Card>\r\n    </PageContainer>\r\n  );\r\n};\r\n\r\nexport default Welcome;\r\n"], "names": [], "mappings": ";;;;;;;4BAqKA;;;eAAA;;;;;;;sCArK8B;4BACL;6BACG;uEACV;;;;;;;;;;;AAElB;;;;CAIC,GACD,MAAM,WAKD,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE;;IAChC,MAAM,EAAE,QAAQ,EAAE,GAAG,WAAK;IAE1B,MAAM,EAAE,KAAK,EAAE,GAAG;IAElB,qBACE,2BAAC;QACC,OAAO;YACL,iBAAiB,MAAM,gBAAgB;YACvC,WAAW,MAAM,SAAS;YAC1B,cAAc;YACd,UAAU;YACV,OAAO,MAAM,kBAAkB;YAC/B,YAAY;YACZ,SAAS;YACT,UAAU;YACV,MAAM;QACR;;0BAEA,2BAAC;gBACC,OAAO;oBACL,SAAS;oBACT,KAAK;oBACL,YAAY;gBACd;;kCAEA,2BAAC;wBACC,OAAO;4BACL,OAAO;4BACP,QAAQ;4BACR,YAAY;4BACZ,gBAAgB;4BAChB,WAAW;4BACX,SAAS;4BACT,OAAO;4BACP,YAAY;4BACZ,iBACE;wBACJ;kCAEC;;;;;;kCAEH,2BAAC;wBACC,OAAO;4BACL,UAAU;4BACV,OAAO,MAAM,SAAS;4BACtB,eAAe;wBACjB;kCAEC;;;;;;;;;;;;0BAGL,2BAAC;gBACC,OAAO;oBACL,UAAU;oBACV,OAAO,MAAM,kBAAkB;oBAC/B,WAAW;oBACX,YAAY;oBACZ,cAAc;gBAChB;0BAEC;;;;;;0BAEH,2BAAC;gBAAE,MAAM;gBAAM,QAAO;gBAAS,KAAI;;oBAAa;oBACxC;;;;;;;;;;;;;AAId;GAzEM;KAAA;AA2EN,MAAM,UAAoB;QAYZ;;IAXZ,MAAM,EAAE,KAAK,EAAE,GAAG,WAAK,CAAC,QAAQ;IAChC,MAAM,EAAE,YAAY,EAAE,GAAG,IAAA,aAAQ,EAAC;IAClC,qBACE,2BAAC,4BAAa;kBACZ,cAAA,2BAAC,UAAI;YACH,OAAO;gBACL,cAAc;YAChB;YACA,QAAQ;gBACN,MAAM;oBACJ,iBACE,CAAA,yBAAA,oCAAA,yBAAA,aAAc,QAAQ,cAAtB,6CAAA,uBAAwB,QAAQ,MAAK,aACjC,uEACA;gBACR;YACF;sBAEA,cAAA,2BAAC;gBACC,OAAO;oBACL,oBAAoB;oBACpB,kBAAkB;oBAClB,gBAAgB;oBAChB,iBACE;gBACJ;;kCAEA,2BAAC;wBACC,OAAO;4BACL,UAAU;4BACV,OAAO,MAAM,gBAAgB;wBAC/B;kCACD;;;;;;kCAGD,2BAAC;wBACC,OAAO;4BACL,UAAU;4BACV,OAAO,MAAM,kBAAkB;4BAC/B,YAAY;4BACZ,WAAW;4BACX,cAAc;4BACd,OAAO;wBACT;kCACD;;;;;;kCAID,2BAAC;wBACC,OAAO;4BACL,SAAS;4BACT,UAAU;4BACV,KAAK;wBACP;;0CAEA,2BAAC;gCACC,OAAO;gCACP,MAAK;gCACL,OAAM;gCACN,MAAK;;;;;;0CAEP,2BAAC;gCACC,OAAO;gCACP,OAAM;gCACN,MAAK;gCACL,MAAK;;;;;;0CAEP,2BAAC;gCACC,OAAO;gCACP,OAAM;gCACN,MAAK;gCACL,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnB;IA9EM;;QACc,WAAK,CAAC;QACC,aAAQ;;;MAF7B;IAgFN,WAAe"}