import React, { useRef, useState } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import { ProTable, ActionType, ProColumns } from '@ant-design/pro-table';
import { Button, Space, Tag, message, Modal, Switch, Tooltip } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, KeyOutlined, ReloadOutlined } from '@ant-design/icons';
import { useIntl, FormattedMessage } from '@umijs/max';
import { getMyApps, deleteApp, updateAppStatus, regenerateApiKeys, resetApiCalls } from '@/services/rwxai';
import { createSimpleProTableRequest, defaultPaginationConfig } from '@/utils/pageDataHandler';
import AppForm from './components/AppForm';
import AppDetail from './components/AppDetail';

const AppsPage: React.FC = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<RwxAI.App>();

  const handleDelete = async (record: RwxAI.App) => {
    Modal.confirm({
      title: intl.formatMessage({ id: 'pages.apps.delete.confirm.title' }),
      content: intl.formatMessage({ id: 'pages.apps.delete.confirm.content' }),
      onOk: async () => {
        try {
          await deleteApp(record.Id);
          message.success(intl.formatMessage({ id: 'pages.apps.delete.success' }));
          actionRef.current?.reload();
        } catch (error) {
          message.error(intl.formatMessage({ id: 'pages.apps.delete.error' }));
        }
      },
    });
  };

  const handleStatusChange = async (record: RwxAI.App, checked: boolean) => {
    try {
      await updateAppStatus(record.Id, { IsEnabled: checked });
      message.success(intl.formatMessage({ id: 'pages.apps.status.update.success' }));
      actionRef.current?.reload();
    } catch (error) {
      message.error(intl.formatMessage({ id: 'pages.apps.status.update.error' }));
    }
  };

  const handleRegenerateKeys = async (record: RwxAI.App) => {
    Modal.confirm({
      title: intl.formatMessage({ id: 'pages.apps.regenerateKeys.confirm.title' }),
      content: intl.formatMessage({ id: 'pages.apps.regenerateKeys.confirm.content' }),
      onOk: async () => {
        try {
          await regenerateApiKeys(record.Id);
          message.success(intl.formatMessage({ id: 'pages.apps.regenerateKeys.success' }));
          actionRef.current?.reload();
        } catch (error) {
          message.error(intl.formatMessage({ id: 'pages.apps.regenerateKeys.error' }));
        }
      },
    });
  };

  const handleResetApiCalls = async (record: RwxAI.App) => {
    Modal.confirm({
      title: intl.formatMessage({ id: 'pages.apps.resetApiCalls.confirm.title' }),
      content: intl.formatMessage({ id: 'pages.apps.resetApiCalls.confirm.content' }),
      onOk: async () => {
        try {
          await resetApiCalls(record.Id);
          message.success(intl.formatMessage({ id: 'pages.apps.resetApiCalls.success' }));
          actionRef.current?.reload();
        } catch (error) {
          message.error(intl.formatMessage({ id: 'pages.apps.resetApiCalls.error' }));
        }
      },
    });
  };

  const columns: ProColumns<RwxAI.App>[] = [
    {
      title: <FormattedMessage id="pages.apps.table.name" />,
      dataIndex: 'Name',
      key: 'Name',
      ellipsis: true,
    },
    {
      title: <FormattedMessage id="pages.apps.table.description" />,
      dataIndex: 'Description',
      key: 'Description',
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: <FormattedMessage id="pages.apps.table.status" />,
      dataIndex: 'IsEnabled',
      key: 'IsEnabled',
      render: (_, record) => (
        <Switch
          checked={record.IsEnabled}
          size="small"
          onChange={(checked) => handleStatusChange(record, checked)}
        />
      ),
    },
    {
      title: <FormattedMessage id="pages.apps.table.apiCalls" />,
      key: 'apiCalls',
      hideInSearch: true,
      render: (_, record) => (
        <span>
          {record.ApiCallCount || 0} / {record.MaxApiCalls || '∞'}
        </span>
      ),
    },
    {
      title: <FormattedMessage id="pages.apps.table.rateLimit" />,
      dataIndex: 'RateLimitPerMinute',
      key: 'RateLimitPerMinute',
      hideInSearch: true,
      render: (value) => value ? `${value}/min` : '-',
    },
    {
      title: <FormattedMessage id="pages.apps.table.createdTime" />,
      dataIndex: 'CreatedTime',
      key: 'CreatedTime',
      valueType: 'dateTime',
      width: 180,
      hideInSearch: true,
    },
    {
      title: <FormattedMessage id="pages.apps.table.actions" />,
      key: 'actions',
      width: 280,
      render: (_, record) => (
        <Space>
          <Tooltip title={intl.formatMessage({ id: 'pages.apps.actions.view' })}>
            <Button
              type="link"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => {
                setCurrentRecord(record);
                setDetailModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title={intl.formatMessage({ id: 'pages.apps.actions.edit' })}>
            <Button
              type="link"
              size="small"
              icon={<EditOutlined />}
              onClick={() => {
                setCurrentRecord(record);
                setEditModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title={intl.formatMessage({ id: 'pages.apps.actions.regenerateKeys' })}>
            <Button
              type="link"
              size="small"
              icon={<KeyOutlined />}
              onClick={() => handleRegenerateKeys(record)}
            />
          </Tooltip>
          <Tooltip title={intl.formatMessage({ id: 'pages.apps.actions.resetApiCalls' })}>
            <Button
              type="link"
              size="small"
              icon={<ReloadOutlined />}
              onClick={() => handleResetApiCalls(record)}
            />
          </Tooltip>
          <Tooltip title={intl.formatMessage({ id: 'pages.apps.actions.delete' })}>
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDelete(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <PageContainer>
      <ProTable<RwxAI.App>
        headerTitle={intl.formatMessage({ id: 'pages.apps.title' })}
        actionRef={actionRef}
        rowKey="Id"
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            icon={<PlusOutlined />}
            onClick={() => setCreateModalVisible(true)}
          >
            <FormattedMessage id="pages.apps.actions.create" />
          </Button>,
        ]}
        request={createSimpleProTableRequest(getMyApps)}
        columns={columns}
      />

      <AppForm
        visible={createModalVisible}
        onVisibleChange={setCreateModalVisible}
        onSuccess={() => {
          actionRef.current?.reload();
          setCreateModalVisible(false);
        }}
      />

      <AppForm
        visible={editModalVisible}
        onVisibleChange={setEditModalVisible}
        initialValues={currentRecord}
        onSuccess={() => {
          actionRef.current?.reload();
          setEditModalVisible(false);
        }}
      />

      <AppDetail
        visible={detailModalVisible}
        onVisibleChange={setDetailModalVisible}
        data={currentRecord}
      />
    </PageContainer>
  );
};

export default AppsPage;
