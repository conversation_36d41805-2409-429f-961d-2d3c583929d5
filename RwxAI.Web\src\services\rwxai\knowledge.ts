import { httpRequest } from '@/utils/request';
import { getApiPrefix } from '@/utils/api';
import { ResponseHandleResult } from '@/types/response';

const API_PREFIX: string = getApiPrefix();

/**
 * 知识库管理相关API
 */

// 获取所有知识库列表
export async function getKnowledgeBases(): Promise<ResponseHandleResult<RwxAI.Knowledge[]>> {
  return httpRequest.get<RwxAI.Knowledge[]>(`${API_PREFIX}/Knowledge`, {
    showErrorNotification: true,
  });
}

// 创建新的知识库
export async function createKnowledgeBase(data: RwxAI.Knowledge): Promise<ResponseHandleResult<RwxAI.Knowledge>> {
  return httpRequest.post<RwxAI.Knowledge>(`${API_PREFIX}/Knowledge`, data, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: '创建知识库成功',
  });
}

// 根据ID获取指定知识库
export async function getKnowledgeBaseById(id: string): Promise<ResponseHandleResult<RwxAI.Knowledge>> {
  return httpRequest.get<RwxAI.Knowledge>(`${API_PREFIX}/Knowledge/${id}`, {
    showErrorNotification: true,
  });
}

// 更新知识库信息
export async function updateKnowledgeBase(id: string, data: RwxAI.Knowledge): Promise<ResponseHandleResult<RwxAI.Knowledge>> {
  return httpRequest.put<RwxAI.Knowledge>(`${API_PREFIX}/Knowledge/${id}`, data, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: '更新知识库成功',
  });
}

// 删除指定的知识库
export async function deleteKnowledgeBase(id: string): Promise<ResponseHandleResult<void>> {
  return httpRequest.delete<void>(`${API_PREFIX}/Knowledge/${id}`, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: '删除知识库成功',
  });
}

// 获取知识库中的所有文件
export async function getKnowledgeFiles(knowledgeId: string): Promise<ResponseHandleResult<RwxAI.KnowledgeFile[]>> {
  return httpRequest.get<RwxAI.KnowledgeFile[]>(`${API_PREFIX}/Knowledge/${knowledgeId}/files`, {
    showErrorNotification: true,
  });
}

// 上传文件到知识库
export async function uploadKnowledgeFile(knowledgeId: string, file: File): Promise<ResponseHandleResult<RwxAI.KnowledgeFile>> {
  const formData = new FormData();
  formData.append('file', file);

  // 使用fetch直接上传文件，因为httpRequest可能不支持FormData
  try {
    const response = await fetch(`${API_PREFIX}/Knowledge/${knowledgeId}/files`, {
      method: 'POST',
      body: formData,
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('rwxai_token')}`,
      },
    });

    if (response.ok) {
      const data = await response.json();
      return { success: true, data, message: '文件上传成功' };
    } else {
      const errorData = await response.json().catch(() => ({}));
      return { success: false, message: errorData.message || '文件上传失败' };
    }
  } catch (error: any) {
    return { success: false, message: error.message || '文件上传失败' };
  }
}

// 处理知识库文件（解析和向量化）
export async function processKnowledgeFile(fileId: string): Promise<ResponseHandleResult<void>> {
  return httpRequest.post<void>(`${API_PREFIX}/Knowledge/files/${fileId}/process`, {}, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: '文件处理成功',
  });
}

// 删除知识库文件
export async function deleteKnowledgeFile(fileId: string): Promise<ResponseHandleResult<void>> {
  return httpRequest.delete<void>(`${API_PREFIX}/Knowledge/files/${fileId}`, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: '删除文件成功',
  });
}
