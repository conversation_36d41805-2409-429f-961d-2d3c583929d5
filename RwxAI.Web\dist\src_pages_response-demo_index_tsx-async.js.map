{"version": 3, "sources": ["src/pages/response-demo/index.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { PageContainer } from '@ant-design/pro-components';\nimport { Card, Button, Space, message, Alert, Divider } from 'antd';\nimport { useIntl, FormattedMessage } from '@umijs/max';\n\nconst ResponseDemo: React.FC = () => {\n  const intl = useIntl();\n  const [loading, setLoading] = useState(false);\n\n  const handleSuccessDemo = () => {\n    setLoading(true);\n    setTimeout(() => {\n      message.success('操作成功！');\n      setLoading(false);\n    }, 1000);\n  };\n\n  const handleErrorDemo = () => {\n    setLoading(true);\n    setTimeout(() => {\n      message.error('操作失败！');\n      setLoading(false);\n    }, 1000);\n  };\n\n  const handleWarningDemo = () => {\n    setLoading(true);\n    setTimeout(() => {\n      message.warning('警告信息！');\n      setLoading(false);\n    }, 1000);\n  };\n\n  return (\n    <PageContainer\n      title=\"响应处理演示\"\n      subTitle=\"演示各种响应处理效果\"\n    >\n      <Card title=\"消息提示演示\">\n        <Alert\n          message=\"这是一个演示页面\"\n          description=\"用于测试各种响应处理效果，包括成功、失败、警告等消息提示。\"\n          type=\"info\"\n          showIcon\n          style={{ marginBottom: 16 }}\n        />\n        \n        <Space>\n          <Button \n            type=\"primary\" \n            loading={loading}\n            onClick={handleSuccessDemo}\n          >\n            成功消息\n          </Button>\n          <Button \n            danger \n            loading={loading}\n            onClick={handleErrorDemo}\n          >\n            错误消息\n          </Button>\n          <Button \n            loading={loading}\n            onClick={handleWarningDemo}\n          >\n            警告消息\n          </Button>\n        </Space>\n\n        <Divider />\n\n        <Alert\n          message=\"开发说明\"\n          description=\"这个页面是为了解决路由配置中缺失的组件而创建的临时页面。在实际开发中，您可以根据需要修改或删除此页面。\"\n          type=\"warning\"\n          showIcon\n        />\n      </Card>\n    </PageContainer>\n  );\n};\n\nexport default ResponseDemo;\n"], "names": [], "mappings": ";;;;;;;4BAmFA;;;eAAA;;;;;;wEAnFgC;sCACF;6BAC+B;4BACnB;;;;;;;;;;AAE1C,MAAM,eAAyB;;IAChB,IAAA,YAAO;IACpB,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IAEvC,MAAM,oBAAoB;QACxB,WAAW;QACX,WAAW;YACT,aAAO,CAAC,OAAO,CAAC;YAChB,WAAW;QACb,GAAG;IACL;IAEA,MAAM,kBAAkB;QACtB,WAAW;QACX,WAAW;YACT,aAAO,CAAC,KAAK,CAAC;YACd,WAAW;QACb,GAAG;IACL;IAEA,MAAM,oBAAoB;QACxB,WAAW;QACX,WAAW;YACT,aAAO,CAAC,OAAO,CAAC;YAChB,WAAW;QACb,GAAG;IACL;IAEA,qBACE,2BAAC,4BAAa;QACZ,OAAM;QACN,UAAS;kBAET,cAAA,2BAAC,UAAI;YAAC,OAAM;;8BACV,2BAAC,WAAK;oBACJ,SAAQ;oBACR,aAAY;oBACZ,MAAK;oBACL,QAAQ;oBACR,OAAO;wBAAE,cAAc;oBAAG;;;;;;8BAG5B,2BAAC,WAAK;;sCACJ,2BAAC,YAAM;4BACL,MAAK;4BACL,SAAS;4BACT,SAAS;sCACV;;;;;;sCAGD,2BAAC,YAAM;4BACL,MAAM;4BACN,SAAS;4BACT,SAAS;sCACV;;;;;;sCAGD,2BAAC,YAAM;4BACL,SAAS;4BACT,SAAS;sCACV;;;;;;;;;;;;8BAKH,2BAAC,aAAO;;;;;8BAER,2BAAC,WAAK;oBACJ,SAAQ;oBACR,aAAY;oBACZ,MAAK;oBACL,QAAQ;;;;;;;;;;;;;;;;;AAKlB;GA5EM;;QACS,YAAO;;;KADhB;IA8EN,WAAe"}