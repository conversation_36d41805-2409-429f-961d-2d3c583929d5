globalThis.makoModuleHotUpdate('src/pages/chat/chat-interface.tsx', {
    modules: {
        "src/pages/chat/chat-interface.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _ChatInterface = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/chat/components/ChatInterface.tsx"));
            var _SessionForm = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/chat/components/SessionForm.tsx"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const ChatInterfacePage = ()=>{
                _s();
                (0, _max.useIntl)();
                const [sessionFormVisible, setSessionFormVisible] = (0, _react.useState)(false);
                const [editingSession, setEditingSession] = (0, _react.useState)(null);
                const handleCreateSession = ()=>{
                    setEditingSession(null);
                    setSessionFormVisible(true);
                };
                const handleEditSession = (session)=>{
                    setEditingSession(session);
                    setSessionFormVisible(true);
                };
                const handleSessionFormClose = ()=>{
                    setSessionFormVisible(false);
                    setEditingSession(null);
                };
                const handleSessionFormSuccess = ()=>{
                    setSessionFormVisible(false);
                    setEditingSession(null);
                    // 这里可以触发聊天界面的刷新
                    window.location.reload();
                };
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        height: '100vh',
                        overflow: 'hidden'
                    },
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_ChatInterface.default, {
                            onCreateSession: handleCreateSession,
                            onEditSession: handleEditSession
                        }, void 0, false, {
                            fileName: "src/pages/chat/chat-interface.tsx",
                            lineNumber: 36,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                            title: editingSession ? '编辑会话' : '创建新会话',
                            open: sessionFormVisible,
                            onCancel: handleSessionFormClose,
                            footer: null,
                            width: 600,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_SessionForm.default, {
                                visible: sessionFormVisible,
                                onVisibleChange: setSessionFormVisible,
                                onSuccess: handleSessionFormSuccess,
                                initialValues: editingSession
                            }, void 0, false, {
                                fileName: "src/pages/chat/chat-interface.tsx",
                                lineNumber: 48,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/chat/chat-interface.tsx",
                            lineNumber: 41,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/chat/chat-interface.tsx",
                    lineNumber: 35,
                    columnNumber: 5
                }, this);
            };
            _s(ChatInterfacePage, "8Z1Ttdwxe6VfYz4VsHKIvkp6RxM=", false, function() {
                return [
                    _max.useIntl
                ];
            });
            _c = ChatInterfacePage;
            var _default = ChatInterfacePage;
            var _c;
            $RefreshReg$(_c, "ChatInterfacePage");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '16922430050210772433';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/.umi/plugin-openapi/openapi.tsx": [
            "vendors",
            "src/.umi/plugin-openapi/openapi.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Admin.tsx": [
            "p__Admin"
        ],
        "src/pages/Welcome.tsx": [
            "p__Welcome"
        ],
        "src/pages/ai-models/index.tsx": [
            "common",
            "src/pages/ai-models/index.tsx"
        ],
        "src/pages/api-test/index.tsx": [
            "common",
            "src/pages/api-test/index.tsx"
        ],
        "src/pages/apps/index.tsx": [
            "common",
            "p__apps__index"
        ],
        "src/pages/auth-test/index.tsx": [
            "common",
            "src/pages/auth-test/index.tsx"
        ],
        "src/pages/chat/chat-interface.tsx": [
            "vendors",
            "common",
            "src/pages/chat/chat-interface.tsx"
        ],
        "src/pages/chat/index.tsx": [
            "common",
            "p__chat__index"
        ],
        "src/pages/chat/session/[id].tsx": [
            "common",
            "p__chat__session__id"
        ],
        "src/pages/knowledge/[id]/files.tsx": [
            "common",
            "p__knowledge__id__files"
        ],
        "src/pages/knowledge/index.tsx": [
            "common",
            "p__knowledge__index"
        ],
        "src/pages/plugins/index.tsx": [
            "common",
            "p__plugins__index"
        ],
        "src/pages/response-demo/index.tsx": [
            "src/pages/response-demo/index.tsx"
        ],
        "src/pages/table-list/index.tsx": [
            "src/pages/table-list/index.tsx"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ],
        "src/pages/user/register/index.tsx": [
            "common",
            "p__user__register__index"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_chat_chat-interface_tsx-async.15970108932533672692.hot-update.js.map