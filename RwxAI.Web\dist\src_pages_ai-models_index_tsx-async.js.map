{"version": 3, "sources": ["src/pages/ai-models/components/DataFormatTest.tsx", "src/pages/ai-models/components/ModelDetail.tsx", "src/pages/ai-models/components/ModelForm.tsx", "src/pages/ai-models/index.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { Card, Typography, Table, Tag, Switch, Button, Space } from 'antd';\r\nimport { createProTableRequest, buildPagedQuery, handlePagedResponse } from '@/utils/pageDataHandler';\r\n\r\nconst { Title, Paragraph, Text } = Typography;\r\n\r\n// 模拟您提供的后台返回数据格式\r\nconst mockBackendResponse = {\r\n  \"Items\": [\r\n    {\r\n      \"Id\": \"3fa85f64-5717-4562-b3fc-2c963f66afa6\",\r\n      \"ModelTemplateId\": \"cd90513b-e7bb-4710-b46b-f302b818a779\",\r\n      \"Name\": \"deepseek\",\r\n      \"ModelId\": \"deepseek-chat\",\r\n      \"Endpoint\": \"https://api.deepseek.com\",\r\n      \"ProviderCode\": \"\",\r\n      \"DisplayName\": \"deepseek-chat\",\r\n      \"Description\": \"string\",\r\n      \"ApiKey\": \"sk-ab6f4de3f1f1454facae3428554f7f36\",\r\n      \"IsEnabled\": true,\r\n      \"IsDefault\": true,\r\n      \"MaxTokens\": 0,\r\n      \"Temperature\": 0,\r\n      \"CreatedTime\": \"2025-05-04T22:31:39.557\",\r\n      \"UpdatedTime\": \"2025-05-04T14:31:27.313\",\r\n      \"Template\": {\r\n        \"Id\": \"cd90513b-e7bb-4710-b46b-f302b818a779\",\r\n        \"Name\": \"deepseek-chat\",\r\n        \"DisplayName\": \"deepseek-chat\",\r\n        \"ModelId\": \"deepseek-chat\",\r\n        \"Endpoint\": \"https://api.deepseek.com/v1\",\r\n        \"Type\": \"Chat\",\r\n        \"Description\": \"deepseek的聊天模型\",\r\n        \"MaxContextLength\": 32768,\r\n        \"MaxOutputLength\": 4096,\r\n        \"SupportsStreaming\": true,\r\n        \"SupportsFunctionCalling\": true,\r\n        \"SupportsVision\": false,\r\n        \"Notes\": null,\r\n        \"Provider\": {\r\n          \"Id\": \"55555555-5555-5555-5555-555555555555\",\r\n          \"Name\": \"DeepSeek\",\r\n          \"DisplayName\": \"DeepSeek\",\r\n          \"Code\": \"DeepSeek\",\r\n          \"Description\": \"DeepSeek\",\r\n          \"Website\": \"https://www.deepseek.com/\",\r\n          \"IconUrl\": null\r\n        }\r\n      }\r\n    }\r\n  ],\r\n  \"TotalCount\": 1,\r\n  \"PageNumber\": 1,\r\n  \"PageSize\": 20,\r\n  \"TotalPages\": 1,\r\n  \"HasPreviousPage\": false,\r\n  \"HasNextPage\": false\r\n};\r\n\r\nconst DataFormatTest: React.FC = () => {\r\n  const [testResults, setTestResults] = React.useState<any[]>([]);\r\n\r\n  const testUnifiedHandler = () => {\r\n    // 测试buildPagedQuery函数\r\n    const testParams = {\r\n      current: 1,\r\n      pageSize: 20,\r\n      Name: 'deepseek',\r\n      ModelId: 'deepseek-chat',\r\n      keyWord: 'test search'\r\n    };\r\n\r\n    const fieldMapping = {\r\n      Name: 'Name',\r\n      ModelId: 'ModelId',\r\n      ProviderCode: 'ProviderCode',\r\n      IsEnabled: 'IsEnabled',\r\n    };\r\n\r\n    const query = buildPagedQuery(testParams, fieldMapping);\r\n\r\n    // 测试handlePagedResponse函数\r\n    const mockResponse = {\r\n      success: true,\r\n      data: mockBackendResponse\r\n    };\r\n\r\n    const result = handlePagedResponse(mockResponse);\r\n\r\n    setTestResults([\r\n      {\r\n        function: 'buildPagedQuery',\r\n        input: { testParams, fieldMapping },\r\n        output: query\r\n      },\r\n      {\r\n        function: 'handlePagedResponse',\r\n        input: mockResponse,\r\n        output: result\r\n      }\r\n    ]);\r\n  };\r\n\r\n  const getModelTypeTag = (type?: string) => {\r\n    const typeMap: Record<string, { text: string; color: string }> = {\r\n      'Chat': { text: '对话模型', color: 'blue' },\r\n      'Embedding': { text: '嵌入模型', color: 'green' },\r\n      'TextToImage': { text: '文本生成图像模型', color: 'purple' },\r\n      'ImageToText': { text: '图像描述模型', color: 'orange' },\r\n    };\r\n    const typeInfo = typeMap[type || ''];\r\n    return typeInfo ? <Tag color={typeInfo.color}>{typeInfo.text}</Tag> : <Tag>-</Tag>;\r\n  };\r\n\r\n  const columns = [\r\n    {\r\n      title: '模型名称',\r\n      dataIndex: 'Name',\r\n      key: 'Name',\r\n      render: (_: any, record: any) => {\r\n        return record.DisplayName || record.Name || '-';\r\n      },\r\n    },\r\n    {\r\n      title: '模型ID',\r\n      dataIndex: 'ModelId',\r\n      key: 'ModelId',\r\n    },\r\n    {\r\n      title: '端点',\r\n      dataIndex: 'Endpoint',\r\n      key: 'Endpoint',\r\n      render: (text: string) => text || '-',\r\n    },\r\n    {\r\n      title: '模型类型',\r\n      dataIndex: 'ModelType',\r\n      key: 'ModelType',\r\n      render: (_: any, record: any) => {\r\n        return getModelTypeTag(record.Template?.Type);\r\n      },\r\n    },\r\n    {\r\n      title: '提供商',\r\n      dataIndex: 'Provider',\r\n      key: 'Provider',\r\n      render: (_: any, record: any) => {\r\n        const providerName = record.Template?.Provider?.DisplayName ||\r\n          record.Template?.Provider?.Name ||\r\n          '-';\r\n        return providerName;\r\n      },\r\n    },\r\n    {\r\n      title: '状态',\r\n      dataIndex: 'IsEnabled',\r\n      key: 'IsEnabled',\r\n      render: (_: any, record: any) => (\r\n        <Switch\r\n          checked={record.IsEnabled}\r\n          size=\"small\"\r\n          disabled\r\n        />\r\n      ),\r\n    },\r\n    {\r\n      title: '默认模型',\r\n      dataIndex: 'IsDefault',\r\n      key: 'IsDefault',\r\n      render: (_: any, record: any) => (\r\n        <Switch\r\n          checked={record.IsDefault}\r\n          size=\"small\"\r\n          disabled\r\n        />\r\n      ),\r\n    },\r\n    {\r\n      title: '最大令牌',\r\n      dataIndex: 'MaxTokens',\r\n      key: 'MaxTokens',\r\n    },\r\n    {\r\n      title: '温度',\r\n      dataIndex: 'Temperature',\r\n      key: 'Temperature',\r\n    },\r\n    {\r\n      title: '创建时间',\r\n      dataIndex: 'CreatedTime',\r\n      key: 'CreatedTime',\r\n      render: (text: string) => new Date(text).toLocaleString(),\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <div style={{ padding: '24px' }}>\r\n      <Card>\r\n        <Title level={3}>后台数据格式测试</Title>\r\n\r\n        <Space style={{ marginBottom: 16 }}>\r\n          <Button type=\"primary\" onClick={testUnifiedHandler}>\r\n            测试统一处理函数\r\n          </Button>\r\n        </Space>\r\n\r\n        {testResults.length > 0 && (\r\n          <>\r\n            <Title level={4}>统一处理函数测试结果：</Title>\r\n            {testResults.map((result, index) => (\r\n              <Card key={index} size=\"small\" style={{ marginBottom: 8 }}>\r\n                <Text strong>{result.function}:</Text>\r\n                <pre style={{ background: '#f5f5f5', padding: '8px', borderRadius: '4px', fontSize: '12px' }}>\r\n                  输入: {JSON.stringify(result.input, null, 2)}\r\n                  {'\\n'}\r\n                  输出: {JSON.stringify(result.output, null, 2)}\r\n                </pre>\r\n              </Card>\r\n            ))}\r\n          </>\r\n        )}\r\n\r\n        <Paragraph>\r\n          <Text strong>后台返回的分页数据格式：</Text>\r\n        </Paragraph>\r\n        <Paragraph>\r\n          <pre style={{ background: '#f5f5f5', padding: '12px', borderRadius: '4px' }}>\r\n            {JSON.stringify({\r\n              Items: \"数据数组\",\r\n              TotalCount: \"总记录数\",\r\n              PageNumber: \"当前页码\",\r\n              PageSize: \"每页大小\",\r\n              TotalPages: \"总页数\",\r\n              HasPreviousPage: \"是否有上一页\",\r\n              HasNextPage: \"是否有下一页\"\r\n            }, null, 2)}\r\n          </pre>\r\n        </Paragraph>\r\n        \r\n        <Title level={4}>数据展示效果：</Title>\r\n        <Table\r\n          columns={columns}\r\n          dataSource={mockBackendResponse.Items}\r\n          rowKey=\"Id\"\r\n          pagination={{\r\n            current: mockBackendResponse.PageNumber,\r\n            pageSize: mockBackendResponse.PageSize,\r\n            total: mockBackendResponse.TotalCount,\r\n            showSizeChanger: true,\r\n            showQuickJumper: true,\r\n            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,\r\n          }}\r\n        />\r\n        \r\n        <Title level={4}>分页信息：</Title>\r\n        <ul>\r\n          <li>总记录数: {mockBackendResponse.TotalCount}</li>\r\n          <li>当前页码: {mockBackendResponse.PageNumber}</li>\r\n          <li>每页大小: {mockBackendResponse.PageSize}</li>\r\n          <li>总页数: {mockBackendResponse.TotalPages}</li>\r\n          <li>是否有上一页: {mockBackendResponse.HasPreviousPage ? '是' : '否'}</li>\r\n          <li>是否有下一页: {mockBackendResponse.HasNextPage ? '是' : '否'}</li>\r\n        </ul>\r\n      </Card>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DataFormatTest;\r\n", "import React from 'react';\nimport { Modal, Descriptions, Tag, Switch, Button, Space, Card, Row, Col, Typography } from 'antd';\nimport { EditOutlined, CloseOutlined } from '@ant-design/icons';\nimport { useIntl, FormattedMessage } from '@umijs/max';\n\nconst { Title, Text } = Typography;\n\ninterface ModelDetailProps {\n  visible: boolean;\n  onVisibleChange: (visible: boolean) => void;\n  onEdit?: () => void;\n  data?: RwxAI.AIModel;\n}\n\nconst ModelDetail: React.FC<ModelDetailProps> = ({\n  visible,\n  onVisibleChange,\n  onEdit,\n  data,\n}) => {\n  const intl = useIntl();\n\n  const getModelTypeTag = (type?: string) => {\n    const typeMap: Record<string, { text: string; color: string }> = {\n      'Chat': { text: '对话模型', color: 'blue' },\n      'Embedding': { text: '嵌入模型', color: 'green' },\n      'TextToImage': { text: '文本生成图像模型', color: 'purple' },\n      'ImageToText': { text: '图像描述模型', color: 'orange' },\n    };\n    const typeInfo = typeMap[type || ''];\n    return <Tag color={typeInfo?.color}>{typeInfo?.text}</Tag>;\n  };\n\n  return (\n    <Modal\n      title={intl.formatMessage({ id: 'pages.aiModels.detail.title' })}\n      open={visible}\n      onCancel={() => onVisibleChange(false)}\n      footer={\n        <Space>\n          <Button\n            icon={<CloseOutlined />}\n            onClick={() => onVisibleChange(false)}\n          >\n            <FormattedMessage id=\"pages.common.close\" />\n          </Button>\n          {onEdit && (\n            <Button\n              type=\"primary\"\n              icon={<EditOutlined />}\n              onClick={() => {\n                onEdit();\n                onVisibleChange(false);\n              }}\n            >\n              <FormattedMessage id=\"pages.common.edit\" />\n            </Button>\n          )}\n        </Space>\n      }\n      width={900}\n      style={{ top: 50 }}\n      styles={{\n        body: { maxHeight: '70vh', overflowY: 'auto', padding: '24px' }\n      }}\n    >\n      {data && (\n        <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>\n          {/* 第一步：AI模型提供商和模板信息 */}\n          <Card size=\"small\" title={<FormattedMessage id=\"pages.aiModels.detail.templateInfo\" />}>\n            <Row gutter={16}>\n              <Col span={12}>\n                <div style={{ marginBottom: '16px' }}>\n                  <Text strong><FormattedMessage id=\"pages.aiModels.detail.provider\" />：</Text>\n                  <Text>{data.Template?.Provider?.DisplayName || data.Template?.Provider?.Name || '-'}</Text>\n                </div>\n              </Col>\n              <Col span={12}>\n                <div style={{ marginBottom: '16px' }}>\n                  <Text strong><FormattedMessage id=\"pages.aiModels.detail.modelId\" />：</Text>\n                  <Text>{data.Template?.ModelId || '-'}</Text>\n                </div>\n              </Col>\n              <Col span={12}>\n                <div style={{ marginBottom: '16px' }}>\n                  <Text strong><FormattedMessage id=\"pages.aiModels.detail.modelType\" />：</Text>\n                  {getModelTypeTag(data.Template?.Type)}\n                </div>\n              </Col>\n              <Col span={12}>\n                <div style={{ marginBottom: '16px' }}>\n                  <Text strong><FormattedMessage id=\"pages.aiModels.detail.endpoint\" />：</Text>\n                  <Text>{data.Endpoint || '-'}</Text>\n                </div>\n              </Col>\n              <Col span={12}>\n                <div style={{ marginBottom: '16px' }}>\n                  <Text strong><FormattedMessage id=\"pages.aiModels.detail.supportsStreaming\" />：</Text>\n                  <Tag color={data.Template?.SupportsStreaming ? 'green' : 'red'}>\n                    {data.Template?.SupportsStreaming ? '支持' : '不支持'}\n                  </Tag>\n                </div>\n              </Col>\n              <Col span={12}>\n                <div style={{ marginBottom: '16px' }}>\n                  <Text strong><FormattedMessage id=\"pages.aiModels.detail.supportsFunctionCalling\" />：</Text>\n                  <Tag color={data.Template?.SupportsFunctionCalling ? 'green' : 'red'}>\n                    {data.Template?.SupportsFunctionCalling ? '支持' : '不支持'}\n                  </Tag>\n                </div>\n              </Col>\n              <Col span={12}>\n                <div style={{ marginBottom: '16px' }}>\n                  <Text strong><FormattedMessage id=\"pages.aiModels.detail.supportsVision\" />：</Text>\n                  <Tag color={data.Template?.SupportsVision ? 'green' : 'red'}>\n                    {data.Template?.SupportsVision ? '支持' : '不支持'}\n                  </Tag>\n                </div>\n              </Col>\n            </Row>\n          </Card>\n\n          {/* 基本信息 */}\n          <Card size=\"small\" title={<FormattedMessage id=\"pages.aiModels.detail.basicInfo\" />}>\n            <Row gutter={16}>\n              <Col span={12}>\n                <div style={{ marginBottom: '16px' }}>\n                  <Text strong><FormattedMessage id=\"pages.aiModels.detail.name\" />：</Text>\n                  <Text>{data.Name || '-'}</Text>\n                </div>\n              </Col>\n              <Col span={12}>\n                <div style={{ marginBottom: '16px' }}>\n                  <Text strong><FormattedMessage id=\"pages.aiModels.detail.displayName\" />：</Text>\n                  <Text>{data.DisplayName || '-'}</Text>\n                </div>\n              </Col>\n              <Col span={24}>\n                <div style={{ marginBottom: '16px' }}>\n                  <Text strong><FormattedMessage id=\"pages.aiModels.detail.description\" />：</Text>\n                  <Text>{data.Description || '-'}</Text>\n                </div>\n              </Col>\n              <Col span={12}>\n                <div style={{ marginBottom: '16px' }}>\n                  <Text strong><FormattedMessage id=\"pages.aiModels.detail.apiKey\" />：</Text>\n                  <Text>{data.ApiKey ? '***' : '-'}</Text>\n                </div>\n              </Col>\n              <Col span={12}>\n                <div style={{ marginBottom: '16px' }}>\n                  <Text strong><FormattedMessage id=\"pages.aiModels.detail.isDefault\" />：</Text>\n                  <Switch checked={data.IsDefault} disabled size=\"small\" />\n                </div>\n              </Col>\n              <Col span={12}>\n                <div style={{ marginBottom: '16px' }}>\n                  <Text strong><FormattedMessage id=\"pages.aiModels.detail.status\" />：</Text>\n                  <Switch checked={data.IsEnabled} disabled size=\"small\" />\n                </div>\n              </Col>\n            </Row>\n          </Card>\n\n          {/* 可配置参数 */}\n          <Card size=\"small\" title={<FormattedMessage id=\"pages.aiModels.detail.configParams\" />}>\n            <Row gutter={16}>\n              <Col span={12}>\n                <div style={{ marginBottom: '16px' }}>\n                  <Text strong><FormattedMessage id=\"pages.aiModels.detail.maxTokens\" />：</Text>\n                  <Text>{data.MaxTokens || '-'}</Text>\n                </div>\n              </Col>\n              <Col span={12}>\n                <div style={{ marginBottom: '16px' }}>\n                  <Text strong><FormattedMessage id=\"pages.aiModels.detail.temperature\" />：</Text>\n                  <Text>{data.Temperature || '-'}</Text>\n                </div>\n              </Col>\n              <Col span={12}>\n                <div style={{ marginBottom: '16px' }}>\n                  <Text strong><FormattedMessage id=\"pages.aiModels.detail.maxContextLength\" />：</Text>\n                  <Text>{data.Template?.MaxContextLength || '-'}</Text>\n                </div>\n              </Col>\n              <Col span={12}>\n                <div style={{ marginBottom: '16px' }}>\n                  <Text strong><FormattedMessage id=\"pages.aiModels.detail.maxOutputLength\" />：</Text>\n                  <Text>{data.Template?.MaxOutputLength || '-'}</Text>\n                </div>\n              </Col>\n            </Row>\n          </Card>\n\n          {/* 其他信息 */}\n          <Card size=\"small\" title={<FormattedMessage id=\"pages.aiModels.detail.otherInfo\" />}>\n            <Row gutter={16}>\n              <Col span={12}>\n                <div style={{ marginBottom: '16px' }}>\n                  <Text strong><FormattedMessage id=\"pages.aiModels.detail.createdTime\" />：</Text>\n                  <Text>{data.CreatedTime ? new Date(data.CreatedTime).toLocaleString() : '-'}</Text>\n                </div>\n              </Col>\n              <Col span={12}>\n                <div style={{ marginBottom: '16px' }}>\n                  <Text strong><FormattedMessage id=\"pages.aiModels.detail.updatedTime\" />：</Text>\n                  <Text>{data.UpdatedTime ? new Date(data.UpdatedTime).toLocaleString() : '-'}</Text>\n                </div>\n              </Col>\n              <Col span={24}>\n                <div style={{ marginBottom: '16px' }}>\n                  <Text strong><FormattedMessage id=\"pages.aiModels.detail.notes\" />：</Text>\n                  <Text>{data.Template?.Notes || '-'}</Text>\n                </div>\n              </Col>\n            </Row>\n          </Card>\n        </div>\n      )}\n    </Modal>\n  );\n};\n\nexport default ModelDetail;\n", "import React, { useEffect, useState } from 'react';\r\nimport { Modal, Form, Input, Select, InputNumber, Switch } from 'antd';\r\nimport { useIntl, FormattedMessage } from '@umijs/max';\r\nimport {\r\n  createAIModel,\r\n  updateAIModel,\r\n  getEnabledAIProviders,\r\n  getAIModelTemplatesByProviderAndType\r\n} from '@/services/rwxai';\r\n\r\ninterface ModelFormProps {\r\n  visible: boolean;\r\n  onVisibleChange: (visible: boolean) => void;\r\n  initialValues?: RwxAI.AIModel;\r\n  onSuccess: () => void;\r\n}\r\n\r\nconst ModelForm: React.FC<ModelFormProps> = ({\r\n  visible,\r\n  onVisibleChange,\r\n  initialValues,\r\n  onSuccess,\r\n}) => {\r\n  const intl = useIntl();\r\n  const [form] = Form.useForm();\r\n  const [loading, setLoading] = useState(false);\r\n  const [providers, setProviders] = useState<RwxAI.AIProvider[]>([]);\r\n  const [modelTemplates, setModelTemplates] = useState<RwxAI.AIModelTemplate[]>([]);\r\n  const [loadingTemplates, setLoadingTemplates] = useState(false);\r\n\r\n  const isEdit = !!initialValues?.Id;\r\n\r\n  useEffect(() => {\r\n    if (visible) {\r\n      initializeForm();\r\n    }\r\n  }, [visible, initialValues]);\r\n\r\n  const initializeForm = async () => {\r\n    // 先加载提供商数据\r\n    await loadProviders();\r\n\r\n    if (initialValues) {\r\n      // 映射后端数据结构到表单字段\r\n      const formData = {\r\n        // 基本字段\r\n        Name: initialValues.Name,\r\n\r\n        // 模型ID\r\n        ModelId: initialValues.ModelId,\r\n\r\n        // 模型服务端点\r\n        Endpoint: initialValues.Endpoint,\r\n\r\n        // 提供商代码\r\n        ProviderCode: initialValues.ProviderCode,\r\n\r\n        DisplayName: initialValues.DisplayName,\r\n        Description: initialValues.Description,\r\n        ApiKey: initialValues.ApiKey,\r\n        IsEnabled: initialValues.IsEnabled,\r\n        IsDefault: initialValues.IsDefault,\r\n\r\n        // 配置参数\r\n        MaxTokens: initialValues.MaxTokens,\r\n        Temperature: initialValues.Temperature,\r\n        FrequencyPenalty: initialValues.FrequencyPenalty,\r\n        PresencePenalty: initialValues.PresencePenalty,\r\n        SystemPrompt: initialValues.SystemPrompt,\r\n\r\n        // 关联字段\r\n        TemplateId: initialValues.ModelTemplateId,\r\n        ProviderId: initialValues.Template?.Provider?.Id, // 添加提供商ID\r\n\r\n        // 只读字段，从模板中获取\r\n        ModelType: initialValues.Template?.Type,\r\n        SupportsStreaming: initialValues.Template?.SupportsStreaming,\r\n      };\r\n\r\n      form.setFieldsValue(formData);\r\n\r\n      // 如果有提供商ID，加载对应的模型模板\r\n      const providerId = initialValues.Template?.Provider?.Id;\r\n      if (providerId) {\r\n        await loadModelTemplates(providerId);\r\n      }\r\n    } else {\r\n      form.resetFields();\r\n      setModelTemplates([]);\r\n    }\r\n  };\r\n\r\n  // 监听表单字段变化\r\n  const handleFormValuesChange = (changedValues: any) => {\r\n    // 当提供商发生变化时，重新加载模型模板\r\n    if (changedValues.ProviderId !== undefined) {\r\n      const providerId = changedValues.ProviderId;\r\n\r\n      loadModelTemplates(providerId);\r\n\r\n      // 清空模型模板选择\r\n      form.setFieldValue('TemplateId', undefined);\r\n\r\n      // 清空相关字段，因为要重新选择模板\r\n      form.setFieldsValue({\r\n        ModelId: undefined,\r\n        ModelType: undefined,\r\n        Endpoint: undefined,\r\n        SupportsStreaming: undefined,\r\n        Name: undefined,\r\n        Description: undefined,\r\n        DisplayName: undefined,\r\n        ProviderCode: undefined,\r\n      });\r\n    }\r\n\r\n    // 当选择模型模板时，自动填充相关字段\r\n    if (changedValues.TemplateId !== undefined) {\r\n      const selectedTemplate = modelTemplates.find(template =>\r\n        template.Id === changedValues.TemplateId\r\n      );\r\n      if (selectedTemplate) {\r\n        // 自动填充模板的所有信息\r\n        const updates: any = {\r\n          ModelType: selectedTemplate.Type,\r\n          Name: selectedTemplate.Name,\r\n          ModelId: selectedTemplate.ModelId,\r\n          Endpoint: selectedTemplate.Endpoint,\r\n          Description: selectedTemplate.Description,\r\n          DisplayName: selectedTemplate.DisplayName,\r\n          SupportsStreaming: selectedTemplate.SupportsStreaming,\r\n          ProviderCode: selectedTemplate.Provider?.Code,\r\n        };\r\n\r\n        // 填充可选的配置参数\r\n        if (selectedTemplate.MaxOutputLength !== undefined) {\r\n          updates.MaxTokens = selectedTemplate.MaxOutputLength;\r\n        }\r\n\r\n        form.setFieldsValue(updates);\r\n      }\r\n    }\r\n  };\r\n\r\n  const loadProviders = async () => {\r\n    try {\r\n      const response = await getEnabledAIProviders();\r\n      if (response.success) {\r\n        setProviders(response.data || []);\r\n      } else {\r\n        setProviders([]);\r\n      }\r\n    } catch (error) {\r\n      setProviders([]);\r\n    }\r\n    // 错误消息会由统一响应处理系统自动显示\r\n  };\r\n\r\n  // 根据提供商加载模型模板\r\n  const loadModelTemplates = async (providerId?: string) => {\r\n    if (!providerId) {\r\n      setModelTemplates([]);\r\n      return;\r\n    }\r\n\r\n    setLoadingTemplates(true);\r\n    try {\r\n      // 根据提供商获取所有模板\r\n      const response = await getAIModelTemplatesByProviderAndType({\r\n        providerId,\r\n        modelType: undefined, // 不限制模型类型，获取该提供商的所有模板\r\n      });\r\n\r\n      if (response.success) {\r\n        setModelTemplates(response.data || []);\r\n      } else {\r\n        setModelTemplates([]);\r\n      }\r\n    } catch (error) {\r\n      setModelTemplates([]);\r\n    } finally {\r\n      setLoadingTemplates(false);\r\n    }\r\n  };\r\n\r\n  const handleSubmit = async () => {\r\n    try {\r\n      const values = await form.validateFields();\r\n      setLoading(true);\r\n\r\n      // 构建提交数据，只保存模板ID和必要字段\r\n      const submitData: any = {\r\n        modelTemplateId: values.TemplateId, // 只保存模板ID        \r\n        name: values.Name,\r\n        modelId: values.ModelId,\r\n        endpoint: values.Endpoint,\r\n        providerCode: values.ProviderCode,\r\n        displayName: values.DisplayName,\r\n        description: values.Description,\r\n        apiKey: values.ApiKey,\r\n        isEnabled: values.IsEnabled,\r\n        isDefault: values.IsDefault,\r\n        // 可选的自定义配置参数\r\n        maxTokens: values.MaxTokens,\r\n        temperature: values.Temperature,\r\n        frequencyPenalty: values.FrequencyPenalty,\r\n        presencePenalty: values.PresencePenalty,\r\n        systemPrompt: values.SystemPrompt,\r\n      };\r\n\r\n      let response;\r\n      if (isEdit) {\r\n        response = await updateAIModel(initialValues!.Id, { ...initialValues, ...submitData });\r\n      } else {\r\n        response = await createAIModel(submitData);\r\n      }\r\n\r\n      if (response.success) {\r\n        onSuccess();\r\n      }\r\n      // 成功和错误消息会由统一响应处理系统自动显示\r\n    } catch (error) {\r\n      // 表单验证错误等\r\n      console.error('Form validation error:', error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Modal\r\n      title={intl.formatMessage({\r\n        id: isEdit ? 'pages.aiModels.form.edit.title' : 'pages.aiModels.form.create.title',\r\n      })}\r\n      open={visible}\r\n      onCancel={() => onVisibleChange(false)}\r\n      onOk={handleSubmit}\r\n      confirmLoading={loading}\r\n      width={900}\r\n      style={{ top: 50 }}\r\n      styles={{\r\n        body: { maxHeight: '70vh', overflowY: 'auto', padding: '24px' }\r\n      }}\r\n    >\r\n      <Form\r\n        form={form}\r\n        layout=\"vertical\"\r\n        onValuesChange={handleFormValuesChange}\r\n        initialValues={{\r\n          IsEnabled: true,\r\n          Temperature: 0.7,\r\n        }}\r\n      >\r\n        {/* 第一步：选择AI模型提供商和模板 */}\r\n        <div style={{ display: 'flex', gap: '16px' }}>\r\n          <Form.Item\r\n            name=\"ProviderId\"\r\n            label={<FormattedMessage id=\"pages.aiModels.form.provider\" />}\r\n            rules={[{ required: true, message: intl.formatMessage({ id: 'pages.aiModels.form.provider.required' }) }]}\r\n            tooltip={<FormattedMessage id=\"pages.aiModels.form.provider.tooltip\" />}\r\n            style={{ flex: 1 }}\r\n          >\r\n            <Select placeholder={intl.formatMessage({ id: 'pages.aiModels.form.provider.placeholder' })}>\r\n              {providers.map((provider) => (\r\n                <Select.Option key={provider.Id} value={provider.Id}>\r\n                  {provider.DisplayName || provider.Name}\r\n                </Select.Option>\r\n              ))}\r\n            </Select>\r\n          </Form.Item>\r\n\r\n          <Form.Item\r\n            name=\"TemplateId\"\r\n            label={<FormattedMessage id=\"pages.aiModels.form.template\" />}\r\n            rules={[{ required: true, message: intl.formatMessage({ id: 'pages.aiModels.form.template.required' }) }]}\r\n            tooltip={<FormattedMessage id=\"pages.aiModels.form.template.tooltip\" />}\r\n            style={{ flex: 1 }}\r\n          >\r\n            <Select\r\n              placeholder={intl.formatMessage({ id: 'pages.aiModels.form.template.placeholder' })}\r\n              loading={loadingTemplates}\r\n              allowClear\r\n              showSearch\r\n              optionFilterProp=\"children\"\r\n              filterOption={(input, option) =>\r\n                String(option?.children || '').toLowerCase().includes(input.toLowerCase())\r\n              }\r\n            >\r\n              {modelTemplates.map((template) => (\r\n                <Select.Option key={template.Id} value={template.Id}>\r\n                  {template.DisplayName || template.Name} ({template.ModelId})\r\n                </Select.Option>\r\n              ))}\r\n            </Select>\r\n          </Form.Item>\r\n        </div>\r\n\r\n        {/* 基本信息（从模板自动填充） */}\r\n        <div style={{ display: 'flex', gap: '16px' }}>\r\n          <Form.Item\r\n            name=\"Name\"\r\n            label={<FormattedMessage id=\"pages.aiModels.form.name\" />}\r\n            rules={[{ required: true, message: intl.formatMessage({ id: 'pages.aiModels.form.name.required' }) }]}\r\n            style={{ flex: 1 }}\r\n          >\r\n            <Input placeholder={intl.formatMessage({ id: 'pages.aiModels.form.name.placeholder' })} />\r\n          </Form.Item>\r\n\r\n          <Form.Item\r\n            name=\"DisplayName\"\r\n            label={<FormattedMessage id=\"pages.aiModels.form.displayName\" />}\r\n            style={{ flex: 1 }}\r\n          >\r\n            <Input placeholder={intl.formatMessage({ id: 'pages.aiModels.form.displayName.placeholder' })} />\r\n          </Form.Item>\r\n        </div>\r\n\r\n        <Form.Item\r\n          name=\"Description\"\r\n          label={<FormattedMessage id=\"pages.aiModels.form.description\" />}\r\n        >\r\n          <Input.TextArea\r\n            rows={2}\r\n            placeholder={intl.formatMessage({ id: 'pages.aiModels.form.description.placeholder' })}\r\n          />\r\n        </Form.Item>\r\n\r\n        <Form.Item\r\n          name=\"SystemPrompt\"\r\n          label={<FormattedMessage id=\"pages.aiModels.form.systemPrompt\" />}\r\n          tooltip={<FormattedMessage id=\"pages.aiModels.form.systemPrompt.tooltip\" />}\r\n        >\r\n          <Input.TextArea\r\n            rows={3}\r\n            placeholder={intl.formatMessage({ id: 'pages.aiModels.form.systemPrompt.placeholder' })}\r\n          />\r\n        </Form.Item>\r\n\r\n        <Form.Item\r\n          name=\"ApiKey\"\r\n          label={<FormattedMessage id=\"pages.aiModels.form.apiKey\" />}\r\n          tooltip={<FormattedMessage id=\"pages.aiModels.form.apiKey.tooltip\" />}\r\n        >\r\n          <Input.Password placeholder={intl.formatMessage({ id: 'pages.aiModels.form.apiKey.placeholder' })} />\r\n        </Form.Item>\r\n\r\n        {/* 只读字段，显示从模板获取的信息 */}\r\n        <div style={{ display: 'flex', gap: '16px' }}>\r\n          <Form.Item\r\n            name=\"ModelId\"\r\n            label={<FormattedMessage id=\"pages.aiModels.form.modelId\" />}\r\n            style={{ flex: 1 }}\r\n          >\r\n            <Input disabled placeholder={intl.formatMessage({ id: 'pages.aiModels.form.modelId.readonly' })} />\r\n          </Form.Item>\r\n\r\n          <Form.Item\r\n            name=\"ModelType\"\r\n            label={<FormattedMessage id=\"pages.aiModels.form.modelType\" />}\r\n            style={{ flex: 1 }}\r\n          >\r\n            <Select disabled placeholder={intl.formatMessage({ id: 'pages.aiModels.form.modelType.readonly' })}>\r\n              <Select.Option value=\"0\">Chat</Select.Option>\r\n              <Select.Option value=\"1\">Embedding</Select.Option>\r\n              <Select.Option value=\"2\">Image Generation</Select.Option>\r\n              <Select.Option value=\"3\">Text to Speech</Select.Option>\r\n            </Select>\r\n          </Form.Item>\r\n        </div>\r\n\r\n        <div style={{ display: 'flex', gap: '16px' }}>\r\n          <Form.Item\r\n            name=\"Endpoint\"\r\n            label={<FormattedMessage id=\"pages.aiModels.form.endpoint\" />}\r\n            style={{ flex: 1 }}\r\n          >\r\n            <Input disabled placeholder=\"从模板自动获取\" />\r\n          </Form.Item>\r\n\r\n          <Form.Item\r\n            name=\"SupportsStreaming\"\r\n            label={<FormattedMessage id=\"pages.aiModels.form.supportsStreaming\" />}\r\n            style={{ flex: 1 }}\r\n          >\r\n            <Select disabled placeholder=\"从模板自动获取\">\r\n              <Select.Option value={true}>支持</Select.Option>\r\n              <Select.Option value={false}>不支持</Select.Option>\r\n            </Select>\r\n          </Form.Item>\r\n        </div>\r\n\r\n        {/* 可配置参数 */}\r\n        <div style={{ display: 'flex', gap: '16px' }}>\r\n          <Form.Item\r\n            name=\"MaxTokens\"\r\n            label={<FormattedMessage id=\"pages.aiModels.form.maxTokens\" />}\r\n            style={{ flex: 1 }}\r\n          >\r\n            <InputNumber\r\n              min={1}\r\n              max={100000}\r\n              style={{ width: '100%' }}\r\n              placeholder={intl.formatMessage({ id: 'pages.aiModels.form.maxTokens.placeholder' })}\r\n            />\r\n          </Form.Item>\r\n\r\n          <Form.Item\r\n            name=\"Temperature\"\r\n            label={<FormattedMessage id=\"pages.aiModels.form.temperature\" />}\r\n            style={{ flex: 1 }}\r\n          >\r\n            <InputNumber\r\n              min={0}\r\n              max={2}\r\n              step={0.1}\r\n              style={{ width: '100%' }}\r\n              placeholder={intl.formatMessage({ id: 'pages.aiModels.form.temperature.placeholder' })}\r\n            />\r\n          </Form.Item>\r\n        </div>\r\n\r\n        <div style={{ display: 'flex', gap: '16px', alignItems: 'flex-end' }}>\r\n          <Form.Item\r\n            name=\"IsEnabled\"\r\n            label={<FormattedMessage id=\"pages.aiModels.form.isEnabled\" />}\r\n            valuePropName=\"checked\"\r\n            style={{ flex: 1 }}\r\n          >\r\n            <Switch />\r\n          </Form.Item>\r\n\r\n          <Form.Item\r\n            name=\"IsDefault\"\r\n            label={<FormattedMessage id=\"pages.aiModels.form.isDefault\" />}\r\n            valuePropName=\"checked\"\r\n            tooltip={<FormattedMessage id=\"pages.aiModels.form.isDefault.tooltip\" />}\r\n            style={{ flex: 1 }}\r\n          >\r\n            <Switch />\r\n          </Form.Item>\r\n        </div>\r\n\r\n      </Form>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport default ModelForm;\r\n", "import React, { useRef, useState } from 'react';\r\nimport { PageContainer } from '@ant-design/pro-components';\r\nimport { ProTable, ActionType, ProColumns } from '@ant-design/pro-table';\r\nimport { Button, Space, Tag, Modal, Switch } from 'antd';\r\nimport { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, ExperimentOutlined } from '@ant-design/icons';\r\nimport { useIntl, FormattedMessage } from '@umijs/max';\r\nimport { getAIModels, deleteAIModel } from '@/services/rwxai';\r\nimport { createProTableRequest, defaultPaginationConfig } from '@/utils/pageDataHandler';\r\nimport ModelForm from './components/ModelForm';\r\nimport ModelDetail from './components/ModelDetail';\r\nimport DataFormatTest from './components/DataFormatTest';\r\n\r\nconst AIModelsPage: React.FC = () => {\r\n  const intl = useIntl();\r\n  const actionRef = useRef<ActionType>(null);\r\n  const [createModalVisible, setCreateModalVisible] = useState(false);\r\n  const [editModalVisible, setEditModalVisible] = useState(false);\r\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\r\n  const [testModalVisible, setTestModalVisible] = useState(false);\r\n  const [currentRecord, setCurrentRecord] = useState<RwxAI.AIModel>();\r\n\r\n  const handleDelete = async (record: RwxAI.AIModel) => {\r\n    Modal.confirm({\r\n      title: intl.formatMessage({ id: 'pages.aiModels.delete.confirm.title' }),\r\n      content: intl.formatMessage({ id: 'pages.aiModels.delete.confirm.content' }),\r\n      onOk: async () => {\r\n        const response = await deleteAIModel(record.Id);\r\n        if (response.success) {\r\n          actionRef.current?.reload();\r\n        }\r\n        // 成功和错误消息会由统一响应处理系统自动显示\r\n      },\r\n    });\r\n  };\r\n  const getModelTypeTag = (type?: string | number) => {\r\n    // 允许后端返回字符串枚举或数字枚举\r\n    const normalized = typeof type === 'number'\r\n      ? ({ 0: 'Chat', 1: 'Embedding', 2: 'TextToImage', 3: 'ImageToText' } as Record<number, string>)[type]\r\n      : type;\r\n    const typeMap: Record<string, { text: string; color: string }> = {\r\n      Chat: { text: '对话模型', color: 'blue' },\r\n      Embedding: { text: '嵌入模型', color: 'green' },\r\n      TextToImage: { text: '文本生成图像模型', color: 'purple' },\r\n      ImageToText: { text: '图像描述模型', color: 'orange' },\r\n    };\r\n    const info = normalized ? typeMap[normalized] : undefined;\r\n    if (!info) return <Tag>-</Tag>;\r\n    return <Tag color={info.color}>{info.text}</Tag>;\r\n  };\r\n  const columns: ProColumns<RwxAI.AIModel>[] = [\r\n    {\r\n      title: <FormattedMessage id=\"pages.aiModels.table.name\" />,\r\n      dataIndex: 'Name',\r\n      key: 'Name',\r\n      ellipsis: true,\r\n      render: (_, record) => {\r\n        // 优先显示 DisplayName，如果没有则显示 Name\r\n        return record.DisplayName || record.Name || '-';\r\n      },\r\n    },\r\n    {\r\n      title: <FormattedMessage id=\"pages.aiModels.table.modelId\" />,\r\n      dataIndex: 'ModelId',\r\n      key: 'ModelId',\r\n      ellipsis: true,\r\n    },\r\n    {\r\n      title: '端点',\r\n      dataIndex: 'Endpoint',\r\n      key: 'Endpoint',\r\n      ellipsis: true,\r\n      hideInSearch: true,\r\n      width: 200,\r\n      render: (text) => text || '-',\r\n    },\r\n    {\r\n      title: <FormattedMessage id=\"pages.aiModels.table.modelType\" />,\r\n      dataIndex: 'ModelType',\r\n      key: 'ModelType',\r\n      render: (_, record) => {\r\n        return getModelTypeTag((record as any).Template?.Type);\r\n      },\r\n    },\r\n    {\r\n      title: <FormattedMessage id=\"pages.aiModels.table.provider\" />,\r\n      dataIndex: 'Provider',\r\n      key: 'Provider',\r\n      ellipsis: true,\r\n      render: (_, record) => {\r\n        // 优先显示 DisplayName，如果没有则显示 Name\r\n        const providerName = record.Template?.Provider?.DisplayName ||\r\n          record.Template?.Provider?.Name ||\r\n          '-';\r\n        return providerName;\r\n      },\r\n    },\r\n    {\r\n      title: <FormattedMessage id=\"pages.aiModels.table.status\" />,\r\n      dataIndex: 'IsEnabled',\r\n      key: 'IsEnabled',\r\n      render: (_, record) => (\r\n        <Switch\r\n          checked={record.IsEnabled}\r\n          size=\"small\"\r\n          disabled\r\n        />\r\n      ),\r\n    },\r\n    {\r\n      title: '默认模型',\r\n      dataIndex: 'IsDefault',\r\n      key: 'IsDefault',\r\n      hideInSearch: true,\r\n      width: 100,\r\n      render: (_, record) => (\r\n        <Switch\r\n          checked={record.IsDefault}\r\n          size=\"small\"\r\n          disabled\r\n        />\r\n      ),\r\n    },\r\n    {\r\n      title: <FormattedMessage id=\"pages.aiModels.table.maxTokens\" />,\r\n      dataIndex: 'MaxTokens',\r\n      key: 'MaxTokens',\r\n      hideInSearch: true,\r\n      width: 120,\r\n    },\r\n    {\r\n      title: <FormattedMessage id=\"pages.aiModels.table.temperature\" />,\r\n      dataIndex: 'Temperature',\r\n      key: 'Temperature',\r\n      hideInSearch: true,\r\n      width: 120,\r\n    },\r\n    {\r\n      title: <FormattedMessage id=\"pages.aiModels.table.createdTime\" />,\r\n      dataIndex: 'CreatedTime',\r\n      key: 'CreatedTime',\r\n      hideInSearch: true,\r\n      valueType: 'dateTime',\r\n      width: 180,\r\n    },\r\n    {\r\n      title: <FormattedMessage id=\"pages.aiModels.table.actions\" />,\r\n      key: 'actions',\r\n      hideInSearch: true,\r\n      width: 200,\r\n      render: (_, record) => (\r\n        <Space>\r\n          <Button\r\n            type=\"link\"\r\n            size=\"small\"\r\n            icon={<EyeOutlined />}\r\n            onClick={() => {\r\n              setCurrentRecord(record);\r\n              setDetailModalVisible(true);\r\n            }}\r\n          >\r\n            <FormattedMessage id=\"pages.common.view\" />\r\n          </Button>\r\n          <Button\r\n            type=\"link\"\r\n            size=\"small\"\r\n            icon={<EditOutlined />}\r\n            onClick={() => {\r\n              setCurrentRecord(record);\r\n              setEditModalVisible(true);\r\n            }}\r\n          >\r\n            <FormattedMessage id=\"pages.common.edit\" />\r\n          </Button>\r\n          <Button\r\n            type=\"link\"\r\n            size=\"small\"\r\n            danger\r\n            icon={<DeleteOutlined />}\r\n            onClick={() => handleDelete(record)}\r\n          >\r\n            <FormattedMessage id=\"pages.common.delete\" />\r\n          </Button>\r\n        </Space>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <PageContainer>\r\n      <ProTable<RwxAI.AIModel>\r\n        headerTitle={intl.formatMessage({ id: 'pages.aiModels.title' })}\r\n        actionRef={actionRef}\r\n        rowKey=\"Id\"\r\n        pagination={defaultPaginationConfig}\r\n        search={{\r\n          labelWidth: 120,\r\n        }}\r\n        toolBarRender={() => [\r\n          <Button\r\n            key=\"test\"\r\n            icon={<ExperimentOutlined />}\r\n            onClick={() => setTestModalVisible(true)}\r\n          >\r\n            数据格式测试\r\n          </Button>,\r\n          <Button\r\n            type=\"primary\"\r\n            key=\"primary\"\r\n            icon={<PlusOutlined />}\r\n            onClick={() => setCreateModalVisible(true)}\r\n          >\r\n            <FormattedMessage id=\"pages.common.create\" />\r\n          </Button>,\r\n        ]}\r\n        request={createProTableRequest(getAIModels, {\r\n          Name: 'Name',\r\n          ModelId: 'ModelId',\r\n          ProviderCode: 'ProviderCode',\r\n          IsEnabled: 'IsEnabled',\r\n        })}\r\n        columns={columns}\r\n      />\r\n\r\n      <ModelForm\r\n        visible={createModalVisible}\r\n        onVisibleChange={setCreateModalVisible}\r\n        onSuccess={() => {\r\n          actionRef.current?.reload();\r\n          setCreateModalVisible(false);\r\n        }}\r\n      />\r\n\r\n      <ModelForm\r\n        visible={editModalVisible}\r\n        onVisibleChange={setEditModalVisible}\r\n        initialValues={currentRecord}\r\n        onSuccess={() => {\r\n          actionRef.current?.reload();\r\n          setEditModalVisible(false);\r\n        }}\r\n      />\r\n\r\n      <ModelDetail\r\n        visible={detailModalVisible}\r\n        onVisibleChange={setDetailModalVisible}\r\n        onEdit={() => {\r\n          setEditModalVisible(true);\r\n          setDetailModalVisible(false);\r\n        }}\r\n        data={currentRecord}\r\n      />\r\n\r\n      <Modal\r\n        title=\"后台数据格式测试\"\r\n        open={testModalVisible}\r\n        onCancel={() => setTestModalVisible(false)}\r\n        footer={null}\r\n        width={1200}\r\n        style={{ top: 20 }}\r\n      >\r\n        <DataFormatTest />\r\n      </Modal>\r\n    </PageContainer>\r\n  );\r\n};\r\n\r\nexport default AIModelsPage;\r\n"], "names": [], "mappings": ";;;;;;;4BA4QA;;;eAAA;;;;;;;uEA5QkB;6BACkD;wCACQ;;;;;;;;;;AAE5E,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,gBAAU;AAE7C,iBAAiB;AACjB,MAAM,sBAAsB;IAC1B,SAAS;QACP;YACE,MAAM;YACN,mBAAmB;YACnB,QAAQ;YACR,WAAW;YACX,YAAY;YACZ,gBAAgB;YAChB,eAAe;YACf,eAAe;YACf,UAAU;YACV,aAAa;YACb,aAAa;YACb,aAAa;YACb,eAAe;YACf,eAAe;YACf,eAAe;YACf,YAAY;gBACV,MAAM;gBACN,QAAQ;gBACR,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,QAAQ;gBACR,eAAe;gBACf,oBAAoB;gBACpB,mBAAmB;gBACnB,qBAAqB;gBACrB,2BAA2B;gBAC3B,kBAAkB;gBAClB,SAAS;gBACT,YAAY;oBACV,MAAM;oBACN,QAAQ;oBACR,eAAe;oBACf,QAAQ;oBACR,eAAe;oBACf,WAAW;oBACX,WAAW;gBACb;YACF;QACF;KACD;IACD,cAAc;IACd,cAAc;IACd,YAAY;IACZ,cAAc;IACd,mBAAmB;IACnB,eAAe;AACjB;AAEA,MAAM,iBAA2B;;IAC/B,MAAM,CAAC,aAAa,eAAe,GAAG,cAAK,CAAC,QAAQ,CAAQ,EAAE;IAE9D,MAAM,qBAAqB;QACzB,sBAAsB;QACtB,MAAM,aAAa;YACjB,SAAS;YACT,UAAU;YACV,MAAM;YACN,SAAS;YACT,SAAS;QACX;QAEA,MAAM,eAAe;YACnB,MAAM;YACN,SAAS;YACT,cAAc;YACd,WAAW;QACb;QAEA,MAAM,QAAQ,IAAA,gCAAe,EAAC,YAAY;QAE1C,0BAA0B;QAC1B,MAAM,eAAe;YACnB,SAAS;YACT,MAAM;QACR;QAEA,MAAM,SAAS,IAAA,oCAAmB,EAAC;QAEnC,eAAe;YACb;gBACE,UAAU;gBACV,OAAO;oBAAE;oBAAY;gBAAa;gBAClC,QAAQ;YACV;YACA;gBACE,UAAU;gBACV,OAAO;gBACP,QAAQ;YACV;SACD;IACH;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAA2D;YAC/D,QAAQ;gBAAE,MAAM;gBAAQ,OAAO;YAAO;YACtC,aAAa;gBAAE,MAAM;gBAAQ,OAAO;YAAQ;YAC5C,eAAe;gBAAE,MAAM;gBAAY,OAAO;YAAS;YACnD,eAAe;gBAAE,MAAM;gBAAU,OAAO;YAAS;QACnD;QACA,MAAM,WAAW,OAAO,CAAC,QAAQ,GAAG;QACpC,OAAO,yBAAW,2BAAC,SAAG;YAAC,OAAO,SAAS,KAAK;sBAAG,SAAS,IAAI;;;;;iCAAU,2BAAC,SAAG;sBAAC;;;;;;IAC7E;IAEA,MAAM,UAAU;QACd;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,GAAQ;gBACf,OAAO,OAAO,WAAW,IAAI,OAAO,IAAI,IAAI;YAC9C;QACF;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;QACP;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,OAAiB,QAAQ;QACpC;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,GAAQ;oBACQ;gBAAvB,OAAO,iBAAgB,mBAAA,OAAO,QAAQ,cAAf,uCAAA,iBAAiB,IAAI;YAC9C;QACF;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,GAAQ;oBACM,2BAAA,kBACnB,4BAAA;gBADF,MAAM,eAAe,EAAA,mBAAA,OAAO,QAAQ,cAAf,wCAAA,4BAAA,iBAAiB,QAAQ,cAAzB,gDAAA,0BAA2B,WAAW,OACzD,oBAAA,OAAO,QAAQ,cAAf,yCAAA,6BAAA,kBAAiB,QAAQ,cAAzB,iDAAA,2BAA2B,IAAI,KAC/B;gBACF,OAAO;YACT;QACF;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,GAAQ,uBACf,2BAAC,YAAM;oBACL,SAAS,OAAO,SAAS;oBACzB,MAAK;oBACL,QAAQ;;;;;;QAGd;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,GAAQ,uBACf,2BAAC,YAAM;oBACL,SAAS,OAAO,SAAS;oBACzB,MAAK;oBACL,QAAQ;;;;;;QAGd;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;QACP;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;QACP;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,OAAiB,IAAI,KAAK,MAAM,cAAc;QACzD;KACD;IAED,qBACE,2BAAC;QAAI,OAAO;YAAE,SAAS;QAAO;kBAC5B,cAAA,2BAAC,UAAI;;8BACH,2BAAC;oBAAM,OAAO;8BAAG;;;;;;8BAEjB,2BAAC,WAAK;oBAAC,OAAO;wBAAE,cAAc;oBAAG;8BAC/B,cAAA,2BAAC,YAAM;wBAAC,MAAK;wBAAU,SAAS;kCAAoB;;;;;;;;;;;gBAKrD,YAAY,MAAM,GAAG,mBACpB;;sCACE,2BAAC;4BAAM,OAAO;sCAAG;;;;;;wBAChB,YAAY,GAAG,CAAC,CAAC,QAAQ,sBACxB,2BAAC,UAAI;gCAAa,MAAK;gCAAQ,OAAO;oCAAE,cAAc;gCAAE;;kDACtD,2BAAC;wCAAK,MAAM;;4CAAE,OAAO,QAAQ;4CAAC;;;;;;;kDAC9B,2BAAC;wCAAI,OAAO;4CAAE,YAAY;4CAAW,SAAS;4CAAO,cAAc;4CAAO,UAAU;wCAAO;;4CAAG;4CACvF,KAAK,SAAS,CAAC,OAAO,KAAK,EAAE,MAAM;4CACvC;4CAAK;4CACD,KAAK,SAAS,CAAC,OAAO,MAAM,EAAE,MAAM;;;;;;;;+BALlC;;;;;;;8BAYjB,2BAAC;8BACC,cAAA,2BAAC;wBAAK,MAAM;kCAAC;;;;;;;;;;;8BAEf,2BAAC;8BACC,cAAA,2BAAC;wBAAI,OAAO;4BAAE,YAAY;4BAAW,SAAS;4BAAQ,cAAc;wBAAM;kCACvE,KAAK,SAAS,CAAC;4BACd,OAAO;4BACP,YAAY;4BACZ,YAAY;4BACZ,UAAU;4BACV,YAAY;4BACZ,iBAAiB;4BACjB,aAAa;wBACf,GAAG,MAAM;;;;;;;;;;;8BAIb,2BAAC;oBAAM,OAAO;8BAAG;;;;;;8BACjB,2BAAC,WAAK;oBACJ,SAAS;oBACT,YAAY,oBAAoB,KAAK;oBACrC,QAAO;oBACP,YAAY;wBACV,SAAS,oBAAoB,UAAU;wBACvC,UAAU,oBAAoB,QAAQ;wBACtC,OAAO,oBAAoB,UAAU;wBACrC,iBAAiB;wBACjB,iBAAiB;wBACjB,WAAW,CAAC,OAAO,QAAU,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC;oBAC1E;;;;;;8BAGF,2BAAC;oBAAM,OAAO;8BAAG;;;;;;8BACjB,2BAAC;;sCACC,2BAAC;;gCAAG;gCAAO,oBAAoB,UAAU;;;;;;;sCACzC,2BAAC;;gCAAG;gCAAO,oBAAoB,UAAU;;;;;;;sCACzC,2BAAC;;gCAAG;gCAAO,oBAAoB,QAAQ;;;;;;;sCACvC,2BAAC;;gCAAG;gCAAM,oBAAoB,UAAU;;;;;;;sCACxC,2BAAC;;gCAAG;gCAAS,oBAAoB,eAAe,GAAG,MAAM;;;;;;;sCACzD,2BAAC;;gCAAG;gCAAS,oBAAoB,WAAW,GAAG,MAAM;;;;;;;;;;;;;;;;;;;;;;;;AAK/D;GA/MM;KAAA;IAiNN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BC7Cf;;;eAAA;;;;;;;uEA/NkB;6BAC0E;8BAChD;4BACF;;;;;;;;;;AAE1C,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;AASlC,MAAM,cAA0C,CAAC,EAC/C,OAAO,EACP,eAAe,EACf,MAAM,EACN,IAAI,EACL;QAuDwB,yBAAA,gBAAwC,0BAAA,iBAMxC,iBAMU,iBAYL,iBACT,iBAOS,iBACT,iBAOS,iBACT,iBAmEI,kBAMA,kBAwBA;;IAhMvB,MAAM,OAAO,IAAA,YAAO;IAEpB,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAA2D;YAC/D,QAAQ;gBAAE,MAAM;gBAAQ,OAAO;YAAO;YACtC,aAAa;gBAAE,MAAM;gBAAQ,OAAO;YAAQ;YAC5C,eAAe;gBAAE,MAAM;gBAAY,OAAO;YAAS;YACnD,eAAe;gBAAE,MAAM;gBAAU,OAAO;YAAS;QACnD;QACA,MAAM,WAAW,OAAO,CAAC,QAAQ,GAAG;QACpC,qBAAO,2BAAC,SAAG;YAAC,KAAK,EAAE,qBAAA,+BAAA,SAAU,KAAK;sBAAG,qBAAA,+BAAA,SAAU,IAAI;;;;;;IACrD;IAEA,qBACE,2BAAC,WAAK;QACJ,OAAO,KAAK,aAAa,CAAC;YAAE,IAAI;QAA8B;QAC9D,MAAM;QACN,UAAU,IAAM,gBAAgB;QAChC,sBACE,2BAAC,WAAK;;8BACJ,2BAAC,YAAM;oBACL,oBAAM,2BAAC,oBAAa;;;;;oBACpB,SAAS,IAAM,gBAAgB;8BAE/B,cAAA,2BAAC,qBAAgB;wBAAC,IAAG;;;;;;;;;;;gBAEtB,wBACC,2BAAC,YAAM;oBACL,MAAK;oBACL,oBAAM,2BAAC,mBAAY;;;;;oBACnB,SAAS;wBACP;wBACA,gBAAgB;oBAClB;8BAEA,cAAA,2BAAC,qBAAgB;wBAAC,IAAG;;;;;;;;;;;;;;;;;QAK7B,OAAO;QACP,OAAO;YAAE,KAAK;QAAG;QACjB,QAAQ;YACN,MAAM;gBAAE,WAAW;gBAAQ,WAAW;gBAAQ,SAAS;YAAO;QAChE;kBAEC,sBACC,2BAAC;YAAI,OAAO;gBAAE,SAAS;gBAAQ,eAAe;gBAAU,KAAK;YAAO;;8BAElE,2BAAC,UAAI;oBAAC,MAAK;oBAAQ,qBAAO,2BAAC,qBAAgB;wBAAC,IAAG;;;;;;8BAC7C,cAAA,2BAAC,SAAG;wBAAC,QAAQ;;0CACX,2BAAC,SAAG;gCAAC,MAAM;0CACT,cAAA,2BAAC;oCAAI,OAAO;wCAAE,cAAc;oCAAO;;sDACjC,2BAAC;4CAAK,MAAM;;8DAAC,2BAAC,qBAAgB;oDAAC,IAAG;;;;;;gDAAmC;;;;;;;sDACrE,2BAAC;sDAAM,EAAA,iBAAA,KAAK,QAAQ,cAAb,sCAAA,0BAAA,eAAe,QAAQ,cAAvB,8CAAA,wBAAyB,WAAW,OAAI,kBAAA,KAAK,QAAQ,cAAb,uCAAA,2BAAA,gBAAe,QAAQ,cAAvB,+CAAA,yBAAyB,IAAI,KAAI;;;;;;;;;;;;;;;;;0CAGpF,2BAAC,SAAG;gCAAC,MAAM;0CACT,cAAA,2BAAC;oCAAI,OAAO;wCAAE,cAAc;oCAAO;;sDACjC,2BAAC;4CAAK,MAAM;;8DAAC,2BAAC,qBAAgB;oDAAC,IAAG;;;;;;gDAAkC;;;;;;;sDACpE,2BAAC;sDAAM,EAAA,kBAAA,KAAK,QAAQ,cAAb,sCAAA,gBAAe,OAAO,KAAI;;;;;;;;;;;;;;;;;0CAGrC,2BAAC,SAAG;gCAAC,MAAM;0CACT,cAAA,2BAAC;oCAAI,OAAO;wCAAE,cAAc;oCAAO;;sDACjC,2BAAC;4CAAK,MAAM;;8DAAC,2BAAC,qBAAgB;oDAAC,IAAG;;;;;;gDAAoC;;;;;;;wCACrE,iBAAgB,kBAAA,KAAK,QAAQ,cAAb,sCAAA,gBAAe,IAAI;;;;;;;;;;;;0CAGxC,2BAAC,SAAG;gCAAC,MAAM;0CACT,cAAA,2BAAC;oCAAI,OAAO;wCAAE,cAAc;oCAAO;;sDACjC,2BAAC;4CAAK,MAAM;;8DAAC,2BAAC,qBAAgB;oDAAC,IAAG;;;;;;gDAAmC;;;;;;;sDACrE,2BAAC;sDAAM,KAAK,QAAQ,IAAI;;;;;;;;;;;;;;;;;0CAG5B,2BAAC,SAAG;gCAAC,MAAM;0CACT,cAAA,2BAAC;oCAAI,OAAO;wCAAE,cAAc;oCAAO;;sDACjC,2BAAC;4CAAK,MAAM;;8DAAC,2BAAC,qBAAgB;oDAAC,IAAG;;;;;;gDAA4C;;;;;;;sDAC9E,2BAAC,SAAG;4CAAC,OAAO,EAAA,kBAAA,KAAK,QAAQ,cAAb,sCAAA,gBAAe,iBAAiB,IAAG,UAAU;sDACtD,EAAA,kBAAA,KAAK,QAAQ,cAAb,sCAAA,gBAAe,iBAAiB,IAAG,OAAO;;;;;;;;;;;;;;;;;0CAIjD,2BAAC,SAAG;gCAAC,MAAM;0CACT,cAAA,2BAAC;oCAAI,OAAO;wCAAE,cAAc;oCAAO;;sDACjC,2BAAC;4CAAK,MAAM;;8DAAC,2BAAC,qBAAgB;oDAAC,IAAG;;;;;;gDAAkD;;;;;;;sDACpF,2BAAC,SAAG;4CAAC,OAAO,EAAA,kBAAA,KAAK,QAAQ,cAAb,sCAAA,gBAAe,uBAAuB,IAAG,UAAU;sDAC5D,EAAA,kBAAA,KAAK,QAAQ,cAAb,sCAAA,gBAAe,uBAAuB,IAAG,OAAO;;;;;;;;;;;;;;;;;0CAIvD,2BAAC,SAAG;gCAAC,MAAM;0CACT,cAAA,2BAAC;oCAAI,OAAO;wCAAE,cAAc;oCAAO;;sDACjC,2BAAC;4CAAK,MAAM;;8DAAC,2BAAC,qBAAgB;oDAAC,IAAG;;;;;;gDAAyC;;;;;;;sDAC3E,2BAAC,SAAG;4CAAC,OAAO,EAAA,kBAAA,KAAK,QAAQ,cAAb,sCAAA,gBAAe,cAAc,IAAG,UAAU;sDACnD,EAAA,kBAAA,KAAK,QAAQ,cAAb,sCAAA,gBAAe,cAAc,IAAG,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQlD,2BAAC,UAAI;oBAAC,MAAK;oBAAQ,qBAAO,2BAAC,qBAAgB;wBAAC,IAAG;;;;;;8BAC7C,cAAA,2BAAC,SAAG;wBAAC,QAAQ;;0CACX,2BAAC,SAAG;gCAAC,MAAM;0CACT,cAAA,2BAAC;oCAAI,OAAO;wCAAE,cAAc;oCAAO;;sDACjC,2BAAC;4CAAK,MAAM;;8DAAC,2BAAC,qBAAgB;oDAAC,IAAG;;;;;;gDAA+B;;;;;;;sDACjE,2BAAC;sDAAM,KAAK,IAAI,IAAI;;;;;;;;;;;;;;;;;0CAGxB,2BAAC,SAAG;gCAAC,MAAM;0CACT,cAAA,2BAAC;oCAAI,OAAO;wCAAE,cAAc;oCAAO;;sDACjC,2BAAC;4CAAK,MAAM;;8DAAC,2BAAC,qBAAgB;oDAAC,IAAG;;;;;;gDAAsC;;;;;;;sDACxE,2BAAC;sDAAM,KAAK,WAAW,IAAI;;;;;;;;;;;;;;;;;0CAG/B,2BAAC,SAAG;gCAAC,MAAM;0CACT,cAAA,2BAAC;oCAAI,OAAO;wCAAE,cAAc;oCAAO;;sDACjC,2BAAC;4CAAK,MAAM;;8DAAC,2BAAC,qBAAgB;oDAAC,IAAG;;;;;;gDAAsC;;;;;;;sDACxE,2BAAC;sDAAM,KAAK,WAAW,IAAI;;;;;;;;;;;;;;;;;0CAG/B,2BAAC,SAAG;gCAAC,MAAM;0CACT,cAAA,2BAAC;oCAAI,OAAO;wCAAE,cAAc;oCAAO;;sDACjC,2BAAC;4CAAK,MAAM;;8DAAC,2BAAC,qBAAgB;oDAAC,IAAG;;;;;;gDAAiC;;;;;;;sDACnE,2BAAC;sDAAM,KAAK,MAAM,GAAG,QAAQ;;;;;;;;;;;;;;;;;0CAGjC,2BAAC,SAAG;gCAAC,MAAM;0CACT,cAAA,2BAAC;oCAAI,OAAO;wCAAE,cAAc;oCAAO;;sDACjC,2BAAC;4CAAK,MAAM;;8DAAC,2BAAC,qBAAgB;oDAAC,IAAG;;;;;;gDAAoC;;;;;;;sDACtE,2BAAC,YAAM;4CAAC,SAAS,KAAK,SAAS;4CAAE,QAAQ;4CAAC,MAAK;;;;;;;;;;;;;;;;;0CAGnD,2BAAC,SAAG;gCAAC,MAAM;0CACT,cAAA,2BAAC;oCAAI,OAAO;wCAAE,cAAc;oCAAO;;sDACjC,2BAAC;4CAAK,MAAM;;8DAAC,2BAAC,qBAAgB;oDAAC,IAAG;;;;;;gDAAiC;;;;;;;sDACnE,2BAAC,YAAM;4CAAC,SAAS,KAAK,SAAS;4CAAE,QAAQ;4CAAC,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOvD,2BAAC,UAAI;oBAAC,MAAK;oBAAQ,qBAAO,2BAAC,qBAAgB;wBAAC,IAAG;;;;;;8BAC7C,cAAA,2BAAC,SAAG;wBAAC,QAAQ;;0CACX,2BAAC,SAAG;gCAAC,MAAM;0CACT,cAAA,2BAAC;oCAAI,OAAO;wCAAE,cAAc;oCAAO;;sDACjC,2BAAC;4CAAK,MAAM;;8DAAC,2BAAC,qBAAgB;oDAAC,IAAG;;;;;;gDAAoC;;;;;;;sDACtE,2BAAC;sDAAM,KAAK,SAAS,IAAI;;;;;;;;;;;;;;;;;0CAG7B,2BAAC,SAAG;gCAAC,MAAM;0CACT,cAAA,2BAAC;oCAAI,OAAO;wCAAE,cAAc;oCAAO;;sDACjC,2BAAC;4CAAK,MAAM;;8DAAC,2BAAC,qBAAgB;oDAAC,IAAG;;;;;;gDAAsC;;;;;;;sDACxE,2BAAC;sDAAM,KAAK,WAAW,IAAI;;;;;;;;;;;;;;;;;0CAG/B,2BAAC,SAAG;gCAAC,MAAM;0CACT,cAAA,2BAAC;oCAAI,OAAO;wCAAE,cAAc;oCAAO;;sDACjC,2BAAC;4CAAK,MAAM;;8DAAC,2BAAC,qBAAgB;oDAAC,IAAG;;;;;;gDAA2C;;;;;;;sDAC7E,2BAAC;sDAAM,EAAA,mBAAA,KAAK,QAAQ,cAAb,uCAAA,iBAAe,gBAAgB,KAAI;;;;;;;;;;;;;;;;;0CAG9C,2BAAC,SAAG;gCAAC,MAAM;0CACT,cAAA,2BAAC;oCAAI,OAAO;wCAAE,cAAc;oCAAO;;sDACjC,2BAAC;4CAAK,MAAM;;8DAAC,2BAAC,qBAAgB;oDAAC,IAAG;;;;;;gDAA0C;;;;;;;sDAC5E,2BAAC;sDAAM,EAAA,mBAAA,KAAK,QAAQ,cAAb,uCAAA,iBAAe,eAAe,KAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOjD,2BAAC,UAAI;oBAAC,MAAK;oBAAQ,qBAAO,2BAAC,qBAAgB;wBAAC,IAAG;;;;;;8BAC7C,cAAA,2BAAC,SAAG;wBAAC,QAAQ;;0CACX,2BAAC,SAAG;gCAAC,MAAM;0CACT,cAAA,2BAAC;oCAAI,OAAO;wCAAE,cAAc;oCAAO;;sDACjC,2BAAC;4CAAK,MAAM;;8DAAC,2BAAC,qBAAgB;oDAAC,IAAG;;;;;;gDAAsC;;;;;;;sDACxE,2BAAC;sDAAM,KAAK,WAAW,GAAG,IAAI,KAAK,KAAK,WAAW,EAAE,cAAc,KAAK;;;;;;;;;;;;;;;;;0CAG5E,2BAAC,SAAG;gCAAC,MAAM;0CACT,cAAA,2BAAC;oCAAI,OAAO;wCAAE,cAAc;oCAAO;;sDACjC,2BAAC;4CAAK,MAAM;;8DAAC,2BAAC,qBAAgB;oDAAC,IAAG;;;;;;gDAAsC;;;;;;;sDACxE,2BAAC;sDAAM,KAAK,WAAW,GAAG,IAAI,KAAK,KAAK,WAAW,EAAE,cAAc,KAAK;;;;;;;;;;;;;;;;;0CAG5E,2BAAC,SAAG;gCAAC,MAAM;0CACT,cAAA,2BAAC;oCAAI,OAAO;wCAAE,cAAc;oCAAO;;sDACjC,2BAAC;4CAAK,MAAM;;8DAAC,2BAAC,qBAAgB;oDAAC,IAAG;;;;;;gDAAgC;;;;;;;sDAClE,2BAAC;sDAAM,EAAA,mBAAA,KAAK,QAAQ,cAAb,uCAAA,iBAAe,KAAK,KAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASjD;GA/MM;;QAMS,YAAO;;;KANhB;IAiNN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BCgOf;;;eAAA;;;;;;0DA/b2C;6BACqB;4BACtB;8BAMnC;;;;;;;;;;AASP,MAAM,YAAsC,CAAC,EAC3C,OAAO,EACP,eAAe,EACf,aAAa,EACb,SAAS,EACV;;IACC,MAAM,OAAO,IAAA,YAAO;IACpB,MAAM,CAAC,KAAK,GAAG,UAAI,CAAC,OAAO;IAC3B,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAqB,EAAE;IACjE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,eAAQ,EAA0B,EAAE;IAChF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,eAAQ,EAAC;IAEzD,MAAM,SAAS,CAAC,EAAC,0BAAA,oCAAA,cAAe,EAAE;IAElC,IAAA,gBAAS,EAAC;QACR,IAAI,SACF;IAEJ,GAAG;QAAC;QAAS;KAAc;IAE3B,MAAM,iBAAiB;QAErB,MAAM;QAEN,IAAI,eAAe;gBA8BH,kCAAA,yBAGD,0BACQ,0BAMF,mCAAA;YAtCnB,MAAM,WAAW;gBAEf,MAAM,cAAc,IAAI;gBAGxB,SAAS,cAAc,OAAO;gBAG9B,UAAU,cAAc,QAAQ;gBAGhC,cAAc,cAAc,YAAY;gBAExC,aAAa,cAAc,WAAW;gBACtC,aAAa,cAAc,WAAW;gBACtC,QAAQ,cAAc,MAAM;gBAC5B,WAAW,cAAc,SAAS;gBAClC,WAAW,cAAc,SAAS;gBAGlC,WAAW,cAAc,SAAS;gBAClC,aAAa,cAAc,WAAW;gBACtC,kBAAkB,cAAc,gBAAgB;gBAChD,iBAAiB,cAAc,eAAe;gBAC9C,cAAc,cAAc,YAAY;gBAGxC,YAAY,cAAc,eAAe;gBACzC,UAAU,GAAE,0BAAA,cAAc,QAAQ,cAAtB,+CAAA,mCAAA,wBAAwB,QAAQ,cAAhC,uDAAA,iCAAkC,EAAE;gBAGhD,SAAS,GAAE,2BAAA,cAAc,QAAQ,cAAtB,+CAAA,yBAAwB,IAAI;gBACvC,iBAAiB,GAAE,2BAAA,cAAc,QAAQ,cAAtB,+CAAA,yBAAwB,iBAAiB;YAC9D;YAEA,KAAK,cAAc,CAAC;YAGpB,MAAM,cAAa,2BAAA,cAAc,QAAQ,cAAtB,gDAAA,oCAAA,yBAAwB,QAAQ,cAAhC,wDAAA,kCAAkC,EAAE;YACvD,IAAI,YACF,MAAM,mBAAmB;QAE7B,OAAO;YACL,KAAK,WAAW;YAChB,kBAAkB,EAAE;QACtB;IACF;IAGA,MAAM,yBAAyB,CAAC;QAE9B,IAAI,cAAc,UAAU,KAAK,WAAW;YAC1C,MAAM,aAAa,cAAc,UAAU;YAE3C,mBAAmB;YAGnB,KAAK,aAAa,CAAC,cAAc;YAGjC,KAAK,cAAc,CAAC;gBAClB,SAAS;gBACT,WAAW;gBACX,UAAU;gBACV,mBAAmB;gBACnB,MAAM;gBACN,aAAa;gBACb,aAAa;gBACb,cAAc;YAChB;QACF;QAGA,IAAI,cAAc,UAAU,KAAK,WAAW;YAC1C,MAAM,mBAAmB,eAAe,IAAI,CAAC,CAAA,WAC3C,SAAS,EAAE,KAAK,cAAc,UAAU;YAE1C,IAAI,kBAAkB;oBAUJ;gBARhB,MAAM,UAAe;oBACnB,WAAW,iBAAiB,IAAI;oBAChC,MAAM,iBAAiB,IAAI;oBAC3B,SAAS,iBAAiB,OAAO;oBACjC,UAAU,iBAAiB,QAAQ;oBACnC,aAAa,iBAAiB,WAAW;oBACzC,aAAa,iBAAiB,WAAW;oBACzC,mBAAmB,iBAAiB,iBAAiB;oBACrD,YAAY,GAAE,6BAAA,iBAAiB,QAAQ,cAAzB,iDAAA,2BAA2B,IAAI;gBAC/C;gBAGA,IAAI,iBAAiB,eAAe,KAAK,WACvC,QAAQ,SAAS,GAAG,iBAAiB,eAAe;gBAGtD,KAAK,cAAc,CAAC;YACtB;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,IAAA,4BAAqB;YAC5C,IAAI,SAAS,OAAO,EAClB,aAAa,SAAS,IAAI,IAAI,EAAE;iBAEhC,aAAa,EAAE;QAEnB,EAAE,OAAO,OAAO;YACd,aAAa,EAAE;QACjB;IAEF;IAGA,MAAM,qBAAqB,OAAO;QAChC,IAAI,CAAC,YAAY;YACf,kBAAkB,EAAE;YACpB;QACF;QAEA,oBAAoB;QACpB,IAAI;YAEF,MAAM,WAAW,MAAM,IAAA,2CAAoC,EAAC;gBAC1D;gBACA,WAAW;YACb;YAEA,IAAI,SAAS,OAAO,EAClB,kBAAkB,SAAS,IAAI,IAAI,EAAE;iBAErC,kBAAkB,EAAE;QAExB,EAAE,OAAO,OAAO;YACd,kBAAkB,EAAE;QACtB,SAAU;YACR,oBAAoB;QACtB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,SAAS,MAAM,KAAK,cAAc;YACxC,WAAW;YAGX,MAAM,aAAkB;gBACtB,iBAAiB,OAAO,UAAU;gBAClC,MAAM,OAAO,IAAI;gBACjB,SAAS,OAAO,OAAO;gBACvB,UAAU,OAAO,QAAQ;gBACzB,cAAc,OAAO,YAAY;gBACjC,aAAa,OAAO,WAAW;gBAC/B,aAAa,OAAO,WAAW;gBAC/B,QAAQ,OAAO,MAAM;gBACrB,WAAW,OAAO,SAAS;gBAC3B,WAAW,OAAO,SAAS;gBAE3B,WAAW,OAAO,SAAS;gBAC3B,aAAa,OAAO,WAAW;gBAC/B,kBAAkB,OAAO,gBAAgB;gBACzC,iBAAiB,OAAO,eAAe;gBACvC,cAAc,OAAO,YAAY;YACnC;YAEA,IAAI;YACJ,IAAI,QACF,WAAW,MAAM,IAAA,oBAAa,EAAC,cAAe,EAAE,EAAE;gBAAE,GAAG,aAAa;gBAAE,GAAG,UAAU;YAAC;iBAEpF,WAAW,MAAM,IAAA,oBAAa,EAAC;YAGjC,IAAI,SAAS,OAAO,EAClB;QAGJ,EAAE,OAAO,OAAO;YAEd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,WAAW;QACb;IACF;IAEA,OACE,2BAAC,WAAK;QACJ,OAAO,KAAK,aAAa,CAAC;YACxB,IAAI,SAAS,mCAAmC;QAClD;QACA,MAAM;QACN,UAAU,IAAM,gBAAgB;QAChC,MAAM;QACN,gBAAgB;QAChB,OAAO;QACP,OAAO;YAAE,KAAK;QAAG;QACjB,QAAQ;YACN,MAAM;gBAAE,WAAW;gBAAQ,WAAW;gBAAQ,SAAS;YAAO;QAChE;kBAEA,2BAAC,UAAI;YACH,MAAM;YACN,QAAO;YACP,gBAAgB;YAChB,eAAe;gBACb,WAAW;gBACX,aAAa;YACf;;gBAGA,2BAAC;oBAAI,OAAO;wBAAE,SAAS;wBAAQ,KAAK;oBAAO;;wBACzC,2BAAC,UAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAO,2BAAC,qBAAgB;gCAAC,IAAG;;;;;;4BAC5B,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS,KAAK,aAAa,CAAC;wCAAE,IAAI;oCAAwC;gCAAG;6BAAE;4BACzG,SAAS,2BAAC,qBAAgB;gCAAC,IAAG;;;;;;4BAC9B,OAAO;gCAAE,MAAM;4BAAE;sCAEjB,2BAAC,YAAM;gCAAC,aAAa,KAAK,aAAa,CAAC;oCAAE,IAAI;gCAA2C;0CACtF,UAAU,GAAG,CAAC,CAAC,WACd,2BAAC,YAAM,CAAC,MAAM;wCAAmB,OAAO,SAAS,EAAE;kDAChD,SAAS,WAAW,IAAI,SAAS,IAAI;uCADpB,SAAS,EAAE;;;;;;;;;;;;;;;wBAOrC,2BAAC,UAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAO,2BAAC,qBAAgB;gCAAC,IAAG;;;;;;4BAC5B,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS,KAAK,aAAa,CAAC;wCAAE,IAAI;oCAAwC;gCAAG;6BAAE;4BACzG,SAAS,2BAAC,qBAAgB;gCAAC,IAAG;;;;;;4BAC9B,OAAO;gCAAE,MAAM;4BAAE;sCAEjB,2BAAC,YAAM;gCACL,aAAa,KAAK,aAAa,CAAC;oCAAE,IAAI;gCAA2C;gCACjF,SAAS;gCACT,UAAU;gCACV,UAAU;gCACV,kBAAiB;gCACjB,cAAc,CAAC,OAAO,SACpB,OAAO,CAAA,mBAAA,6BAAA,OAAQ,QAAQ,KAAI,IAAI,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW;0CAGxE,eAAe,GAAG,CAAC,CAAC,WACnB,2BAAC,YAAM,CAAC,MAAM;wCAAmB,OAAO,SAAS,EAAE;;4CAChD,SAAS,WAAW,IAAI,SAAS,IAAI;4CAAC;4CAAG,SAAS,OAAO;4CAAC;;uCADzC,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;gBASvC,2BAAC;oBAAI,OAAO;wBAAE,SAAS;wBAAQ,KAAK;oBAAO;;wBACzC,2BAAC,UAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAO,2BAAC,qBAAgB;gCAAC,IAAG;;;;;;4BAC5B,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS,KAAK,aAAa,CAAC;wCAAE,IAAI;oCAAoC;gCAAG;6BAAE;4BACrG,OAAO;gCAAE,MAAM;4BAAE;sCAEjB,2BAAC,WAAK;gCAAC,aAAa,KAAK,aAAa,CAAC;oCAAE,IAAI;gCAAuC;;;;;;;;;;;wBAGtF,2BAAC,UAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAO,2BAAC,qBAAgB;gCAAC,IAAG;;;;;;4BAC5B,OAAO;gCAAE,MAAM;4BAAE;sCAEjB,2BAAC,WAAK;gCAAC,aAAa,KAAK,aAAa,CAAC;oCAAE,IAAI;gCAA8C;;;;;;;;;;;;;;;;;gBAI/F,2BAAC,UAAI,CAAC,IAAI;oBACR,MAAK;oBACL,OAAO,2BAAC,qBAAgB;wBAAC,IAAG;;;;;;8BAE5B,2BAAC,WAAK,CAAC,QAAQ;wBACb,MAAM;wBACN,aAAa,KAAK,aAAa,CAAC;4BAAE,IAAI;wBAA8C;;;;;;;;;;;gBAIxF,2BAAC,UAAI,CAAC,IAAI;oBACR,MAAK;oBACL,OAAO,2BAAC,qBAAgB;wBAAC,IAAG;;;;;;oBAC5B,SAAS,2BAAC,qBAAgB;wBAAC,IAAG;;;;;;8BAE9B,2BAAC,WAAK,CAAC,QAAQ;wBACb,MAAM;wBACN,aAAa,KAAK,aAAa,CAAC;4BAAE,IAAI;wBAA+C;;;;;;;;;;;gBAIzF,2BAAC,UAAI,CAAC,IAAI;oBACR,MAAK;oBACL,OAAO,2BAAC,qBAAgB;wBAAC,IAAG;;;;;;oBAC5B,SAAS,2BAAC,qBAAgB;wBAAC,IAAG;;;;;;8BAE9B,2BAAC,WAAK,CAAC,QAAQ;wBAAC,aAAa,KAAK,aAAa,CAAC;4BAAE,IAAI;wBAAyC;;;;;;;;;;;gBAIjG,2BAAC;oBAAI,OAAO;wBAAE,SAAS;wBAAQ,KAAK;oBAAO;;wBACzC,2BAAC,UAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAO,2BAAC,qBAAgB;gCAAC,IAAG;;;;;;4BAC5B,OAAO;gCAAE,MAAM;4BAAE;sCAEjB,2BAAC,WAAK;gCAAC,QAAQ;gCAAC,aAAa,KAAK,aAAa,CAAC;oCAAE,IAAI;gCAAuC;;;;;;;;;;;wBAG/F,2BAAC,UAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAO,2BAAC,qBAAgB;gCAAC,IAAG;;;;;;4BAC5B,OAAO;gCAAE,MAAM;4BAAE;sCAEjB,2BAAC,YAAM;gCAAC,QAAQ;gCAAC,aAAa,KAAK,aAAa,CAAC;oCAAE,IAAI;gCAAyC;;oCAC9F,2BAAC,YAAM,CAAC,MAAM;wCAAC,OAAM;kDAAI;;;;;;oCACzB,2BAAC,YAAM,CAAC,MAAM;wCAAC,OAAM;kDAAI;;;;;;oCACzB,2BAAC,YAAM,CAAC,MAAM;wCAAC,OAAM;kDAAI;;;;;;oCACzB,2BAAC,YAAM,CAAC,MAAM;wCAAC,OAAM;kDAAI;;;;;;;;;;;;;;;;;;;;;;;gBAK/B,2BAAC;oBAAI,OAAO;wBAAE,SAAS;wBAAQ,KAAK;oBAAO;;wBACzC,2BAAC,UAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAO,2BAAC,qBAAgB;gCAAC,IAAG;;;;;;4BAC5B,OAAO;gCAAE,MAAM;4BAAE;sCAEjB,2BAAC,WAAK;gCAAC,QAAQ;gCAAC,aAAY;;;;;;;;;;;wBAG9B,2BAAC,UAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAO,2BAAC,qBAAgB;gCAAC,IAAG;;;;;;4BAC5B,OAAO;gCAAE,MAAM;4BAAE;sCAEjB,2BAAC,YAAM;gCAAC,QAAQ;gCAAC,aAAY;;oCAC3B,2BAAC,YAAM,CAAC,MAAM;wCAAC,OAAO;kDAAM;;;;;;oCAC5B,2BAAC,YAAM,CAAC,MAAM;wCAAC,OAAO;kDAAO;;;;;;;;;;;;;;;;;;;;;;;gBAMnC,2BAAC;oBAAI,OAAO;wBAAE,SAAS;wBAAQ,KAAK;oBAAO;;wBACzC,2BAAC,UAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAO,2BAAC,qBAAgB;gCAAC,IAAG;;;;;;4BAC5B,OAAO;gCAAE,MAAM;4BAAE;sCAEjB,2BAAC,iBAAW;gCACV,KAAK;gCACL,KAAK;gCACL,OAAO;oCAAE,OAAO;gCAAO;gCACvB,aAAa,KAAK,aAAa,CAAC;oCAAE,IAAI;gCAA4C;;;;;;;;;;;wBAItF,2BAAC,UAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAO,2BAAC,qBAAgB;gCAAC,IAAG;;;;;;4BAC5B,OAAO;gCAAE,MAAM;4BAAE;sCAEjB,2BAAC,iBAAW;gCACV,KAAK;gCACL,KAAK;gCACL,MAAM;gCACN,OAAO;oCAAE,OAAO;gCAAO;gCACvB,aAAa,KAAK,aAAa,CAAC;oCAAE,IAAI;gCAA8C;;;;;;;;;;;;;;;;;gBAK1F,2BAAC;oBAAI,OAAO;wBAAE,SAAS;wBAAQ,KAAK;wBAAQ,YAAY;oBAAW;;wBACjE,2BAAC,UAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAO,2BAAC,qBAAgB;gCAAC,IAAG;;;;;;4BAC5B,eAAc;4BACd,OAAO;gCAAE,MAAM;4BAAE;sCAEjB,2BAAC,YAAM;;;;;;;;;;wBAGT,2BAAC,UAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAO,2BAAC,qBAAgB;gCAAC,IAAG;;;;;;4BAC5B,eAAc;4BACd,SAAS,2BAAC,qBAAgB;gCAAC,IAAG;;;;;;4BAC9B,OAAO;gCAAE,MAAM;4BAAE;sCAEjB,2BAAC,YAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnB;GA5aM;;QAMS,YAAO;QACL,UAAI,CAAC;;;KAPhB;IA8aN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BCrLf;;;eAAA;;;;;;;wEA1QwC;sCACV;iCACmB;6BACC;8BAC0C;4BAClD;8BACC;wCACoB;2EACzC;6EACE;gFACG;;;;;;;;;;AAE3B,MAAM,eAAyB;;IAC7B,MAAM,OAAO,IAAA,YAAO;IACpB,MAAM,YAAY,IAAA,aAAM,EAAa;IACrC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,IAAA,eAAQ,EAAC;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,eAAQ,EAAC;IACzD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,IAAA,eAAQ,EAAC;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,eAAQ,EAAC;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ;IAElD,MAAM,eAAe,OAAO;QAC1B,WAAK,CAAC,OAAO,CAAC;YACZ,OAAO,KAAK,aAAa,CAAC;gBAAE,IAAI;YAAsC;YACtE,SAAS,KAAK,aAAa,CAAC;gBAAE,IAAI;YAAwC;YAC1E,MAAM;gBACJ,MAAM,WAAW,MAAM,IAAA,oBAAa,EAAC,OAAO,EAAE;gBAC9C,IAAI,SAAS,OAAO,EAAE;wBACpB;qBAAA,qBAAA,UAAU,OAAO,cAAjB,iCAAA,mBAAmB,MAAM;gBAC3B;YACA,wBAAwB;YAC1B;QACF;IACF;IACA,MAAM,kBAAkB,CAAC;QACvB,mBAAmB;QACnB,MAAM,aAAa,OAAO,SAAS,WAC/B,AAAC,CAAA;YAAE,GAAG;YAAQ,GAAG;YAAa,GAAG;YAAe,GAAG;QAAc,CAAA,CAA4B,CAAC,KAAK,GACnG;QACJ,MAAM,UAA2D;YAC/D,MAAM;gBAAE,MAAM;gBAAQ,OAAO;YAAO;YACpC,WAAW;gBAAE,MAAM;gBAAQ,OAAO;YAAQ;YAC1C,aAAa;gBAAE,MAAM;gBAAY,OAAO;YAAS;YACjD,aAAa;gBAAE,MAAM;gBAAU,OAAO;YAAS;QACjD;QACA,MAAM,OAAO,aAAa,OAAO,CAAC,WAAW,GAAG;QAChD,IAAI,CAAC,MAAM,qBAAO,2BAAC,SAAG;sBAAC;;;;;;QACvB,qBAAO,2BAAC,SAAG;YAAC,OAAO,KAAK,KAAK;sBAAG,KAAK,IAAI;;;;;;IAC3C;IACA,MAAM,UAAuC;QAC3C;YACE,qBAAO,2BAAC,qBAAgB;gBAAC,IAAG;;;;;;YAC5B,WAAW;YACX,KAAK;YACL,UAAU;YACV,QAAQ,CAAC,GAAG;gBACV,gCAAgC;gBAChC,OAAO,OAAO,WAAW,IAAI,OAAO,IAAI,IAAI;YAC9C;QACF;QACA;YACE,qBAAO,2BAAC,qBAAgB;gBAAC,IAAG;;;;;;YAC5B,WAAW;YACX,KAAK;YACL,UAAU;QACZ;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,UAAU;YACV,cAAc;YACd,OAAO;YACP,QAAQ,CAAC,OAAS,QAAQ;QAC5B;QACA;YACE,qBAAO,2BAAC,qBAAgB;gBAAC,IAAG;;;;;;YAC5B,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,GAAG;oBACa;gBAAvB,OAAO,iBAAgB,YAAA,AAAC,OAAe,QAAQ,cAAxB,gCAAA,UAA0B,IAAI;YACvD;QACF;QACA;YACE,qBAAO,2BAAC,qBAAgB;gBAAC,IAAG;;;;;;YAC5B,WAAW;YACX,KAAK;YACL,UAAU;YACV,QAAQ,CAAC,GAAG;oBAEW,2BAAA,kBACnB,4BAAA;gBAFF,gCAAgC;gBAChC,MAAM,eAAe,EAAA,mBAAA,OAAO,QAAQ,cAAf,wCAAA,4BAAA,iBAAiB,QAAQ,cAAzB,gDAAA,0BAA2B,WAAW,OACzD,oBAAA,OAAO,QAAQ,cAAf,yCAAA,6BAAA,kBAAiB,QAAQ,cAAzB,iDAAA,2BAA2B,IAAI,KAC/B;gBACF,OAAO;YACT;QACF;QACA;YACE,qBAAO,2BAAC,qBAAgB;gBAAC,IAAG;;;;;;YAC5B,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,GAAG,uBACV,2BAAC,YAAM;oBACL,SAAS,OAAO,SAAS;oBACzB,MAAK;oBACL,QAAQ;;;;;;QAGd;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,cAAc;YACd,OAAO;YACP,QAAQ,CAAC,GAAG,uBACV,2BAAC,YAAM;oBACL,SAAS,OAAO,SAAS;oBACzB,MAAK;oBACL,QAAQ;;;;;;QAGd;QACA;YACE,qBAAO,2BAAC,qBAAgB;gBAAC,IAAG;;;;;;YAC5B,WAAW;YACX,KAAK;YACL,cAAc;YACd,OAAO;QACT;QACA;YACE,qBAAO,2BAAC,qBAAgB;gBAAC,IAAG;;;;;;YAC5B,WAAW;YACX,KAAK;YACL,cAAc;YACd,OAAO;QACT;QACA;YACE,qBAAO,2BAAC,qBAAgB;gBAAC,IAAG;;;;;;YAC5B,WAAW;YACX,KAAK;YACL,cAAc;YACd,WAAW;YACX,OAAO;QACT;QACA;YACE,qBAAO,2BAAC,qBAAgB;gBAAC,IAAG;;;;;;YAC5B,KAAK;YACL,cAAc;YACd,OAAO;YACP,QAAQ,CAAC,GAAG,uBACV,2BAAC,WAAK;;sCACJ,2BAAC,YAAM;4BACL,MAAK;4BACL,MAAK;4BACL,oBAAM,2BAAC,kBAAW;;;;;4BAClB,SAAS;gCACP,iBAAiB;gCACjB,sBAAsB;4BACxB;sCAEA,cAAA,2BAAC,qBAAgB;gCAAC,IAAG;;;;;;;;;;;sCAEvB,2BAAC,YAAM;4BACL,MAAK;4BACL,MAAK;4BACL,oBAAM,2BAAC,mBAAY;;;;;4BACnB,SAAS;gCACP,iBAAiB;gCACjB,oBAAoB;4BACtB;sCAEA,cAAA,2BAAC,qBAAgB;gCAAC,IAAG;;;;;;;;;;;sCAEvB,2BAAC,YAAM;4BACL,MAAK;4BACL,MAAK;4BACL,MAAM;4BACN,oBAAM,2BAAC,qBAAc;;;;;4BACrB,SAAS,IAAM,aAAa;sCAE5B,cAAA,2BAAC,qBAAgB;gCAAC,IAAG;;;;;;;;;;;;;;;;;QAI7B;KACD;IAED,qBACE,2BAAC,4BAAa;;0BACZ,2BAAC,kBAAQ;gBACP,aAAa,KAAK,aAAa,CAAC;oBAAE,IAAI;gBAAuB;gBAC7D,WAAW;gBACX,QAAO;gBACP,YAAY,wCAAuB;gBACnC,QAAQ;oBACN,YAAY;gBACd;gBACA,eAAe,IAAM;sCACnB,2BAAC,YAAM;4BAEL,oBAAM,2BAAC,yBAAkB;;;;;4BACzB,SAAS,IAAM,oBAAoB;sCACpC;2BAHK;;;;;sCAMN,2BAAC,YAAM;4BACL,MAAK;4BAEL,oBAAM,2BAAC,mBAAY;;;;;4BACnB,SAAS,IAAM,sBAAsB;sCAErC,cAAA,2BAAC,qBAAgB;gCAAC,IAAG;;;;;;2BAJjB;;;;;qBAMP;gBACD,SAAS,IAAA,sCAAqB,EAAC,kBAAW,EAAE;oBAC1C,MAAM;oBACN,SAAS;oBACT,cAAc;oBACd,WAAW;gBACb;gBACA,SAAS;;;;;;0BAGX,2BAAC,kBAAS;gBACR,SAAS;gBACT,iBAAiB;gBACjB,WAAW;wBACT;qBAAA,qBAAA,UAAU,OAAO,cAAjB,iCAAA,mBAAmB,MAAM;oBACzB,sBAAsB;gBACxB;;;;;;0BAGF,2BAAC,kBAAS;gBACR,SAAS;gBACT,iBAAiB;gBACjB,eAAe;gBACf,WAAW;wBACT;qBAAA,qBAAA,UAAU,OAAO,cAAjB,iCAAA,mBAAmB,MAAM;oBACzB,oBAAoB;gBACtB;;;;;;0BAGF,2BAAC,oBAAW;gBACV,SAAS;gBACT,iBAAiB;gBACjB,QAAQ;oBACN,oBAAoB;oBACpB,sBAAsB;gBACxB;gBACA,MAAM;;;;;;0BAGR,2BAAC,WAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,UAAU,IAAM,oBAAoB;gBACpC,QAAQ;gBACR,OAAO;gBACP,OAAO;oBAAE,KAAK;gBAAG;0BAEjB,cAAA,2BAAC,uBAAc;;;;;;;;;;;;;;;;AAIvB;GA5PM;;QACS,YAAO;;;KADhB;IA8PN,WAAe"}