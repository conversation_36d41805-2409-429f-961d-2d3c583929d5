((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] || []).push([
        ['src/pages/ai-models/index.tsx'],
{ "src/pages/ai-models/components/DataFormatTest.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _pageDataHandler = __mako_require__("src/utils/pageDataHandler.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Paragraph, Text } = _antd.Typography;
// 模拟您提供的后台返回数据格式
const mockBackendResponse = {
    "Items": [
        {
            "Id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
            "ModelTemplateId": "cd90513b-e7bb-4710-b46b-f302b818a779",
            "Name": "deepseek",
            "ModelId": "deepseek-chat",
            "Endpoint": "https://api.deepseek.com",
            "ProviderCode": "",
            "DisplayName": "deepseek-chat",
            "Description": "string",
            "ApiKey": "sk-ab6f4de3f1f1454facae3428554f7f36",
            "IsEnabled": true,
            "IsDefault": true,
            "MaxTokens": 0,
            "Temperature": 0,
            "CreatedTime": "2025-05-04T22:31:39.557",
            "UpdatedTime": "2025-05-04T14:31:27.313",
            "Template": {
                "Id": "cd90513b-e7bb-4710-b46b-f302b818a779",
                "Name": "deepseek-chat",
                "DisplayName": "deepseek-chat",
                "ModelId": "deepseek-chat",
                "Endpoint": "https://api.deepseek.com/v1",
                "Type": "Chat",
                "Description": "deepseek的聊天模型",
                "MaxContextLength": 32768,
                "MaxOutputLength": 4096,
                "SupportsStreaming": true,
                "SupportsFunctionCalling": true,
                "SupportsVision": false,
                "Notes": null,
                "Provider": {
                    "Id": "55555555-5555-5555-5555-555555555555",
                    "Name": "DeepSeek",
                    "DisplayName": "DeepSeek",
                    "Code": "DeepSeek",
                    "Description": "DeepSeek",
                    "Website": "https://www.deepseek.com/",
                    "IconUrl": null
                }
            }
        }
    ],
    "TotalCount": 1,
    "PageNumber": 1,
    "PageSize": 20,
    "TotalPages": 1,
    "HasPreviousPage": false,
    "HasNextPage": false
};
const DataFormatTest = ()=>{
    _s();
    const [testResults, setTestResults] = _react.default.useState([]);
    const testUnifiedHandler = ()=>{
        // 测试buildPagedQuery函数
        const testParams = {
            current: 1,
            pageSize: 20,
            Name: 'deepseek',
            ModelId: 'deepseek-chat',
            keyWord: 'test search'
        };
        const fieldMapping = {
            Name: 'Name',
            ModelId: 'ModelId',
            ProviderCode: 'ProviderCode',
            IsEnabled: 'IsEnabled'
        };
        const query = (0, _pageDataHandler.buildPagedQuery)(testParams, fieldMapping);
        // 测试handlePagedResponse函数
        const mockResponse = {
            success: true,
            data: mockBackendResponse
        };
        const result = (0, _pageDataHandler.handlePagedResponse)(mockResponse);
        setTestResults([
            {
                function: 'buildPagedQuery',
                input: {
                    testParams,
                    fieldMapping
                },
                output: query
            },
            {
                function: 'handlePagedResponse',
                input: mockResponse,
                output: result
            }
        ]);
    };
    const getModelTypeTag = (type)=>{
        const typeMap = {
            'Chat': {
                text: '对话模型',
                color: 'blue'
            },
            'Embedding': {
                text: '嵌入模型',
                color: 'green'
            },
            'TextToImage': {
                text: '文本生成图像模型',
                color: 'purple'
            },
            'ImageToText': {
                text: '图像描述模型',
                color: 'orange'
            }
        };
        const typeInfo = typeMap[type || ''];
        return typeInfo ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
            color: typeInfo.color,
            children: typeInfo.text
        }, void 0, false, {
            fileName: "src/pages/ai-models/components/DataFormatTest.tsx",
            lineNumber: 112,
            columnNumber: 23
        }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
            children: "-"
        }, void 0, false, {
            fileName: "src/pages/ai-models/components/DataFormatTest.tsx",
            lineNumber: 112,
            columnNumber: 75
        }, this);
    };
    const columns = [
        {
            title: '模型名称',
            dataIndex: 'Name',
            key: 'Name',
            render: (_, record)=>{
                return record.DisplayName || record.Name || '-';
            }
        },
        {
            title: '模型ID',
            dataIndex: 'ModelId',
            key: 'ModelId'
        },
        {
            title: '端点',
            dataIndex: 'Endpoint',
            key: 'Endpoint',
            render: (text)=>text || '-'
        },
        {
            title: '模型类型',
            dataIndex: 'ModelType',
            key: 'ModelType',
            render: (_, record)=>{
                var _record_Template;
                return getModelTypeTag((_record_Template = record.Template) === null || _record_Template === void 0 ? void 0 : _record_Template.Type);
            }
        },
        {
            title: '提供商',
            dataIndex: 'Provider',
            key: 'Provider',
            render: (_, record)=>{
                var _record_Template_Provider, _record_Template, _record_Template_Provider1, _record_Template1;
                const providerName = ((_record_Template = record.Template) === null || _record_Template === void 0 ? void 0 : (_record_Template_Provider = _record_Template.Provider) === null || _record_Template_Provider === void 0 ? void 0 : _record_Template_Provider.DisplayName) || ((_record_Template1 = record.Template) === null || _record_Template1 === void 0 ? void 0 : (_record_Template_Provider1 = _record_Template1.Provider) === null || _record_Template_Provider1 === void 0 ? void 0 : _record_Template_Provider1.Name) || '-';
                return providerName;
            }
        },
        {
            title: '状态',
            dataIndex: 'IsEnabled',
            key: 'IsEnabled',
            render: (_, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Switch, {
                    checked: record.IsEnabled,
                    size: "small",
                    disabled: true
                }, void 0, false, {
                    fileName: "src/pages/ai-models/components/DataFormatTest.tsx",
                    lineNumber: 159,
                    columnNumber: 9
                }, this)
        },
        {
            title: '默认模型',
            dataIndex: 'IsDefault',
            key: 'IsDefault',
            render: (_, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Switch, {
                    checked: record.IsDefault,
                    size: "small",
                    disabled: true
                }, void 0, false, {
                    fileName: "src/pages/ai-models/components/DataFormatTest.tsx",
                    lineNumber: 171,
                    columnNumber: 9
                }, this)
        },
        {
            title: '最大令牌',
            dataIndex: 'MaxTokens',
            key: 'MaxTokens'
        },
        {
            title: '温度',
            dataIndex: 'Temperature',
            key: 'Temperature'
        },
        {
            title: '创建时间',
            dataIndex: 'CreatedTime',
            key: 'CreatedTime',
            render: (text)=>new Date(text).toLocaleString()
        }
    ];
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        style: {
            padding: '24px'
        },
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                    level: 3,
                    children: "后台数据格式测试"
                }, void 0, false, {
                    fileName: "src/pages/ai-models/components/DataFormatTest.tsx",
                    lineNumber: 199,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    style: {
                        marginBottom: 16
                    },
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        type: "primary",
                        onClick: testUnifiedHandler,
                        children: "测试统一处理函数"
                    }, void 0, false, {
                        fileName: "src/pages/ai-models/components/DataFormatTest.tsx",
                        lineNumber: 202,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/ai-models/components/DataFormatTest.tsx",
                    lineNumber: 201,
                    columnNumber: 9
                }, this),
                testResults.length > 0 && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                            level: 4,
                            children: "统一处理函数测试结果："
                        }, void 0, false, {
                            fileName: "src/pages/ai-models/components/DataFormatTest.tsx",
                            lineNumber: 209,
                            columnNumber: 13
                        }, this),
                        testResults.map((result, index)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                size: "small",
                                style: {
                                    marginBottom: 8
                                },
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        strong: true,
                                        children: [
                                            result.function,
                                            ":"
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/ai-models/components/DataFormatTest.tsx",
                                        lineNumber: 212,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("pre", {
                                        style: {
                                            background: '#f5f5f5',
                                            padding: '8px',
                                            borderRadius: '4px',
                                            fontSize: '12px'
                                        },
                                        children: [
                                            "输入: ",
                                            JSON.stringify(result.input, null, 2),
                                            '\n',
                                            "输出: ",
                                            JSON.stringify(result.output, null, 2)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/ai-models/components/DataFormatTest.tsx",
                                        lineNumber: 213,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, index, true, {
                                fileName: "src/pages/ai-models/components/DataFormatTest.tsx",
                                lineNumber: 211,
                                columnNumber: 15
                            }, this))
                    ]
                }, void 0, true),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                        strong: true,
                        children: "后台返回的分页数据格式："
                    }, void 0, false, {
                        fileName: "src/pages/ai-models/components/DataFormatTest.tsx",
                        lineNumber: 224,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/ai-models/components/DataFormatTest.tsx",
                    lineNumber: 223,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("pre", {
                        style: {
                            background: '#f5f5f5',
                            padding: '12px',
                            borderRadius: '4px'
                        },
                        children: JSON.stringify({
                            Items: "数据数组",
                            TotalCount: "总记录数",
                            PageNumber: "当前页码",
                            PageSize: "每页大小",
                            TotalPages: "总页数",
                            HasPreviousPage: "是否有上一页",
                            HasNextPage: "是否有下一页"
                        }, null, 2)
                    }, void 0, false, {
                        fileName: "src/pages/ai-models/components/DataFormatTest.tsx",
                        lineNumber: 227,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/ai-models/components/DataFormatTest.tsx",
                    lineNumber: 226,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                    level: 4,
                    children: "数据展示效果："
                }, void 0, false, {
                    fileName: "src/pages/ai-models/components/DataFormatTest.tsx",
                    lineNumber: 240,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Table, {
                    columns: columns,
                    dataSource: mockBackendResponse.Items,
                    rowKey: "Id",
                    pagination: {
                        current: mockBackendResponse.PageNumber,
                        pageSize: mockBackendResponse.PageSize,
                        total: mockBackendResponse.TotalCount,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total, range)=>`第 ${range[0]}-${range[1]} 条/总共 ${total} 条`
                    }
                }, void 0, false, {
                    fileName: "src/pages/ai-models/components/DataFormatTest.tsx",
                    lineNumber: 241,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                    level: 4,
                    children: "分页信息："
                }, void 0, false, {
                    fileName: "src/pages/ai-models/components/DataFormatTest.tsx",
                    lineNumber: 255,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("ul", {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                            children: [
                                "总记录数: ",
                                mockBackendResponse.TotalCount
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/ai-models/components/DataFormatTest.tsx",
                            lineNumber: 257,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                            children: [
                                "当前页码: ",
                                mockBackendResponse.PageNumber
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/ai-models/components/DataFormatTest.tsx",
                            lineNumber: 258,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                            children: [
                                "每页大小: ",
                                mockBackendResponse.PageSize
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/ai-models/components/DataFormatTest.tsx",
                            lineNumber: 259,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                            children: [
                                "总页数: ",
                                mockBackendResponse.TotalPages
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/ai-models/components/DataFormatTest.tsx",
                            lineNumber: 260,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                            children: [
                                "是否有上一页: ",
                                mockBackendResponse.HasPreviousPage ? '是' : '否'
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/ai-models/components/DataFormatTest.tsx",
                            lineNumber: 261,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                            children: [
                                "是否有下一页: ",
                                mockBackendResponse.HasNextPage ? '是' : '否'
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/ai-models/components/DataFormatTest.tsx",
                            lineNumber: 262,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/ai-models/components/DataFormatTest.tsx",
                    lineNumber: 256,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "src/pages/ai-models/components/DataFormatTest.tsx",
            lineNumber: 198,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "src/pages/ai-models/components/DataFormatTest.tsx",
        lineNumber: 197,
        columnNumber: 5
    }, this);
};
_s(DataFormatTest, "qiz51xDMn38Bg3RW8SF/yAqtLQU=");
_c = DataFormatTest;
var _default = DataFormatTest;
var _c;
$RefreshReg$(_c, "DataFormatTest");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/ai-models/components/ModelDetail.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Text } = _antd.Typography;
const ModelDetail = ({ visible, onVisibleChange, onEdit, data })=>{
    var _data_Template_Provider, _data_Template, _data_Template_Provider1, _data_Template1, _data_Template2, _data_Template3, _data_Template4, _data_Template5, _data_Template6, _data_Template7, _data_Template8, _data_Template9, _data_Template10, _data_Template11, _data_Template12;
    _s();
    const intl = (0, _max.useIntl)();
    const getModelTypeTag = (type)=>{
        const typeMap = {
            'Chat': {
                text: '对话模型',
                color: 'blue'
            },
            'Embedding': {
                text: '嵌入模型',
                color: 'green'
            },
            'TextToImage': {
                text: '文本生成图像模型',
                color: 'purple'
            },
            'ImageToText': {
                text: '图像描述模型',
                color: 'orange'
            }
        };
        const typeInfo = typeMap[type || ''];
        return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
            color: typeInfo === null || typeInfo === void 0 ? void 0 : typeInfo.color,
            children: typeInfo === null || typeInfo === void 0 ? void 0 : typeInfo.text
        }, void 0, false, {
            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
            lineNumber: 31,
            columnNumber: 12
        }, this);
    };
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
        title: intl.formatMessage({
            id: 'pages.aiModels.detail.title'
        }),
        open: visible,
        onCancel: ()=>onVisibleChange(false),
        footer: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CloseOutlined, {}, void 0, false, {
                        fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                        lineNumber: 42,
                        columnNumber: 19
                    }, void 0),
                    onClick: ()=>onVisibleChange(false),
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.common.close"
                    }, void 0, false, {
                        fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                        lineNumber: 45,
                        columnNumber: 13
                    }, void 0)
                }, void 0, false, {
                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                    lineNumber: 41,
                    columnNumber: 11
                }, void 0),
                onEdit && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                    type: "primary",
                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                        fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                        lineNumber: 50,
                        columnNumber: 21
                    }, void 0),
                    onClick: ()=>{
                        onEdit();
                        onVisibleChange(false);
                    },
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.common.edit"
                    }, void 0, false, {
                        fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                        lineNumber: 56,
                        columnNumber: 15
                    }, void 0)
                }, void 0, false, {
                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                    lineNumber: 48,
                    columnNumber: 13
                }, void 0)
            ]
        }, void 0, true, {
            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
            lineNumber: 40,
            columnNumber: 9
        }, void 0),
        width: 900,
        style: {
            top: 50
        },
        styles: {
            body: {
                maxHeight: '70vh',
                overflowY: 'auto',
                padding: '24px'
            }
        },
        children: data && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
            style: {
                display: 'flex',
                flexDirection: 'column',
                gap: '24px'
            },
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                    size: "small",
                    title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.aiModels.detail.templateInfo"
                    }, void 0, false, {
                        fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                        lineNumber: 70,
                        columnNumber: 37
                    }, void 0),
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                        gutter: 16,
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                span: 12,
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        marginBottom: '16px'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            strong: true,
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                                    id: "pages.aiModels.detail.provider"
                                                }, void 0, false, {
                                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                                    lineNumber: 74,
                                                    columnNumber: 32
                                                }, this),
                                                "："
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 74,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            children: ((_data_Template = data.Template) === null || _data_Template === void 0 ? void 0 : (_data_Template_Provider = _data_Template.Provider) === null || _data_Template_Provider === void 0 ? void 0 : _data_Template_Provider.DisplayName) || ((_data_Template1 = data.Template) === null || _data_Template1 === void 0 ? void 0 : (_data_Template_Provider1 = _data_Template1.Provider) === null || _data_Template_Provider1 === void 0 ? void 0 : _data_Template_Provider1.Name) || '-'
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 75,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                    lineNumber: 73,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                lineNumber: 72,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                span: 12,
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        marginBottom: '16px'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            strong: true,
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                                    id: "pages.aiModels.detail.modelId"
                                                }, void 0, false, {
                                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                                    lineNumber: 80,
                                                    columnNumber: 32
                                                }, this),
                                                "："
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 80,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            children: ((_data_Template2 = data.Template) === null || _data_Template2 === void 0 ? void 0 : _data_Template2.ModelId) || '-'
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 81,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                    lineNumber: 79,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                lineNumber: 78,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                span: 12,
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        marginBottom: '16px'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            strong: true,
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                                    id: "pages.aiModels.detail.modelType"
                                                }, void 0, false, {
                                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                                    lineNumber: 86,
                                                    columnNumber: 32
                                                }, this),
                                                "："
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 86,
                                            columnNumber: 19
                                        }, this),
                                        getModelTypeTag((_data_Template3 = data.Template) === null || _data_Template3 === void 0 ? void 0 : _data_Template3.Type)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                    lineNumber: 85,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                lineNumber: 84,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                span: 12,
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        marginBottom: '16px'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            strong: true,
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                                    id: "pages.aiModels.detail.endpoint"
                                                }, void 0, false, {
                                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                                    lineNumber: 92,
                                                    columnNumber: 32
                                                }, this),
                                                "："
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 92,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            children: data.Endpoint || '-'
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 93,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                    lineNumber: 91,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                lineNumber: 90,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                span: 12,
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        marginBottom: '16px'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            strong: true,
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                                    id: "pages.aiModels.detail.supportsStreaming"
                                                }, void 0, false, {
                                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                                    lineNumber: 98,
                                                    columnNumber: 32
                                                }, this),
                                                "："
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 98,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                            color: ((_data_Template4 = data.Template) === null || _data_Template4 === void 0 ? void 0 : _data_Template4.SupportsStreaming) ? 'green' : 'red',
                                            children: ((_data_Template5 = data.Template) === null || _data_Template5 === void 0 ? void 0 : _data_Template5.SupportsStreaming) ? '支持' : '不支持'
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 99,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                    lineNumber: 97,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                lineNumber: 96,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                span: 12,
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        marginBottom: '16px'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            strong: true,
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                                    id: "pages.aiModels.detail.supportsFunctionCalling"
                                                }, void 0, false, {
                                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                                    lineNumber: 106,
                                                    columnNumber: 32
                                                }, this),
                                                "："
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 106,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                            color: ((_data_Template6 = data.Template) === null || _data_Template6 === void 0 ? void 0 : _data_Template6.SupportsFunctionCalling) ? 'green' : 'red',
                                            children: ((_data_Template7 = data.Template) === null || _data_Template7 === void 0 ? void 0 : _data_Template7.SupportsFunctionCalling) ? '支持' : '不支持'
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 107,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                    lineNumber: 105,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                lineNumber: 104,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                span: 12,
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        marginBottom: '16px'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            strong: true,
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                                    id: "pages.aiModels.detail.supportsVision"
                                                }, void 0, false, {
                                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                                    lineNumber: 114,
                                                    columnNumber: 32
                                                }, this),
                                                "："
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 114,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                            color: ((_data_Template8 = data.Template) === null || _data_Template8 === void 0 ? void 0 : _data_Template8.SupportsVision) ? 'green' : 'red',
                                            children: ((_data_Template9 = data.Template) === null || _data_Template9 === void 0 ? void 0 : _data_Template9.SupportsVision) ? '支持' : '不支持'
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 115,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                    lineNumber: 113,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                lineNumber: 112,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                        lineNumber: 71,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                    lineNumber: 70,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                    size: "small",
                    title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.aiModels.detail.basicInfo"
                    }, void 0, false, {
                        fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                        lineNumber: 124,
                        columnNumber: 37
                    }, void 0),
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                        gutter: 16,
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                span: 12,
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        marginBottom: '16px'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            strong: true,
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                                    id: "pages.aiModels.detail.name"
                                                }, void 0, false, {
                                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                                    lineNumber: 128,
                                                    columnNumber: 32
                                                }, this),
                                                "："
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 128,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            children: data.Name || '-'
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 129,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                    lineNumber: 127,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                lineNumber: 126,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                span: 12,
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        marginBottom: '16px'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            strong: true,
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                                    id: "pages.aiModels.detail.displayName"
                                                }, void 0, false, {
                                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                                    lineNumber: 134,
                                                    columnNumber: 32
                                                }, this),
                                                "："
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 134,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            children: data.DisplayName || '-'
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 135,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                    lineNumber: 133,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                lineNumber: 132,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                span: 24,
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        marginBottom: '16px'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            strong: true,
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                                    id: "pages.aiModels.detail.description"
                                                }, void 0, false, {
                                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                                    lineNumber: 140,
                                                    columnNumber: 32
                                                }, this),
                                                "："
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 140,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            children: data.Description || '-'
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 141,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                    lineNumber: 139,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                lineNumber: 138,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                span: 12,
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        marginBottom: '16px'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            strong: true,
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                                    id: "pages.aiModels.detail.apiKey"
                                                }, void 0, false, {
                                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                                    lineNumber: 146,
                                                    columnNumber: 32
                                                }, this),
                                                "："
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 146,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            children: data.ApiKey ? '***' : '-'
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 147,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                    lineNumber: 145,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                lineNumber: 144,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                span: 12,
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        marginBottom: '16px'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            strong: true,
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                                    id: "pages.aiModels.detail.isDefault"
                                                }, void 0, false, {
                                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                                    lineNumber: 152,
                                                    columnNumber: 32
                                                }, this),
                                                "："
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 152,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Switch, {
                                            checked: data.IsDefault,
                                            disabled: true,
                                            size: "small"
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 153,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                    lineNumber: 151,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                lineNumber: 150,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                span: 12,
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        marginBottom: '16px'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            strong: true,
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                                    id: "pages.aiModels.detail.status"
                                                }, void 0, false, {
                                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                                    lineNumber: 158,
                                                    columnNumber: 32
                                                }, this),
                                                "："
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 158,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Switch, {
                                            checked: data.IsEnabled,
                                            disabled: true,
                                            size: "small"
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 159,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                    lineNumber: 157,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                lineNumber: 156,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                        lineNumber: 125,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                    lineNumber: 124,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                    size: "small",
                    title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.aiModels.detail.configParams"
                    }, void 0, false, {
                        fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                        lineNumber: 166,
                        columnNumber: 37
                    }, void 0),
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                        gutter: 16,
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                span: 12,
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        marginBottom: '16px'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            strong: true,
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                                    id: "pages.aiModels.detail.maxTokens"
                                                }, void 0, false, {
                                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                                    lineNumber: 170,
                                                    columnNumber: 32
                                                }, this),
                                                "："
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 170,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            children: data.MaxTokens || '-'
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 171,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                    lineNumber: 169,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                lineNumber: 168,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                span: 12,
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        marginBottom: '16px'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            strong: true,
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                                    id: "pages.aiModels.detail.temperature"
                                                }, void 0, false, {
                                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                                    lineNumber: 176,
                                                    columnNumber: 32
                                                }, this),
                                                "："
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 176,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            children: data.Temperature || '-'
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 177,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                    lineNumber: 175,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                lineNumber: 174,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                span: 12,
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        marginBottom: '16px'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            strong: true,
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                                    id: "pages.aiModels.detail.maxContextLength"
                                                }, void 0, false, {
                                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                                    lineNumber: 182,
                                                    columnNumber: 32
                                                }, this),
                                                "："
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 182,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            children: ((_data_Template10 = data.Template) === null || _data_Template10 === void 0 ? void 0 : _data_Template10.MaxContextLength) || '-'
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 183,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                    lineNumber: 181,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                lineNumber: 180,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                span: 12,
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        marginBottom: '16px'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            strong: true,
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                                    id: "pages.aiModels.detail.maxOutputLength"
                                                }, void 0, false, {
                                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                                    lineNumber: 188,
                                                    columnNumber: 32
                                                }, this),
                                                "："
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 188,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            children: ((_data_Template11 = data.Template) === null || _data_Template11 === void 0 ? void 0 : _data_Template11.MaxOutputLength) || '-'
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 189,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                    lineNumber: 187,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                lineNumber: 186,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                        lineNumber: 167,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                    lineNumber: 166,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                    size: "small",
                    title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.aiModels.detail.otherInfo"
                    }, void 0, false, {
                        fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                        lineNumber: 196,
                        columnNumber: 37
                    }, void 0),
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                        gutter: 16,
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                span: 12,
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        marginBottom: '16px'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            strong: true,
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                                    id: "pages.aiModels.detail.createdTime"
                                                }, void 0, false, {
                                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                                    lineNumber: 200,
                                                    columnNumber: 32
                                                }, this),
                                                "："
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 200,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            children: data.CreatedTime ? new Date(data.CreatedTime).toLocaleString() : '-'
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 201,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                    lineNumber: 199,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                lineNumber: 198,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                span: 12,
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        marginBottom: '16px'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            strong: true,
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                                    id: "pages.aiModels.detail.updatedTime"
                                                }, void 0, false, {
                                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                                    lineNumber: 206,
                                                    columnNumber: 32
                                                }, this),
                                                "："
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 206,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            children: data.UpdatedTime ? new Date(data.UpdatedTime).toLocaleString() : '-'
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 207,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                    lineNumber: 205,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                lineNumber: 204,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                span: 24,
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        marginBottom: '16px'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            strong: true,
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                                    id: "pages.aiModels.detail.notes"
                                                }, void 0, false, {
                                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                                    lineNumber: 212,
                                                    columnNumber: 32
                                                }, this),
                                                "："
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 212,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            children: ((_data_Template12 = data.Template) === null || _data_Template12 === void 0 ? void 0 : _data_Template12.Notes) || '-'
                                        }, void 0, false, {
                                            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                            lineNumber: 213,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                    lineNumber: 211,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                                lineNumber: 210,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                        lineNumber: 197,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/ai-models/components/ModelDetail.tsx",
                    lineNumber: 196,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true, {
            fileName: "src/pages/ai-models/components/ModelDetail.tsx",
            lineNumber: 68,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "src/pages/ai-models/components/ModelDetail.tsx",
        lineNumber: 35,
        columnNumber: 5
    }, this);
};
_s(ModelDetail, "rlSgSjbewJ1PrR/Ile8g/kr050o=", false, function() {
    return [
        _max.useIntl
    ];
});
_c = ModelDetail;
var _default = ModelDetail;
var _c;
$RefreshReg$(_c, "ModelDetail");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/ai-models/components/ModelForm.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _rwxai = __mako_require__("src/services/rwxai/index.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const ModelForm = ({ visible, onVisibleChange, initialValues, onSuccess })=>{
    _s();
    const intl = (0, _max.useIntl)();
    const [form] = _antd.Form.useForm();
    const [loading, setLoading] = (0, _react.useState)(false);
    const [providers, setProviders] = (0, _react.useState)([]);
    const [modelTemplates, setModelTemplates] = (0, _react.useState)([]);
    const [loadingTemplates, setLoadingTemplates] = (0, _react.useState)(false);
    const isEdit = !!(initialValues === null || initialValues === void 0 ? void 0 : initialValues.Id);
    (0, _react.useEffect)(()=>{
        if (visible) initializeForm();
    }, [
        visible,
        initialValues
    ]);
    const initializeForm = async ()=>{
        await loadProviders();
        if (initialValues) {
            var _initialValues_Template_Provider, _initialValues_Template, _initialValues_Template1, _initialValues_Template2, _initialValues_Template_Provider1, _initialValues_Template3;
            const formData = {
                Name: initialValues.Name,
                ModelId: initialValues.ModelId,
                Endpoint: initialValues.Endpoint,
                ProviderCode: initialValues.ProviderCode,
                DisplayName: initialValues.DisplayName,
                Description: initialValues.Description,
                ApiKey: initialValues.ApiKey,
                IsEnabled: initialValues.IsEnabled,
                IsDefault: initialValues.IsDefault,
                MaxTokens: initialValues.MaxTokens,
                Temperature: initialValues.Temperature,
                FrequencyPenalty: initialValues.FrequencyPenalty,
                PresencePenalty: initialValues.PresencePenalty,
                SystemPrompt: initialValues.SystemPrompt,
                TemplateId: initialValues.ModelTemplateId,
                ProviderId: (_initialValues_Template = initialValues.Template) === null || _initialValues_Template === void 0 ? void 0 : (_initialValues_Template_Provider = _initialValues_Template.Provider) === null || _initialValues_Template_Provider === void 0 ? void 0 : _initialValues_Template_Provider.Id,
                ModelType: (_initialValues_Template1 = initialValues.Template) === null || _initialValues_Template1 === void 0 ? void 0 : _initialValues_Template1.Type,
                SupportsStreaming: (_initialValues_Template2 = initialValues.Template) === null || _initialValues_Template2 === void 0 ? void 0 : _initialValues_Template2.SupportsStreaming
            };
            form.setFieldsValue(formData);
            const providerId = (_initialValues_Template3 = initialValues.Template) === null || _initialValues_Template3 === void 0 ? void 0 : (_initialValues_Template_Provider1 = _initialValues_Template3.Provider) === null || _initialValues_Template_Provider1 === void 0 ? void 0 : _initialValues_Template_Provider1.Id;
            if (providerId) await loadModelTemplates(providerId);
        } else {
            form.resetFields();
            setModelTemplates([]);
        }
    };
    const handleFormValuesChange = (changedValues)=>{
        if (changedValues.ProviderId !== undefined) {
            const providerId = changedValues.ProviderId;
            loadModelTemplates(providerId);
            form.setFieldValue('TemplateId', undefined);
            form.setFieldsValue({
                ModelId: undefined,
                ModelType: undefined,
                Endpoint: undefined,
                SupportsStreaming: undefined,
                Name: undefined,
                Description: undefined,
                DisplayName: undefined,
                ProviderCode: undefined
            });
        }
        if (changedValues.TemplateId !== undefined) {
            const selectedTemplate = modelTemplates.find((template)=>template.Id === changedValues.TemplateId);
            if (selectedTemplate) {
                var _selectedTemplate_Provider;
                const updates = {
                    ModelType: selectedTemplate.Type,
                    Name: selectedTemplate.Name,
                    ModelId: selectedTemplate.ModelId,
                    Endpoint: selectedTemplate.Endpoint,
                    Description: selectedTemplate.Description,
                    DisplayName: selectedTemplate.DisplayName,
                    SupportsStreaming: selectedTemplate.SupportsStreaming,
                    ProviderCode: (_selectedTemplate_Provider = selectedTemplate.Provider) === null || _selectedTemplate_Provider === void 0 ? void 0 : _selectedTemplate_Provider.Code
                };
                if (selectedTemplate.MaxOutputLength !== undefined) updates.MaxTokens = selectedTemplate.MaxOutputLength;
                form.setFieldsValue(updates);
            }
        }
    };
    const loadProviders = async ()=>{
        try {
            const response = await (0, _rwxai.getEnabledAIProviders)();
            if (response.success) setProviders(response.data || []);
            else setProviders([]);
        } catch (error) {
            setProviders([]);
        }
    };
    const loadModelTemplates = async (providerId)=>{
        if (!providerId) {
            setModelTemplates([]);
            return;
        }
        setLoadingTemplates(true);
        try {
            const response = await (0, _rwxai.getAIModelTemplatesByProviderAndType)({
                providerId,
                modelType: undefined
            });
            if (response.success) setModelTemplates(response.data || []);
            else setModelTemplates([]);
        } catch (error) {
            setModelTemplates([]);
        } finally{
            setLoadingTemplates(false);
        }
    };
    const handleSubmit = async ()=>{
        try {
            const values = await form.validateFields();
            setLoading(true);
            const submitData = {
                modelTemplateId: values.TemplateId,
                name: values.Name,
                modelId: values.ModelId,
                endpoint: values.Endpoint,
                providerCode: values.ProviderCode,
                displayName: values.DisplayName,
                description: values.Description,
                apiKey: values.ApiKey,
                isEnabled: values.IsEnabled,
                isDefault: values.IsDefault,
                maxTokens: values.MaxTokens,
                temperature: values.Temperature,
                frequencyPenalty: values.FrequencyPenalty,
                presencePenalty: values.PresencePenalty,
                systemPrompt: values.SystemPrompt
            };
            let response;
            if (isEdit) response = await (0, _rwxai.updateAIModel)(initialValues.Id, {
                ...initialValues,
                ...submitData
            });
            else response = await (0, _rwxai.createAIModel)(submitData);
            if (response.success) onSuccess();
        } catch (error) {
            console.error('Form validation error:', error);
        } finally{
            setLoading(false);
        }
    };
    return (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
        title: intl.formatMessage({
            id: isEdit ? 'pages.aiModels.form.edit.title' : 'pages.aiModels.form.create.title'
        }),
        open: visible,
        onCancel: ()=>onVisibleChange(false),
        onOk: handleSubmit,
        confirmLoading: loading,
        width: 900,
        style: {
            top: 50
        },
        styles: {
            body: {
                maxHeight: '70vh',
                overflowY: 'auto',
                padding: '24px'
            }
        },
        children: (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
            form: form,
            layout: "vertical",
            onValuesChange: handleFormValuesChange,
            initialValues: {
                IsEnabled: true,
                Temperature: 0.7
            },
            children: [
                (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        display: 'flex',
                        gap: '16px'
                    },
                    children: [
                        (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            name: "ProviderId",
                            label: (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                id: "pages.aiModels.form.provider"
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                lineNumber: 258,
                                columnNumber: 20
                            }, void 0),
                            rules: [
                                {
                                    required: true,
                                    message: intl.formatMessage({
                                        id: 'pages.aiModels.form.provider.required'
                                    })
                                }
                            ],
                            tooltip: (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                id: "pages.aiModels.form.provider.tooltip"
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                lineNumber: 260,
                                columnNumber: 22
                            }, void 0),
                            style: {
                                flex: 1
                            },
                            children: (0, _jsxdevruntime.jsxDEV)(_antd.Select, {
                                placeholder: intl.formatMessage({
                                    id: 'pages.aiModels.form.provider.placeholder'
                                }),
                                children: providers.map((provider)=>(0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                        value: provider.Id,
                                        children: provider.DisplayName || provider.Name
                                    }, provider.Id, false, {
                                        fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                        lineNumber: 265,
                                        columnNumber: 17
                                    }, this))
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                lineNumber: 263,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                            lineNumber: 256,
                            columnNumber: 11
                        }, this),
                        (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            name: "TemplateId",
                            label: (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                id: "pages.aiModels.form.template"
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                lineNumber: 274,
                                columnNumber: 20
                            }, void 0),
                            rules: [
                                {
                                    required: true,
                                    message: intl.formatMessage({
                                        id: 'pages.aiModels.form.template.required'
                                    })
                                }
                            ],
                            tooltip: (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                id: "pages.aiModels.form.template.tooltip"
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                lineNumber: 276,
                                columnNumber: 22
                            }, void 0),
                            style: {
                                flex: 1
                            },
                            children: (0, _jsxdevruntime.jsxDEV)(_antd.Select, {
                                placeholder: intl.formatMessage({
                                    id: 'pages.aiModels.form.template.placeholder'
                                }),
                                loading: loadingTemplates,
                                allowClear: true,
                                showSearch: true,
                                optionFilterProp: "children",
                                filterOption: (input, option)=>String((option === null || option === void 0 ? void 0 : option.children) || '').toLowerCase().includes(input.toLowerCase()),
                                children: modelTemplates.map((template)=>(0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                        value: template.Id,
                                        children: [
                                            template.DisplayName || template.Name,
                                            " (",
                                            template.ModelId,
                                            ")"
                                        ]
                                    }, template.Id, true, {
                                        fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                        lineNumber: 290,
                                        columnNumber: 17
                                    }, this))
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                lineNumber: 279,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                            lineNumber: 272,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/ai-models/components/ModelForm.tsx",
                    lineNumber: 255,
                    columnNumber: 9
                }, this),
                (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        display: 'flex',
                        gap: '16px'
                    },
                    children: [
                        (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            name: "Name",
                            label: (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                id: "pages.aiModels.form.name"
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                lineNumber: 302,
                                columnNumber: 20
                            }, void 0),
                            rules: [
                                {
                                    required: true,
                                    message: intl.formatMessage({
                                        id: 'pages.aiModels.form.name.required'
                                    })
                                }
                            ],
                            style: {
                                flex: 1
                            },
                            children: (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                placeholder: intl.formatMessage({
                                    id: 'pages.aiModels.form.name.placeholder'
                                })
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                lineNumber: 306,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                            lineNumber: 300,
                            columnNumber: 11
                        }, this),
                        (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            name: "DisplayName",
                            label: (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                id: "pages.aiModels.form.displayName"
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                lineNumber: 311,
                                columnNumber: 20
                            }, void 0),
                            style: {
                                flex: 1
                            },
                            children: (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                placeholder: intl.formatMessage({
                                    id: 'pages.aiModels.form.displayName.placeholder'
                                })
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                lineNumber: 314,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                            lineNumber: 309,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/ai-models/components/ModelForm.tsx",
                    lineNumber: 299,
                    columnNumber: 9
                }, this),
                (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    name: "Description",
                    label: (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.aiModels.form.description"
                    }, void 0, false, {
                        fileName: "src/pages/ai-models/components/ModelForm.tsx",
                        lineNumber: 320,
                        columnNumber: 18
                    }, void 0),
                    children: (0, _jsxdevruntime.jsxDEV)(_antd.Input.TextArea, {
                        rows: 2,
                        placeholder: intl.formatMessage({
                            id: 'pages.aiModels.form.description.placeholder'
                        })
                    }, void 0, false, {
                        fileName: "src/pages/ai-models/components/ModelForm.tsx",
                        lineNumber: 322,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/ai-models/components/ModelForm.tsx",
                    lineNumber: 318,
                    columnNumber: 9
                }, this),
                (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    name: "SystemPrompt",
                    label: (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.aiModels.form.systemPrompt"
                    }, void 0, false, {
                        fileName: "src/pages/ai-models/components/ModelForm.tsx",
                        lineNumber: 330,
                        columnNumber: 18
                    }, void 0),
                    tooltip: (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.aiModels.form.systemPrompt.tooltip"
                    }, void 0, false, {
                        fileName: "src/pages/ai-models/components/ModelForm.tsx",
                        lineNumber: 331,
                        columnNumber: 20
                    }, void 0),
                    children: (0, _jsxdevruntime.jsxDEV)(_antd.Input.TextArea, {
                        rows: 3,
                        placeholder: intl.formatMessage({
                            id: 'pages.aiModels.form.systemPrompt.placeholder'
                        })
                    }, void 0, false, {
                        fileName: "src/pages/ai-models/components/ModelForm.tsx",
                        lineNumber: 333,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/ai-models/components/ModelForm.tsx",
                    lineNumber: 328,
                    columnNumber: 9
                }, this),
                (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    name: "ApiKey",
                    label: (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.aiModels.form.apiKey"
                    }, void 0, false, {
                        fileName: "src/pages/ai-models/components/ModelForm.tsx",
                        lineNumber: 341,
                        columnNumber: 18
                    }, void 0),
                    tooltip: (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.aiModels.form.apiKey.tooltip"
                    }, void 0, false, {
                        fileName: "src/pages/ai-models/components/ModelForm.tsx",
                        lineNumber: 342,
                        columnNumber: 20
                    }, void 0),
                    children: (0, _jsxdevruntime.jsxDEV)(_antd.Input.Password, {
                        placeholder: intl.formatMessage({
                            id: 'pages.aiModels.form.apiKey.placeholder'
                        })
                    }, void 0, false, {
                        fileName: "src/pages/ai-models/components/ModelForm.tsx",
                        lineNumber: 344,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/ai-models/components/ModelForm.tsx",
                    lineNumber: 339,
                    columnNumber: 9
                }, this),
                (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        display: 'flex',
                        gap: '16px'
                    },
                    children: [
                        (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            name: "ModelId",
                            label: (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                id: "pages.aiModels.form.modelId"
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                lineNumber: 351,
                                columnNumber: 20
                            }, void 0),
                            style: {
                                flex: 1
                            },
                            children: (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                disabled: true,
                                placeholder: intl.formatMessage({
                                    id: 'pages.aiModels.form.modelId.readonly'
                                })
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                lineNumber: 354,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                            lineNumber: 349,
                            columnNumber: 11
                        }, this),
                        (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            name: "ModelType",
                            label: (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                id: "pages.aiModels.form.modelType"
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                lineNumber: 359,
                                columnNumber: 20
                            }, void 0),
                            style: {
                                flex: 1
                            },
                            children: (0, _jsxdevruntime.jsxDEV)(_antd.Select, {
                                disabled: true,
                                placeholder: intl.formatMessage({
                                    id: 'pages.aiModels.form.modelType.readonly'
                                }),
                                children: [
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                        value: "0",
                                        children: "Chat"
                                    }, void 0, false, {
                                        fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                        lineNumber: 363,
                                        columnNumber: 15
                                    }, this),
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                        value: "1",
                                        children: "Embedding"
                                    }, void 0, false, {
                                        fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                        lineNumber: 364,
                                        columnNumber: 15
                                    }, this),
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                        value: "2",
                                        children: "Image Generation"
                                    }, void 0, false, {
                                        fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                        lineNumber: 365,
                                        columnNumber: 15
                                    }, this),
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                        value: "3",
                                        children: "Text to Speech"
                                    }, void 0, false, {
                                        fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                        lineNumber: 366,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                lineNumber: 362,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                            lineNumber: 357,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/ai-models/components/ModelForm.tsx",
                    lineNumber: 348,
                    columnNumber: 9
                }, this),
                (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        display: 'flex',
                        gap: '16px'
                    },
                    children: [
                        (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            name: "Endpoint",
                            label: (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                id: "pages.aiModels.form.endpoint"
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                lineNumber: 374,
                                columnNumber: 20
                            }, void 0),
                            style: {
                                flex: 1
                            },
                            children: (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                disabled: true,
                                placeholder: "从模板自动获取"
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                lineNumber: 377,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                            lineNumber: 372,
                            columnNumber: 11
                        }, this),
                        (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            name: "SupportsStreaming",
                            label: (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                id: "pages.aiModels.form.supportsStreaming"
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                lineNumber: 382,
                                columnNumber: 20
                            }, void 0),
                            style: {
                                flex: 1
                            },
                            children: (0, _jsxdevruntime.jsxDEV)(_antd.Select, {
                                disabled: true,
                                placeholder: "从模板自动获取",
                                children: [
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                        value: true,
                                        children: "支持"
                                    }, void 0, false, {
                                        fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                        lineNumber: 386,
                                        columnNumber: 15
                                    }, this),
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                        value: false,
                                        children: "不支持"
                                    }, void 0, false, {
                                        fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                        lineNumber: 387,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                lineNumber: 385,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                            lineNumber: 380,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/ai-models/components/ModelForm.tsx",
                    lineNumber: 371,
                    columnNumber: 9
                }, this),
                (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        display: 'flex',
                        gap: '16px'
                    },
                    children: [
                        (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            name: "MaxTokens",
                            label: (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                id: "pages.aiModels.form.maxTokens"
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                lineNumber: 396,
                                columnNumber: 20
                            }, void 0),
                            style: {
                                flex: 1
                            },
                            children: (0, _jsxdevruntime.jsxDEV)(_antd.InputNumber, {
                                min: 1,
                                max: 100000,
                                style: {
                                    width: '100%'
                                },
                                placeholder: intl.formatMessage({
                                    id: 'pages.aiModels.form.maxTokens.placeholder'
                                })
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                lineNumber: 399,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                            lineNumber: 394,
                            columnNumber: 11
                        }, this),
                        (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            name: "Temperature",
                            label: (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                id: "pages.aiModels.form.temperature"
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                lineNumber: 409,
                                columnNumber: 20
                            }, void 0),
                            style: {
                                flex: 1
                            },
                            children: (0, _jsxdevruntime.jsxDEV)(_antd.InputNumber, {
                                min: 0,
                                max: 2,
                                step: 0.1,
                                style: {
                                    width: '100%'
                                },
                                placeholder: intl.formatMessage({
                                    id: 'pages.aiModels.form.temperature.placeholder'
                                })
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                lineNumber: 412,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                            lineNumber: 407,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/ai-models/components/ModelForm.tsx",
                    lineNumber: 393,
                    columnNumber: 9
                }, this),
                (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        display: 'flex',
                        gap: '16px',
                        alignItems: 'flex-end'
                    },
                    children: [
                        (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            name: "IsEnabled",
                            label: (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                id: "pages.aiModels.form.isEnabled"
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                lineNumber: 425,
                                columnNumber: 20
                            }, void 0),
                            valuePropName: "checked",
                            style: {
                                flex: 1
                            },
                            children: (0, _jsxdevruntime.jsxDEV)(_antd.Switch, {}, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                lineNumber: 429,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                            lineNumber: 423,
                            columnNumber: 11
                        }, this),
                        (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            name: "IsDefault",
                            label: (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                id: "pages.aiModels.form.isDefault"
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                lineNumber: 434,
                                columnNumber: 20
                            }, void 0),
                            valuePropName: "checked",
                            tooltip: (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                id: "pages.aiModels.form.isDefault.tooltip"
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                lineNumber: 436,
                                columnNumber: 22
                            }, void 0),
                            style: {
                                flex: 1
                            },
                            children: (0, _jsxdevruntime.jsxDEV)(_antd.Switch, {}, void 0, false, {
                                fileName: "src/pages/ai-models/components/ModelForm.tsx",
                                lineNumber: 439,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/ai-models/components/ModelForm.tsx",
                            lineNumber: 432,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/ai-models/components/ModelForm.tsx",
                    lineNumber: 422,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "src/pages/ai-models/components/ModelForm.tsx",
            lineNumber: 245,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "src/pages/ai-models/components/ModelForm.tsx",
        lineNumber: 231,
        columnNumber: 5
    }, this);
};
_s(ModelForm, "NGfFwAXKyFht0v74cvOAIlexsus=", false, function() {
    return [
        _max.useIntl,
        _antd.Form.useForm
    ];
});
_c = ModelForm;
var _default = ModelForm;
var _c;
$RefreshReg$(_c, "ModelForm");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/ai-models/index.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
var _protable = __mako_require__("node_modules/@ant-design/pro-table/es/index.js");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _rwxai = __mako_require__("src/services/rwxai/index.ts");
var _pageDataHandler = __mako_require__("src/utils/pageDataHandler.ts");
var _ModelForm = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/ai-models/components/ModelForm.tsx"));
var _ModelDetail = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/ai-models/components/ModelDetail.tsx"));
var _DataFormatTest = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/ai-models/components/DataFormatTest.tsx"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const AIModelsPage = ()=>{
    _s();
    const intl = (0, _max.useIntl)();
    const actionRef = (0, _react.useRef)(null);
    const [createModalVisible, setCreateModalVisible] = (0, _react.useState)(false);
    const [editModalVisible, setEditModalVisible] = (0, _react.useState)(false);
    const [detailModalVisible, setDetailModalVisible] = (0, _react.useState)(false);
    const [testModalVisible, setTestModalVisible] = (0, _react.useState)(false);
    const [currentRecord, setCurrentRecord] = (0, _react.useState)();
    const handleDelete = async (record)=>{
        _antd.Modal.confirm({
            title: intl.formatMessage({
                id: 'pages.aiModels.delete.confirm.title'
            }),
            content: intl.formatMessage({
                id: 'pages.aiModels.delete.confirm.content'
            }),
            onOk: async ()=>{
                const response = await (0, _rwxai.deleteAIModel)(record.Id);
                if (response.success) {
                    var _actionRef_current;
                    (_actionRef_current = actionRef.current) === null || _actionRef_current === void 0 || _actionRef_current.reload();
                }
            // 成功和错误消息会由统一响应处理系统自动显示
            }
        });
    };
    const getModelTypeTag = (type)=>{
        // 允许后端返回字符串枚举或数字枚举
        const normalized = typeof type === 'number' ? ({
            0: 'Chat',
            1: 'Embedding',
            2: 'TextToImage',
            3: 'ImageToText'
        })[type] : type;
        const typeMap = {
            Chat: {
                text: '对话模型',
                color: 'blue'
            },
            Embedding: {
                text: '嵌入模型',
                color: 'green'
            },
            TextToImage: {
                text: '文本生成图像模型',
                color: 'purple'
            },
            ImageToText: {
                text: '图像描述模型',
                color: 'orange'
            }
        };
        const info = normalized ? typeMap[normalized] : undefined;
        if (!info) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
            children: "-"
        }, void 0, false, {
            fileName: "src/pages/ai-models/index.tsx",
            lineNumber: 47,
            columnNumber: 23
        }, this);
        return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
            color: info.color,
            children: info.text
        }, void 0, false, {
            fileName: "src/pages/ai-models/index.tsx",
            lineNumber: 48,
            columnNumber: 12
        }, this);
    };
    const columns = [
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.aiModels.table.name"
            }, void 0, false, {
                fileName: "src/pages/ai-models/index.tsx",
                lineNumber: 52,
                columnNumber: 14
            }, this),
            dataIndex: 'Name',
            key: 'Name',
            ellipsis: true,
            render: (_, record)=>{
                // 优先显示 DisplayName，如果没有则显示 Name
                return record.DisplayName || record.Name || '-';
            }
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.aiModels.table.modelId"
            }, void 0, false, {
                fileName: "src/pages/ai-models/index.tsx",
                lineNumber: 62,
                columnNumber: 14
            }, this),
            dataIndex: 'ModelId',
            key: 'ModelId',
            ellipsis: true
        },
        {
            title: '端点',
            dataIndex: 'Endpoint',
            key: 'Endpoint',
            ellipsis: true,
            hideInSearch: true,
            width: 200,
            render: (text)=>text || '-'
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.aiModels.table.modelType"
            }, void 0, false, {
                fileName: "src/pages/ai-models/index.tsx",
                lineNumber: 77,
                columnNumber: 14
            }, this),
            dataIndex: 'ModelType',
            key: 'ModelType',
            render: (_, record)=>{
                var _Template;
                return getModelTypeTag((_Template = record.Template) === null || _Template === void 0 ? void 0 : _Template.Type);
            }
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.aiModels.table.provider"
            }, void 0, false, {
                fileName: "src/pages/ai-models/index.tsx",
                lineNumber: 85,
                columnNumber: 14
            }, this),
            dataIndex: 'Provider',
            key: 'Provider',
            ellipsis: true,
            render: (_, record)=>{
                var _record_Template_Provider, _record_Template, _record_Template_Provider1, _record_Template1;
                // 优先显示 DisplayName，如果没有则显示 Name
                const providerName = ((_record_Template = record.Template) === null || _record_Template === void 0 ? void 0 : (_record_Template_Provider = _record_Template.Provider) === null || _record_Template_Provider === void 0 ? void 0 : _record_Template_Provider.DisplayName) || ((_record_Template1 = record.Template) === null || _record_Template1 === void 0 ? void 0 : (_record_Template_Provider1 = _record_Template1.Provider) === null || _record_Template_Provider1 === void 0 ? void 0 : _record_Template_Provider1.Name) || '-';
                return providerName;
            }
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.aiModels.table.status"
            }, void 0, false, {
                fileName: "src/pages/ai-models/index.tsx",
                lineNumber: 98,
                columnNumber: 14
            }, this),
            dataIndex: 'IsEnabled',
            key: 'IsEnabled',
            render: (_, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Switch, {
                    checked: record.IsEnabled,
                    size: "small",
                    disabled: true
                }, void 0, false, {
                    fileName: "src/pages/ai-models/index.tsx",
                    lineNumber: 102,
                    columnNumber: 9
                }, this)
        },
        {
            title: '默认模型',
            dataIndex: 'IsDefault',
            key: 'IsDefault',
            hideInSearch: true,
            width: 100,
            render: (_, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Switch, {
                    checked: record.IsDefault,
                    size: "small",
                    disabled: true
                }, void 0, false, {
                    fileName: "src/pages/ai-models/index.tsx",
                    lineNumber: 116,
                    columnNumber: 9
                }, this)
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.aiModels.table.maxTokens"
            }, void 0, false, {
                fileName: "src/pages/ai-models/index.tsx",
                lineNumber: 124,
                columnNumber: 14
            }, this),
            dataIndex: 'MaxTokens',
            key: 'MaxTokens',
            hideInSearch: true,
            width: 120
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.aiModels.table.temperature"
            }, void 0, false, {
                fileName: "src/pages/ai-models/index.tsx",
                lineNumber: 131,
                columnNumber: 14
            }, this),
            dataIndex: 'Temperature',
            key: 'Temperature',
            hideInSearch: true,
            width: 120
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.aiModels.table.createdTime"
            }, void 0, false, {
                fileName: "src/pages/ai-models/index.tsx",
                lineNumber: 138,
                columnNumber: 14
            }, this),
            dataIndex: 'CreatedTime',
            key: 'CreatedTime',
            hideInSearch: true,
            valueType: 'dateTime',
            width: 180
        },
        {
            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                id: "pages.aiModels.table.actions"
            }, void 0, false, {
                fileName: "src/pages/ai-models/index.tsx",
                lineNumber: 146,
                columnNumber: 14
            }, this),
            key: 'actions',
            hideInSearch: true,
            width: 200,
            render: (_, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "link",
                            size: "small",
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EyeOutlined, {}, void 0, false, {
                                fileName: "src/pages/ai-models/index.tsx",
                                lineNumber: 155,
                                columnNumber: 19
                            }, void 0),
                            onClick: ()=>{
                                setCurrentRecord(record);
                                setDetailModalVisible(true);
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                id: "pages.common.view"
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/index.tsx",
                                lineNumber: 161,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/ai-models/index.tsx",
                            lineNumber: 152,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "link",
                            size: "small",
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                                fileName: "src/pages/ai-models/index.tsx",
                                lineNumber: 166,
                                columnNumber: 19
                            }, void 0),
                            onClick: ()=>{
                                setCurrentRecord(record);
                                setEditModalVisible(true);
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                id: "pages.common.edit"
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/index.tsx",
                                lineNumber: 172,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/ai-models/index.tsx",
                            lineNumber: 163,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "link",
                            size: "small",
                            danger: true,
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                fileName: "src/pages/ai-models/index.tsx",
                                lineNumber: 178,
                                columnNumber: 19
                            }, void 0),
                            onClick: ()=>handleDelete(record),
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                id: "pages.common.delete"
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/index.tsx",
                                lineNumber: 181,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/ai-models/index.tsx",
                            lineNumber: 174,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/ai-models/index.tsx",
                    lineNumber: 151,
                    columnNumber: 9
                }, this)
        }
    ];
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_protable.ProTable, {
                headerTitle: intl.formatMessage({
                    id: 'pages.aiModels.title'
                }),
                actionRef: actionRef,
                rowKey: "Id",
                pagination: _pageDataHandler.defaultPaginationConfig,
                search: {
                    labelWidth: 120
                },
                toolBarRender: ()=>[
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExperimentOutlined, {}, void 0, false, {
                                fileName: "src/pages/ai-models/index.tsx",
                                lineNumber: 201,
                                columnNumber: 19
                            }, void 0),
                            onClick: ()=>setTestModalVisible(true),
                            children: "数据格式测试"
                        }, "test", false, {
                            fileName: "src/pages/ai-models/index.tsx",
                            lineNumber: 199,
                            columnNumber: 11
                        }, void 0),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "primary",
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PlusOutlined, {}, void 0, false, {
                                fileName: "src/pages/ai-models/index.tsx",
                                lineNumber: 209,
                                columnNumber: 19
                            }, void 0),
                            onClick: ()=>setCreateModalVisible(true),
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                id: "pages.common.create"
                            }, void 0, false, {
                                fileName: "src/pages/ai-models/index.tsx",
                                lineNumber: 212,
                                columnNumber: 13
                            }, void 0)
                        }, "primary", false, {
                            fileName: "src/pages/ai-models/index.tsx",
                            lineNumber: 206,
                            columnNumber: 11
                        }, void 0)
                    ],
                request: (0, _pageDataHandler.createProTableRequest)(_rwxai.getAIModels, {
                    Name: 'Name',
                    ModelId: 'ModelId',
                    ProviderCode: 'ProviderCode',
                    IsEnabled: 'IsEnabled'
                }),
                columns: columns
            }, void 0, false, {
                fileName: "src/pages/ai-models/index.tsx",
                lineNumber: 190,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_ModelForm.default, {
                visible: createModalVisible,
                onVisibleChange: setCreateModalVisible,
                onSuccess: ()=>{
                    var _actionRef_current;
                    (_actionRef_current = actionRef.current) === null || _actionRef_current === void 0 || _actionRef_current.reload();
                    setCreateModalVisible(false);
                }
            }, void 0, false, {
                fileName: "src/pages/ai-models/index.tsx",
                lineNumber: 224,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_ModelForm.default, {
                visible: editModalVisible,
                onVisibleChange: setEditModalVisible,
                initialValues: currentRecord,
                onSuccess: ()=>{
                    var _actionRef_current;
                    (_actionRef_current = actionRef.current) === null || _actionRef_current === void 0 || _actionRef_current.reload();
                    setEditModalVisible(false);
                }
            }, void 0, false, {
                fileName: "src/pages/ai-models/index.tsx",
                lineNumber: 233,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_ModelDetail.default, {
                visible: detailModalVisible,
                onVisibleChange: setDetailModalVisible,
                onEdit: ()=>{
                    setEditModalVisible(true);
                    setDetailModalVisible(false);
                },
                data: currentRecord
            }, void 0, false, {
                fileName: "src/pages/ai-models/index.tsx",
                lineNumber: 243,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                title: "后台数据格式测试",
                open: testModalVisible,
                onCancel: ()=>setTestModalVisible(false),
                footer: null,
                width: 1200,
                style: {
                    top: 20
                },
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_DataFormatTest.default, {}, void 0, false, {
                    fileName: "src/pages/ai-models/index.tsx",
                    lineNumber: 261,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/ai-models/index.tsx",
                lineNumber: 253,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/ai-models/index.tsx",
        lineNumber: 189,
        columnNumber: 5
    }, this);
};
_s(AIModelsPage, "znB5bc8I9rDA7Jd4deNMvbySdRs=", false, function() {
    return [
        _max.useIntl
    ];
});
_c = AIModelsPage;
var _default = AIModelsPage;
var _c;
$RefreshReg$(_c, "AIModelsPage");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=src_pages_ai-models_index_tsx-async.js.map