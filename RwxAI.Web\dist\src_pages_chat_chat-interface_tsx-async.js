((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] || []).push([
        ['src/pages/chat/chat-interface.tsx'],
{ "src/pages/chat/chat-interface.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _ChatInterface = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/chat/components/ChatInterface.tsx"));
var _SessionForm = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/chat/components/SessionForm.tsx"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const ChatInterfacePage = ()=>{
    _s();
    (0, _max.useIntl)();
    const [sessionFormVisible, setSessionFormVisible] = (0, _react.useState)(false);
    const [editingSession, setEditingSession] = (0, _react.useState)(null);
    const [formLoading, setFormLoading] = (0, _react.useState)(false);
    const formRef = (0, _react.useRef)(null);
    const handleCreateSession = ()=>{
        setEditingSession(null);
        setSessionFormVisible(true);
    };
    const handleEditSession = (session)=>{
        setEditingSession(session);
        setSessionFormVisible(true);
    };
    const handleSessionFormClose = ()=>{
        setSessionFormVisible(false);
        setEditingSession(null);
        setFormLoading(false);
    };
    const handleSessionFormSuccess = ()=>{
        setSessionFormVisible(false);
        setEditingSession(null);
        setFormLoading(false);
        // 这里可以触发聊天界面的刷新
        window.location.reload();
    };
    const handleFormSubmit = ()=>{
        if (formRef.current) {
            setFormLoading(true);
            formRef.current.submit();
        }
    };
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        style: {
            height: '100vh',
            overflow: 'hidden'
        },
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_ChatInterface.default, {
                onCreateSession: handleCreateSession,
                onEditSession: handleEditSession
            }, void 0, false, {
                fileName: "src/pages/chat/chat-interface.tsx",
                lineNumber: 47,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                title: editingSession ? '编辑会话' : '创建新会话',
                open: sessionFormVisible,
                onCancel: handleSessionFormClose,
                onOk: handleFormSubmit,
                confirmLoading: formLoading,
                width: 600,
                footer: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        onClick: handleSessionFormClose,
                        children: "取消"
                    }, "cancel", false, {
                        fileName: "src/pages/chat/chat-interface.tsx",
                        lineNumber: 60,
                        columnNumber: 11
                    }, void 0),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        type: "primary",
                        loading: formLoading,
                        onClick: handleFormSubmit,
                        children: editingSession ? '更新' : '创建'
                    }, "submit", false, {
                        fileName: "src/pages/chat/chat-interface.tsx",
                        lineNumber: 63,
                        columnNumber: 11
                    }, void 0)
                ],
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_SessionForm.default, {
                    ref: formRef,
                    visible: sessionFormVisible,
                    onVisibleChange: setSessionFormVisible,
                    onSuccess: handleSessionFormSuccess,
                    initialValues: editingSession
                }, void 0, false, {
                    fileName: "src/pages/chat/chat-interface.tsx",
                    lineNumber: 73,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/chat/chat-interface.tsx",
                lineNumber: 52,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/chat/chat-interface.tsx",
        lineNumber: 46,
        columnNumber: 5
    }, this);
};
_s(ChatInterfacePage, "B9kt/C4ejVY4+byttLtUwh77U8w=", false, function() {
    return [
        _max.useIntl
    ];
});
_c = ChatInterfacePage;
var _default = ChatInterfacePage;
var _c;
$RefreshReg$(_c, "ChatInterfacePage");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/chat/components/ChatInterface.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _x = __mako_require__("node_modules/@ant-design/x/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _rwxai = __mako_require__("src/services/rwxai/index.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Sider, Content } = _antd.Layout;
const { Text, Title } = _antd.Typography;
const ChatInterface = ({ onCreateSession, onEditSession })=>{
    _s();
    (0, _max.useIntl)();
    const [sessions, setSessions] = (0, _react.useState)([]);
    const [currentSession, setCurrentSession] = (0, _react.useState)(null);
    const [messages, setMessages] = (0, _react.useState)([]);
    const [loading, setLoading] = (0, _react.useState)(false);
    const [sessionsLoading, setSessionsLoading] = (0, _react.useState)(true);
    // 加载会话列表
    const loadSessions = async ()=>{
        try {
            setSessionsLoading(true);
            const response = await (0, _rwxai.getChatSessions)();
            if (response.success) {
                var _response_data;
                setSessions(response.data || []);
                // 如果有会话且没有选中的会话，选中第一个
                if (((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.length) > 0 && !currentSession) setCurrentSession(response.data[0]);
            }
        } catch (error) {
            _antd.message.error('加载会话列表失败');
        } finally{
            setSessionsLoading(false);
        }
    };
    // 加载消息
    const loadMessages = (session)=>{
        if (session.Messages) {
            const formattedMessages = session.Messages.map((msg)=>({
                    id: msg.Id,
                    content: msg.Content,
                    role: msg.Role,
                    createdAt: msg.CreatedTime
                }));
            setMessages(formattedMessages);
        } else setMessages([]);
    };
    (0, _react.useEffect)(()=>{
        loadSessions();
    }, []);
    (0, _react.useEffect)(()=>{
        if (currentSession) loadMessages(currentSession);
    }, [
        currentSession
    ]);
    // 发送消息的处理函数
    const handleSendMessage = async (messageText)=>{
        if (!currentSession) {
            _antd.message.error('请先选择一个会话');
            return;
        }
        if (!messageText.trim()) return;
        try {
            setLoading(true);
            // 添加用户消息到本地状态
            const userMessage = {
                id: Date.now().toString(),
                content: messageText,
                role: 'user',
                timestamp: new Date().toISOString()
            };
            setMessages((prev)=>[
                    ...prev,
                    userMessage
                ]);
            // 发送消息到后端
            const response = await (0, _rwxai.sendMessage)(currentSession.Id, {
                Content: messageText,
                ModelId: currentSession.ModelId,
                Temperature: currentSession.Temperature,
                MaxTokens: currentSession.MaxTokens
            });
            // 添加AI回复到本地状态
            if (response.success && response.data) {
                const aiMessage = {
                    id: (Date.now() + 1).toString(),
                    content: response.data.Content || '抱歉，我现在无法回复。',
                    role: 'assistant',
                    timestamp: new Date().toISOString()
                };
                setMessages((prev)=>[
                        ...prev,
                        aiMessage
                    ]);
            }
        } catch (error) {
            console.error('发送消息失败:', error);
            _antd.message.error('发送消息失败，请重试');
        } finally{
            setLoading(false);
        }
    };
    // 删除会话
    const handleDeleteSession = async (session)=>{
        try {
            await (0, _rwxai.deleteChatSession)(session.Id);
            _antd.message.success('删除成功');
            await loadSessions();
            if ((currentSession === null || currentSession === void 0 ? void 0 : currentSession.Id) === session.Id) {
                setCurrentSession(null);
                setMessages([]);
            }
        } catch (error) {
            _antd.message.error('删除失败');
        }
    };
    // 会话列表项渲染
    const renderSessionItem = (session)=>{
        var _session_Messages, _session_Model;
        const isActive = (currentSession === null || currentSession === void 0 ? void 0 : currentSession.Id) === session.Id;
        const messageCount = ((_session_Messages = session.Messages) === null || _session_Messages === void 0 ? void 0 : _session_Messages.length) || 0;
        return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
            className: `session-item ${isActive ? 'active' : ''}`,
            onClick: ()=>setCurrentSession(session),
            onDoubleClick: ()=>{
                // 双击激活对话，确保会话被选中并且可以继续对话
                setCurrentSession(session);
                // 可以在这里添加额外的激活逻辑，比如聚焦到输入框
                setTimeout(()=>{
                    const inputElement = document.querySelector('.ant-input');
                    if (inputElement) inputElement.focus();
                }, 100);
            },
            style: {
                padding: '12px 16px',
                cursor: 'pointer',
                borderRadius: '8px',
                margin: '4px 8px',
                backgroundColor: isActive ? '#e6f7ff' : 'transparent',
                border: isActive ? '1px solid #91d5ff' : '1px solid transparent',
                transition: 'all 0.2s ease'
            },
            onMouseEnter: (e)=>{
                if (!isActive) e.currentTarget.style.backgroundColor = '#f5f5f5';
            },
            onMouseLeave: (e)=>{
                if (!isActive) e.currentTarget.style.backgroundColor = 'transparent';
            },
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                style: {
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'flex-start'
                },
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            flex: 1,
                            minWidth: 0
                        },
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    display: 'flex',
                                    alignItems: 'center',
                                    marginBottom: '4px'
                                },
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        strong: true,
                                        ellipsis: true,
                                        style: {
                                            fontSize: '14px'
                                        },
                                        children: session.Name
                                    }, void 0, false, {
                                        fileName: "src/pages/chat/components/ChatInterface.tsx",
                                        lineNumber: 208,
                                        columnNumber: 15
                                    }, this),
                                    session.IsActive && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Badge, {
                                        status: "processing",
                                        style: {
                                            marginLeft: '8px'
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/chat/components/ChatInterface.tsx",
                                        lineNumber: 212,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/chat/components/ChatInterface.tsx",
                                lineNumber: 207,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                type: "secondary",
                                style: {
                                    fontSize: '12px'
                                },
                                children: ((_session_Model = session.Model) === null || _session_Model === void 0 ? void 0 : _session_Model.Name) || '未知模型'
                            }, void 0, false, {
                                fileName: "src/pages/chat/components/ChatInterface.tsx",
                                lineNumber: 218,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    marginTop: '4px'
                                },
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    type: "secondary",
                                    style: {
                                        fontSize: '11px'
                                    },
                                    children: [
                                        messageCount,
                                        " 条消息"
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/chat/components/ChatInterface.tsx",
                                    lineNumber: 222,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/chat/components/ChatInterface.tsx",
                                lineNumber: 221,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/chat/components/ChatInterface.tsx",
                        lineNumber: 206,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                        size: "small",
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                title: "编辑",
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    type: "text",
                                    size: "small",
                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                                        fileName: "src/pages/chat/components/ChatInterface.tsx",
                                        lineNumber: 232,
                                        columnNumber: 23
                                    }, void 0),
                                    onClick: (e)=>{
                                        e.stopPropagation();
                                        onEditSession === null || onEditSession === void 0 || onEditSession(session);
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/chat/components/ChatInterface.tsx",
                                    lineNumber: 229,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/chat/components/ChatInterface.tsx",
                                lineNumber: 228,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                title: "删除",
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    type: "text",
                                    size: "small",
                                    danger: true,
                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                        fileName: "src/pages/chat/components/ChatInterface.tsx",
                                        lineNumber: 244,
                                        columnNumber: 23
                                    }, void 0),
                                    onClick: (e)=>{
                                        e.stopPropagation();
                                        handleDeleteSession(session);
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/chat/components/ChatInterface.tsx",
                                    lineNumber: 240,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/chat/components/ChatInterface.tsx",
                                lineNumber: 239,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/chat/components/ChatInterface.tsx",
                        lineNumber: 227,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/chat/components/ChatInterface.tsx",
                lineNumber: 205,
                columnNumber: 9
            }, this)
        }, session.Id, false, {
            fileName: "src/pages/chat/components/ChatInterface.tsx",
            lineNumber: 170,
            columnNumber: 7
        }, this);
    };
    // 欢迎页面的建议提示
    const welcomePrompts = [
        {
            key: 'help',
            label: '如何使用这个AI助手？'
        },
        {
            key: 'features',
            label: '有哪些功能可以使用？'
        },
        {
            key: 'examples',
            label: '给我一些使用示例'
        },
        {
            key: 'settings',
            label: '如何调整对话设置？'
        }
    ];
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Layout, {
        style: {
            height: '100vh',
            backgroundColor: '#fff'
        },
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Sider, {
                width: 280,
                style: {
                    backgroundColor: '#fafafa',
                    borderRight: '1px solid #f0f0f0',
                    overflow: 'hidden'
                },
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column'
                    },
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                padding: '16px',
                                borderBottom: '1px solid #f0f0f0'
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    display: 'flex',
                                    justifyContent: 'space-between',
                                    alignItems: 'center'
                                },
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                        level: 4,
                                        style: {
                                            margin: 0
                                        },
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                            id: "pages.chat.sessions"
                                        }, void 0, false, {
                                            fileName: "src/pages/chat/components/ChatInterface.tsx",
                                            lineNumber: 293,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/chat/components/ChatInterface.tsx",
                                        lineNumber: 292,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "primary",
                                        size: "small",
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PlusOutlined, {}, void 0, false, {
                                            fileName: "src/pages/chat/components/ChatInterface.tsx",
                                            lineNumber: 298,
                                            columnNumber: 23
                                        }, void 0),
                                        onClick: onCreateSession,
                                        children: "新建"
                                    }, void 0, false, {
                                        fileName: "src/pages/chat/components/ChatInterface.tsx",
                                        lineNumber: 295,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/chat/components/ChatInterface.tsx",
                                lineNumber: 291,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/chat/components/ChatInterface.tsx",
                            lineNumber: 290,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                flex: 1,
                                overflow: 'auto'
                            },
                            children: sessionsLoading ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    padding: '20px',
                                    textAlign: 'center'
                                },
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    type: "secondary",
                                    children: "加载中..."
                                }, void 0, false, {
                                    fileName: "src/pages/chat/components/ChatInterface.tsx",
                                    lineNumber: 310,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/chat/components/ChatInterface.tsx",
                                lineNumber: 309,
                                columnNumber: 15
                            }, this) : sessions.length === 0 ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    padding: '20px',
                                    textAlign: 'center'
                                },
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    type: "secondary",
                                    children: "暂无会话"
                                }, void 0, false, {
                                    fileName: "src/pages/chat/components/ChatInterface.tsx",
                                    lineNumber: 314,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/chat/components/ChatInterface.tsx",
                                lineNumber: 313,
                                columnNumber: 15
                            }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    padding: '8px 0'
                                },
                                children: sessions.map(renderSessionItem)
                            }, void 0, false, {
                                fileName: "src/pages/chat/components/ChatInterface.tsx",
                                lineNumber: 317,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/chat/components/ChatInterface.tsx",
                            lineNumber: 307,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/chat/components/ChatInterface.tsx",
                    lineNumber: 288,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/chat/components/ChatInterface.tsx",
                lineNumber: 280,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Content, {
                style: {
                    display: 'flex',
                    flexDirection: 'column'
                },
                children: currentSession ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_x.Conversations, {
                    items: messages,
                    renderItem: (item)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_x.Bubble, {
                            content: item.content,
                            avatar: item.role === 'user' ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                    fileName: "src/pages/chat/components/ChatInterface.tsx",
                                    lineNumber: 335,
                                    columnNumber: 35
                                }, void 0),
                                style: {
                                    backgroundColor: '#1890ff'
                                }
                            }, void 0, false, {
                                fileName: "src/pages/chat/components/ChatInterface.tsx",
                                lineNumber: 335,
                                columnNumber: 21
                            }, void 0) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.RobotOutlined, {}, void 0, false, {
                                    fileName: "src/pages/chat/components/ChatInterface.tsx",
                                    lineNumber: 337,
                                    columnNumber: 35
                                }, void 0),
                                style: {
                                    backgroundColor: '#52c41a'
                                }
                            }, void 0, false, {
                                fileName: "src/pages/chat/components/ChatInterface.tsx",
                                lineNumber: 337,
                                columnNumber: 21
                            }, void 0),
                            placement: item.role === 'user' ? 'end' : 'start',
                            typing: item.role === 'assistant' && loading
                        }, void 0, false, {
                            fileName: "src/pages/chat/components/ChatInterface.tsx",
                            lineNumber: 331,
                            columnNumber: 15
                        }, void 0),
                    renderHeader: ()=>{
                        var _currentSession_Model;
                        return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                padding: '16px 24px',
                                borderBottom: '1px solid #f0f0f0',
                                backgroundColor: '#fff'
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                    level: 4,
                                    style: {
                                        margin: 0
                                    },
                                    children: currentSession.Name
                                }, void 0, false, {
                                    fileName: "src/pages/chat/components/ChatInterface.tsx",
                                    lineNumber: 350,
                                    columnNumber: 17
                                }, void 0),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    type: "secondary",
                                    children: [
                                        (_currentSession_Model = currentSession.Model) === null || _currentSession_Model === void 0 ? void 0 : _currentSession_Model.Name,
                                        " • 温度: ",
                                        currentSession.Temperature,
                                        " • 最大令牌: ",
                                        currentSession.MaxTokens
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/chat/components/ChatInterface.tsx",
                                    lineNumber: 353,
                                    columnNumber: 17
                                }, void 0)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/chat/components/ChatInterface.tsx",
                            lineNumber: 345,
                            columnNumber: 15
                        }, void 0);
                    },
                    renderFooter: ()=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                padding: '16px 24px',
                                borderTop: '1px solid #f0f0f0'
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_x.Sender, {
                                loading: loading,
                                onSubmit: handleSendMessage,
                                placeholder: "输入消息..."
                            }, void 0, false, {
                                fileName: "src/pages/chat/components/ChatInterface.tsx",
                                lineNumber: 360,
                                columnNumber: 17
                            }, void 0)
                        }, void 0, false, {
                            fileName: "src/pages/chat/components/ChatInterface.tsx",
                            lineNumber: 359,
                            columnNumber: 15
                        }, void 0),
                    style: {
                        height: '100%'
                    }
                }, void 0, false, {
                    fileName: "src/pages/chat/components/ChatInterface.tsx",
                    lineNumber: 328,
                    columnNumber: 11
                }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        height: '100%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        flexDirection: 'column'
                    },
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_x.Welcome, {
                        title: "欢迎使用 RwxAI 聊天助手",
                        description: "选择一个会话开始对话，或创建新的会话",
                        extra: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_x.Prompts, {
                            title: "快速开始",
                            items: welcomePrompts,
                            onItemClick: (item)=>{
                                // 这里可以处理快速提示的点击
                                _antd.message.info(`您选择了: ${item.label}`);
                            }
                        }, void 0, false, {
                            fileName: "src/pages/chat/components/ChatInterface.tsx",
                            lineNumber: 381,
                            columnNumber: 17
                        }, void 0)
                    }, void 0, false, {
                        fileName: "src/pages/chat/components/ChatInterface.tsx",
                        lineNumber: 377,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/chat/components/ChatInterface.tsx",
                    lineNumber: 370,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "src/pages/chat/components/ChatInterface.tsx",
                lineNumber: 326,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/chat/components/ChatInterface.tsx",
        lineNumber: 278,
        columnNumber: 5
    }, this);
};
_s(ChatInterface, "aoPAnT+wovGEbkuNzjQfTbBVgF8=", false, function() {
    return [
        _max.useIntl
    ];
});
_c = ChatInterface;
var _default = ChatInterface;
var _c;
$RefreshReg$(_c, "ChatInterface");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=src_pages_chat_chat-interface_tsx-async.js.map