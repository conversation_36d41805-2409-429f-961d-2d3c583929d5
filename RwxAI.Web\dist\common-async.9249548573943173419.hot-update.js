globalThis.makoModuleHotUpdate('common', {
    modules: {
        "src/services/rwxai/aiModels.ts": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            function _export(target, all) {
                for(var name in all)Object.defineProperty(target, name, {
                    enumerable: true,
                    get: all[name]
                });
            }
            __mako_require__.e(exports, {
                aiModelsApi: function() {
                    return aiModelsApi;
                },
                batchDeleteAIModels: function() {
                    return batchDeleteAIModels;
                },
                createAIModel: function() {
                    return createAIModel;
                },
                /**
 * 默认导出统一API对象（可选）
 */ default: function() {
                    return _default;
                },
                deleteAIModel: function() {
                    return deleteAIModel;
                },
                getAIModelById: function() {
                    return getAIModelById;
                },
                getAIModelTemplateById: function() {
                    return getAIModelTemplateById;
                },
                getAIModelTemplatesByProviderAndType: function() {
                    return getAIModelTemplatesByProviderAndType;
                },
                getAIModelTemplatesByType: function() {
                    return getAIModelTemplatesByType;
                },
                getAIModels: function() {
                    return getAIModels;
                },
                getAllEnabledModels: function() {
                    return getAllEnabledModels;
                },
                getEnabledAIProviders: function() {
                    return getEnabledAIProviders;
                },
                getModelsByProviderCode: function() {
                    return getModelsByProviderCode;
                },
                getModelsByProviderId: function() {
                    return getModelsByProviderId;
                },
                getModelsByProviderType: function() {
                    return getModelsByProviderType;
                },
                testAIModelConnection: function() {
                    return testAIModelConnection;
                },
                toggleAIModelStatus: function() {
                    return toggleAIModelStatus;
                },
                updateAIModel: function() {
                    return updateAIModel;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
            var _request = __mako_require__("src/utils/request.ts");
            var _pageDataHandler = __mako_require__("src/utils/pageDataHandler.ts");
            var _api = __mako_require__("src/utils/api.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            const API_PREFIX = (0, _api.getApiPrefix)();
            async function getAIModels(params) {
                const url = params ? `${API_PREFIX}/AIModels/PageModels?${(0, _pageDataHandler.toQueryString)(params)}` : `${API_PREFIX}/AIModels/PageModels`;
                return _request.httpRequest.get(url, {
                    showErrorNotification: true
                });
            }
            async function getAIModelById(id) {
                return _request.httpRequest.get(`${API_PREFIX}/AIModels/${id}`, {
                    showErrorNotification: true
                });
            }
            async function createAIModel(data) {
                return _request.httpRequest.post(`${API_PREFIX}/AIModels`, data, {
                    showSuccessMessage: true,
                    showErrorNotification: true,
                    successMessage: '创建AI模型成功'
                });
            }
            async function updateAIModel(id, data) {
                return _request.httpRequest.put(`${API_PREFIX}/AIModels/${id}`, data, {
                    showSuccessMessage: true,
                    showErrorNotification: true,
                    successMessage: '更新AI模型成功'
                });
            }
            async function deleteAIModel(id) {
                return _request.httpRequest.delete(`${API_PREFIX}/AIModels/${id}`, {
                    showSuccessMessage: true,
                    showErrorNotification: true,
                    successMessage: '删除AI模型成功'
                });
            }
            async function batchDeleteAIModels(ids) {
                return _request.httpRequest.post(`${API_PREFIX}/AIModels/batch-delete`, {
                    ids
                }, {
                    showSuccessMessage: true,
                    showErrorNotification: true,
                    successMessage: `成功删除 ${ids.length} 个AI模型`
                });
            }
            async function testAIModelConnection(id) {
                return _request.httpRequest.post(`${API_PREFIX}/AIModels/${id}/test`, {}, {
                    showSuccessMessage: true,
                    showErrorNotification: true,
                    successMessage: 'AI模型连接测试成功'
                });
            }
            async function toggleAIModelStatus(id, enabled) {
                return _request.httpRequest.patch(`${API_PREFIX}/AIModels/${id}/status`, {
                    enabled
                }, {
                    showSuccessMessage: true,
                    showErrorNotification: true,
                    successMessage: enabled ? '启用AI模型成功' : '禁用AI模型成功'
                });
            }
            async function getAIModelTemplatesByType(modelType) {
                return _request.httpRequest.get(`${API_PREFIX}/AIModelTemplates/by-type/${modelType}`, {
                    showErrorNotification: true
                });
            }
            async function getAIModelTemplatesByProviderAndType(params) {
                const queryParams = new URLSearchParams();
                if (params.providerId) queryParams.append('providerId', params.providerId);
                if (params.modelType) queryParams.append('modelType', params.modelType.toString());
                return _request.httpRequest.get(`${API_PREFIX}/AIModelTemplates/by-provider-and-type?${queryParams}`, {
                    showErrorNotification: true
                });
            }
            async function getAIModelTemplateById(id) {
                return _request.httpRequest.get(`${API_PREFIX}/AIModelTemplates/${id}`, {
                    showErrorNotification: true
                });
            }
            async function getEnabledAIProviders() {
                return _request.httpRequest.get(`${API_PREFIX}/AIProviders/enabled`, {
                    showErrorNotification: true
                });
            }
            async function getModelsByProviderId(providerId) {
                return _request.httpRequest.get(`${API_PREFIX}/AIProviders/${providerId}/models`, {
                    showErrorNotification: true
                });
            }
            async function getModelsByProviderCode(providerCode) {
                return _request.httpRequest.get(`${API_PREFIX}/AIProviders/by-code/${providerCode}/models`, {
                    showErrorNotification: true
                });
            }
            async function getModelsByProviderType(providerType) {
                return _request.httpRequest.get(`${API_PREFIX}/AIProviders/type/${providerType}/models`, {
                    showErrorNotification: true
                });
            }
            async function getAllEnabledModels() {
                return _request.httpRequest.get(`${API_PREFIX}/AIProviders/models/all`, {
                    showErrorNotification: true
                });
            }
            const aiModelsApi = {
                // 核心CRUD操作
                getAIModels,
                getAIModelById,
                createAIModel,
                updateAIModel,
                deleteAIModel,
                // 增强功能
                batchDeleteAIModels,
                testAIModelConnection,
                toggleAIModelStatus,
                // 模板管理
                getAIModelTemplatesByType,
                getAIModelTemplatesByProviderAndType,
                getAIModelTemplateById,
                // 提供商管理
                getEnabledAIProviders,
                getModelsByProviderId,
                getModelsByProviderCode,
                getModelsByProviderType,
                getAllEnabledModels
            };
            var _default = aiModelsApi;
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '18442320157183392171';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/.umi/plugin-openapi/openapi.tsx": [
            "vendors",
            "src/.umi/plugin-openapi/openapi.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Admin.tsx": [
            "p__Admin"
        ],
        "src/pages/Welcome.tsx": [
            "p__Welcome"
        ],
        "src/pages/ai-models/index.tsx": [
            "common",
            "src/pages/ai-models/index.tsx"
        ],
        "src/pages/api-test/index.tsx": [
            "common",
            "src/pages/api-test/index.tsx"
        ],
        "src/pages/apps/index.tsx": [
            "common",
            "p__apps__index"
        ],
        "src/pages/auth-test/index.tsx": [
            "common",
            "src/pages/auth-test/index.tsx"
        ],
        "src/pages/chat/chat-interface.tsx": [
            "vendors",
            "common",
            "src/pages/chat/chat-interface.tsx"
        ],
        "src/pages/chat/index.tsx": [
            "common",
            "p__chat__index"
        ],
        "src/pages/chat/session/[id].tsx": [
            "common",
            "p__chat__session__id"
        ],
        "src/pages/knowledge/[id]/files.tsx": [
            "common",
            "p__knowledge__id__files"
        ],
        "src/pages/knowledge/index.tsx": [
            "common",
            "p__knowledge__index"
        ],
        "src/pages/plugins/index.tsx": [
            "common",
            "p__plugins__index"
        ],
        "src/pages/response-demo/index.tsx": [
            "src/pages/response-demo/index.tsx"
        ],
        "src/pages/table-list/index.tsx": [
            "src/pages/table-list/index.tsx"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ],
        "src/pages/user/register/index.tsx": [
            "common",
            "p__user__register__index"
        ]
    });
    ;
});

//# sourceMappingURL=common-async.9249548573943173419.hot-update.js.map