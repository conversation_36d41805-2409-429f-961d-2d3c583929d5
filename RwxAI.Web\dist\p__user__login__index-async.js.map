{"version": 3, "sources": ["src/pages/user/login/index.tsx"], "sourcesContent": ["import {\r\n  LockOutlined,\r\n  UserOutlined,\r\n  RobotOutlined,\r\n  SafetyCertificateOutlined,\r\n} from '@ant-design/icons';\r\nimport {\r\n  LoginForm,\r\n  ProFormCheckbox,\r\n  ProFormText,\r\n} from '@ant-design/pro-components';\r\nimport {\r\n  FormattedMessage,\r\n  Helmet,\r\n  SelectLang,\r\n  useIntl,\r\n  useModel,\r\n} from '@umijs/max';\r\nimport { Alert, App, Card, Typography } from 'antd';\r\nimport { createStyles } from 'antd-style';\r\nimport React, { useState } from 'react';\r\nimport { flushSync } from 'react-dom';\r\nimport { login } from '@/services/rwxai';\r\nimport { saveLoginInfo } from '@/utils/auth';\r\nimport Settings from '../../../../config/defaultSettings';\r\n\r\nconst { Title, Paragraph } = Typography;\r\n\r\nconst useStyles = createStyles(({ token }) => {\r\n  return {\r\n    container: {\r\n      display: 'flex',\r\n      height: '100vh',\r\n      overflow: 'hidden',\r\n      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\r\n      position: 'relative',\r\n    },\r\n    leftSection: {\r\n      flex: 1,\r\n      display: 'flex',\r\n      flexDirection: 'column',\r\n      justifyContent: 'center',\r\n      alignItems: 'center',\r\n      padding: '0 80px',\r\n      color: 'white',\r\n      position: 'relative',\r\n      '&::before': {\r\n        content: '\"\"',\r\n        position: 'absolute',\r\n        top: 0,\r\n        left: 0,\r\n        right: 0,\r\n        bottom: 0,\r\n        background: 'rgba(0, 0, 0, 0.1)',\r\n        backdropFilter: 'blur(10px)',\r\n      },\r\n    },\r\n    rightSection: {\r\n      width: '420px',\r\n      minWidth: '380px',\r\n      maxWidth: '450px',\r\n      display: 'flex',\r\n      flexDirection: 'column',\r\n      justifyContent: 'center',\r\n      alignItems: 'center',\r\n      padding: '30px 25px',\r\n      backgroundColor: 'rgba(255, 255, 255, 0.95)',\r\n      backdropFilter: 'blur(20px)',\r\n      boxShadow: '-10px 0 30px rgba(0, 0, 0, 0.1)',\r\n      '@media (max-width: 768px)': {\r\n        width: '100%',\r\n        minWidth: 'auto',\r\n        maxWidth: 'none',\r\n        padding: '20px',\r\n      },\r\n    },\r\n    brandSection: {\r\n      textAlign: 'center',\r\n      marginBottom: '60px',\r\n      position: 'relative',\r\n      zIndex: 1,\r\n    },\r\n    brandTitle: {\r\n      fontSize: '48px',\r\n      fontWeight: 'bold',\r\n      marginBottom: '16px',\r\n      background: 'linear-gradient(45deg, #fff, #f0f0f0)',\r\n      backgroundClip: 'text',\r\n      WebkitBackgroundClip: 'text',\r\n      WebkitTextFillColor: 'transparent',\r\n      textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)',\r\n    },\r\n    brandSubtitle: {\r\n      fontSize: '18px',\r\n      opacity: 0.9,\r\n      marginBottom: '40px',\r\n    },\r\n    featureList: {\r\n      listStyle: 'none',\r\n      padding: 0,\r\n      margin: 0,\r\n      '& li': {\r\n        display: 'flex',\r\n        alignItems: 'center',\r\n        marginBottom: '20px',\r\n        fontSize: '16px',\r\n        '& .anticon': {\r\n          marginRight: '12px',\r\n          fontSize: '20px',\r\n          color: '#fff',\r\n        },\r\n      },\r\n    },\r\n    loginCard: {\r\n      width: '100%',\r\n      maxWidth: '380px',\r\n      border: 'none',\r\n      borderRadius: '12px',\r\n      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\r\n      overflow: 'hidden',\r\n      '& .ant-card-body': {\r\n        padding: '0',\r\n      },\r\n    },\r\n    loginHeader: {\r\n      textAlign: 'center',\r\n      marginBottom: '32px',\r\n    },\r\n    logo: {\r\n      width: '64px',\r\n      height: '64px',\r\n      marginBottom: '16px',\r\n    },\r\n    loginTitle: {\r\n      fontSize: '28px',\r\n      fontWeight: 'bold',\r\n      color: token.colorText,\r\n      marginBottom: '8px',\r\n    },\r\n    loginSubtitle: {\r\n      color: token.colorTextSecondary,\r\n      fontSize: '14px',\r\n    },\r\n    lang: {\r\n      position: 'fixed',\r\n      top: 16,\r\n      right: 16,\r\n      zIndex: 1000,\r\n      width: 42,\r\n      height: 42,\r\n      lineHeight: '42px',\r\n      borderRadius: token.borderRadius,\r\n      backgroundColor: 'rgba(255, 255, 255, 0.1)',\r\n      backdropFilter: 'blur(10px)',\r\n      ':hover': {\r\n        backgroundColor: 'rgba(255, 255, 255, 0.2)',\r\n      },\r\n    },\r\n    formItem: {\r\n      marginBottom: '24px',\r\n      '& .ant-input-affix-wrapper': {\r\n        height: '48px',\r\n        borderRadius: '8px',\r\n        border: '1px solid #e0e0e0',\r\n        '&:hover': {\r\n          borderColor: token.colorPrimary,\r\n        },\r\n        '&:focus-within': {\r\n          borderColor: token.colorPrimary,\r\n          boxShadow: `0 0 0 2px ${token.colorPrimary}20`,\r\n        },\r\n      },\r\n    },\r\n    submitButton: {\r\n      height: '48px',\r\n      borderRadius: '8px',\r\n      fontSize: '16px',\r\n      fontWeight: 'bold',\r\n      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\r\n      border: 'none',\r\n      '&:hover': {\r\n        background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',\r\n      },\r\n    },\r\n  };\r\n});\r\n\r\nconst BrandSection = () => {\r\n  const { styles } = useStyles();\r\n  const intl = useIntl();\r\n\r\n  return (\r\n    <div className={styles.brandSection}>\r\n      <Title className={styles.brandTitle}>RwxAI</Title>\r\n      <Paragraph className={styles.brandSubtitle}>\r\n        {intl.formatMessage({\r\n          id: 'pages.login.brand.subtitle',\r\n          defaultMessage: '智能AI助手平台，让AI为您的工作赋能',\r\n        })}\r\n      </Paragraph>\r\n      <ul className={styles.featureList}>\r\n        <li>\r\n          <RobotOutlined />\r\n          {intl.formatMessage({\r\n            id: 'pages.login.feature.ai',\r\n            defaultMessage: '强大的AI模型集成',\r\n          })}\r\n        </li>\r\n        <li>\r\n          <SafetyCertificateOutlined />\r\n          {intl.formatMessage({\r\n            id: 'pages.login.feature.security',\r\n            defaultMessage: '企业级安全保障',\r\n          })}\r\n        </li>\r\n        <li>\r\n          <UserOutlined />\r\n          {intl.formatMessage({\r\n            id: 'pages.login.feature.easy',\r\n            defaultMessage: '简单易用的界面',\r\n          })}\r\n        </li>\r\n      </ul>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst Lang = () => {\r\n  const { styles } = useStyles();\r\n\r\n  return (\r\n    <div className={styles.lang} data-lang>\r\n      {SelectLang && <SelectLang />}\r\n    </div>\r\n  );\r\n};\r\n\r\nconst LoginMessage: React.FC<{\r\n  content: string;\r\n}> = ({ content }) => {\r\n  return (\r\n    <Alert\r\n      style={{\r\n        marginBottom: 24,\r\n      }}\r\n      message={content}\r\n      type=\"error\"\r\n      showIcon\r\n    />\r\n  );\r\n};\r\n\r\nconst Login: React.FC = () => {\r\n  const [userLoginState, setUserLoginState] = useState<API.LoginResult>({});\r\n  const { initialState, setInitialState } = useModel('@@initialState');\r\n  const { styles } = useStyles();\r\n  const { message } = App.useApp();\r\n  const intl = useIntl();\r\n\r\n  const fetchUserInfo = async () => {\r\n    const userInfo = await initialState?.fetchUserInfo?.();\r\n    if (userInfo) {\r\n      flushSync(() => {\r\n        setInitialState((s) => ({\r\n          ...s,\r\n          currentUser: userInfo,\r\n        }));\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleSubmit = async (values: API.LoginParams) => {\r\n    try {\r\n      // 调用RwxAI登录接口\r\n      const loginData = {\r\n        Username: values.username,\r\n        Password: values.password,\r\n      };\r\n\r\n      const response = await login(loginData);\r\n\r\n      if (response.success && response.data?.token) {\r\n        // 保存JWT令牌和用户信息\r\n        saveLoginInfo(response.data);\r\n\r\n        const defaultLoginSuccessMessage = intl.formatMessage({\r\n          id: 'pages.login.success',\r\n          defaultMessage: '登录成功！',\r\n        });\r\n        message.success(defaultLoginSuccessMessage);\r\n\r\n        // 刷新用户信息\r\n        await fetchUserInfo();\r\n\r\n        // 跳转到目标页面\r\n        const urlParams = new URL(window.location.href).searchParams;\r\n        window.location.href = urlParams.get('redirect') || '/';\r\n        return;\r\n      }\r\n\r\n      // 登录失败\r\n      setUserLoginState({ status: 'error', type: 'account' });\r\n    } catch (error: any) {\r\n      const defaultLoginFailureMessage = intl.formatMessage({\r\n        id: 'pages.login.failure',\r\n        defaultMessage: '登录失败，请重试！',\r\n      });\r\n      console.error('登录错误:', error);\r\n      message.error(error.message || defaultLoginFailureMessage);\r\n      setUserLoginState({ status: 'error', type: 'account' });\r\n    }\r\n  };\r\n  const { status } = userLoginState;\r\n\r\n  return (\r\n    <div className={styles.container}>\r\n      <Helmet>\r\n        <title>\r\n          {intl.formatMessage({\r\n            id: 'menu.login',\r\n            defaultMessage: '登录页',\r\n          })}\r\n          {Settings.title && ` - ${Settings.title}`}\r\n        </title>\r\n      </Helmet>\r\n      <Lang />\r\n\r\n      {/* 左侧品牌展示区域 */}\r\n      <div className={styles.leftSection}>\r\n        <BrandSection />\r\n      </div>\r\n\r\n      {/* 右侧登录表单区域 */}\r\n      <div className={styles.rightSection}>\r\n        <Card className={styles.loginCard}>\r\n          <div className={styles.loginHeader}>\r\n            <img alt=\"logo\" src=\"/logo.svg\" className={styles.logo} />\r\n            <Title level={2} className={styles.loginTitle}>\r\n              {intl.formatMessage({\r\n                id: 'pages.login.title',\r\n                defaultMessage: '欢迎登录',\r\n              })}\r\n            </Title>\r\n            <div className={styles.loginSubtitle}>\r\n              {intl.formatMessage({\r\n                id: 'pages.login.subtitle',\r\n                defaultMessage: '请输入您的账号和密码',\r\n              })}\r\n            </div>\r\n          </div>\r\n\r\n          <LoginForm\r\n            submitter={{\r\n              searchConfig: {\r\n                submitText: intl.formatMessage({\r\n                  id: 'pages.login.submit',\r\n                  defaultMessage: '登录',\r\n                }),\r\n              },\r\n              submitButtonProps: {\r\n                className: styles.submitButton,\r\n                size: 'large',\r\n              },\r\n            }}\r\n            onFinish={async (values) => {\r\n              await handleSubmit(values as API.LoginParams);\r\n            }}\r\n          >\r\n            {status === 'error' && (\r\n              <LoginMessage\r\n                content={intl.formatMessage({\r\n                  id: 'pages.login.accountLogin.errorMessage',\r\n                  defaultMessage: '用户名或密码错误，请重试',\r\n                })}\r\n              />\r\n            )}\r\n\r\n            <div className={styles.formItem}>\r\n              <ProFormText\r\n                name=\"username\"\r\n                fieldProps={{\r\n                  size: 'large',\r\n                  prefix: <UserOutlined style={{ color: '#999' }} />,\r\n                }}\r\n                placeholder={intl.formatMessage({\r\n                  id: 'pages.login.username.placeholder',\r\n                  defaultMessage: '请输入用户名',\r\n                })}\r\n                rules={[\r\n                  {\r\n                    required: true,\r\n                    message: (\r\n                      <FormattedMessage\r\n                        id=\"pages.login.username.required\"\r\n                        defaultMessage=\"请输入用户名!\"\r\n                      />\r\n                    ),\r\n                  },\r\n                ]}\r\n              />\r\n            </div>\r\n\r\n            <div className={styles.formItem}>\r\n              <ProFormText.Password\r\n                name=\"password\"\r\n                fieldProps={{\r\n                  size: 'large',\r\n                  prefix: <LockOutlined style={{ color: '#999' }} />,\r\n                }}\r\n                placeholder={intl.formatMessage({\r\n                  id: 'pages.login.password.placeholder',\r\n                  defaultMessage: '请输入密码',\r\n                })}\r\n                rules={[\r\n                  {\r\n                    required: true,\r\n                    message: (\r\n                      <FormattedMessage\r\n                        id=\"pages.login.password.required\"\r\n                        defaultMessage=\"请输入密码！\"\r\n                      />\r\n                    ),\r\n                  },\r\n                ]}\r\n              />\r\n            </div>\r\n\r\n            <div\r\n              style={{\r\n                marginBottom: 24,\r\n                display: 'flex',\r\n                justifyContent: 'space-between',\r\n                alignItems: 'center',\r\n              }}\r\n            >\r\n              <ProFormCheckbox noStyle name=\"autoLogin\">\r\n                <FormattedMessage\r\n                  id=\"pages.login.rememberMe\"\r\n                  defaultMessage=\"记住我\"\r\n                />\r\n              </ProFormCheckbox>\r\n              <a\r\n                style={{\r\n                  color: '#667eea',\r\n                  textDecoration: 'none',\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  e.currentTarget.style.textDecoration = 'underline';\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  e.currentTarget.style.textDecoration = 'none';\r\n                }}\r\n              >\r\n                <FormattedMessage\r\n                  id=\"pages.login.forgotPassword\"\r\n                  defaultMessage=\"忘记密码？\"\r\n                />\r\n              </a>\r\n            </div>\r\n          </LoginForm>\r\n        </Card>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Login;\r\n"], "names": [], "mappings": ";;;;;;;4BAkdA;;;eAAA;;;;;;;8BA7cO;sCAKA;4BAOA;6BACsC;kCAChB;wEACG;iCACN;8BACJ;6BACQ;iFACT;;;;;;;;;;;;AAErB,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,gBAAU;AAEvC,MAAM,YAAY,IAAA,uBAAY,EAAC,CAAC,EAAE,KAAK,EAAE;IACvC,OAAO;QACL,WAAW;YACT,SAAS;YACT,QAAQ;YACR,UAAU;YACV,YAAY;YACZ,UAAU;QACZ;QACA,aAAa;YACX,MAAM;YACN,SAAS;YACT,eAAe;YACf,gBAAgB;YAChB,YAAY;YACZ,SAAS;YACT,OAAO;YACP,UAAU;YACV,aAAa;gBACX,SAAS;gBACT,UAAU;gBACV,KAAK;gBACL,MAAM;gBACN,OAAO;gBACP,QAAQ;gBACR,YAAY;gBACZ,gBAAgB;YAClB;QACF;QACA,cAAc;YACZ,OAAO;YACP,UAAU;YACV,UAAU;YACV,SAAS;YACT,eAAe;YACf,gBAAgB;YAChB,YAAY;YACZ,SAAS;YACT,iBAAiB;YACjB,gBAAgB;YAChB,WAAW;YACX,6BAA6B;gBAC3B,OAAO;gBACP,UAAU;gBACV,UAAU;gBACV,SAAS;YACX;QACF;QACA,cAAc;YACZ,WAAW;YACX,cAAc;YACd,UAAU;YACV,QAAQ;QACV;QACA,YAAY;YACV,UAAU;YACV,YAAY;YACZ,cAAc;YACd,YAAY;YACZ,gBAAgB;YAChB,sBAAsB;YACtB,qBAAqB;YACrB,YAAY;QACd;QACA,eAAe;YACb,UAAU;YACV,SAAS;YACT,cAAc;QAChB;QACA,aAAa;YACX,WAAW;YACX,SAAS;YACT,QAAQ;YACR,QAAQ;gBACN,SAAS;gBACT,YAAY;gBACZ,cAAc;gBACd,UAAU;gBACV,cAAc;oBACZ,aAAa;oBACb,UAAU;oBACV,OAAO;gBACT;YACF;QACF;QACA,WAAW;YACT,OAAO;YACP,UAAU;YACV,QAAQ;YACR,cAAc;YACd,WAAW;YACX,UAAU;YACV,oBAAoB;gBAClB,SAAS;YACX;QACF;QACA,aAAa;YACX,WAAW;YACX,cAAc;QAChB;QACA,MAAM;YACJ,OAAO;YACP,QAAQ;YACR,cAAc;QAChB;QACA,YAAY;YACV,UAAU;YACV,YAAY;YACZ,OAAO,MAAM,SAAS;YACtB,cAAc;QAChB;QACA,eAAe;YACb,OAAO,MAAM,kBAAkB;YAC/B,UAAU;QACZ;QACA,MAAM;YACJ,UAAU;YACV,KAAK;YACL,OAAO;YACP,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,cAAc,MAAM,YAAY;YAChC,iBAAiB;YACjB,gBAAgB;YAChB,UAAU;gBACR,iBAAiB;YACnB;QACF;QACA,UAAU;YACR,cAAc;YACd,8BAA8B;gBAC5B,QAAQ;gBACR,cAAc;gBACd,QAAQ;gBACR,WAAW;oBACT,aAAa,MAAM,YAAY;gBACjC;gBACA,kBAAkB;oBAChB,aAAa,MAAM,YAAY;oBAC/B,WAAW,CAAC,UAAU,EAAE,MAAM,YAAY,CAAC,EAAE,CAAC;gBAChD;YACF;QACF;QACA,cAAc;YACZ,QAAQ;YACR,cAAc;YACd,UAAU;YACV,YAAY;YACZ,YAAY;YACZ,QAAQ;YACR,WAAW;gBACT,YAAY;YACd;QACF;IACF;AACF;AAEA,MAAM,eAAe;;IACnB,MAAM,EAAE,MAAM,EAAE,GAAG;IACnB,MAAM,OAAO,IAAA,YAAO;IAEpB,qBACE,2BAAC;QAAI,WAAW,OAAO,YAAY;;0BACjC,2BAAC;gBAAM,WAAW,OAAO,UAAU;0BAAE;;;;;;0BACrC,2BAAC;gBAAU,WAAW,OAAO,aAAa;0BACvC,KAAK,aAAa,CAAC;oBAClB,IAAI;oBACJ,gBAAgB;gBAClB;;;;;;0BAEF,2BAAC;gBAAG,WAAW,OAAO,WAAW;;kCAC/B,2BAAC;;0CACC,2BAAC,oBAAa;;;;;4BACb,KAAK,aAAa,CAAC;gCAClB,IAAI;gCACJ,gBAAgB;4BAClB;;;;;;;kCAEF,2BAAC;;0CACC,2BAAC,gCAAyB;;;;;4BACzB,KAAK,aAAa,CAAC;gCAClB,IAAI;gCACJ,gBAAgB;4BAClB;;;;;;;kCAEF,2BAAC;;0CACC,2BAAC,mBAAY;;;;;4BACZ,KAAK,aAAa,CAAC;gCAClB,IAAI;gCACJ,gBAAgB;4BAClB;;;;;;;;;;;;;;;;;;;AAKV;GAtCM;;QACe;QACN,YAAO;;;KAFhB;AAwCN,MAAM,OAAO;;IACX,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,qBACE,2BAAC;QAAI,WAAW,OAAO,IAAI;QAAE,WAAS;kBACnC,eAAU,kBAAI,2BAAC,eAAU;;;;;;;;;;AAGhC;IARM;;QACe;;;MADf;AAUN,MAAM,eAED,CAAC,EAAE,OAAO,EAAE;IACf,qBACE,2BAAC,WAAK;QACJ,OAAO;YACL,cAAc;QAChB;QACA,SAAS;QACT,MAAK;QACL,QAAQ;;;;;;AAGd;MAbM;AAeN,MAAM,QAAkB;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,eAAQ,EAAkB,CAAC;IACvE,MAAM,EAAE,YAAY,EAAE,eAAe,EAAE,GAAG,IAAA,aAAQ,EAAC;IACnD,MAAM,EAAE,MAAM,EAAE,GAAG;IACnB,MAAM,EAAE,OAAO,EAAE,GAAG,SAAG,CAAC,MAAM;IAC9B,MAAM,OAAO,IAAA,YAAO;IAEpB,MAAM,gBAAgB;YACG;QAAvB,MAAM,WAAW,OAAM,yBAAA,oCAAA,8BAAA,aAAc,aAAa,cAA3B,kDAAA,iCAAA;QACvB,IAAI,UACF,IAAA,mBAAS,EAAC;YACR,gBAAgB,CAAC,IAAO,CAAA;oBACtB,GAAG,CAAC;oBACJ,aAAa;gBACf,CAAA;QACF;IAEJ;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI;gBASsB;YARxB,cAAc;YACd,MAAM,YAAY;gBAChB,UAAU,OAAO,QAAQ;gBACzB,UAAU,OAAO,QAAQ;YAC3B;YAEA,MAAM,WAAW,MAAM,IAAA,YAAK,EAAC;YAE7B,IAAI,SAAS,OAAO,MAAI,iBAAA,SAAS,IAAI,cAAb,qCAAA,eAAe,KAAK,GAAE;gBAC5C,eAAe;gBACf,IAAA,mBAAa,EAAC,SAAS,IAAI;gBAE3B,MAAM,6BAA6B,KAAK,aAAa,CAAC;oBACpD,IAAI;oBACJ,gBAAgB;gBAClB;gBACA,QAAQ,OAAO,CAAC;gBAEhB,SAAS;gBACT,MAAM;gBAEN,UAAU;gBACV,MAAM,YAAY,IAAI,IAAI,OAAO,QAAQ,CAAC,IAAI,EAAE,YAAY;gBAC5D,OAAO,QAAQ,CAAC,IAAI,GAAG,UAAU,GAAG,CAAC,eAAe;gBACpD;YACF;YAEA,OAAO;YACP,kBAAkB;gBAAE,QAAQ;gBAAS,MAAM;YAAU;QACvD,EAAE,OAAO,OAAY;YACnB,MAAM,6BAA6B,KAAK,aAAa,CAAC;gBACpD,IAAI;gBACJ,gBAAgB;YAClB;YACA,QAAQ,KAAK,CAAC,SAAS;YACvB,QAAQ,KAAK,CAAC,MAAM,OAAO,IAAI;YAC/B,kBAAkB;gBAAE,QAAQ;gBAAS,MAAM;YAAU;QACvD;IACF;IACA,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,qBACE,2BAAC;QAAI,WAAW,OAAO,SAAS;;0BAC9B,2BAAC,WAAM;0BACL,cAAA,2BAAC;;wBACE,KAAK,aAAa,CAAC;4BAClB,IAAI;4BACJ,gBAAgB;wBAClB;wBACC,wBAAQ,CAAC,KAAK,IAAI,CAAC,GAAG,EAAE,wBAAQ,CAAC,KAAK,CAAC,CAAC;;;;;;;;;;;;0BAG7C,2BAAC;;;;;0BAGD,2BAAC;gBAAI,WAAW,OAAO,WAAW;0BAChC,cAAA,2BAAC;;;;;;;;;;0BAIH,2BAAC;gBAAI,WAAW,OAAO,YAAY;0BACjC,cAAA,2BAAC,UAAI;oBAAC,WAAW,OAAO,SAAS;;sCAC/B,2BAAC;4BAAI,WAAW,OAAO,WAAW;;8CAChC,2BAAC;oCAAI,KAAI;oCAAO,KAAI;oCAAY,WAAW,OAAO,IAAI;;;;;;8CACtD,2BAAC;oCAAM,OAAO;oCAAG,WAAW,OAAO,UAAU;8CAC1C,KAAK,aAAa,CAAC;wCAClB,IAAI;wCACJ,gBAAgB;oCAClB;;;;;;8CAEF,2BAAC;oCAAI,WAAW,OAAO,aAAa;8CACjC,KAAK,aAAa,CAAC;wCAClB,IAAI;wCACJ,gBAAgB;oCAClB;;;;;;;;;;;;sCAIJ,2BAAC,wBAAS;4BACR,WAAW;gCACT,cAAc;oCACZ,YAAY,KAAK,aAAa,CAAC;wCAC7B,IAAI;wCACJ,gBAAgB;oCAClB;gCACF;gCACA,mBAAmB;oCACjB,WAAW,OAAO,YAAY;oCAC9B,MAAM;gCACR;4BACF;4BACA,UAAU,OAAO;gCACf,MAAM,aAAa;4BACrB;;gCAEC,WAAW,yBACV,2BAAC;oCACC,SAAS,KAAK,aAAa,CAAC;wCAC1B,IAAI;wCACJ,gBAAgB;oCAClB;;;;;;8CAIJ,2BAAC;oCAAI,WAAW,OAAO,QAAQ;8CAC7B,cAAA,2BAAC,0BAAW;wCACV,MAAK;wCACL,YAAY;4CACV,MAAM;4CACN,sBAAQ,2BAAC,mBAAY;gDAAC,OAAO;oDAAE,OAAO;gDAAO;;;;;;wCAC/C;wCACA,aAAa,KAAK,aAAa,CAAC;4CAC9B,IAAI;4CACJ,gBAAgB;wCAClB;wCACA,OAAO;4CACL;gDACE,UAAU;gDACV,uBACE,2BAAC,qBAAgB;oDACf,IAAG;oDACH,gBAAe;;;;;;4CAGrB;yCACD;;;;;;;;;;;8CAIL,2BAAC;oCAAI,WAAW,OAAO,QAAQ;8CAC7B,cAAA,2BAAC,0BAAW,CAAC,QAAQ;wCACnB,MAAK;wCACL,YAAY;4CACV,MAAM;4CACN,sBAAQ,2BAAC,mBAAY;gDAAC,OAAO;oDAAE,OAAO;gDAAO;;;;;;wCAC/C;wCACA,aAAa,KAAK,aAAa,CAAC;4CAC9B,IAAI;4CACJ,gBAAgB;wCAClB;wCACA,OAAO;4CACL;gDACE,UAAU;gDACV,uBACE,2BAAC,qBAAgB;oDACf,IAAG;oDACH,gBAAe;;;;;;4CAGrB;yCACD;;;;;;;;;;;8CAIL,2BAAC;oCACC,OAAO;wCACL,cAAc;wCACd,SAAS;wCACT,gBAAgB;wCAChB,YAAY;oCACd;;sDAEA,2BAAC,8BAAe;4CAAC,OAAO;4CAAC,MAAK;sDAC5B,cAAA,2BAAC,qBAAgB;gDACf,IAAG;gDACH,gBAAe;;;;;;;;;;;sDAGnB,2BAAC;4CACC,OAAO;gDACL,OAAO;gDACP,gBAAgB;4CAClB;4CACA,cAAc,CAAC;gDACb,EAAE,aAAa,CAAC,KAAK,CAAC,cAAc,GAAG;4CACzC;4CACA,cAAc,CAAC;gDACb,EAAE,aAAa,CAAC,KAAK,CAAC,cAAc,GAAG;4CACzC;sDAEA,cAAA,2BAAC,qBAAgB;gDACf,IAAG;gDACH,gBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASjC;IApNM;;QAEsC,aAAQ;QAC/B;QACC,SAAG,CAAC;QACX,YAAO;;;MALhB;IAsNN,WAAe"}