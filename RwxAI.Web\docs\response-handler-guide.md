# 统一响应处理系统使用指南

## 📋 概述

统一响应处理系统为RwxAI.Web项目提供了完整的API响应处理机制，自动处理各种HTTP状态码，提供用户友好的错误提示，并集成JWT认证流程。

## 🏗️ 系统架构

### 核心文件
- `src/types/response.ts` - 响应类型定义
- `src/utils/responseHandler.ts` - 统一响应处理器
- `src/utils/request.ts` - 增强的请求工具
- `src/services/rwxai/enhanced-*.ts` - 使用新系统的API服务

## 🔧 使用方法

### 1. 基础API调用

```typescript
import { httpRequest } from '@/utils/request';

// GET请求
const response = await httpRequest.get<User[]>('/api/users');
if (response.success) {
  console.log('用户列表:', response.data);
} else {
  console.error('请求失败:', response.message);
}

// POST请求
const response = await httpRequest.post<User>('/api/users', {
  name: '<PERSON>',
  email: '<EMAIL>'
}, {
  showSuccessMessage: true,
  successMessage: '用户创建成功'
});
```

### 2. 配置选项

```typescript
interface RequestOptions {
  showSuccessMessage?: boolean;      // 是否显示成功消息
  showErrorNotification?: boolean;   // 是否显示错误通知
  successMessage?: string;           // 自定义成功消息
  skipAuth?: boolean;               // 跳过JWT认证
  timeout?: number;                 // 请求超时时间
}
```

### 3. 在API服务中使用

```typescript
// services/rwxai/users.ts
import { httpRequest } from '@/utils/request';
import { ResponseHandleResult } from '@/types/response';

export async function getUsers(): Promise<ResponseHandleResult<User[]>> {
  return httpRequest.get<User[]>('/api/users', {
    showErrorNotification: true,
  });
}

export async function createUser(data: CreateUserRequest): Promise<ResponseHandleResult<User>> {
  return httpRequest.post<User>('/api/users', data, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: '用户创建成功',
  });
}
```

### 4. 在组件中使用

```typescript
// pages/users/index.tsx
import React, { useState, useEffect } from 'react';
import { getUsers, createUser } from '@/services/rwxai/users';

const UsersPage: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);

  const loadUsers = async () => {
    setLoading(true);
    const response = await getUsers();
    if (response.success) {
      setUsers(response.data || []);
    }
    setLoading(false);
  };

  const handleCreateUser = async (userData: CreateUserRequest) => {
    const response = await createUser(userData);
    if (response.success) {
      await loadUsers(); // 重新加载列表
    }
  };

  useEffect(() => {
    loadUsers();
  }, []);

  // ... 组件渲染逻辑
};
```

## 🎯 HTTP状态码处理

### 成功响应 (2xx)
- **200 OK**: 正常返回数据
- **201 Created**: 资源创建成功
- **204 No Content**: 操作成功，无返回内容

### 客户端错误 (4xx)
- **400 Bad Request**: 显示参数错误消息
- **401 Unauthorized**: 自动刷新JWT令牌，失败则跳转登录
- **403 Forbidden**: 显示权限不足提示
- **404 Not Found**: 显示资源不存在提示
- **422 Unprocessable Entity**: 显示详细的验证错误信息
- **429 Too Many Requests**: 显示请求频率限制提示

### 服务器错误 (5xx)
- **500 Internal Server Error**: 显示服务器错误通知
- **502 Bad Gateway**: 显示网关错误通知
- **503 Service Unavailable**: 显示服务不可用通知
- **504 Gateway Timeout**: 显示网关超时通知

## 🔐 JWT认证集成

### 自动令牌管理
- 自动在请求头添加 `Authorization: Bearer {token}`
- 401错误时自动尝试刷新令牌
- 刷新失败时自动跳转登录页面

### 跳过认证
```typescript
// 对于不需要认证的接口
const response = await httpRequest.get('/api/public/data', {
  skipAuth: true
});
```

## 🎨 用户体验优化

### 错误提示策略
- **验证错误**: 使用notification显示详细信息
- **权限错误**: 使用message显示简短提示
- **服务器错误**: 使用notification显示详细错误信息
- **网络错误**: 使用notification提示检查网络连接

### 成功提示
```typescript
// 自动显示成功消息
const response = await httpRequest.post('/api/users', data, {
  showSuccessMessage: true,
  successMessage: '用户创建成功'
});
```

## 🧪 测试和调试

### 响应处理演示页面
访问 `/response-demo` 页面可以测试各种HTTP状态码的处理效果。

### 认证测试页面
访问 `/auth-test` 页面可以测试JWT认证流程。

## 📝 最佳实践

### 1. API服务设计
```typescript
// ✅ 推荐：使用统一的响应处理
export async function createUser(data: CreateUserRequest) {
  return httpRequest.post<User>('/api/users', data, {
    showSuccessMessage: true,
    successMessage: '用户创建成功',
  });
}

// ❌ 不推荐：手动处理响应
export async function createUser(data: CreateUserRequest) {
  try {
    const response = await fetch('/api/users', {
      method: 'POST',
      body: JSON.stringify(data),
    });
    if (response.ok) {
      message.success('用户创建成功');
      return await response.json();
    } else {
      message.error('创建失败');
      throw new Error('创建失败');
    }
  } catch (error) {
    message.error('网络错误');
    throw error;
  }
}
```

### 2. 错误处理
```typescript
// ✅ 推荐：让系统自动处理错误
const response = await getUsers();
if (response.success) {
  setUsers(response.data);
}
// 错误会自动显示给用户

// ❌ 不推荐：手动处理每个错误
try {
  const users = await getUsers();
  setUsers(users);
} catch (error) {
  if (error.status === 401) {
    message.error('未授权');
    // 手动跳转登录...
  } else if (error.status === 500) {
    message.error('服务器错误');
  }
  // ... 更多错误处理
}
```

### 3. 加载状态管理
```typescript
const [loading, setLoading] = useState(false);

const handleSubmit = async (data: any) => {
  setLoading(true);
  const response = await createUser(data);
  if (response.success) {
    // 成功处理
    form.resetFields();
  }
  setLoading(false);
};
```

## 🔧 自定义配置

### 修改错误消息
在 `src/utils/responseHandler.ts` 中修改 `ERROR_MESSAGES` 对象。

### 添加新的错误类型
在 `src/types/response.ts` 中添加新的 `ErrorType` 枚举值。

### 自定义错误处理逻辑
在 `src/utils/responseHandler.ts` 中的 `showErrorNotification` 函数中添加新的处理逻辑。

## 🚀 迁移指南

### 从旧API服务迁移
1. 导入新的请求工具：`import { httpRequest } from '@/utils/request';`
2. 替换 `request` 调用为 `httpRequest.get/post/put/delete`
3. 更新返回类型为 `ResponseHandleResult<T>`
4. 移除手动的错误处理代码
5. 添加适当的配置选项

### 示例迁移
```typescript
// 旧代码
export async function getUsers() {
  return request<User[]>('/api/users');
}

// 新代码
export async function getUsers(): Promise<ResponseHandleResult<User[]>> {
  return httpRequest.get<User[]>('/api/users', {
    showErrorNotification: true,
  });
}
```
