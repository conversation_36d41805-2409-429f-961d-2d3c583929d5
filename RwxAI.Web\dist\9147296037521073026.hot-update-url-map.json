{"js": {"p__chat__session__id": "p__chat__session__id-async.js", "src/pages/api-test/index.tsx": "src_pages_api-test_index_tsx-async.js", "p__plugins__index": "p__plugins__index-async.js", "src/pages/auth-test/index.tsx": "src_pages_auth-test_index_tsx-async.js", "common": "common-async.js", "src/.umi/plugin-layout/Layout.tsx": "src__umi_plugin-layout_Layout_tsx-async.js", "p__404": "p__404-async.js", "src/.umi/umi.ts?hmr": "umi.js", "p__apps__index": "p__apps__index-async.js", "src/pages/ai-models/index.tsx": "src_pages_ai-models_index_tsx-async.js", "p__knowledge__id__files": "p__knowledge__id__files-async.js", "src/.umi/core/EmptyRoute.tsx": "src__umi_core_EmptyRoute_tsx-async.js", "src/pages/response-demo/index.tsx": "src_pages_response-demo_index_tsx-async.js", "vendors": "vendors-async.js", "p__Welcome": "p__Welcome-async.js", "src/pages/table-list/index.tsx": "src_pages_table-list_index_tsx-async.js", "p__Admin": "p__Admin-async.js", "src/.umi/plugin-openapi/openapi.tsx": "src__umi_plugin-openapi_openapi_tsx-async.js", "src/pages/chat/chat-interface.tsx": "src_pages_chat_chat-interface_tsx-async.js", "p__user__register__index": "p__user__register__index-async.js", "p__knowledge__index": "p__knowledge__index-async.js", "p__chat__index": "p__chat__index-async.js", "p__user__login__index": "p__user__login__index-async.js"}, "css": {"src/.umi/umi.ts?hmr": "umi.css", "vendors": "vendors-async.css", "src/.umi/plugin-layout/Layout.tsx": "src__umi_plugin-layout_Layout_tsx-async.css"}}