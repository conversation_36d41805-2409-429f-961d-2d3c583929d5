/**
 * 统一的API响应处理器
 */

import { message, notification } from 'antd';
import { 
  HttpStatusCode, 
  ErrorType, 
  ApiError, 
  ApiResponse, 
  ErrorResponse,
  ResponseHandleResult 
} from '@/types/response';
import { logout } from './auth';

// 错误消息映射
const ERROR_MESSAGES: Record<number, string> = {
  [HttpStatusCode.BAD_REQUEST]: '请求参数错误',
  [HttpStatusCode.UNAUTHORIZED]: '未授权，请重新登录',
  [HttpStatusCode.FORBIDDEN]: '权限不足，无法访问',
  [HttpStatusCode.NOT_FOUND]: '请求的资源不存在',
  [HttpStatusCode.METHOD_NOT_ALLOWED]: '请求方法不被允许',
  [HttpStatusCode.CONFLICT]: '请求冲突，资源已存在',
  [HttpStatusCode.UNPROCESSABLE_ENTITY]: '请求数据验证失败',
  [HttpStatusCode.TOO_MANY_REQUESTS]: '请求过于频繁，请稍后重试',
  [HttpStatusCode.INTERNAL_SERVER_ERROR]: '服务器内部错误',
  [HttpStatusCode.BAD_GATEWAY]: '网关错误',
  [HttpStatusCode.SERVICE_UNAVAILABLE]: '服务暂时不可用',
  [HttpStatusCode.GATEWAY_TIMEOUT]: '网关超时',
};

// 根据状态码获取错误类型
function getErrorType(status: number): ErrorType {
  switch (status) {
    case HttpStatusCode.BAD_REQUEST:
    case HttpStatusCode.UNPROCESSABLE_ENTITY:
      return ErrorType.VALIDATION_ERROR;
    case HttpStatusCode.UNAUTHORIZED:
      return ErrorType.AUTHENTICATION_ERROR;
    case HttpStatusCode.FORBIDDEN:
      return ErrorType.AUTHORIZATION_ERROR;
    case HttpStatusCode.NOT_FOUND:
      return ErrorType.NOT_FOUND_ERROR;
    case HttpStatusCode.INTERNAL_SERVER_ERROR:
    case HttpStatusCode.BAD_GATEWAY:
    case HttpStatusCode.SERVICE_UNAVAILABLE:
    case HttpStatusCode.GATEWAY_TIMEOUT:
      return ErrorType.SERVER_ERROR;
    default:
      return ErrorType.UNKNOWN_ERROR;
  }
}

// 处理验证错误
function handleValidationError(errorResponse: ErrorResponse): string {
  if (errorResponse.errors) {
    const errorMessages: string[] = [];
    Object.entries(errorResponse.errors).forEach(([field, messages]) => {
      messages.forEach(msg => {
        errorMessages.push(`${field}: ${msg}`);
      });
    });
    return errorMessages.join('; ');
  }
  return errorResponse.detail || errorResponse.title || '数据验证失败';
}

// 显示错误通知
export function showErrorNotification(error: ApiError, showNotification = true) {
  const { errorType, message: errorMessage } = error;

  // 根据错误类型决定显示方式
  switch (errorType) {
    case ErrorType.AUTHENTICATION_ERROR:
      message.error('登录已过期，请重新登录');
      // 自动跳转到登录页
      setTimeout(() => logout(), 1000);
      break;
      
    case ErrorType.AUTHORIZATION_ERROR:
      message.error('权限不足，无法执行此操作');
      break;
      
    case ErrorType.VALIDATION_ERROR:
      if (showNotification) {
        notification.error({
          message: '数据验证失败',
          description: errorMessage,
          duration: 5,
        });
      } else {
        message.error(errorMessage);
      }
      break;
      
    case ErrorType.NOT_FOUND_ERROR:
      message.error('请求的资源不存在');
      break;
      
    case ErrorType.SERVER_ERROR:
      notification.error({
        message: '服务器错误',
        description: errorMessage,
        duration: 8,
      });
      break;
      
    case ErrorType.NETWORK_ERROR:
      notification.error({
        message: '网络错误',
        description: '请检查网络连接后重试',
        duration: 5,
      });
      break;
      
    default:
      message.error(errorMessage || '未知错误');
      break;
  }
}

// 处理成功响应
function handleSuccessResponse<T>(_response: Response, data: T): ResponseHandleResult<T> {
  return {
    success: true,
    data,
    message: '操作成功',
  };
}

// 处理错误响应
async function handleErrorResponse(response: Response): Promise<ResponseHandleResult> {
  const status = response.status;
  const errorType = getErrorType(status);
  let errorMessage = ERROR_MESSAGES[status] || '未知错误';
  let errorDetails: any = null;

  try {
    const responseText = await response.text();
    if (responseText) {
      const errorData = JSON.parse(responseText) as ErrorResponse;
      
      // 根据错误类型处理不同的错误格式
      if (errorType === ErrorType.VALIDATION_ERROR) {
        errorMessage = handleValidationError(errorData);
      } else {
        errorMessage = errorData.detail || errorData.title || errorMessage;
      }
      
      errorDetails = errorData;
    }
  } catch (parseError) {
    console.warn('Failed to parse error response:', parseError);
  }

  const apiError = new ApiError(
    errorMessage,
    status,
    errorType,
    errorDetails?.type,
    errorDetails
  );

  return {
    success: false,
    error: apiError,
    message: errorMessage,
  };
}

// 主要的响应处理函数
export async function handleApiResponse<T = any>(
  response: Response,
  options: {
    showSuccessMessage?: boolean;
    showErrorNotification?: boolean;
    successMessage?: string;
  } = {}
): Promise<ResponseHandleResult<T>> {
  const {
    showSuccessMessage = false,
    showErrorNotification: shouldShowErrorNotification = true,
    successMessage,
  } = options;

  try {
    // 处理成功响应
    if (response.ok) {
      let data: T;
      
      // 处理不同的响应类型
      const contentType = response.headers.get('content-type');
      if (contentType?.includes('application/json')) {
        const jsonData = await response.json();
        // 如果是标准的API响应格式
        if (typeof jsonData === 'object' && 'success' in jsonData) {
          const apiResponse = jsonData as ApiResponse<T>;
          data = apiResponse.data as T;
          if (showSuccessMessage) {
            message.success(successMessage || apiResponse.message || '操作成功');
          }
        } else {
          data = jsonData;
          if (showSuccessMessage) {
            message.success(successMessage || '操作成功');
          }
        }
      } else if (response.status === HttpStatusCode.NO_CONTENT) {
        data = null as T;
        if (showSuccessMessage) {
          message.success(successMessage || '操作成功');
        }
      } else {
        data = (await response.text()) as T;
        if (showSuccessMessage) {
          message.success(successMessage || '操作成功');
        }
      }

      return handleSuccessResponse(response, data);
    }

    // 处理错误响应
    const errorResult = await handleErrorResponse(response);
    
    if (errorResult.error && shouldShowErrorNotification) {
      showErrorNotification(errorResult.error, true);
    }

    return errorResult;

  } catch (error) {
    // 处理网络错误或其他异常
    const networkError = new ApiError(
      '网络连接失败，请检查网络后重试',
      0,
      ErrorType.NETWORK_ERROR
    );

    if (shouldShowErrorNotification) {
      showErrorNotification(networkError, false);
    }

    return {
      success: false,
      error: networkError,
      message: networkError.message,
    };
  }
}

// 便捷的错误处理函数
export function handleApiError(error: any, showNotification = true): ApiError {
  if (error instanceof ApiError) {
    if (showNotification) {
      showErrorNotification(error, true);
    }
    return error;
  }

  // 处理其他类型的错误
  const apiError = new ApiError(
    error.message || '未知错误',
    error.status || 0,
    ErrorType.UNKNOWN_ERROR
  );

  if (showNotification) {
    showErrorNotification(apiError, false);
  }

  return apiError;
}
