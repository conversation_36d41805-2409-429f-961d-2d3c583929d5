((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] || []).push([
        ['p__user__register__index'],
{ "src/pages/user/register/index.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _antdstyle = __mako_require__("node_modules/antd-style/es/index.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _components = __mako_require__("src/components/index.ts");
var _rwxai = __mako_require__("src/services/rwxai/index.ts");
var _defaultSettings = /*#__PURE__*/ _interop_require_default._(__mako_require__("config/defaultSettings.ts"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const useStyles = (0, _antdstyle.createStyles)(({ token })=>{
    return {
        action: {
            marginLeft: '8px',
            color: 'rgba(0, 0, 0, 0.2)',
            fontSize: '24px',
            verticalAlign: 'middle',
            cursor: 'pointer',
            transition: 'color 0.3s',
            '&:hover': {
                color: token.colorPrimaryActive
            }
        },
        lang: {
            width: 42,
            height: 42,
            lineHeight: '42px',
            position: 'fixed',
            right: 16,
            borderRadius: token.borderRadius,
            ':hover': {
                backgroundColor: token.colorBgTextHover
            }
        },
        container: {
            display: 'flex',
            flexDirection: 'column',
            height: '100vh',
            overflow: 'auto',
            backgroundImage: "url('https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr')",
            backgroundSize: '100% 100%'
        }
    };
});
const RegisterMessage = ({ content })=>{
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
        style: {
            marginBottom: 24
        },
        message: content,
        type: "error",
        showIcon: true
    }, void 0, false, {
        fileName: "src/pages/user/register/index.tsx",
        lineNumber: 64,
        columnNumber: 5
    }, this);
};
_c = RegisterMessage;
const Register = ()=>{
    _s();
    const [userRegisterState, setUserRegisterState] = (0, _react.useState)({});
    const { styles } = useStyles();
    const intl = (0, _max.useIntl)();
    const { message } = _antd.App.useApp();
    const handleSubmit = async (values)=>{
        try {
            // 注册
            const msg = await (0, _rwxai.register)(values);
            if (msg.success) {
                const defaultRegisterSuccessMessage = intl.formatMessage({
                    id: 'pages.register.success',
                    defaultMessage: '注册成功！'
                });
                message.success(defaultRegisterSuccessMessage);
                // 注册成功后跳转到登录页
                setTimeout(()=>{
                    _max.history.push('/user/login');
                }, 1000);
                return;
            }
            // 如果失败去设置用户错误信息
            setUserRegisterState({
                status: 'error',
                message: msg.message
            });
        } catch (error) {
            const defaultRegisterFailureMessage = intl.formatMessage({
                id: 'pages.register.failure',
                defaultMessage: '注册失败，请重试！'
            });
            message.error(defaultRegisterFailureMessage);
        }
    };
    const { status, message: registerMessage } = userRegisterState;
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        className: styles.container,
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.Helmet, {
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("title", {
                    children: [
                        intl.formatMessage({
                            id: 'menu.register',
                            defaultMessage: '注册页'
                        }),
                        "- ",
                        _defaultSettings.default.title
                    ]
                }, void 0, true, {
                    fileName: "src/pages/user/register/index.tsx",
                    lineNumber: 122,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/user/register/index.tsx",
                lineNumber: 121,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                style: {
                    flex: '1',
                    padding: '32px 0'
                },
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.LoginForm, {
                    contentStyle: {
                        minWidth: 280,
                        maxWidth: '75vw'
                    },
                    logo: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("img", {
                        alt: "logo",
                        src: "/logo.svg"
                    }, void 0, false, {
                        fileName: "src/pages/user/register/index.tsx",
                        lineNumber: 141,
                        columnNumber: 17
                    }, void 0),
                    title: "RwxAI",
                    subTitle: intl.formatMessage({
                        id: 'pages.layouts.userLayout.title'
                    }),
                    initialValues: {
                        autoLogin: true
                    },
                    onFinish: async (values)=>{
                        await handleSubmit(values);
                    },
                    submitter: {
                        searchConfig: {
                            submitText: intl.formatMessage({
                                id: 'pages.register.submit',
                                defaultMessage: '注册'
                            })
                        }
                    },
                    children: [
                        status === 'error' && registerMessage && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(RegisterMessage, {
                            content: registerMessage
                        }, void 0, false, {
                            fileName: "src/pages/user/register/index.tsx",
                            lineNumber: 160,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProFormText, {
                            name: "Username",
                            fieldProps: {
                                size: 'large',
                                prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                    fileName: "src/pages/user/register/index.tsx",
                                    lineNumber: 167,
                                    columnNumber: 23
                                }, void 0)
                            },
                            placeholder: intl.formatMessage({
                                id: 'pages.register.username.placeholder',
                                defaultMessage: '用户名'
                            }),
                            rules: [
                                {
                                    required: true,
                                    message: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                        id: "pages.register.username.required",
                                        defaultMessage: "请输入用户名!"
                                    }, void 0, false, {
                                        fileName: "src/pages/user/register/index.tsx",
                                        lineNumber: 177,
                                        columnNumber: 19
                                    }, void 0)
                                }
                            ]
                        }, void 0, false, {
                            fileName: "src/pages/user/register/index.tsx",
                            lineNumber: 163,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProFormText, {
                            name: "Email",
                            fieldProps: {
                                size: 'large',
                                prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {}, void 0, false, {
                                    fileName: "src/pages/user/register/index.tsx",
                                    lineNumber: 190,
                                    columnNumber: 23
                                }, void 0)
                            },
                            placeholder: intl.formatMessage({
                                id: 'pages.register.email.placeholder',
                                defaultMessage: '邮箱'
                            }),
                            rules: [
                                {
                                    required: true,
                                    message: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                        id: "pages.register.email.required",
                                        defaultMessage: "请输入邮箱!"
                                    }, void 0, false, {
                                        fileName: "src/pages/user/register/index.tsx",
                                        lineNumber: 200,
                                        columnNumber: 19
                                    }, void 0)
                                },
                                {
                                    type: 'email',
                                    message: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                        id: "pages.register.email.invalid",
                                        defaultMessage: "邮箱格式错误!"
                                    }, void 0, false, {
                                        fileName: "src/pages/user/register/index.tsx",
                                        lineNumber: 209,
                                        columnNumber: 19
                                    }, void 0)
                                }
                            ]
                        }, void 0, false, {
                            fileName: "src/pages/user/register/index.tsx",
                            lineNumber: 186,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProFormText, {
                            name: "FirstName",
                            fieldProps: {
                                size: 'large',
                                prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                    fileName: "src/pages/user/register/index.tsx",
                                    lineNumber: 222,
                                    columnNumber: 23
                                }, void 0)
                            },
                            placeholder: intl.formatMessage({
                                id: 'pages.register.firstName.placeholder',
                                defaultMessage: '名'
                            })
                        }, void 0, false, {
                            fileName: "src/pages/user/register/index.tsx",
                            lineNumber: 218,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProFormText, {
                            name: "LastName",
                            fieldProps: {
                                size: 'large',
                                prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                    fileName: "src/pages/user/register/index.tsx",
                                    lineNumber: 234,
                                    columnNumber: 23
                                }, void 0)
                            },
                            placeholder: intl.formatMessage({
                                id: 'pages.register.lastName.placeholder',
                                defaultMessage: '姓'
                            })
                        }, void 0, false, {
                            fileName: "src/pages/user/register/index.tsx",
                            lineNumber: 230,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProFormText.Password, {
                            name: "Password",
                            fieldProps: {
                                size: 'large',
                                prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.LockOutlined, {}, void 0, false, {
                                    fileName: "src/pages/user/register/index.tsx",
                                    lineNumber: 246,
                                    columnNumber: 23
                                }, void 0)
                            },
                            placeholder: intl.formatMessage({
                                id: 'pages.register.password.placeholder',
                                defaultMessage: '密码'
                            }),
                            rules: [
                                {
                                    required: true,
                                    message: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                        id: "pages.register.password.required",
                                        defaultMessage: "请输入密码！"
                                    }, void 0, false, {
                                        fileName: "src/pages/user/register/index.tsx",
                                        lineNumber: 256,
                                        columnNumber: 19
                                    }, void 0)
                                },
                                {
                                    min: 6,
                                    message: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                        id: "pages.register.password.min",
                                        defaultMessage: "密码长度不能少于6位！"
                                    }, void 0, false, {
                                        fileName: "src/pages/user/register/index.tsx",
                                        lineNumber: 265,
                                        columnNumber: 19
                                    }, void 0)
                                }
                            ]
                        }, void 0, false, {
                            fileName: "src/pages/user/register/index.tsx",
                            lineNumber: 242,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                marginBottom: 24,
                                textAlign: 'center'
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("a", {
                                style: {
                                    float: 'right'
                                },
                                onClick: ()=>_max.history.push('/user/login'),
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                    id: "pages.register.loginAccount",
                                    defaultMessage: "已有账户？去登录"
                                }, void 0, false, {
                                    fileName: "src/pages/user/register/index.tsx",
                                    lineNumber: 286,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/user/register/index.tsx",
                                lineNumber: 280,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/user/register/index.tsx",
                            lineNumber: 274,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/user/register/index.tsx",
                    lineNumber: 136,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/user/register/index.tsx",
                lineNumber: 130,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_components.Footer, {}, void 0, false, {
                fileName: "src/pages/user/register/index.tsx",
                lineNumber: 291,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.SelectLang, {
                className: styles.lang
            }, void 0, false, {
                fileName: "src/pages/user/register/index.tsx",
                lineNumber: 292,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/user/register/index.tsx",
        lineNumber: 120,
        columnNumber: 5
    }, this);
};
_s(Register, "7nW5E/XOeI8w8Z4hMW5owf+Uij4=", false, function() {
    return [
        useStyles,
        _max.useIntl,
        _antd.App.useApp
    ];
});
_c1 = Register;
var _default = Register;
var _c;
var _c1;
$RefreshReg$(_c, "RegisterMessage");
$RefreshReg$(_c1, "Register");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=p__user__register__index-async.js.map