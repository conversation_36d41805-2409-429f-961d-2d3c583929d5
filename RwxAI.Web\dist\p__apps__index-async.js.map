{"version": 3, "sources": ["src/pages/apps/components/AppDetail.tsx", "src/pages/apps/components/AppForm.tsx", "src/pages/apps/index.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Modal, Descriptions, Switch, Button, Typography, Space, message } from 'antd';\nimport { CopyOutlined, EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';\nimport { useIntl, FormattedMessage } from '@umijs/max';\n\nconst { Text } = Typography;\n\ninterface AppDetailProps {\n  visible: boolean;\n  onVisibleChange: (visible: boolean) => void;\n  data?: RwxAI.App;\n}\n\nconst AppDetail: React.FC<AppDetailProps> = ({\n  visible,\n  onVisibleChange,\n  data,\n}) => {\n  const intl = useIntl();\n  const [showApiKey, setShowApiKey] = useState(false);\n  const [showApiSecret, setShowApiSecret] = useState(false);\n\n  const copyToClipboard = (text: string, type: string) => {\n    navigator.clipboard.writeText(text).then(() => {\n      message.success(intl.formatMessage({ id: `pages.apps.detail.copy.${type}.success` }));\n    });\n  };\n\n  const maskString = (str?: string) => {\n    if (!str) return '-';\n    if (str.length <= 8) return '*'.repeat(str.length);\n    return str.substring(0, 4) + '*'.repeat(str.length - 8) + str.substring(str.length - 4);\n  };\n\n  return (\n    <Modal\n      title={intl.formatMessage({ id: 'pages.apps.detail.title' })}\n      open={visible}\n      onCancel={() => onVisibleChange(false)}\n      footer={null}\n      width={800}\n    >\n      {data && (\n        <Descriptions column={2} bordered>\n          <Descriptions.Item\n            label={<FormattedMessage id=\"pages.apps.detail.name\" />}\n            span={2}\n          >\n            {data.Name}\n          </Descriptions.Item>\n          \n          <Descriptions.Item\n            label={<FormattedMessage id=\"pages.apps.detail.description\" />}\n            span={2}\n          >\n            {data.Description || '-'}\n          </Descriptions.Item>\n          \n          <Descriptions.Item\n            label={<FormattedMessage id=\"pages.apps.detail.apiKey\" />}\n            span={2}\n          >\n            <Space>\n              <Text code copyable={false}>\n                {showApiKey ? data.ApiKey : maskString(data.ApiKey)}\n              </Text>\n              <Button\n                type=\"link\"\n                size=\"small\"\n                icon={showApiKey ? <EyeInvisibleOutlined /> : <EyeOutlined />}\n                onClick={() => setShowApiKey(!showApiKey)}\n              />\n              <Button\n                type=\"link\"\n                size=\"small\"\n                icon={<CopyOutlined />}\n                onClick={() => copyToClipboard(data.ApiKey || '', 'apiKey')}\n              />\n            </Space>\n          </Descriptions.Item>\n          \n          <Descriptions.Item\n            label={<FormattedMessage id=\"pages.apps.detail.apiSecret\" />}\n            span={2}\n          >\n            <Space>\n              <Text code copyable={false}>\n                {showApiSecret ? data.ApiSecret : maskString(data.ApiSecret)}\n              </Text>\n              <Button\n                type=\"link\"\n                size=\"small\"\n                icon={showApiSecret ? <EyeInvisibleOutlined /> : <EyeOutlined />}\n                onClick={() => setShowApiSecret(!showApiSecret)}\n              />\n              <Button\n                type=\"link\"\n                size=\"small\"\n                icon={<CopyOutlined />}\n                onClick={() => copyToClipboard(data.ApiSecret || '', 'apiSecret')}\n              />\n            </Space>\n          </Descriptions.Item>\n          \n          <Descriptions.Item\n            label={<FormattedMessage id=\"pages.apps.detail.status\" />}\n          >\n            <Switch checked={data.IsEnabled} disabled size=\"small\" />\n          </Descriptions.Item>\n          \n          <Descriptions.Item\n            label={<FormattedMessage id=\"pages.apps.detail.apiCalls\" />}\n          >\n            {data.ApiCallCount || 0} / {data.MaxApiCalls || '∞'}\n          </Descriptions.Item>\n          \n          <Descriptions.Item\n            label={<FormattedMessage id=\"pages.apps.detail.rateLimit\" />}\n          >\n            {data.RateLimitPerMinute ? `${data.RateLimitPerMinute}/min` : '-'}\n          </Descriptions.Item>\n          \n          <Descriptions.Item\n            label={<FormattedMessage id=\"pages.apps.detail.allowedOrigins\" />}\n          >\n            {data.AllowedOrigins || '-'}\n          </Descriptions.Item>\n          \n          <Descriptions.Item\n            label={<FormattedMessage id=\"pages.apps.detail.createdTime\" />}\n          >\n            {data.CreatedTime ? new Date(data.CreatedTime).toLocaleString() : '-'}\n          </Descriptions.Item>\n          \n          <Descriptions.Item\n            label={<FormattedMessage id=\"pages.apps.detail.updatedTime\" />}\n          >\n            {data.UpdatedTime ? new Date(data.UpdatedTime).toLocaleString() : '-'}\n          </Descriptions.Item>\n          \n          <Descriptions.Item\n            label={<FormattedMessage id=\"pages.apps.detail.configJson\" />}\n            span={2}\n          >\n            {data.ConfigJson ? (\n              <pre style={{ whiteSpace: 'pre-wrap', fontSize: '12px' }}>\n                {JSON.stringify(JSON.parse(data.ConfigJson), null, 2)}\n              </pre>\n            ) : (\n              '-'\n            )}\n          </Descriptions.Item>\n        </Descriptions>\n      )}\n    </Modal>\n  );\n};\n\nexport default AppDetail;\n", "import React, { useEffect, useState } from 'react';\nimport { Modal, Form, Input, InputNumber, Switch, message } from 'antd';\nimport { useIntl, FormattedMessage } from '@umijs/max';\nimport { createApp, updateApp } from '@/services/rwxai';\n\ninterface AppFormProps {\n  visible: boolean;\n  onVisibleChange: (visible: boolean) => void;\n  initialValues?: RwxAI.App;\n  onSuccess: () => void;\n}\n\nconst AppForm: React.FC<AppFormProps> = ({\n  visible,\n  onVisibleChange,\n  initialValues,\n  onSuccess,\n}) => {\n  const intl = useIntl();\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n\n  const isEdit = !!initialValues?.Id;\n\n  useEffect(() => {\n    if (visible) {\n      if (initialValues) {\n        form.setFieldsValue(initialValues);\n      } else {\n        form.resetFields();\n      }\n    }\n  }, [visible, initialValues]);\n\n  const handleSubmit = async () => {\n    try {\n      const values = await form.validateFields();\n      setLoading(true);\n\n      let response;\n      if (isEdit) {\n        response = await updateApp(initialValues!.Id, { ...initialValues, ...values });\n      } else {\n        response = await createApp(values);\n      }\n\n      if (response.success) {\n        onSuccess();\n      }\n      // 成功和错误消息会由统一响应处理系统自动显示\n    } catch (error) {\n      // 表单验证错误等\n      console.error('Form validation error:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Modal\n      title={intl.formatMessage({\n        id: isEdit ? 'pages.apps.form.edit.title' : 'pages.apps.form.create.title',\n      })}\n      open={visible}\n      onCancel={() => onVisibleChange(false)}\n      onOk={handleSubmit}\n      confirmLoading={loading}\n      width={600}\n    >\n      <Form\n        form={form}\n        layout=\"vertical\"\n        initialValues={{\n          IsEnabled: true,\n          RateLimitPerMinute: 60,\n        }}\n      >\n        <Form.Item\n          name=\"Name\"\n          label={<FormattedMessage id=\"pages.apps.form.name\" />}\n          rules={[{ required: true, message: intl.formatMessage({ id: 'pages.apps.form.name.required' }) }]}\n        >\n          <Input placeholder={intl.formatMessage({ id: 'pages.apps.form.name.placeholder' })} />\n        </Form.Item>\n\n        <Form.Item\n          name=\"Description\"\n          label={<FormattedMessage id=\"pages.apps.form.description\" />}\n        >\n          <Input.TextArea\n            rows={3}\n            placeholder={intl.formatMessage({ id: 'pages.apps.form.description.placeholder' })}\n          />\n        </Form.Item>\n\n        <Form.Item\n          name=\"MaxApiCalls\"\n          label={<FormattedMessage id=\"pages.apps.form.maxApiCalls\" />}\n          help={intl.formatMessage({ id: 'pages.apps.form.maxApiCalls.help' })}\n        >\n          <InputNumber\n            min={0}\n            style={{ width: '100%' }}\n            placeholder={intl.formatMessage({ id: 'pages.apps.form.maxApiCalls.placeholder' })}\n          />\n        </Form.Item>\n\n        <Form.Item\n          name=\"RateLimitPerMinute\"\n          label={<FormattedMessage id=\"pages.apps.form.rateLimit\" />}\n          help={intl.formatMessage({ id: 'pages.apps.form.rateLimit.help' })}\n        >\n          <InputNumber\n            min={1}\n            max={10000}\n            style={{ width: '100%' }}\n            placeholder={intl.formatMessage({ id: 'pages.apps.form.rateLimit.placeholder' })}\n          />\n        </Form.Item>\n\n        <Form.Item\n          name=\"AllowedOrigins\"\n          label={<FormattedMessage id=\"pages.apps.form.allowedOrigins\" />}\n          help={intl.formatMessage({ id: 'pages.apps.form.allowedOrigins.help' })}\n        >\n          <Input.TextArea\n            rows={2}\n            placeholder={intl.formatMessage({ id: 'pages.apps.form.allowedOrigins.placeholder' })}\n          />\n        </Form.Item>\n\n        <Form.Item\n          name=\"IsEnabled\"\n          label={<FormattedMessage id=\"pages.apps.form.isEnabled\" />}\n          valuePropName=\"checked\"\n        >\n          <Switch />\n        </Form.Item>\n      </Form>\n    </Modal>\n  );\n};\n\nexport default AppForm;\n", "import React, { useRef, useState } from 'react';\r\nimport { PageContainer } from '@ant-design/pro-components';\r\nimport { ProTable, ActionType, ProColumns } from '@ant-design/pro-table';\r\nimport { Button, Space, Tag, message, Modal, Switch, Tooltip } from 'antd';\r\nimport { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, KeyOutlined, ReloadOutlined } from '@ant-design/icons';\r\nimport { useIntl, FormattedMessage } from '@umijs/max';\r\nimport { getMyApps, deleteApp, updateAppStatus, regenerateApiKeys, resetApiCalls } from '@/services/rwxai';\r\nimport { createSimpleProTableRequest, defaultPaginationConfig } from '@/utils/pageDataHandler';\r\nimport AppForm from './components/AppForm';\r\nimport AppDetail from './components/AppDetail';\r\n\r\nconst AppsPage: React.FC = () => {\r\n  const intl = useIntl();\r\n  const actionRef = useRef<ActionType>();\r\n  const [createModalVisible, setCreateModalVisible] = useState(false);\r\n  const [editModalVisible, setEditModalVisible] = useState(false);\r\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\r\n  const [currentRecord, setCurrentRecord] = useState<RwxAI.App>();\r\n\r\n  const handleDelete = async (record: RwxAI.App) => {\r\n    Modal.confirm({\r\n      title: intl.formatMessage({ id: 'pages.apps.delete.confirm.title' }),\r\n      content: intl.formatMessage({ id: 'pages.apps.delete.confirm.content' }),\r\n      onOk: async () => {\r\n        try {\r\n          await deleteApp(record.Id);\r\n          message.success(intl.formatMessage({ id: 'pages.apps.delete.success' }));\r\n          actionRef.current?.reload();\r\n        } catch (error) {\r\n          message.error(intl.formatMessage({ id: 'pages.apps.delete.error' }));\r\n        }\r\n      },\r\n    });\r\n  };\r\n\r\n  const handleStatusChange = async (record: RwxAI.App, checked: boolean) => {\r\n    try {\r\n      await updateAppStatus(record.Id, { IsEnabled: checked });\r\n      message.success(intl.formatMessage({ id: 'pages.apps.status.update.success' }));\r\n      actionRef.current?.reload();\r\n    } catch (error) {\r\n      message.error(intl.formatMessage({ id: 'pages.apps.status.update.error' }));\r\n    }\r\n  };\r\n\r\n  const handleRegenerateKeys = async (record: RwxAI.App) => {\r\n    Modal.confirm({\r\n      title: intl.formatMessage({ id: 'pages.apps.regenerateKeys.confirm.title' }),\r\n      content: intl.formatMessage({ id: 'pages.apps.regenerateKeys.confirm.content' }),\r\n      onOk: async () => {\r\n        try {\r\n          await regenerateApiKeys(record.Id);\r\n          message.success(intl.formatMessage({ id: 'pages.apps.regenerateKeys.success' }));\r\n          actionRef.current?.reload();\r\n        } catch (error) {\r\n          message.error(intl.formatMessage({ id: 'pages.apps.regenerateKeys.error' }));\r\n        }\r\n      },\r\n    });\r\n  };\r\n\r\n  const handleResetApiCalls = async (record: RwxAI.App) => {\r\n    Modal.confirm({\r\n      title: intl.formatMessage({ id: 'pages.apps.resetApiCalls.confirm.title' }),\r\n      content: intl.formatMessage({ id: 'pages.apps.resetApiCalls.confirm.content' }),\r\n      onOk: async () => {\r\n        try {\r\n          await resetApiCalls(record.Id);\r\n          message.success(intl.formatMessage({ id: 'pages.apps.resetApiCalls.success' }));\r\n          actionRef.current?.reload();\r\n        } catch (error) {\r\n          message.error(intl.formatMessage({ id: 'pages.apps.resetApiCalls.error' }));\r\n        }\r\n      },\r\n    });\r\n  };\r\n\r\n  const columns: ProColumns<RwxAI.App>[] = [\r\n    {\r\n      title: <FormattedMessage id=\"pages.apps.table.name\" />,\r\n      dataIndex: 'Name',\r\n      key: 'Name',\r\n      ellipsis: true,\r\n    },\r\n    {\r\n      title: <FormattedMessage id=\"pages.apps.table.description\" />,\r\n      dataIndex: 'Description',\r\n      key: 'Description',\r\n      ellipsis: true,\r\n      hideInSearch: true,\r\n    },\r\n    {\r\n      title: <FormattedMessage id=\"pages.apps.table.status\" />,\r\n      dataIndex: 'IsEnabled',\r\n      key: 'IsEnabled',\r\n      render: (_, record) => (\r\n        <Switch\r\n          checked={record.IsEnabled}\r\n          size=\"small\"\r\n          onChange={(checked) => handleStatusChange(record, checked)}\r\n        />\r\n      ),\r\n    },\r\n    {\r\n      title: <FormattedMessage id=\"pages.apps.table.apiCalls\" />,\r\n      key: 'apiCalls',\r\n      hideInSearch: true,\r\n      render: (_, record) => (\r\n        <span>\r\n          {record.ApiCallCount || 0} / {record.MaxApiCalls || '∞'}\r\n        </span>\r\n      ),\r\n    },\r\n    {\r\n      title: <FormattedMessage id=\"pages.apps.table.rateLimit\" />,\r\n      dataIndex: 'RateLimitPerMinute',\r\n      key: 'RateLimitPerMinute',\r\n      hideInSearch: true,\r\n      render: (value) => value ? `${value}/min` : '-',\r\n    },\r\n    {\r\n      title: <FormattedMessage id=\"pages.apps.table.createdTime\" />,\r\n      dataIndex: 'CreatedTime',\r\n      key: 'CreatedTime',\r\n      valueType: 'dateTime',\r\n      width: 180,\r\n      hideInSearch: true,\r\n    },\r\n    {\r\n      title: <FormattedMessage id=\"pages.apps.table.actions\" />,\r\n      key: 'actions',\r\n      width: 280,\r\n      render: (_, record) => (\r\n        <Space>\r\n          <Tooltip title={intl.formatMessage({ id: 'pages.apps.actions.view' })}>\r\n            <Button\r\n              type=\"link\"\r\n              size=\"small\"\r\n              icon={<EyeOutlined />}\r\n              onClick={() => {\r\n                setCurrentRecord(record);\r\n                setDetailModalVisible(true);\r\n              }}\r\n            />\r\n          </Tooltip>\r\n          <Tooltip title={intl.formatMessage({ id: 'pages.apps.actions.edit' })}>\r\n            <Button\r\n              type=\"link\"\r\n              size=\"small\"\r\n              icon={<EditOutlined />}\r\n              onClick={() => {\r\n                setCurrentRecord(record);\r\n                setEditModalVisible(true);\r\n              }}\r\n            />\r\n          </Tooltip>\r\n          <Tooltip title={intl.formatMessage({ id: 'pages.apps.actions.regenerateKeys' })}>\r\n            <Button\r\n              type=\"link\"\r\n              size=\"small\"\r\n              icon={<KeyOutlined />}\r\n              onClick={() => handleRegenerateKeys(record)}\r\n            />\r\n          </Tooltip>\r\n          <Tooltip title={intl.formatMessage({ id: 'pages.apps.actions.resetApiCalls' })}>\r\n            <Button\r\n              type=\"link\"\r\n              size=\"small\"\r\n              icon={<ReloadOutlined />}\r\n              onClick={() => handleResetApiCalls(record)}\r\n            />\r\n          </Tooltip>\r\n          <Tooltip title={intl.formatMessage({ id: 'pages.apps.actions.delete' })}>\r\n            <Button\r\n              type=\"link\"\r\n              size=\"small\"\r\n              danger\r\n              icon={<DeleteOutlined />}\r\n              onClick={() => handleDelete(record)}\r\n            />\r\n          </Tooltip>\r\n        </Space>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <PageContainer>\r\n      <ProTable<RwxAI.App>\r\n        headerTitle={intl.formatMessage({ id: 'pages.apps.title' })}\r\n        actionRef={actionRef}\r\n        rowKey=\"Id\"\r\n        search={{\r\n          labelWidth: 120,\r\n        }}\r\n        toolBarRender={() => [\r\n          <Button\r\n            type=\"primary\"\r\n            key=\"primary\"\r\n            icon={<PlusOutlined />}\r\n            onClick={() => setCreateModalVisible(true)}\r\n          >\r\n            <FormattedMessage id=\"pages.apps.actions.create\" />\r\n          </Button>,\r\n        ]}\r\n        request={createSimpleProTableRequest(getMyApps)}\r\n        columns={columns}\r\n      />\r\n\r\n      <AppForm\r\n        visible={createModalVisible}\r\n        onVisibleChange={setCreateModalVisible}\r\n        onSuccess={() => {\r\n          actionRef.current?.reload();\r\n          setCreateModalVisible(false);\r\n        }}\r\n      />\r\n\r\n      <AppForm\r\n        visible={editModalVisible}\r\n        onVisibleChange={setEditModalVisible}\r\n        initialValues={currentRecord}\r\n        onSuccess={() => {\r\n          actionRef.current?.reload();\r\n          setEditModalVisible(false);\r\n        }}\r\n      />\r\n\r\n      <AppDetail\r\n        visible={detailModalVisible}\r\n        onVisibleChange={setDetailModalVisible}\r\n        data={currentRecord}\r\n      />\r\n    </PageContainer>\r\n  );\r\n};\r\n\r\nexport default AppsPage;\r\n"], "names": [], "mappings": ";;;;;;;4BA8JA;;;eAAA;;;;;;wEA9JgC;6BACgD;8BAChB;4BACtB;;;;;;;;;;AAE1C,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAU;AAQ3B,MAAM,YAAsC,CAAC,EAC3C,OAAO,EACP,eAAe,EACf,IAAI,EACL;;IACC,MAAM,OAAO,IAAA,YAAO;IACpB,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAC;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAC;IAEnD,MAAM,kBAAkB,CAAC,MAAc;QACrC,UAAU,SAAS,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC;YACvC,aAAO,CAAC,OAAO,CAAC,KAAK,aAAa,CAAC;gBAAE,IAAI,CAAC,uBAAuB,EAAE,KAAK,QAAQ,CAAC;YAAC;QACpF;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,KAAK,OAAO;QACjB,IAAI,IAAI,MAAM,IAAI,GAAG,OAAO,IAAI,MAAM,CAAC,IAAI,MAAM;QACjD,OAAO,IAAI,SAAS,CAAC,GAAG,KAAK,IAAI,MAAM,CAAC,IAAI,MAAM,GAAG,KAAK,IAAI,SAAS,CAAC,IAAI,MAAM,GAAG;IACvF;IAEA,qBACE,2BAAC,WAAK;QACJ,OAAO,KAAK,aAAa,CAAC;YAAE,IAAI;QAA0B;QAC1D,MAAM;QACN,UAAU,IAAM,gBAAgB;QAChC,QAAQ;QACR,OAAO;kBAEN,sBACC,2BAAC,kBAAY;YAAC,QAAQ;YAAG,QAAQ;;8BAC/B,2BAAC,kBAAY,CAAC,IAAI;oBAChB,qBAAO,2BAAC,qBAAgB;wBAAC,IAAG;;;;;;oBAC5B,MAAM;8BAEL,KAAK,IAAI;;;;;;8BAGZ,2BAAC,kBAAY,CAAC,IAAI;oBAChB,qBAAO,2BAAC,qBAAgB;wBAAC,IAAG;;;;;;oBAC5B,MAAM;8BAEL,KAAK,WAAW,IAAI;;;;;;8BAGvB,2BAAC,kBAAY,CAAC,IAAI;oBAChB,qBAAO,2BAAC,qBAAgB;wBAAC,IAAG;;;;;;oBAC5B,MAAM;8BAEN,cAAA,2BAAC,WAAK;;0CACJ,2BAAC;gCAAK,IAAI;gCAAC,UAAU;0CAClB,aAAa,KAAK,MAAM,GAAG,WAAW,KAAK,MAAM;;;;;;0CAEpD,2BAAC,YAAM;gCACL,MAAK;gCACL,MAAK;gCACL,MAAM,2BAAa,2BAAC,2BAAoB;;;;2DAAM,2BAAC,kBAAW;;;;;gCAC1D,SAAS,IAAM,cAAc,CAAC;;;;;;0CAEhC,2BAAC,YAAM;gCACL,MAAK;gCACL,MAAK;gCACL,oBAAM,2BAAC,mBAAY;;;;;gCACnB,SAAS,IAAM,gBAAgB,KAAK,MAAM,IAAI,IAAI;;;;;;;;;;;;;;;;;8BAKxD,2BAAC,kBAAY,CAAC,IAAI;oBAChB,qBAAO,2BAAC,qBAAgB;wBAAC,IAAG;;;;;;oBAC5B,MAAM;8BAEN,cAAA,2BAAC,WAAK;;0CACJ,2BAAC;gCAAK,IAAI;gCAAC,UAAU;0CAClB,gBAAgB,KAAK,SAAS,GAAG,WAAW,KAAK,SAAS;;;;;;0CAE7D,2BAAC,YAAM;gCACL,MAAK;gCACL,MAAK;gCACL,MAAM,8BAAgB,2BAAC,2BAAoB;;;;2DAAM,2BAAC,kBAAW;;;;;gCAC7D,SAAS,IAAM,iBAAiB,CAAC;;;;;;0CAEnC,2BAAC,YAAM;gCACL,MAAK;gCACL,MAAK;gCACL,oBAAM,2BAAC,mBAAY;;;;;gCACnB,SAAS,IAAM,gBAAgB,KAAK,SAAS,IAAI,IAAI;;;;;;;;;;;;;;;;;8BAK3D,2BAAC,kBAAY,CAAC,IAAI;oBAChB,qBAAO,2BAAC,qBAAgB;wBAAC,IAAG;;;;;;8BAE5B,cAAA,2BAAC,YAAM;wBAAC,SAAS,KAAK,SAAS;wBAAE,QAAQ;wBAAC,MAAK;;;;;;;;;;;8BAGjD,2BAAC,kBAAY,CAAC,IAAI;oBAChB,qBAAO,2BAAC,qBAAgB;wBAAC,IAAG;;;;;;;wBAE3B,KAAK,YAAY,IAAI;wBAAE;wBAAI,KAAK,WAAW,IAAI;;;;;;;8BAGlD,2BAAC,kBAAY,CAAC,IAAI;oBAChB,qBAAO,2BAAC,qBAAgB;wBAAC,IAAG;;;;;;8BAE3B,KAAK,kBAAkB,GAAG,CAAC,EAAE,KAAK,kBAAkB,CAAC,IAAI,CAAC,GAAG;;;;;;8BAGhE,2BAAC,kBAAY,CAAC,IAAI;oBAChB,qBAAO,2BAAC,qBAAgB;wBAAC,IAAG;;;;;;8BAE3B,KAAK,cAAc,IAAI;;;;;;8BAG1B,2BAAC,kBAAY,CAAC,IAAI;oBAChB,qBAAO,2BAAC,qBAAgB;wBAAC,IAAG;;;;;;8BAE3B,KAAK,WAAW,GAAG,IAAI,KAAK,KAAK,WAAW,EAAE,cAAc,KAAK;;;;;;8BAGpE,2BAAC,kBAAY,CAAC,IAAI;oBAChB,qBAAO,2BAAC,qBAAgB;wBAAC,IAAG;;;;;;8BAE3B,KAAK,WAAW,GAAG,IAAI,KAAK,KAAK,WAAW,EAAE,cAAc,KAAK;;;;;;8BAGpE,2BAAC,kBAAY,CAAC,IAAI;oBAChB,qBAAO,2BAAC,qBAAgB;wBAAC,IAAG;;;;;;oBAC5B,MAAM;8BAEL,KAAK,UAAU,iBACd,2BAAC;wBAAI,OAAO;4BAAE,YAAY;4BAAY,UAAU;wBAAO;kCACpD,KAAK,SAAS,CAAC,KAAK,KAAK,CAAC,KAAK,UAAU,GAAG,MAAM;;;;;+BAGrD;;;;;;;;;;;;;;;;;AAOd;GA/IM;;QAKS,YAAO;;;KALhB;IAiJN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BCff;;;eAAA;;;;;;wEA/I2C;6BACsB;4BACvB;8BACL;;;;;;;;;;AASrC,MAAM,UAAkC,CAAC,EACvC,OAAO,EACP,eAAe,EACf,aAAa,EACb,SAAS,EACV;;IACC,MAAM,OAAO,IAAA,YAAO;IACpB,MAAM,CAAC,KAAK,GAAG,UAAI,CAAC,OAAO;IAC3B,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IAEvC,MAAM,SAAS,CAAC,EAAC,0BAAA,oCAAA,cAAe,EAAE;IAElC,IAAA,gBAAS,EAAC;QACR,IAAI;YACF,IAAI,eACF,KAAK,cAAc,CAAC;iBAEpB,KAAK,WAAW;;IAGtB,GAAG;QAAC;QAAS;KAAc;IAE3B,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,SAAS,MAAM,KAAK,cAAc;YACxC,WAAW;YAEX,IAAI;YACJ,IAAI,QACF,WAAW,MAAM,IAAA,gBAAS,EAAC,cAAe,EAAE,EAAE;gBAAE,GAAG,aAAa;gBAAE,GAAG,MAAM;YAAC;iBAE5E,WAAW,MAAM,IAAA,gBAAS,EAAC;YAG7B,IAAI,SAAS,OAAO,EAClB;QAEF,wBAAwB;QAC1B,EAAE,OAAO,OAAO;YACd,UAAU;YACV,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,2BAAC,WAAK;QACJ,OAAO,KAAK,aAAa,CAAC;YACxB,IAAI,SAAS,+BAA+B;QAC9C;QACA,MAAM;QACN,UAAU,IAAM,gBAAgB;QAChC,MAAM;QACN,gBAAgB;QAChB,OAAO;kBAEP,cAAA,2BAAC,UAAI;YACH,MAAM;YACN,QAAO;YACP,eAAe;gBACb,WAAW;gBACX,oBAAoB;YACtB;;8BAEA,2BAAC,UAAI,CAAC,IAAI;oBACR,MAAK;oBACL,qBAAO,2BAAC,qBAAgB;wBAAC,IAAG;;;;;;oBAC5B,OAAO;wBAAC;4BAAE,UAAU;4BAAM,SAAS,KAAK,aAAa,CAAC;gCAAE,IAAI;4BAAgC;wBAAG;qBAAE;8BAEjG,cAAA,2BAAC,WAAK;wBAAC,aAAa,KAAK,aAAa,CAAC;4BAAE,IAAI;wBAAmC;;;;;;;;;;;8BAGlF,2BAAC,UAAI,CAAC,IAAI;oBACR,MAAK;oBACL,qBAAO,2BAAC,qBAAgB;wBAAC,IAAG;;;;;;8BAE5B,cAAA,2BAAC,WAAK,CAAC,QAAQ;wBACb,MAAM;wBACN,aAAa,KAAK,aAAa,CAAC;4BAAE,IAAI;wBAA0C;;;;;;;;;;;8BAIpF,2BAAC,UAAI,CAAC,IAAI;oBACR,MAAK;oBACL,qBAAO,2BAAC,qBAAgB;wBAAC,IAAG;;;;;;oBAC5B,MAAM,KAAK,aAAa,CAAC;wBAAE,IAAI;oBAAmC;8BAElE,cAAA,2BAAC,iBAAW;wBACV,KAAK;wBACL,OAAO;4BAAE,OAAO;wBAAO;wBACvB,aAAa,KAAK,aAAa,CAAC;4BAAE,IAAI;wBAA0C;;;;;;;;;;;8BAIpF,2BAAC,UAAI,CAAC,IAAI;oBACR,MAAK;oBACL,qBAAO,2BAAC,qBAAgB;wBAAC,IAAG;;;;;;oBAC5B,MAAM,KAAK,aAAa,CAAC;wBAAE,IAAI;oBAAiC;8BAEhE,cAAA,2BAAC,iBAAW;wBACV,KAAK;wBACL,KAAK;wBACL,OAAO;4BAAE,OAAO;wBAAO;wBACvB,aAAa,KAAK,aAAa,CAAC;4BAAE,IAAI;wBAAwC;;;;;;;;;;;8BAIlF,2BAAC,UAAI,CAAC,IAAI;oBACR,MAAK;oBACL,qBAAO,2BAAC,qBAAgB;wBAAC,IAAG;;;;;;oBAC5B,MAAM,KAAK,aAAa,CAAC;wBAAE,IAAI;oBAAsC;8BAErE,cAAA,2BAAC,WAAK,CAAC,QAAQ;wBACb,MAAM;wBACN,aAAa,KAAK,aAAa,CAAC;4BAAE,IAAI;wBAA6C;;;;;;;;;;;8BAIvF,2BAAC,UAAI,CAAC,IAAI;oBACR,MAAK;oBACL,qBAAO,2BAAC,qBAAgB;wBAAC,IAAG;;;;;;oBAC5B,eAAc;8BAEd,cAAA,2BAAC,YAAM;;;;;;;;;;;;;;;;;;;;;AAKjB;GAjIM;;QAMS,YAAO;QACL,UAAI,CAAC;;;KAPhB;IAmIN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BC8Ff;;;eAAA;;;;;;;wEA7OwC;sCACV;iCACmB;6BACmB;8BACiC;4BAC3D;8BAC8C;wCACnB;yEACjD;2EACE;;;;;;;;;;AAEtB,MAAM,WAAqB;;IACzB,MAAM,OAAO,IAAA,YAAO;IACpB,MAAM,YAAY,IAAA,aAAM;IACxB,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,IAAA,eAAQ,EAAC;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,eAAQ,EAAC;IACzD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,IAAA,eAAQ,EAAC;IAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ;IAElD,MAAM,eAAe,OAAO;QAC1B,WAAK,CAAC,OAAO,CAAC;YACZ,OAAO,KAAK,aAAa,CAAC;gBAAE,IAAI;YAAkC;YAClE,SAAS,KAAK,aAAa,CAAC;gBAAE,IAAI;YAAoC;YACtE,MAAM;gBACJ,IAAI;wBAGF;oBAFA,MAAM,IAAA,gBAAS,EAAC,OAAO,EAAE;oBACzB,aAAO,CAAC,OAAO,CAAC,KAAK,aAAa,CAAC;wBAAE,IAAI;oBAA4B;qBACrE,qBAAA,UAAU,OAAO,cAAjB,iCAAA,mBAAmB,MAAM;gBAC3B,EAAE,OAAO,OAAO;oBACd,aAAO,CAAC,KAAK,CAAC,KAAK,aAAa,CAAC;wBAAE,IAAI;oBAA0B;gBACnE;YACF;QACF;IACF;IAEA,MAAM,qBAAqB,OAAO,QAAmB;QACnD,IAAI;gBAGF;YAFA,MAAM,IAAA,sBAAe,EAAC,OAAO,EAAE,EAAE;gBAAE,WAAW;YAAQ;YACtD,aAAO,CAAC,OAAO,CAAC,KAAK,aAAa,CAAC;gBAAE,IAAI;YAAmC;aAC5E,qBAAA,UAAU,OAAO,cAAjB,iCAAA,mBAAmB,MAAM;QAC3B,EAAE,OAAO,OAAO;YACd,aAAO,CAAC,KAAK,CAAC,KAAK,aAAa,CAAC;gBAAE,IAAI;YAAiC;QAC1E;IACF;IAEA,MAAM,uBAAuB,OAAO;QAClC,WAAK,CAAC,OAAO,CAAC;YACZ,OAAO,KAAK,aAAa,CAAC;gBAAE,IAAI;YAA0C;YAC1E,SAAS,KAAK,aAAa,CAAC;gBAAE,IAAI;YAA4C;YAC9E,MAAM;gBACJ,IAAI;wBAGF;oBAFA,MAAM,IAAA,wBAAiB,EAAC,OAAO,EAAE;oBACjC,aAAO,CAAC,OAAO,CAAC,KAAK,aAAa,CAAC;wBAAE,IAAI;oBAAoC;qBAC7E,qBAAA,UAAU,OAAO,cAAjB,iCAAA,mBAAmB,MAAM;gBAC3B,EAAE,OAAO,OAAO;oBACd,aAAO,CAAC,KAAK,CAAC,KAAK,aAAa,CAAC;wBAAE,IAAI;oBAAkC;gBAC3E;YACF;QACF;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,WAAK,CAAC,OAAO,CAAC;YACZ,OAAO,KAAK,aAAa,CAAC;gBAAE,IAAI;YAAyC;YACzE,SAAS,KAAK,aAAa,CAAC;gBAAE,IAAI;YAA2C;YAC7E,MAAM;gBACJ,IAAI;wBAGF;oBAFA,MAAM,IAAA,oBAAa,EAAC,OAAO,EAAE;oBAC7B,aAAO,CAAC,OAAO,CAAC,KAAK,aAAa,CAAC;wBAAE,IAAI;oBAAmC;qBAC5E,qBAAA,UAAU,OAAO,cAAjB,iCAAA,mBAAmB,MAAM;gBAC3B,EAAE,OAAO,OAAO;oBACd,aAAO,CAAC,KAAK,CAAC,KAAK,aAAa,CAAC;wBAAE,IAAI;oBAAiC;gBAC1E;YACF;QACF;IACF;IAEA,MAAM,UAAmC;QACvC;YACE,qBAAO,2BAAC,qBAAgB;gBAAC,IAAG;;;;;;YAC5B,WAAW;YACX,KAAK;YACL,UAAU;QACZ;QACA;YACE,qBAAO,2BAAC,qBAAgB;gBAAC,IAAG;;;;;;YAC5B,WAAW;YACX,KAAK;YACL,UAAU;YACV,cAAc;QAChB;QACA;YACE,qBAAO,2BAAC,qBAAgB;gBAAC,IAAG;;;;;;YAC5B,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,GAAG,uBACV,2BAAC,YAAM;oBACL,SAAS,OAAO,SAAS;oBACzB,MAAK;oBACL,UAAU,CAAC,UAAY,mBAAmB,QAAQ;;;;;;QAGxD;QACA;YACE,qBAAO,2BAAC,qBAAgB;gBAAC,IAAG;;;;;;YAC5B,KAAK;YACL,cAAc;YACd,QAAQ,CAAC,GAAG,uBACV,2BAAC;;wBACE,OAAO,YAAY,IAAI;wBAAE;wBAAI,OAAO,WAAW,IAAI;;;;;;;QAG1D;QACA;YACE,qBAAO,2BAAC,qBAAgB;gBAAC,IAAG;;;;;;YAC5B,WAAW;YACX,KAAK;YACL,cAAc;YACd,QAAQ,CAAC,QAAU,QAAQ,CAAC,EAAE,MAAM,IAAI,CAAC,GAAG;QAC9C;QACA;YACE,qBAAO,2BAAC,qBAAgB;gBAAC,IAAG;;;;;;YAC5B,WAAW;YACX,KAAK;YACL,WAAW;YACX,OAAO;YACP,cAAc;QAChB;QACA;YACE,qBAAO,2BAAC,qBAAgB;gBAAC,IAAG;;;;;;YAC5B,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,GAAG,uBACV,2BAAC,WAAK;;sCACJ,2BAAC,aAAO;4BAAC,OAAO,KAAK,aAAa,CAAC;gCAAE,IAAI;4BAA0B;sCACjE,cAAA,2BAAC,YAAM;gCACL,MAAK;gCACL,MAAK;gCACL,oBAAM,2BAAC,kBAAW;;;;;gCAClB,SAAS;oCACP,iBAAiB;oCACjB,sBAAsB;gCACxB;;;;;;;;;;;sCAGJ,2BAAC,aAAO;4BAAC,OAAO,KAAK,aAAa,CAAC;gCAAE,IAAI;4BAA0B;sCACjE,cAAA,2BAAC,YAAM;gCACL,MAAK;gCACL,MAAK;gCACL,oBAAM,2BAAC,mBAAY;;;;;gCACnB,SAAS;oCACP,iBAAiB;oCACjB,oBAAoB;gCACtB;;;;;;;;;;;sCAGJ,2BAAC,aAAO;4BAAC,OAAO,KAAK,aAAa,CAAC;gCAAE,IAAI;4BAAoC;sCAC3E,cAAA,2BAAC,YAAM;gCACL,MAAK;gCACL,MAAK;gCACL,oBAAM,2BAAC,kBAAW;;;;;gCAClB,SAAS,IAAM,qBAAqB;;;;;;;;;;;sCAGxC,2BAAC,aAAO;4BAAC,OAAO,KAAK,aAAa,CAAC;gCAAE,IAAI;4BAAmC;sCAC1E,cAAA,2BAAC,YAAM;gCACL,MAAK;gCACL,MAAK;gCACL,oBAAM,2BAAC,qBAAc;;;;;gCACrB,SAAS,IAAM,oBAAoB;;;;;;;;;;;sCAGvC,2BAAC,aAAO;4BAAC,OAAO,KAAK,aAAa,CAAC;gCAAE,IAAI;4BAA4B;sCACnE,cAAA,2BAAC,YAAM;gCACL,MAAK;gCACL,MAAK;gCACL,MAAM;gCACN,oBAAM,2BAAC,qBAAc;;;;;gCACrB,SAAS,IAAM,aAAa;;;;;;;;;;;;;;;;;QAKtC;KACD;IAED,qBACE,2BAAC,4BAAa;;0BACZ,2BAAC,kBAAQ;gBACP,aAAa,KAAK,aAAa,CAAC;oBAAE,IAAI;gBAAmB;gBACzD,WAAW;gBACX,QAAO;gBACP,QAAQ;oBACN,YAAY;gBACd;gBACA,eAAe,IAAM;sCACnB,2BAAC,YAAM;4BACL,MAAK;4BAEL,oBAAM,2BAAC,mBAAY;;;;;4BACnB,SAAS,IAAM,sBAAsB;sCAErC,cAAA,2BAAC,qBAAgB;gCAAC,IAAG;;;;;;2BAJjB;;;;;qBAMP;gBACD,SAAS,IAAA,4CAA2B,EAAC,gBAAS;gBAC9C,SAAS;;;;;;0BAGX,2BAAC,gBAAO;gBACN,SAAS;gBACT,iBAAiB;gBACjB,WAAW;wBACT;qBAAA,qBAAA,UAAU,OAAO,cAAjB,iCAAA,mBAAmB,MAAM;oBACzB,sBAAsB;gBACxB;;;;;;0BAGF,2BAAC,gBAAO;gBACN,SAAS;gBACT,iBAAiB;gBACjB,eAAe;gBACf,WAAW;wBACT;qBAAA,qBAAA,UAAU,OAAO,cAAjB,iCAAA,mBAAmB,MAAM;oBACzB,oBAAoB;gBACtB;;;;;;0BAGF,2BAAC,kBAAS;gBACR,SAAS;gBACT,iBAAiB;gBACjB,MAAM;;;;;;;;;;;;AAId;GAhOM;;QACS,YAAO;;;KADhB;IAkON,WAAe"}