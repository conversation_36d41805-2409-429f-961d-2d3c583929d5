import React, { useRef, useState, useEffect } from 'react';
import { <PERSON>Container } from '@ant-design/pro-components';
import { ProTable, ActionType, ProColumns } from '@ant-design/pro-table';
import { Button, Space, message, Modal, Upload, Tag, Progress } from 'antd';
import { UploadOutlined, DeleteOutlined, PlayCircleOutlined, FileOutlined } from '@ant-design/icons';
import { useParams, useIntl, FormattedMessage } from '@umijs/max';
import { getKnowledgeFiles, uploadKnowledgeFile, deleteKnowledgeFile, processKnowledgeFile, getKnowledgeBaseById } from '@/services/rwxai';

const KnowledgeFilesPage: React.FC = () => {
  const intl = useIntl();
  const { id } = useParams<{ id: string }>();
  const actionRef = useRef<ActionType>();
  const [knowledgeBase, setKnowledgeBase] = useState<RwxAI.Knowledge>();
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [uploading, setUploading] = useState(false);

  useEffect(() => {
    if (id) {
      loadKnowledgeBase();
    }
  }, [id]);

  const loadKnowledgeBase = async () => {
    try {
      const data = await getKnowledgeBaseById(id!);
      setKnowledgeBase(data);
    } catch (error) {
      message.error(intl.formatMessage({ id: 'pages.knowledge.files.loadKnowledge.error' }));
    }
  };

  const handleDelete = async (record: RwxAI.KnowledgeFile) => {
    Modal.confirm({
      title: intl.formatMessage({ id: 'pages.knowledge.files.delete.confirm.title' }),
      content: intl.formatMessage({ id: 'pages.knowledge.files.delete.confirm.content' }),
      onOk: async () => {
        try {
          await deleteKnowledgeFile(record.Id);
          message.success(intl.formatMessage({ id: 'pages.knowledge.files.delete.success' }));
          actionRef.current?.reload();
        } catch (error) {
          message.error(intl.formatMessage({ id: 'pages.knowledge.files.delete.error' }));
        }
      },
    });
  };

  const handleProcess = async (record: RwxAI.KnowledgeFile) => {
    try {
      await processKnowledgeFile(record.Id);
      message.success(intl.formatMessage({ id: 'pages.knowledge.files.process.success' }));
      actionRef.current?.reload();
    } catch (error) {
      message.error(intl.formatMessage({ id: 'pages.knowledge.files.process.error' }));
    }
  };

  const handleUpload = async (file: File) => {
    try {
      setUploading(true);
      await uploadKnowledgeFile(id!, file);
      message.success(intl.formatMessage({ id: 'pages.knowledge.files.upload.success' }));
      setUploadModalVisible(false);
      actionRef.current?.reload();
    } catch (error) {
      message.error(intl.formatMessage({ id: 'pages.knowledge.files.upload.error' }));
    } finally {
      setUploading(false);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const columns: ProColumns<RwxAI.KnowledgeFile>[] = [
    {
      title: <FormattedMessage id="pages.knowledge.files.table.fileName" />,
      dataIndex: 'FileName',
      key: 'FileName',
      ellipsis: true,
      render: (_, record) => (
        <Space>
          <FileOutlined />
          {record.FileName}
        </Space>
      ),
    },
    {
      title: <FormattedMessage id="pages.knowledge.files.table.contentType" />,
      dataIndex: 'ContentType',
      key: 'ContentType',
      hideInSearch: true,
    },
    {
      title: <FormattedMessage id="pages.knowledge.files.table.fileSize" />,
      dataIndex: 'FileSize',
      key: 'FileSize',
      hideInSearch: true,
      render: (_, record) => formatFileSize(record.FileSize),
    },
    {
      title: <FormattedMessage id="pages.knowledge.files.table.status" />,
      dataIndex: 'IsProcessed',
      key: 'IsProcessed',
      render: (_, record) => (
        <Tag color={record.IsProcessed ? 'green' : 'orange'}>
          {record.IsProcessed ? 
            intl.formatMessage({ id: 'pages.knowledge.files.status.processed' }) : 
            intl.formatMessage({ id: 'pages.knowledge.files.status.pending' })
          }
        </Tag>
      ),
    },
    {
      title: <FormattedMessage id="pages.knowledge.files.table.processedTime" />,
      dataIndex: 'ProcessedTime',
      key: 'ProcessedTime',
      valueType: 'dateTime',
      hideInSearch: true,
      render: (_, record) => record.ProcessedTime ? new Date(record.ProcessedTime).toLocaleString() : '-',
    },
    {
      title: <FormattedMessage id="pages.knowledge.files.table.createdTime" />,
      dataIndex: 'CreatedTime',
      key: 'CreatedTime',
      valueType: 'dateTime',
      width: 180,
      hideInSearch: true,
    },
    {
      title: <FormattedMessage id="pages.knowledge.files.table.actions" />,
      key: 'actions',
      width: 200,
      render: (_, record) => (
        <Space>
          {!record.IsProcessed && (
            <Button
              type="link"
              size="small"
              icon={<PlayCircleOutlined />}
              onClick={() => handleProcess(record)}
            >
              <FormattedMessage id="pages.knowledge.files.actions.process" />
            </Button>
          )}
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          >
            <FormattedMessage id="pages.knowledge.files.actions.delete" />
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <PageContainer
      title={knowledgeBase?.Name}
      subTitle={intl.formatMessage({ id: 'pages.knowledge.files.subtitle' })}
    >
      <ProTable<RwxAI.KnowledgeFile>
        headerTitle={intl.formatMessage({ id: 'pages.knowledge.files.title' })}
        actionRef={actionRef}
        rowKey="Id"
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            icon={<UploadOutlined />}
            onClick={() => setUploadModalVisible(true)}
          >
            <FormattedMessage id="pages.knowledge.files.actions.upload" />
          </Button>,
        ]}
        request={async () => {
          try {
            const data = await getKnowledgeFiles(id!);
            return {
              data: data || [],
              success: true,
            };
          } catch (error) {
            return {
              data: [],
              success: false,
            };
          }
        }}
        columns={columns}
      />

      <Modal
        title={intl.formatMessage({ id: 'pages.knowledge.files.upload.title' })}
        open={uploadModalVisible}
        onCancel={() => setUploadModalVisible(false)}
        footer={null}
      >
        <Upload.Dragger
          name="file"
          multiple={false}
          beforeUpload={(file) => {
            handleUpload(file);
            return false;
          }}
          disabled={uploading}
        >
          <p className="ant-upload-drag-icon">
            <UploadOutlined />
          </p>
          <p className="ant-upload-text">
            <FormattedMessage id="pages.knowledge.files.upload.dragText" />
          </p>
          <p className="ant-upload-hint">
            <FormattedMessage id="pages.knowledge.files.upload.hint" />
          </p>
        </Upload.Dragger>
        {uploading && (
          <div style={{ marginTop: 16 }}>
            <Progress percent={100} status="active" />
          </div>
        )}
      </Modal>
    </PageContainer>
  );
};

export default KnowledgeFilesPage;
