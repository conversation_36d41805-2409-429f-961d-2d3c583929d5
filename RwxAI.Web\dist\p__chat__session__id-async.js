((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] || []).push([
        ['p__chat__session__id'],
{ "src/pages/chat/session/[id].tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _rwxai = __mako_require__("src/services/rwxai/index.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { TextArea } = _antd.Input;
const { Text } = _antd.Typography;
const ChatSessionPage = ()=>{
    _s();
    const intl = (0, _max.useIntl)();
    const { id } = (0, _max.useParams)();
    const [session, setSession] = (0, _react.useState)();
    const [messages, setMessages] = (0, _react.useState)([]);
    const [inputValue, setInputValue] = (0, _react.useState)('');
    const [loading, setLoading] = (0, _react.useState)(false);
    const [sessionLoading, setSessionLoading] = (0, _react.useState)(true);
    const messagesEndRef = (0, _react.useRef)(null);
    (0, _react.useEffect)(()=>{
        if (id) loadSession();
    }, [
        id
    ]);
    (0, _react.useEffect)(()=>{
        scrollToBottom();
    }, [
        messages
    ]);
    // 获取模型显示名称的辅助函数
    const getModelDisplayName = (session)=>{
        var _session_Model, _session_Model1;
        // 优先使用 Model 对象中的信息
        if ((_session_Model = session.Model) === null || _session_Model === void 0 ? void 0 : _session_Model.Name) return session.Model.Name;
        if ((_session_Model1 = session.Model) === null || _session_Model1 === void 0 ? void 0 : _session_Model1.DisplayName) return session.Model.DisplayName;
        // 如果没有 Model 对象，尝试使用 ModelId
        if (session.ModelId) return session.ModelId;
        // 最后的默认值
        return '未配置模型';
    };
    const scrollToBottom = ()=>{
        var _messagesEndRef_current;
        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 || _messagesEndRef_current.scrollIntoView({
            behavior: 'smooth'
        });
    };
    const loadSession = async ()=>{
        try {
            setSessionLoading(true);
            const data = await (0, _rwxai.getChatSessionById)(id);
            setSession(data);
            setMessages(data.Messages || []);
        } catch (error) {
            _antd.message.error(intl.formatMessage({
                id: 'pages.chat.session.load.error'
            }));
        } finally{
            setSessionLoading(false);
        }
    };
    const handleSendMessage = async ()=>{
        if (!inputValue.trim() || !session) return;
        const userMessage = {
            Id: `temp-${Date.now()}`,
            Role: 'user',
            Content: inputValue,
            CreatedTime: new Date().toISOString(),
            SessionId: session.Id
        };
        setMessages((prev)=>[
                ...prev,
                userMessage
            ]);
        setInputValue('');
        setLoading(true);
        try {
            const response = await (0, _rwxai.sendMessage)(session.Id, {
                Content: inputValue,
                ModelId: session.ModelId,
                Temperature: session.Temperature,
                MaxTokens: session.MaxTokens
            });
            setMessages((prev)=>[
                    ...prev,
                    response
                ]);
        } catch (error) {
            _antd.message.error(intl.formatMessage({
                id: 'pages.chat.session.send.error'
            }));
            // 移除临时用户消息
            setMessages((prev)=>prev.filter((msg)=>msg.Id !== userMessage.Id));
        } finally{
            setLoading(false);
        }
    };
    const handleKeyPress = (e)=>{
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSendMessage();
        }
    };
    const renderMessage = (message)=>{
        const isUser = message.Role === 'user';
        return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item, {
            style: {
                padding: '12px 0',
                justifyContent: isUser ? 'flex-end' : 'flex-start'
            },
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                style: {
                    display: 'flex',
                    flexDirection: isUser ? 'row-reverse' : 'row',
                    alignItems: 'flex-start',
                    maxWidth: '80%'
                },
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                        icon: isUser ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                            fileName: "src/pages/chat/session/[id].tsx",
                            lineNumber: 126,
                            columnNumber: 28
                        }, void 0) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.RobotOutlined, {}, void 0, false, {
                            fileName: "src/pages/chat/session/[id].tsx",
                            lineNumber: 126,
                            columnNumber: 47
                        }, void 0),
                        style: {
                            backgroundColor: isUser ? '#1890ff' : '#52c41a',
                            margin: isUser ? '0 0 0 8px' : '0 8px 0 0'
                        }
                    }, void 0, false, {
                        fileName: "src/pages/chat/session/[id].tsx",
                        lineNumber: 125,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            backgroundColor: isUser ? '#e6f7ff' : '#f6ffed',
                            padding: '8px 12px',
                            borderRadius: '8px',
                            border: `1px solid ${isUser ? '#91d5ff' : '#b7eb8f'}`
                        },
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                children: message.Content
                            }, void 0, false, {
                                fileName: "src/pages/chat/session/[id].tsx",
                                lineNumber: 140,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    marginTop: '4px',
                                    fontSize: '12px',
                                    color: '#999'
                                },
                                children: message.CreatedTime ? new Date(message.CreatedTime).toLocaleTimeString() : ''
                            }, void 0, false, {
                                fileName: "src/pages/chat/session/[id].tsx",
                                lineNumber: 141,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/chat/session/[id].tsx",
                        lineNumber: 132,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/chat/session/[id].tsx",
                lineNumber: 117,
                columnNumber: 9
            }, this)
        }, message.Id, false, {
            fileName: "src/pages/chat/session/[id].tsx",
            lineNumber: 110,
            columnNumber: 7
        }, this);
    };
    if (sessionLoading) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
            style: {
                textAlign: 'center',
                padding: '50px'
            },
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                size: "large"
            }, void 0, false, {
                fileName: "src/pages/chat/session/[id].tsx",
                lineNumber: 154,
                columnNumber: 11
            }, this)
        }, void 0, false, {
            fileName: "src/pages/chat/session/[id].tsx",
            lineNumber: 153,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "src/pages/chat/session/[id].tsx",
        lineNumber: 152,
        columnNumber: 7
    }, this);
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        title: session === null || session === void 0 ? void 0 : session.Name,
        subTitle: session ? getModelDisplayName(session) : undefined,
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
            style: {
                height: 'calc(100vh - 200px)',
                display: 'flex',
                flexDirection: 'column'
            },
            bodyStyle: {
                flex: 1,
                display: 'flex',
                flexDirection: 'column',
                padding: 0
            },
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        flex: 1,
                        overflow: 'auto',
                        padding: '16px',
                        backgroundColor: '#fafafa'
                    },
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List, {
                            dataSource: messages,
                            renderItem: renderMessage,
                            style: {
                                backgroundColor: 'transparent'
                            }
                        }, void 0, false, {
                            fileName: "src/pages/chat/session/[id].tsx",
                            lineNumber: 177,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            ref: messagesEndRef
                        }, void 0, false, {
                            fileName: "src/pages/chat/session/[id].tsx",
                            lineNumber: 182,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/chat/session/[id].tsx",
                    lineNumber: 169,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        padding: '16px',
                        borderTop: '1px solid #f0f0f0'
                    },
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space.Compact, {
                        style: {
                            width: '100%'
                        },
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TextArea, {
                                value: inputValue,
                                onChange: (e)=>setInputValue(e.target.value),
                                onKeyPress: handleKeyPress,
                                placeholder: intl.formatMessage({
                                    id: 'pages.chat.session.input.placeholder'
                                }),
                                autoSize: {
                                    minRows: 1,
                                    maxRows: 4
                                },
                                disabled: loading
                            }, void 0, false, {
                                fileName: "src/pages/chat/session/[id].tsx",
                                lineNumber: 187,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "primary",
                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SendOutlined, {}, void 0, false, {
                                    fileName: "src/pages/chat/session/[id].tsx",
                                    lineNumber: 197,
                                    columnNumber: 21
                                }, void 0),
                                onClick: handleSendMessage,
                                loading: loading,
                                disabled: !inputValue.trim(),
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                    id: "pages.chat.session.send"
                                }, void 0, false, {
                                    fileName: "src/pages/chat/session/[id].tsx",
                                    lineNumber: 202,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/chat/session/[id].tsx",
                                lineNumber: 195,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/chat/session/[id].tsx",
                        lineNumber: 186,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/chat/session/[id].tsx",
                    lineNumber: 185,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "src/pages/chat/session/[id].tsx",
            lineNumber: 165,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "src/pages/chat/session/[id].tsx",
        lineNumber: 161,
        columnNumber: 5
    }, this);
};
_s(ChatSessionPage, "EpHSYFYaJk+IV23JBT4ZPMCIg1c=", false, function() {
    return [
        _max.useIntl,
        _max.useParams
    ];
});
_c = ChatSessionPage;
var _default = ChatSessionPage;
var _c;
$RefreshReg$(_c, "ChatSessionPage");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=p__chat__session__id-async.js.map