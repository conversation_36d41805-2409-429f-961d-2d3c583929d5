((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] || []).push([
        ['src/pages/response-demo/index.tsx'],
{ "src/pages/response-demo/index.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const ResponseDemo = ()=>{
    _s();
    (0, _max.useIntl)();
    const [loading, setLoading] = (0, _react.useState)(false);
    const handleSuccessDemo = ()=>{
        setLoading(true);
        setTimeout(()=>{
            _antd.message.success('操作成功！');
            setLoading(false);
        }, 1000);
    };
    const handleErrorDemo = ()=>{
        setLoading(true);
        setTimeout(()=>{
            _antd.message.error('操作失败！');
            setLoading(false);
        }, 1000);
    };
    const handleWarningDemo = ()=>{
        setLoading(true);
        setTimeout(()=>{
            _antd.message.warning('警告信息！');
            setLoading(false);
        }, 1000);
    };
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        title: "响应处理演示",
        subTitle: "演示各种响应处理效果",
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
            title: "消息提示演示",
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                    message: "这是一个演示页面",
                    description: "用于测试各种响应处理效果，包括成功、失败、警告等消息提示。",
                    type: "info",
                    showIcon: true,
                    style: {
                        marginBottom: 16
                    }
                }, void 0, false, {
                    fileName: "src/pages/response-demo/index.tsx",
                    lineNumber: 40,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "primary",
                            loading: loading,
                            onClick: handleSuccessDemo,
                            children: "成功消息"
                        }, void 0, false, {
                            fileName: "src/pages/response-demo/index.tsx",
                            lineNumber: 49,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            danger: true,
                            loading: loading,
                            onClick: handleErrorDemo,
                            children: "错误消息"
                        }, void 0, false, {
                            fileName: "src/pages/response-demo/index.tsx",
                            lineNumber: 56,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            loading: loading,
                            onClick: handleWarningDemo,
                            children: "警告消息"
                        }, void 0, false, {
                            fileName: "src/pages/response-demo/index.tsx",
                            lineNumber: 63,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/response-demo/index.tsx",
                    lineNumber: 48,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {}, void 0, false, {
                    fileName: "src/pages/response-demo/index.tsx",
                    lineNumber: 71,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                    message: "开发说明",
                    description: "这个页面是为了解决路由配置中缺失的组件而创建的临时页面。在实际开发中，您可以根据需要修改或删除此页面。",
                    type: "warning",
                    showIcon: true
                }, void 0, false, {
                    fileName: "src/pages/response-demo/index.tsx",
                    lineNumber: 73,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "src/pages/response-demo/index.tsx",
            lineNumber: 39,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "src/pages/response-demo/index.tsx",
        lineNumber: 35,
        columnNumber: 5
    }, this);
};
_s(ResponseDemo, "G6mZ0ruigMR38uD55XYX4AOjNvk=", false, function() {
    return [
        _max.useIntl
    ];
});
_c = ResponseDemo;
var _default = ResponseDemo;
var _c;
$RefreshReg$(_c, "ResponseDemo");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=src_pages_response-demo_index_tsx-async.js.map