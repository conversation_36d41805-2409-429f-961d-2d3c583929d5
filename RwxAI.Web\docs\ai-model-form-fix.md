# AI模型编辑界面服务提供商字段修复

## 问题描述

在AI模型管理的编辑界面中，服务提供商字段没有默认加载，导致用户无法看到当前模型所属的提供商。

## 问题原因

1. **缺少ProviderId字段映射** - 在编辑模式下，表单初始化时没有设置 `ProviderId` 字段的值
2. **加载顺序问题** - 提供商数据可能还没有加载完成，就尝试设置表单值

## 修复方案

### 1. 添加ProviderId字段映射

在表单数据映射中添加了 `ProviderId` 字段：

```typescript
const formData = {
  // ... 其他字段
  TemplateId: initialValues.ModelTemplateId,
  ProviderId: initialValues.Template?.Provider?.Id, // 添加提供商ID
  // ... 其他字段
};
```

### 2. 优化加载顺序

将同步的 `useEffect` 改为异步的初始化函数，确保提供商数据先加载完成：

```typescript
// 修改前
useEffect(() => {
  if (visible) {
    loadProviders(); // 同步调用
    if (initialValues) {
      // 立即设置表单值
      form.setFieldsValue(formData);
    }
  }
}, [visible, initialValues]);

// 修改后
useEffect(() => {
  if (visible) {
    initializeForm(); // 调用异步初始化函数
  }
}, [visible, initialValues]);

const initializeForm = async () => {
  // 先加载提供商数据
  await loadProviders();
  
  if (initialValues) {
    // 然后设置表单值
    form.setFieldsValue(formData);
    
    // 最后加载模型模板
    if (providerId) {
      await loadModelTemplates(providerId);
    }
  }
};
```

### 3. 增强错误处理

为 `loadProviders` 函数添加了 try-catch 错误处理：

```typescript
const loadProviders = async () => {
  try {
    const response = await getEnabledAIProviders();
    if (response.success) {
      setProviders(response.data || []);
    } else {
      setProviders([]);
    }
  } catch (error) {
    setProviders([]);
  }
};
```

## 修复效果

### 修复前
- ❌ 编辑模式下服务提供商字段为空
- ❌ 用户无法看到当前模型的提供商信息
- ❌ 可能导致数据不一致

### 修复后
- ✅ 编辑模式下正确显示当前模型的服务提供商
- ✅ 提供商字段会自动选中对应的值
- ✅ 模型模板列表会根据提供商自动加载
- ✅ 数据加载顺序正确，避免竞态条件

## 数据流程

1. **打开编辑对话框** → 触发 `initializeForm()`
2. **加载提供商列表** → `await loadProviders()`
3. **设置表单初始值** → `form.setFieldsValue(formData)`
4. **加载模型模板** → `await loadModelTemplates(providerId)`
5. **界面完全初始化** → 用户可以看到完整信息

## 相关文件

- `src/pages/ai-models/components/ModelForm.tsx` - 主要修复文件
- `src/services/rwxai/aiModels.ts` - API服务文件

## 测试建议

1. **编辑现有模型** - 验证服务提供商字段是否正确显示
2. **切换提供商** - 验证模型模板列表是否正确更新
3. **保存修改** - 验证数据是否正确提交
4. **错误处理** - 验证网络错误时的处理是否正常

## 注意事项

- 确保后端API返回的数据结构包含 `Template.Provider.Id` 字段
- 如果提供商数据加载失败，表单仍然可以正常使用
- 修改不影响新建模型的功能
