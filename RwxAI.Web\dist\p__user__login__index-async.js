((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] || []).push([
        ['p__user__login__index'],
{ "src/pages/user/login/index.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _antdstyle = __mako_require__("node_modules/antd-style/es/index.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _reactdom = __mako_require__("node_modules/react-dom/index.js");
var _rwxai = __mako_require__("src/services/rwxai/index.ts");
var _auth = __mako_require__("src/utils/auth.ts");
var _defaultSettings = /*#__PURE__*/ _interop_require_default._(__mako_require__("config/defaultSettings.ts"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
var _s1 = $RefreshSig$();
var _s2 = $RefreshSig$();
const { Title, Paragraph } = _antd.Typography;
const useStyles = (0, _antdstyle.createStyles)(({ token })=>{
    return {
        container: {
            display: 'flex',
            height: '100vh',
            overflow: 'hidden',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            position: 'relative'
        },
        leftSection: {
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            padding: '0 80px',
            color: 'white',
            position: 'relative',
            '&::before': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: 'rgba(0, 0, 0, 0.1)',
                backdropFilter: 'blur(10px)'
            }
        },
        rightSection: {
            width: '420px',
            minWidth: '380px',
            maxWidth: '450px',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            padding: '30px 25px',
            backgroundColor: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(20px)',
            boxShadow: '-10px 0 30px rgba(0, 0, 0, 0.1)',
            '@media (max-width: 768px)': {
                width: '100%',
                minWidth: 'auto',
                maxWidth: 'none',
                padding: '20px'
            }
        },
        brandSection: {
            textAlign: 'center',
            marginBottom: '60px',
            position: 'relative',
            zIndex: 1
        },
        brandTitle: {
            fontSize: '48px',
            fontWeight: 'bold',
            marginBottom: '16px',
            background: 'linear-gradient(45deg, #fff, #f0f0f0)',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)'
        },
        brandSubtitle: {
            fontSize: '18px',
            opacity: 0.9,
            marginBottom: '40px'
        },
        featureList: {
            listStyle: 'none',
            padding: 0,
            margin: 0,
            '& li': {
                display: 'flex',
                alignItems: 'center',
                marginBottom: '20px',
                fontSize: '16px',
                '& .anticon': {
                    marginRight: '12px',
                    fontSize: '20px',
                    color: '#fff'
                }
            }
        },
        loginCard: {
            width: '100%',
            maxWidth: '380px',
            border: 'none',
            borderRadius: '12px',
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
            overflow: 'hidden',
            '& .ant-card-body': {
                padding: '0'
            }
        },
        loginHeader: {
            textAlign: 'center',
            marginBottom: '32px'
        },
        logo: {
            width: '64px',
            height: '64px',
            marginBottom: '16px'
        },
        loginTitle: {
            fontSize: '28px',
            fontWeight: 'bold',
            color: token.colorText,
            marginBottom: '8px'
        },
        loginSubtitle: {
            color: token.colorTextSecondary,
            fontSize: '14px'
        },
        lang: {
            position: 'fixed',
            top: 16,
            right: 16,
            zIndex: 1000,
            width: 42,
            height: 42,
            lineHeight: '42px',
            borderRadius: token.borderRadius,
            backgroundColor: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(10px)',
            ':hover': {
                backgroundColor: 'rgba(255, 255, 255, 0.2)'
            }
        },
        formItem: {
            marginBottom: '24px',
            '& .ant-input-affix-wrapper': {
                height: '48px',
                borderRadius: '8px',
                border: '1px solid #e0e0e0',
                '&:hover': {
                    borderColor: token.colorPrimary
                },
                '&:focus-within': {
                    borderColor: token.colorPrimary,
                    boxShadow: `0 0 0 2px ${token.colorPrimary}20`
                }
            }
        },
        submitButton: {
            height: '48px',
            borderRadius: '8px',
            fontSize: '16px',
            fontWeight: 'bold',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            border: 'none',
            '&:hover': {
                background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)'
            }
        }
    };
});
const BrandSection = ()=>{
    _s();
    const { styles } = useStyles();
    const intl = (0, _max.useIntl)();
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        className: styles.brandSection,
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                className: styles.brandTitle,
                children: "RwxAI"
            }, void 0, false, {
                fileName: "src/pages/user/login/index.tsx",
                lineNumber: 194,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                className: styles.brandSubtitle,
                children: intl.formatMessage({
                    id: 'pages.login.brand.subtitle',
                    defaultMessage: '智能AI助手平台，让AI为您的工作赋能'
                })
            }, void 0, false, {
                fileName: "src/pages/user/login/index.tsx",
                lineNumber: 195,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("ul", {
                className: styles.featureList,
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.RobotOutlined, {}, void 0, false, {
                                fileName: "src/pages/user/login/index.tsx",
                                lineNumber: 203,
                                columnNumber: 11
                            }, this),
                            intl.formatMessage({
                                id: 'pages.login.feature.ai',
                                defaultMessage: '强大的AI模型集成'
                            })
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/user/login/index.tsx",
                        lineNumber: 202,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SafetyCertificateOutlined, {}, void 0, false, {
                                fileName: "src/pages/user/login/index.tsx",
                                lineNumber: 210,
                                columnNumber: 11
                            }, this),
                            intl.formatMessage({
                                id: 'pages.login.feature.security',
                                defaultMessage: '企业级安全保障'
                            })
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/user/login/index.tsx",
                        lineNumber: 209,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                fileName: "src/pages/user/login/index.tsx",
                                lineNumber: 217,
                                columnNumber: 11
                            }, this),
                            intl.formatMessage({
                                id: 'pages.login.feature.easy',
                                defaultMessage: '简单易用的界面'
                            })
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/user/login/index.tsx",
                        lineNumber: 216,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/user/login/index.tsx",
                lineNumber: 201,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/user/login/index.tsx",
        lineNumber: 193,
        columnNumber: 5
    }, this);
};
_s(BrandSection, "OjJ+EllAvksx/JBYcwLswiJQH8M=", false, function() {
    return [
        useStyles,
        _max.useIntl
    ];
});
_c = BrandSection;
const Lang = ()=>{
    _s1();
    const { styles } = useStyles();
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        className: styles.lang,
        "data-lang": true,
        children: _max.SelectLang && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.SelectLang, {}, void 0, false, {
            fileName: "src/pages/user/login/index.tsx",
            lineNumber: 233,
            columnNumber: 22
        }, this)
    }, void 0, false, {
        fileName: "src/pages/user/login/index.tsx",
        lineNumber: 232,
        columnNumber: 5
    }, this);
};
_s1(Lang, "1BGFRu6BGAbhzJ8kKgs1GUjvI6w=", false, function() {
    return [
        useStyles
    ];
});
_c1 = Lang;
const LoginMessage = ({ content })=>{
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
        style: {
            marginBottom: 24
        },
        message: content,
        type: "error",
        showIcon: true
    }, void 0, false, {
        fileName: "src/pages/user/login/index.tsx",
        lineNumber: 242,
        columnNumber: 5
    }, this);
};
_c2 = LoginMessage;
const Login = ()=>{
    _s2();
    const [userLoginState, setUserLoginState] = (0, _react.useState)({});
    const { initialState, setInitialState } = (0, _max.useModel)('@@initialState');
    const { styles } = useStyles();
    const { message } = _antd.App.useApp();
    const intl = (0, _max.useIntl)();
    const fetchUserInfo = async ()=>{
        var _initialState_fetchUserInfo;
        const userInfo = await (initialState === null || initialState === void 0 ? void 0 : (_initialState_fetchUserInfo = initialState.fetchUserInfo) === null || _initialState_fetchUserInfo === void 0 ? void 0 : _initialState_fetchUserInfo.call(initialState));
        if (userInfo) (0, _reactdom.flushSync)(()=>{
            setInitialState((s)=>({
                    ...s,
                    currentUser: userInfo
                }));
        });
    };
    const handleSubmit = async (values)=>{
        try {
            var _response_data;
            // 调用RwxAI登录接口
            const loginData = {
                Username: values.username,
                Password: values.password
            };
            const response = await (0, _rwxai.login)(loginData);
            if (response.success && ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.token)) {
                // 保存JWT令牌和用户信息
                (0, _auth.saveLoginInfo)(response.data);
                const defaultLoginSuccessMessage = intl.formatMessage({
                    id: 'pages.login.success',
                    defaultMessage: '登录成功！'
                });
                message.success(defaultLoginSuccessMessage);
                // 刷新用户信息
                await fetchUserInfo();
                // 跳转到目标页面
                const urlParams = new URL(window.location.href).searchParams;
                window.location.href = urlParams.get('redirect') || '/';
                return;
            }
            // 登录失败
            setUserLoginState({
                status: 'error',
                type: 'account'
            });
        } catch (error) {
            const defaultLoginFailureMessage = intl.formatMessage({
                id: 'pages.login.failure',
                defaultMessage: '登录失败，请重试！'
            });
            console.error('登录错误:', error);
            message.error(error.message || defaultLoginFailureMessage);
            setUserLoginState({
                status: 'error',
                type: 'account'
            });
        }
    };
    const { status } = userLoginState;
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        className: styles.container,
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.Helmet, {
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("title", {
                    children: [
                        intl.formatMessage({
                            id: 'menu.login',
                            defaultMessage: '登录页'
                        }),
                        _defaultSettings.default.title && ` - ${_defaultSettings.default.title}`
                    ]
                }, void 0, true, {
                    fileName: "src/pages/user/login/index.tsx",
                    lineNumber: 318,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/user/login/index.tsx",
                lineNumber: 317,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Lang, {}, void 0, false, {
                fileName: "src/pages/user/login/index.tsx",
                lineNumber: 326,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                className: styles.leftSection,
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(BrandSection, {}, void 0, false, {
                    fileName: "src/pages/user/login/index.tsx",
                    lineNumber: 330,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/user/login/index.tsx",
                lineNumber: 329,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                className: styles.rightSection,
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                    className: styles.loginCard,
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            className: styles.loginHeader,
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("img", {
                                    alt: "logo",
                                    src: "/logo.svg",
                                    className: styles.logo
                                }, void 0, false, {
                                    fileName: "src/pages/user/login/index.tsx",
                                    lineNumber: 337,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                    level: 2,
                                    className: styles.loginTitle,
                                    children: intl.formatMessage({
                                        id: 'pages.login.title',
                                        defaultMessage: '欢迎登录'
                                    })
                                }, void 0, false, {
                                    fileName: "src/pages/user/login/index.tsx",
                                    lineNumber: 338,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    className: styles.loginSubtitle,
                                    children: intl.formatMessage({
                                        id: 'pages.login.subtitle',
                                        defaultMessage: '请输入您的账号和密码'
                                    })
                                }, void 0, false, {
                                    fileName: "src/pages/user/login/index.tsx",
                                    lineNumber: 344,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/user/login/index.tsx",
                            lineNumber: 336,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.LoginForm, {
                            submitter: {
                                searchConfig: {
                                    submitText: intl.formatMessage({
                                        id: 'pages.login.submit',
                                        defaultMessage: '登录'
                                    })
                                },
                                submitButtonProps: {
                                    className: styles.submitButton,
                                    size: 'large'
                                }
                            },
                            onFinish: async (values)=>{
                                await handleSubmit(values);
                            },
                            children: [
                                status === 'error' && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(LoginMessage, {
                                    content: intl.formatMessage({
                                        id: 'pages.login.accountLogin.errorMessage',
                                        defaultMessage: '用户名或密码错误，请重试'
                                    })
                                }, void 0, false, {
                                    fileName: "src/pages/user/login/index.tsx",
                                    lineNumber: 370,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    className: styles.formItem,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProFormText, {
                                        name: "username",
                                        fieldProps: {
                                            size: 'large',
                                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {
                                                style: {
                                                    color: '#999'
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/user/login/index.tsx",
                                                lineNumber: 383,
                                                columnNumber: 27
                                            }, void 0)
                                        },
                                        placeholder: intl.formatMessage({
                                            id: 'pages.login.username.placeholder',
                                            defaultMessage: '请输入用户名'
                                        }),
                                        rules: [
                                            {
                                                required: true,
                                                message: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                                    id: "pages.login.username.required",
                                                    defaultMessage: "请输入用户名!"
                                                }, void 0, false, {
                                                    fileName: "src/pages/user/login/index.tsx",
                                                    lineNumber: 393,
                                                    columnNumber: 23
                                                }, void 0)
                                            }
                                        ]
                                    }, void 0, false, {
                                        fileName: "src/pages/user/login/index.tsx",
                                        lineNumber: 379,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/user/login/index.tsx",
                                    lineNumber: 378,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    className: styles.formItem,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProFormText.Password, {
                                        name: "password",
                                        fieldProps: {
                                            size: 'large',
                                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.LockOutlined, {
                                                style: {
                                                    color: '#999'
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/user/login/index.tsx",
                                                lineNumber: 408,
                                                columnNumber: 27
                                            }, void 0)
                                        },
                                        placeholder: intl.formatMessage({
                                            id: 'pages.login.password.placeholder',
                                            defaultMessage: '请输入密码'
                                        }),
                                        rules: [
                                            {
                                                required: true,
                                                message: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                                    id: "pages.login.password.required",
                                                    defaultMessage: "请输入密码！"
                                                }, void 0, false, {
                                                    fileName: "src/pages/user/login/index.tsx",
                                                    lineNumber: 418,
                                                    columnNumber: 23
                                                }, void 0)
                                            }
                                        ]
                                    }, void 0, false, {
                                        fileName: "src/pages/user/login/index.tsx",
                                        lineNumber: 404,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/user/login/index.tsx",
                                    lineNumber: 403,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        marginBottom: 24,
                                        display: 'flex',
                                        justifyContent: 'space-between',
                                        alignItems: 'center'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProFormCheckbox, {
                                            noStyle: true,
                                            name: "autoLogin",
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                                id: "pages.login.rememberMe",
                                                defaultMessage: "记住我"
                                            }, void 0, false, {
                                                fileName: "src/pages/user/login/index.tsx",
                                                lineNumber: 437,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/user/login/index.tsx",
                                            lineNumber: 436,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("a", {
                                            style: {
                                                color: '#667eea',
                                                textDecoration: 'none'
                                            },
                                            onMouseEnter: (e)=>{
                                                e.currentTarget.style.textDecoration = 'underline';
                                            },
                                            onMouseLeave: (e)=>{
                                                e.currentTarget.style.textDecoration = 'none';
                                            },
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                                id: "pages.login.forgotPassword",
                                                defaultMessage: "忘记密码？"
                                            }, void 0, false, {
                                                fileName: "src/pages/user/login/index.tsx",
                                                lineNumber: 454,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/user/login/index.tsx",
                                            lineNumber: 442,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/user/login/index.tsx",
                                    lineNumber: 428,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/user/login/index.tsx",
                            lineNumber: 352,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/user/login/index.tsx",
                    lineNumber: 335,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/user/login/index.tsx",
                lineNumber: 334,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/user/login/index.tsx",
        lineNumber: 316,
        columnNumber: 5
    }, this);
};
_s2(Login, "0fPv87dowfD0OjNGv6SlW2C9aFY=", false, function() {
    return [
        _max.useModel,
        useStyles,
        _antd.App.useApp,
        _max.useIntl
    ];
});
_c3 = Login;
var _default = Login;
var _c;
var _c1;
var _c2;
var _c3;
$RefreshReg$(_c, "BrandSection");
$RefreshReg$(_c1, "Lang");
$RefreshReg$(_c2, "LoginMessage");
$RefreshReg$(_c3, "Login");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=p__user__login__index-async.js.map