((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] || []).push([
        ['src/pages/auth-test/index.tsx'],
{ "src/pages/auth-test/index.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _auth = __mako_require__("src/utils/auth.ts");
var _rwxai = __mako_require__("src/services/rwxai/index.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Text, Paragraph } = _antd.Typography;
const AuthTestPage = ()=>{
    _s();
    const [tokenInfo, setTokenInfo] = (0, _react.useState)(null);
    const [userInfo, setUserInfo] = (0, _react.useState)(null);
    const [apiTestResult, setApiTestResult] = (0, _react.useState)('');
    const [loading, setLoading] = (0, _react.useState)(false);
    (0, _react.useEffect)(()=>{
        loadAuthInfo();
    }, []);
    const loadAuthInfo = ()=>{
        const token = (0, _auth.getToken)();
        const user = (0, _auth.getUserInfo)();
        setTokenInfo({
            token: token ? `${token.substring(0, 20)}...` : null,
            isLoggedIn: (0, _auth.isLoggedIn)(),
            fullToken: token
        });
        setUserInfo(user);
    };
    const testApiCall = async ()=>{
        setLoading(true);
        try {
            const response = await (0, _rwxai.getAIModels)();
            if (response.success) {
                var _response_data;
                setApiTestResult(`✅ 成功获取 ${((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.length) || 0} 个AI模型`);
            } else setApiTestResult(`❌ API调用失败: ${response.message}`);
        } catch (error) {
            setApiTestResult(`❌ 异常: ${error.message}`);
        } finally{
            setLoading(false);
        }
    };
    const handleLogout = ()=>{
        (0, _auth.logout)();
        loadAuthInfo();
        _antd.message.info('已退出登录');
    };
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        title: "JWT认证测试页面",
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
            direction: "vertical",
            size: "large",
            style: {
                width: '100%'
            },
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                    title: "认证状态",
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions, {
                        column: 1,
                        bordered: true,
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                label: "登录状态",
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    type: (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.isLoggedIn) ? 'success' : 'danger',
                                    children: (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.isLoggedIn) ? '已登录' : '未登录'
                                }, void 0, false, {
                                    fileName: "src/pages/auth-test/index.tsx",
                                    lineNumber: 60,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/auth-test/index.tsx",
                                lineNumber: 59,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                label: "JWT令牌",
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    code: true,
                                    children: (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.token) || '无'
                                }, void 0, false, {
                                    fileName: "src/pages/auth-test/index.tsx",
                                    lineNumber: 65,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/auth-test/index.tsx",
                                lineNumber: 64,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                label: "用户信息",
                                children: userInfo ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            children: [
                                                "用户名: ",
                                                userInfo.username || userInfo.name
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/auth-test/index.tsx",
                                            lineNumber: 70,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            children: [
                                                "邮箱: ",
                                                userInfo.email
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/auth-test/index.tsx",
                                            lineNumber: 71,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            children: [
                                                "姓名: ",
                                                userInfo.firstName,
                                                " ",
                                                userInfo.lastName
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/auth-test/index.tsx",
                                            lineNumber: 72,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/auth-test/index.tsx",
                                    lineNumber: 69,
                                    columnNumber: 17
                                }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    type: "secondary",
                                    children: "无用户信息"
                                }, void 0, false, {
                                    fileName: "src/pages/auth-test/index.tsx",
                                    lineNumber: 75,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/auth-test/index.tsx",
                                lineNumber: 67,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/auth-test/index.tsx",
                        lineNumber: 58,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/auth-test/index.tsx",
                    lineNumber: 57,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                    title: "API测试",
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                        direction: "vertical",
                        style: {
                            width: '100%'
                        },
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                                children: "点击下面的按钮测试带JWT令牌的API调用："
                            }, void 0, false, {
                                fileName: "src/pages/auth-test/index.tsx",
                                lineNumber: 83,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "primary",
                                        onClick: testApiCall,
                                        loading: loading,
                                        disabled: !(tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.isLoggedIn),
                                        children: "测试获取AI模型列表"
                                    }, void 0, false, {
                                        fileName: "src/pages/auth-test/index.tsx",
                                        lineNumber: 87,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        onClick: loadAuthInfo,
                                        children: "刷新认证信息"
                                    }, void 0, false, {
                                        fileName: "src/pages/auth-test/index.tsx",
                                        lineNumber: 95,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        danger: true,
                                        onClick: handleLogout,
                                        disabled: !(tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.isLoggedIn),
                                        children: "退出登录"
                                    }, void 0, false, {
                                        fileName: "src/pages/auth-test/index.tsx",
                                        lineNumber: 98,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/auth-test/index.tsx",
                                lineNumber: 86,
                                columnNumber: 13
                            }, this),
                            apiTestResult && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                size: "small",
                                style: {
                                    marginTop: 16
                                },
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    children: apiTestResult
                                }, void 0, false, {
                                    fileName: "src/pages/auth-test/index.tsx",
                                    lineNumber: 104,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/auth-test/index.tsx",
                                lineNumber: 103,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/auth-test/index.tsx",
                        lineNumber: 82,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/auth-test/index.tsx",
                    lineNumber: 81,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                    title: "JWT令牌详情",
                    size: "small",
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                            code: true,
                            style: {
                                wordBreak: 'break-all',
                                fontSize: '12px'
                            },
                            children: (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.fullToken) || '无令牌'
                        }, void 0, false, {
                            fileName: "src/pages/auth-test/index.tsx",
                            lineNumber: 112,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/auth-test/index.tsx",
                        lineNumber: 111,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/auth-test/index.tsx",
                    lineNumber: 110,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                    title: "使用说明",
                    size: "small",
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                level: 5,
                                children: "测试步骤："
                            }, void 0, false, {
                                fileName: "src/pages/auth-test/index.tsx",
                                lineNumber: 120,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("ol", {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                        children: [
                                            "首先访问 ",
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                code: true,
                                                children: "/user/login"
                                            }, void 0, false, {
                                                fileName: "src/pages/auth-test/index.tsx",
                                                lineNumber: 122,
                                                columnNumber: 24
                                            }, this),
                                            " 页面进行登录"
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/auth-test/index.tsx",
                                        lineNumber: 122,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                        children: "登录成功后会自动保存JWT令牌到localStorage"
                                    }, void 0, false, {
                                        fileName: "src/pages/auth-test/index.tsx",
                                        lineNumber: 123,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                        children: "返回此页面查看认证状态"
                                    }, void 0, false, {
                                        fileName: "src/pages/auth-test/index.tsx",
                                        lineNumber: 124,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                        children: '点击"测试获取AI模型列表"按钮验证API调用'
                                    }, void 0, false, {
                                        fileName: "src/pages/auth-test/index.tsx",
                                        lineNumber: 125,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                        children: "所有后续的API请求都会自动携带JWT令牌"
                                    }, void 0, false, {
                                        fileName: "src/pages/auth-test/index.tsx",
                                        lineNumber: 126,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/auth-test/index.tsx",
                                lineNumber: 121,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/auth-test/index.tsx",
                        lineNumber: 119,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/auth-test/index.tsx",
                    lineNumber: 118,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "src/pages/auth-test/index.tsx",
            lineNumber: 56,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "src/pages/auth-test/index.tsx",
        lineNumber: 55,
        columnNumber: 5
    }, this);
};
_s(AuthTestPage, "wzAVMISWgXGNzJfaxDSxGwTtwHM=");
_c = AuthTestPage;
var _default = AuthTestPage;
var _c;
$RefreshReg$(_c, "AuthTestPage");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=src_pages_auth-test_index_tsx-async.js.map