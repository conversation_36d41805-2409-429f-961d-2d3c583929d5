import React, { useState } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import { Card, Button, Space, Typography, Alert, Divider, Input, Form } from 'antd';
import { aiModelsApi } from '@/services/rwxai/enhanced-aiModels';
import { httpRequest } from '@/utils/request';
import { getApiPrefix } from '@/utils/api';

const { Title, Paragraph, Text } = Typography;

const ResponseDemoPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<string>('');
  const [form] = Form.useForm();

  // 测试成功响应 (200)
  const testSuccessResponse = async () => {
    setLoading(true);
    try {
      const response = await aiModelsApi.getAIModels();
      if (response.success) {
        setResult(`✅ 成功获取 ${response.data?.length || 0} 个AI模型`);
      } else {
        setResult(`❌ 请求失败: ${response.message}`);
      }
    } catch (error) {
      setResult(`❌ 异常: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // 测试400错误 - 请求参数错误
  const test400Error = async () => {
    setLoading(true);
    try {
      const response = await httpRequest.post(`${getApiPrefix()}/AIModels`, {
        // 故意发送无效数据
        invalidField: 'invalid value',
      });
      setResult(`结果: ${response.success ? '成功' : response.message}`);
    } catch (error) {
      setResult(`异常: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // 测试401错误 - 未授权
  const test401Error = async () => {
    setLoading(true);
    try {
      const response = await httpRequest.get(`${getApiPrefix()}/AIModels`, {
        skipAuth: true, // 跳过认证
      });
      setResult(`结果: ${response.success ? '成功' : response.message}`);
    } catch (error) {
      setResult(`异常: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // 测试403错误 - 权限不足
  const test403Error = async () => {
    setLoading(true);
    try {
      const response = await httpRequest.get(`${getApiPrefix()}/admin/system-settings`);
      setResult(`结果: ${response.success ? '成功' : response.message}`);
    } catch (error) {
      setResult(`异常: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // 测试404错误 - 资源不存在
  const test404Error = async () => {
    setLoading(true);
    try {
      const response = await aiModelsApi.getAIModelById('non-existent-id');
      setResult(`结果: ${response.success ? '成功' : response.message}`);
    } catch (error) {
      setResult(`异常: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // 测试422错误 - 数据验证失败
  const test422Error = async () => {
    setLoading(true);
    try {
      const response = await aiModelsApi.createAIModel({
        name: '', // 空名称应该会触发验证错误
        modelKey: '',
        modelType: 'Chat',
        provider: '',
      } as any);
      setResult(`结果: ${response.success ? '成功' : response.message}`);
    } catch (error) {
      setResult(`异常: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // 测试500错误 - 服务器内部错误
  const test500Error = async () => {
    setLoading(true);
    try {
      const response = await httpRequest.get(`${getApiPrefix()}/test/server-error`);
      setResult(`结果: ${response.success ? '成功' : response.message}`);
    } catch (error) {
      setResult(`异常: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // 测试网络错误
  const testNetworkError = async () => {
    setLoading(true);
    try {
      const response = await httpRequest.get('http://invalid-domain.com/api/test', {
        timeout: 3000,
      });
      setResult(`结果: ${response.success ? '成功' : response.message}`);
    } catch (error) {
      setResult(`异常: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // 自定义API测试
  const testCustomApi = async (values: any) => {
    setLoading(true);
    try {
      const { url, method } = values;
      const response = await httpRequest[method.toLowerCase()](url);
      setResult(`结果: ${response.success ? '成功' : response.message}`);
    } catch (error) {
      setResult(`异常: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <PageContainer title="响应处理系统演示">
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Alert
          message="响应处理系统说明"
          description="此页面演示统一的API响应处理系统，包括各种HTTP状态码的处理和用户友好的错误提示。"
          type="info"
          showIcon
        />

        <Card title="成功响应测试">
          <Space wrap>
            <Button type="primary" onClick={testSuccessResponse} loading={loading}>
              测试200 - 获取AI模型列表
            </Button>
          </Space>
        </Card>

        <Card title="客户端错误测试 (4xx)">
          <Space wrap>
            <Button onClick={test400Error} loading={loading}>
              测试400 - 请求参数错误
            </Button>
            <Button onClick={test401Error} loading={loading}>
              测试401 - 未授权
            </Button>
            <Button onClick={test403Error} loading={loading}>
              测试403 - 权限不足
            </Button>
            <Button onClick={test404Error} loading={loading}>
              测试404 - 资源不存在
            </Button>
            <Button onClick={test422Error} loading={loading}>
              测试422 - 数据验证失败
            </Button>
          </Space>
        </Card>

        <Card title="服务器错误测试 (5xx)">
          <Space wrap>
            <Button onClick={test500Error} loading={loading}>
              测试500 - 服务器内部错误
            </Button>
          </Space>
        </Card>

        <Card title="网络错误测试">
          <Space wrap>
            <Button onClick={testNetworkError} loading={loading}>
              测试网络连接错误
            </Button>
          </Space>
        </Card>

        <Card title="自定义API测试">
          <Form form={form} onFinish={testCustomApi} layout="inline">
            <Form.Item name="url" label="API地址" initialValue={`${getApiPrefix()}/AIModels`}>
              <Input style={{ width: 300 }} />
            </Form.Item>
            <Form.Item name="method" label="请求方法" initialValue="GET">
              <Input style={{ width: 100 }} />
            </Form.Item>
            <Form.Item>
              <Button type="primary" htmlType="submit" loading={loading}>
                发送请求
              </Button>
            </Form.Item>
          </Form>
        </Card>

        {result && (
          <Card title="测试结果" size="small">
            <Text code>{result}</Text>
          </Card>
        )}

        <Card title="响应处理特性" size="small">
          <Paragraph>
            <Title level={5}>自动处理的功能：</Title>
            <ul>
              <li>✅ 自动添加JWT令牌到请求头</li>
              <li>✅ 401错误自动刷新令牌并重试</li>
              <li>✅ 根据错误类型显示不同的提示方式</li>
              <li>✅ 验证错误显示详细的字段信息</li>
              <li>✅ 服务器错误显示通知而非简单消息</li>
              <li>✅ 网络错误自动重试机制</li>
              <li>✅ 统一的成功/失败消息提示</li>
              <li>✅ 可配置的消息显示选项</li>
            </ul>
          </Paragraph>
        </Card>
      </Space>
    </PageContainer>
  );
};

export default ResponseDemoPage;
