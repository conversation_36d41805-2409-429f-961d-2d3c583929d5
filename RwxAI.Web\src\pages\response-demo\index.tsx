import React, { useState } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import { Card, Button, Space, message, Alert, Divider } from 'antd';
import { useIntl, FormattedMessage } from '@umijs/max';

const ResponseDemo: React.FC = () => {
  const intl = useIntl();
  const [loading, setLoading] = useState(false);

  const handleSuccessDemo = () => {
    setLoading(true);
    setTimeout(() => {
      message.success('操作成功！');
      setLoading(false);
    }, 1000);
  };

  const handleErrorDemo = () => {
    setLoading(true);
    setTimeout(() => {
      message.error('操作失败！');
      setLoading(false);
    }, 1000);
  };

  const handleWarningDemo = () => {
    setLoading(true);
    setTimeout(() => {
      message.warning('警告信息！');
      setLoading(false);
    }, 1000);
  };

  return (
    <PageContainer
      title="响应处理演示"
      subTitle="演示各种响应处理效果"
    >
      <Card title="消息提示演示">
        <Alert
          message="这是一个演示页面"
          description="用于测试各种响应处理效果，包括成功、失败、警告等消息提示。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
        
        <Space>
          <Button 
            type="primary" 
            loading={loading}
            onClick={handleSuccessDemo}
          >
            成功消息
          </Button>
          <Button 
            danger 
            loading={loading}
            onClick={handleErrorDemo}
          >
            错误消息
          </Button>
          <Button 
            loading={loading}
            onClick={handleWarningDemo}
          >
            警告消息
          </Button>
        </Space>

        <Divider />

        <Alert
          message="开发说明"
          description="这个页面是为了解决路由配置中缺失的组件而创建的临时页面。在实际开发中，您可以根据需要修改或删除此页面。"
          type="warning"
          showIcon
        />
      </Card>
    </PageContainer>
  );
};

export default ResponseDemo;
