import { httpRequest } from '@/utils/request';
import { getApiPrefix } from '@/utils/api';
import { ResponseHandleResult } from '@/types/response';

const API_PREFIX: string = getApiPrefix();

/**
 * 用户认证相关API
 */

// 用户登录接口
export async function login(data: RwxAI.LoginRequest): Promise<ResponseHandleResult<{
  token: string;
  refreshToken: string;
  user: any;
  expiresIn: number;
}>> {
  return httpRequest.post(`${API_PREFIX}/Auth/login`, data, {
    skipAuth: true, // 登录接口不需要认证
    showErrorNotification: true,
  });
}

// 用户注册接口
export async function register(data: RwxAI.RegisterRequest): Promise<ResponseHandleResult<{
  success: boolean;
  message: string;
}>> {
  return httpRequest.post(`${API_PREFIX}/Auth/register`, data, {
    skipAuth: true, // 注册接口不需要认证
    showErrorNotification: true,
  });
}

// 刷新令牌接口
export async function refreshToken(data: RwxAI.RefreshTokenRequest): Promise<ResponseHandleResult<{
  token: string;
  refreshToken: string;
  expiresIn: number;
}>> {
  return httpRequest.post(`${API_PREFIX}/Auth/refresh-token`, data, {
    skipAuth: true, // 刷新令牌接口不需要认证
    showErrorNotification: false, // 刷新令牌失败不显示错误通知
  });
}

// 修改密码接口
export async function changePassword(data: RwxAI.ChangePasswordRequest): Promise<ResponseHandleResult<{
  success: boolean;
  message: string;
}>> {
  return httpRequest.post(`${API_PREFIX}/Auth/change-password`, data, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: '密码修改成功',
  });
}
