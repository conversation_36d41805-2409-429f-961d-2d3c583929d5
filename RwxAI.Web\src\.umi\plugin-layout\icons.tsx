// @ts-nocheck
// This file is generated by Um<PERSON> automatically
// DO NOT CHANGE IT MANUALLY!
import SmileOutlined from 'D:/rwx-ai/RwxAI.Web/node_modules/@umijs/plugins/node_modules/@ant-design/icons/es/icons/SmileOutlined';
import RobotOutlined from 'D:/rwx-ai/RwxAI.Web/node_modules/@umijs/plugins/node_modules/@ant-design/icons/es/icons/RobotOutlined';
import AppstoreOutlined from 'D:/rwx-ai/RwxAI.Web/node_modules/@umijs/plugins/node_modules/@ant-design/icons/es/icons/AppstoreOutlined';
import MessageOutlined from 'D:/rwx-ai/RwxAI.Web/node_modules/@umijs/plugins/node_modules/@ant-design/icons/es/icons/MessageOutlined';
import BookOutlined from 'D:/rwx-ai/RwxAI.Web/node_modules/@umijs/plugins/node_modules/@ant-design/icons/es/icons/BookOutlined';
import ApiOutlined from 'D:/rwx-ai/RwxAI.Web/node_modules/@umijs/plugins/node_modules/@ant-design/icons/es/icons/ApiOutlined';
import SafetyOutlined from 'D:/rwx-ai/RwxAI.Web/node_modules/@umijs/plugins/node_modules/@ant-design/icons/es/icons/SafetyOutlined';
import ExperimentOutlined from 'D:/rwx-ai/RwxAI.Web/node_modules/@umijs/plugins/node_modules/@ant-design/icons/es/icons/ExperimentOutlined';
import CrownOutlined from 'D:/rwx-ai/RwxAI.Web/node_modules/@umijs/plugins/node_modules/@ant-design/icons/es/icons/CrownOutlined';
import TableOutlined from 'D:/rwx-ai/RwxAI.Web/node_modules/@umijs/plugins/node_modules/@ant-design/icons/es/icons/TableOutlined';
export default { SmileOutlined, RobotOutlined, AppstoreOutlined, MessageOutlined, BookOutlined, ApiOutlined, SafetyOutlined, ExperimentOutlined, CrownOutlined, TableOutlined };
