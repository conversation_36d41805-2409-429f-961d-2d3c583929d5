{"name": "@umijs/renderer-react", "version": "4.4.12", "description": "@umijs/renderer-react", "homepage": "https://github.com/umijs/umi/tree/master/packages/renderer-react#readme", "bugs": "https://github.com/umijs/umi/issues", "repository": {"type": "git", "url": "https://github.com/umijs/umi"}, "license": "MIT", "sideEffects": false, "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "dependencies": {"@babel/runtime": "7.23.6", "@loadable/component": "5.15.2", "history": "5.3.0", "react-helmet-async": "1.3.0", "react-router-dom": "6.3.0"}, "devDependencies": {"react": "18.3.1", "react-dom": "18.3.1"}, "peerDependencies": {"react": ">=16.8", "react-dom": ">=16.8"}, "publishConfig": {"access": "public"}, "authors": ["chen<PERSON> <<EMAIL>> (https://github.com/sorrycc)"], "scripts": {"build": "umi-scripts father build", "build:deps": "umi-scripts bundleDeps", "dev": "umi-scripts father dev", "test": "umi-scripts jest-turbo"}}