((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] || []).push([
        ['common'],
{ "src/pages/chat/components/SessionForm.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _rwxai = __mako_require__("src/services/rwxai/index.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const SessionForm = ({ visible, onVisibleChange, initialValues, onSuccess })=>{
    _s();
    const intl = (0, _max.useIntl)();
    const [form] = _antd.Form.useForm();
    const [loading, setLoading] = (0, _react.useState)(false);
    const [models, setModels] = (0, _react.useState)([]);
    const [selectedModel, setSelectedModel] = (0, _react.useState)(null);
    const isEdit = !!(initialValues === null || initialValues === void 0 ? void 0 : initialValues.Id);
    (0, _react.useEffect)(()=>{
        if (visible) {
            loadModels();
            if (initialValues) {
                form.setFieldsValue(initialValues);
                if (initialValues.ModelId) {
                    const model = models.find((m)=>m.Id === initialValues.ModelId);
                    setSelectedModel(model || null);
                }
            } else {
                form.resetFields();
                setSelectedModel(null);
            }
        }
    }, [
        visible,
        initialValues,
        models
    ]);
    const loadModels = async ()=>{
        const response = await (0, _rwxai.getAIModels)();
        if (response.success) {
            const chatModels = (response.data || []).filter((model)=>{
                var _model_Template;
                return ((_model_Template = model.Template) === null || _model_Template === void 0 ? void 0 : _model_Template.Type) === 0 && model.IsEnabled;
            });
            setModels(chatModels);
        } else setModels([]);
    };
    const handleModelChange = (modelId)=>{
        const model = models.find((m)=>m.Id === modelId);
        setSelectedModel(model || null);
        if (model) form.setFieldsValue({
            Temperature: model.Temperature || 0.7,
            MaxTokens: model.MaxTokens || 2000,
            SystemPrompt: model.SystemPrompt || ''
        });
    };
    const handleSubmit = async ()=>{
        try {
            const values = await form.validateFields();
            setLoading(true);
            let response;
            if (isEdit) response = await (0, _rwxai.updateChatSession)(initialValues.Id, {
                ...initialValues,
                ...values
            });
            else response = await (0, _rwxai.createChatSession)(values);
            if (response.success) onSuccess();
        } catch (error) {
            console.error('Form validation error:', error);
        } finally{
            setLoading(false);
        }
    };
    return (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
        title: intl.formatMessage({
            id: isEdit ? 'pages.chat.form.edit.title' : 'pages.chat.form.create.title'
        }),
        open: visible,
        onCancel: ()=>onVisibleChange(false),
        onOk: handleSubmit,
        confirmLoading: loading,
        width: 600,
        children: (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
            form: form,
            layout: "vertical",
            initialValues: {
                IsActive: true,
                Temperature: 0.7,
                MaxTokens: 2000
            },
            children: [
                (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    name: "Name",
                    label: (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.chat.form.name"
                    }, void 0, false, {
                        fileName: "src/pages/chat/components/SessionForm.tsx",
                        lineNumber: 117,
                        columnNumber: 18
                    }, void 0),
                    rules: [
                        {
                            required: true,
                            message: intl.formatMessage({
                                id: 'pages.chat.form.name.required'
                            })
                        }
                    ],
                    children: (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                        placeholder: intl.formatMessage({
                            id: 'pages.chat.form.name.placeholder'
                        })
                    }, void 0, false, {
                        fileName: "src/pages/chat/components/SessionForm.tsx",
                        lineNumber: 120,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/chat/components/SessionForm.tsx",
                    lineNumber: 115,
                    columnNumber: 9
                }, this),
                (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    name: "ModelId",
                    label: (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.chat.form.model"
                    }, void 0, false, {
                        fileName: "src/pages/chat/components/SessionForm.tsx",
                        lineNumber: 125,
                        columnNumber: 18
                    }, void 0),
                    rules: [
                        {
                            required: true,
                            message: intl.formatMessage({
                                id: 'pages.chat.form.model.required'
                            })
                        }
                    ],
                    children: (0, _jsxdevruntime.jsxDEV)(_antd.Select, {
                        placeholder: intl.formatMessage({
                            id: 'pages.chat.form.model.placeholder'
                        }),
                        onChange: handleModelChange,
                        children: models.map((model)=>{
                            var _model_Template;
                            return (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                value: model.Id,
                                children: [
                                    model.Name,
                                    " (",
                                    (_model_Template = model.Template) === null || _model_Template === void 0 ? void 0 : _model_Template.ModelId,
                                    ")"
                                ]
                            }, model.Id, true, {
                                fileName: "src/pages/chat/components/SessionForm.tsx",
                                lineNumber: 133,
                                columnNumber: 15
                            }, this);
                        })
                    }, void 0, false, {
                        fileName: "src/pages/chat/components/SessionForm.tsx",
                        lineNumber: 128,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/chat/components/SessionForm.tsx",
                    lineNumber: 123,
                    columnNumber: 9
                }, this),
                selectedModel && (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                    children: [
                        (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            name: "SystemPrompt",
                            label: (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                id: "pages.chat.form.systemPrompt"
                            }, void 0, false, {
                                fileName: "src/pages/chat/components/SessionForm.tsx",
                                lineNumber: 145,
                                columnNumber: 22
                            }, void 0),
                            help: "从选中的模型自动获取",
                            children: (0, _jsxdevruntime.jsxDEV)(_antd.Input.TextArea, {
                                rows: 3,
                                disabled: true,
                                placeholder: "从模型配置中自动获取"
                            }, void 0, false, {
                                fileName: "src/pages/chat/components/SessionForm.tsx",
                                lineNumber: 148,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/chat/components/SessionForm.tsx",
                            lineNumber: 143,
                            columnNumber: 13
                        }, this),
                        (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                display: 'flex',
                                gap: '16px'
                            },
                            children: [
                                (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                    name: "Temperature",
                                    label: (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                        id: "pages.chat.form.temperature"
                                    }, void 0, false, {
                                        fileName: "src/pages/chat/components/SessionForm.tsx",
                                        lineNumber: 158,
                                        columnNumber: 24
                                    }, void 0),
                                    style: {
                                        flex: 1
                                    },
                                    children: (0, _jsxdevruntime.jsxDEV)(_antd.InputNumber, {
                                        disabled: true,
                                        style: {
                                            width: '100%'
                                        },
                                        placeholder: "从模型配置中自动获取"
                                    }, void 0, false, {
                                        fileName: "src/pages/chat/components/SessionForm.tsx",
                                        lineNumber: 161,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/chat/components/SessionForm.tsx",
                                    lineNumber: 156,
                                    columnNumber: 15
                                }, this),
                                (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                    name: "MaxTokens",
                                    label: (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                                        id: "pages.chat.form.maxTokens"
                                    }, void 0, false, {
                                        fileName: "src/pages/chat/components/SessionForm.tsx",
                                        lineNumber: 170,
                                        columnNumber: 24
                                    }, void 0),
                                    style: {
                                        flex: 1
                                    },
                                    children: (0, _jsxdevruntime.jsxDEV)(_antd.InputNumber, {
                                        disabled: true,
                                        style: {
                                            width: '100%'
                                        },
                                        placeholder: "从模型配置中自动获取"
                                    }, void 0, false, {
                                        fileName: "src/pages/chat/components/SessionForm.tsx",
                                        lineNumber: 173,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/chat/components/SessionForm.tsx",
                                    lineNumber: 168,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/chat/components/SessionForm.tsx",
                            lineNumber: 155,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true),
                (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    name: "IsActive",
                    label: (0, _jsxdevruntime.jsxDEV)(_max.FormattedMessage, {
                        id: "pages.chat.form.isActive"
                    }, void 0, false, {
                        fileName: "src/pages/chat/components/SessionForm.tsx",
                        lineNumber: 185,
                        columnNumber: 18
                    }, void 0),
                    valuePropName: "checked",
                    children: (0, _jsxdevruntime.jsxDEV)(_antd.Switch, {}, void 0, false, {
                        fileName: "src/pages/chat/components/SessionForm.tsx",
                        lineNumber: 188,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/chat/components/SessionForm.tsx",
                    lineNumber: 183,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "src/pages/chat/components/SessionForm.tsx",
            lineNumber: 106,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "src/pages/chat/components/SessionForm.tsx",
        lineNumber: 96,
        columnNumber: 5
    }, this);
};
_s(SessionForm, "IGErTDGogsOg+DvDQlsWPasSz8A=", false, function() {
    return [
        _max.useIntl,
        _antd.Form.useForm
    ];
});
_c = SessionForm;
var _default = SessionForm;
var _c;
$RefreshReg$(_c, "SessionForm");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/services/rwxai/aiModels.ts": function (module, exports, __mako_require__){
/**
 * 统一的AI模型管理API服务
 * 合并了原aiModels.ts和enhanced-aiModels.ts的功能
 * 提供完整的AI模型、模板、提供商管理功能
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
__mako_require__.e(exports, {
    aiModelsApi: function() {
        return aiModelsApi;
    },
    batchDeleteAIModels: function() {
        return batchDeleteAIModels;
    },
    createAIModel: function() {
        return createAIModel;
    },
    /**
 * 默认导出统一API对象（可选）
 */ default: function() {
        return _default;
    },
    deleteAIModel: function() {
        return deleteAIModel;
    },
    getAIModelById: function() {
        return getAIModelById;
    },
    getAIModelTemplateById: function() {
        return getAIModelTemplateById;
    },
    getAIModelTemplatesByProviderAndType: function() {
        return getAIModelTemplatesByProviderAndType;
    },
    getAIModelTemplatesByType: function() {
        return getAIModelTemplatesByType;
    },
    getAIModels: function() {
        return getAIModels;
    },
    getAllEnabledModels: function() {
        return getAllEnabledModels;
    },
    getEnabledAIProviders: function() {
        return getEnabledAIProviders;
    },
    getModelsByProviderCode: function() {
        return getModelsByProviderCode;
    },
    getModelsByProviderId: function() {
        return getModelsByProviderId;
    },
    getModelsByProviderType: function() {
        return getModelsByProviderType;
    },
    testAIModelConnection: function() {
        return testAIModelConnection;
    },
    toggleAIModelStatus: function() {
        return toggleAIModelStatus;
    },
    updateAIModel: function() {
        return updateAIModel;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
var _request = __mako_require__("src/utils/request.ts");
var _pageDataHandler = __mako_require__("src/utils/pageDataHandler.ts");
var _api = __mako_require__("src/utils/api.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
const API_PREFIX = (0, _api.getApiPrefix)();
async function getAIModels(params) {
    const url = params ? `${API_PREFIX}/AIModels?${(0, _pageDataHandler.toQueryString)(params)}` : `${API_PREFIX}/AIModels`;
    return _request.httpRequest.get(url, {
        showErrorNotification: true
    });
}
async function getAIModelById(id) {
    return _request.httpRequest.get(`${API_PREFIX}/AIModels/${id}`, {
        showErrorNotification: true
    });
}
async function createAIModel(data) {
    return _request.httpRequest.post(`${API_PREFIX}/AIModels`, data, {
        showSuccessMessage: true,
        showErrorNotification: true,
        successMessage: '创建AI模型成功'
    });
}
async function updateAIModel(id, data) {
    return _request.httpRequest.put(`${API_PREFIX}/AIModels/${id}`, data, {
        showSuccessMessage: true,
        showErrorNotification: true,
        successMessage: '更新AI模型成功'
    });
}
async function deleteAIModel(id) {
    return _request.httpRequest.delete(`${API_PREFIX}/AIModels/${id}`, {
        showSuccessMessage: true,
        showErrorNotification: true,
        successMessage: '删除AI模型成功'
    });
}
async function batchDeleteAIModels(ids) {
    return _request.httpRequest.post(`${API_PREFIX}/AIModels/batch-delete`, {
        ids
    }, {
        showSuccessMessage: true,
        showErrorNotification: true,
        successMessage: `成功删除 ${ids.length} 个AI模型`
    });
}
async function testAIModelConnection(id) {
    return _request.httpRequest.post(`${API_PREFIX}/AIModels/${id}/test`, {}, {
        showSuccessMessage: true,
        showErrorNotification: true,
        successMessage: 'AI模型连接测试成功'
    });
}
async function toggleAIModelStatus(id, enabled) {
    return _request.httpRequest.patch(`${API_PREFIX}/AIModels/${id}/status`, {
        enabled
    }, {
        showSuccessMessage: true,
        showErrorNotification: true,
        successMessage: enabled ? '启用AI模型成功' : '禁用AI模型成功'
    });
}
async function getAIModelTemplatesByType(modelType) {
    return _request.httpRequest.get(`${API_PREFIX}/AIModelTemplates/by-type/${modelType}`, {
        showErrorNotification: true
    });
}
async function getAIModelTemplatesByProviderAndType(params) {
    const queryParams = new URLSearchParams();
    if (params.providerId) queryParams.append('providerId', params.providerId);
    if (params.modelType) queryParams.append('modelType', params.modelType.toString());
    return _request.httpRequest.get(`${API_PREFIX}/AIModelTemplates/by-provider-and-type?${queryParams}`, {
        showErrorNotification: true
    });
}
async function getAIModelTemplateById(id) {
    return _request.httpRequest.get(`${API_PREFIX}/AIModelTemplates/${id}`, {
        showErrorNotification: true
    });
}
async function getEnabledAIProviders() {
    return _request.httpRequest.get(`${API_PREFIX}/AIProviders/enabled`, {
        showErrorNotification: true
    });
}
async function getModelsByProviderId(providerId) {
    return _request.httpRequest.get(`${API_PREFIX}/AIProviders/${providerId}/models`, {
        showErrorNotification: true
    });
}
async function getModelsByProviderCode(providerCode) {
    return _request.httpRequest.get(`${API_PREFIX}/AIProviders/by-code/${providerCode}/models`, {
        showErrorNotification: true
    });
}
async function getModelsByProviderType(providerType) {
    return _request.httpRequest.get(`${API_PREFIX}/AIProviders/type/${providerType}/models`, {
        showErrorNotification: true
    });
}
async function getAllEnabledModels() {
    return _request.httpRequest.get(`${API_PREFIX}/AIProviders/models/all`, {
        showErrorNotification: true
    });
}
const aiModelsApi = {
    // 核心CRUD操作
    getAIModels,
    getAIModelById,
    createAIModel,
    updateAIModel,
    deleteAIModel,
    // 增强功能
    batchDeleteAIModels,
    testAIModelConnection,
    toggleAIModelStatus,
    // 模板管理
    getAIModelTemplatesByType,
    getAIModelTemplatesByProviderAndType,
    getAIModelTemplateById,
    // 提供商管理
    getEnabledAIProviders,
    getModelsByProviderId,
    getModelsByProviderCode,
    getModelsByProviderType,
    getAllEnabledModels
};
var _default = aiModelsApi;
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/services/rwxai/apps.ts": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
__mako_require__.e(exports, {
    createApp: function() {
        return createApp;
    },
    deleteApp: function() {
        return deleteApp;
    },
    getAppById: function() {
        return getAppById;
    },
    getApps: function() {
        return getApps;
    },
    getMyApps: function() {
        return getMyApps;
    },
    regenerateApiKeys: function() {
        return regenerateApiKeys;
    },
    resetApiCalls: function() {
        return resetApiCalls;
    },
    updateApp: function() {
        return updateApp;
    },
    updateAppStatus: function() {
        return updateAppStatus;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
var _request = __mako_require__("src/utils/request.ts");
var _api = __mako_require__("src/utils/api.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
const API_PREFIX = (0, _api.getApiPrefix)();
async function getApps() {
    return _request.httpRequest.get(`${API_PREFIX}/Apps`, {
        showErrorNotification: true
    });
}
async function createApp(data) {
    return _request.httpRequest.post(`${API_PREFIX}/Apps`, data, {
        showSuccessMessage: true,
        showErrorNotification: true,
        successMessage: '创建应用成功'
    });
}
async function getMyApps() {
    return _request.httpRequest.get(`${API_PREFIX}/Apps/my`, {
        showErrorNotification: true
    });
}
async function getAppById(id) {
    return _request.httpRequest.get(`${API_PREFIX}/Apps/${id}`, {
        showErrorNotification: true
    });
}
async function updateApp(id, data) {
    return _request.httpRequest.put(`${API_PREFIX}/Apps/${id}`, data, {
        showSuccessMessage: true,
        showErrorNotification: true,
        successMessage: '更新应用成功'
    });
}
async function deleteApp(id) {
    return _request.httpRequest.delete(`${API_PREFIX}/Apps/${id}`, {
        showSuccessMessage: true,
        showErrorNotification: true,
        successMessage: '删除应用成功'
    });
}
async function regenerateApiKeys(id) {
    return _request.httpRequest.post(`${API_PREFIX}/Apps/${id}/regenerate-keys`, {}, {
        showSuccessMessage: true,
        showErrorNotification: true,
        successMessage: 'API密钥重新生成成功'
    });
}
async function updateAppStatus(id, data) {
    return _request.httpRequest.patch(`${API_PREFIX}/Apps/${id}/status`, data, {
        showSuccessMessage: true,
        showErrorNotification: true,
        successMessage: '应用状态更新成功'
    });
}
async function resetApiCalls(id) {
    return _request.httpRequest.post(`${API_PREFIX}/Apps/${id}/reset-api-calls`, {}, {
        showSuccessMessage: true,
        showErrorNotification: true,
        successMessage: 'API调用计数重置成功'
    });
}
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/services/rwxai/auth.ts": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
__mako_require__.e(exports, {
    changePassword: function() {
        return changePassword;
    },
    login: function() {
        return login;
    },
    refreshToken: function() {
        return refreshToken;
    },
    register: function() {
        return register;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
var _request = __mako_require__("src/utils/request.ts");
var _api = __mako_require__("src/utils/api.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
const API_PREFIX = (0, _api.getApiPrefix)();
async function login(data) {
    return _request.httpRequest.post(`${API_PREFIX}/Auth/login`, data, {
        skipAuth: true,
        showErrorNotification: true
    });
}
async function register(data) {
    return _request.httpRequest.post(`${API_PREFIX}/Auth/register`, data, {
        skipAuth: true,
        showErrorNotification: true
    });
}
async function refreshToken(data) {
    return _request.httpRequest.post(`${API_PREFIX}/Auth/refresh-token`, data, {
        skipAuth: true,
        showErrorNotification: false
    });
}
async function changePassword(data) {
    return _request.httpRequest.post(`${API_PREFIX}/Auth/change-password`, data, {
        showSuccessMessage: true,
        showErrorNotification: true,
        successMessage: '密码修改成功'
    });
}
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/services/rwxai/chat.ts": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
__mako_require__.e(exports, {
    createChatSession: function() {
        return createChatSession;
    },
    deleteChatSession: function() {
        return deleteChatSession;
    },
    getChatSessionById: function() {
        return getChatSessionById;
    },
    getChatSessions: function() {
        return getChatSessions;
    },
    sendMessage: function() {
        return sendMessage;
    },
    sendMessageStream: function() {
        return sendMessageStream;
    },
    updateChatSession: function() {
        return updateChatSession;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
var _request = __mako_require__("src/utils/request.ts");
var _api = __mako_require__("src/utils/api.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
const API_PREFIX = (0, _api.getApiPrefix)();
async function getChatSessions() {
    return _request.httpRequest.get(`${API_PREFIX}/Chat/sessions`, {
        showErrorNotification: true
    });
}
async function createChatSession(data) {
    return _request.httpRequest.post(`${API_PREFIX}/Chat/sessions`, data, {
        showSuccessMessage: true,
        showErrorNotification: true,
        successMessage: '创建聊天会话成功'
    });
}
async function getChatSessionById(id) {
    return _request.httpRequest.get(`${API_PREFIX}/Chat/sessions/${id}`, {
        showErrorNotification: true
    });
}
async function updateChatSession(id, data) {
    return _request.httpRequest.put(`${API_PREFIX}/Chat/sessions/${id}`, data, {
        showSuccessMessage: true,
        showErrorNotification: true,
        successMessage: '更新聊天会话成功'
    });
}
async function deleteChatSession(id) {
    return _request.httpRequest.delete(`${API_PREFIX}/Chat/sessions/${id}`, {
        showSuccessMessage: true,
        showErrorNotification: true,
        successMessage: '删除聊天会话成功'
    });
}
async function sendMessage(sessionId, data) {
    return _request.httpRequest.post(`${API_PREFIX}/Chat/sessions/${sessionId}/messages`, data, {
        showErrorNotification: true
    });
}
async function sendMessageStream(sessionId, data) {
    return _request.httpRequest.post(`${API_PREFIX}/Chat/sessions/${sessionId}/messages/stream`, data, {
        showErrorNotification: true
    });
}
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/services/rwxai/index.ts": function (module, exports, __mako_require__){
// 导出所有API服务
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "chatApi", {
    enumerable: true,
    get: function() {
        return _migratedchat.chatApi;
    }
});
var _export_star = __mako_require__("@swc/helpers/_/_export_star");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
_export_star._(__mako_require__("src/services/rwxai/aiModels.ts"), exports);
_export_star._(__mako_require__("src/services/rwxai/apps.ts"), exports);
_export_star._(__mako_require__("src/services/rwxai/auth.ts"), exports);
_export_star._(__mako_require__("src/services/rwxai/chat.ts"), exports);
_export_star._(__mako_require__("src/services/rwxai/knowledge.ts"), exports);
_export_star._(__mako_require__("src/services/rwxai/plugins.ts"), exports);
var _migratedchat = __mako_require__("src/services/rwxai/migrated-chat.ts");
_export_star._(__mako_require__("src/services/rwxai/typings.d.ts"), exports);
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}
 // 注意：aiModelsApi 现在从 './aiModels' 中导出，不再需要从 enhanced-aiModels 导出

},
"src/services/rwxai/knowledge.ts": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
__mako_require__.e(exports, {
    createKnowledgeBase: function() {
        return createKnowledgeBase;
    },
    deleteKnowledgeBase: function() {
        return deleteKnowledgeBase;
    },
    deleteKnowledgeFile: function() {
        return deleteKnowledgeFile;
    },
    getKnowledgeBaseById: function() {
        return getKnowledgeBaseById;
    },
    getKnowledgeBases: function() {
        return getKnowledgeBases;
    },
    getKnowledgeFiles: function() {
        return getKnowledgeFiles;
    },
    processKnowledgeFile: function() {
        return processKnowledgeFile;
    },
    updateKnowledgeBase: function() {
        return updateKnowledgeBase;
    },
    uploadKnowledgeFile: function() {
        return uploadKnowledgeFile;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
var _request = __mako_require__("src/utils/request.ts");
var _api = __mako_require__("src/utils/api.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
const API_PREFIX = (0, _api.getApiPrefix)();
async function getKnowledgeBases() {
    return _request.httpRequest.get(`${API_PREFIX}/Knowledge`, {
        showErrorNotification: true
    });
}
async function createKnowledgeBase(data) {
    return _request.httpRequest.post(`${API_PREFIX}/Knowledge`, data, {
        showSuccessMessage: true,
        showErrorNotification: true,
        successMessage: '创建知识库成功'
    });
}
async function getKnowledgeBaseById(id) {
    return _request.httpRequest.get(`${API_PREFIX}/Knowledge/${id}`, {
        showErrorNotification: true
    });
}
async function updateKnowledgeBase(id, data) {
    return _request.httpRequest.put(`${API_PREFIX}/Knowledge/${id}`, data, {
        showSuccessMessage: true,
        showErrorNotification: true,
        successMessage: '更新知识库成功'
    });
}
async function deleteKnowledgeBase(id) {
    return _request.httpRequest.delete(`${API_PREFIX}/Knowledge/${id}`, {
        showSuccessMessage: true,
        showErrorNotification: true,
        successMessage: '删除知识库成功'
    });
}
async function getKnowledgeFiles(knowledgeId) {
    return _request.httpRequest.get(`${API_PREFIX}/Knowledge/${knowledgeId}/files`, {
        showErrorNotification: true
    });
}
async function uploadKnowledgeFile(knowledgeId, file) {
    const formData = new FormData();
    formData.append('file', file);
    // 使用fetch直接上传文件，因为httpRequest可能不支持FormData
    try {
        const response = await fetch(`${API_PREFIX}/Knowledge/${knowledgeId}/files`, {
            method: 'POST',
            body: formData,
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('rwxai_token')}`
            }
        });
        if (response.ok) {
            const data = await response.json();
            return {
                success: true,
                data,
                message: '文件上传成功'
            };
        } else {
            const errorData = await response.json().catch(()=>({}));
            return {
                success: false,
                message: errorData.message || '文件上传失败'
            };
        }
    } catch (error) {
        return {
            success: false,
            message: error.message || '文件上传失败'
        };
    }
}
async function processKnowledgeFile(fileId) {
    return _request.httpRequest.post(`${API_PREFIX}/Knowledge/files/${fileId}/process`, {}, {
        showSuccessMessage: true,
        showErrorNotification: true,
        successMessage: '文件处理成功'
    });
}
async function deleteKnowledgeFile(fileId) {
    return _request.httpRequest.delete(`${API_PREFIX}/Knowledge/files/${fileId}`, {
        showSuccessMessage: true,
        showErrorNotification: true,
        successMessage: '删除文件成功'
    });
}
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/services/rwxai/migrated-chat.ts": function (module, exports, __mako_require__){
/**
 * 聊天会话管理API服务 - 已迁移到统一响应处理系统
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
__mako_require__.e(exports, {
    batchDeleteChatSessions: function() {
        return batchDeleteChatSessions;
    },
    chatApi: function() {
        return chatApi;
    },
    clearChatMessages: function() {
        return clearChatMessages;
    },
    copyChatSession: function() {
        return copyChatSession;
    },
    createChatSession: function() {
        return createChatSession;
    },
    deleteChatMessage: function() {
        return deleteChatMessage;
    },
    deleteChatSession: function() {
        return deleteChatSession;
    },
    exportChatSession: function() {
        return exportChatSession;
    },
    getChatMessages: function() {
        return getChatMessages;
    },
    getChatSessionById: function() {
        return getChatSessionById;
    },
    getChatSessionStats: function() {
        return getChatSessionStats;
    },
    getChatSessions: function() {
        return getChatSessions;
    },
    getUserChatSessions: function() {
        return getUserChatSessions;
    },
    searchChatMessages: function() {
        return searchChatMessages;
    },
    sendChatMessage: function() {
        return sendChatMessage;
    },
    updateChatSession: function() {
        return updateChatSession;
    },
    updateChatSessionStatus: function() {
        return updateChatSessionStatus;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
var _request = __mako_require__("src/utils/request.ts");
var _api = __mako_require__("src/utils/api.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
const API_PREFIX = (0, _api.getApiPrefix)();
async function getChatSessions() {
    return _request.httpRequest.get(`${API_PREFIX}/Chat/sessions`, {
        showErrorNotification: true
    });
}
async function createChatSession(data) {
    return _request.httpRequest.post(`${API_PREFIX}/Chat/sessions`, data, {
        showSuccessMessage: true,
        showErrorNotification: true,
        successMessage: '创建聊天会话成功'
    });
}
async function getChatSessionById(id) {
    return _request.httpRequest.get(`${API_PREFIX}/Chat/sessions/${id}`, {
        showErrorNotification: true
    });
}
async function updateChatSession(id, data) {
    return _request.httpRequest.put(`${API_PREFIX}/Chat/sessions/${id}`, data, {
        showSuccessMessage: true,
        showErrorNotification: true,
        successMessage: '更新聊天会话成功'
    });
}
async function deleteChatSession(id) {
    return _request.httpRequest.delete(`${API_PREFIX}/Chat/sessions/${id}`, {
        showSuccessMessage: true,
        showErrorNotification: true,
        successMessage: '删除聊天会话成功'
    });
}
async function getChatMessages(sessionId) {
    return _request.httpRequest.get(`${API_PREFIX}/Chat/sessions/${sessionId}/messages`, {
        showErrorNotification: true
    });
}
async function sendChatMessage(sessionId, data) {
    return _request.httpRequest.post(`${API_PREFIX}/Chat/sessions/${sessionId}/messages`, data, {
        showErrorNotification: true
    });
}
async function deleteChatMessage(sessionId, messageId) {
    return _request.httpRequest.delete(`${API_PREFIX}/Chat/sessions/${sessionId}/messages/${messageId}`, {
        showSuccessMessage: true,
        showErrorNotification: true,
        successMessage: '删除消息成功'
    });
}
async function clearChatMessages(sessionId) {
    return _request.httpRequest.delete(`${API_PREFIX}/Chat/sessions/${sessionId}/messages`, {
        showSuccessMessage: true,
        showErrorNotification: true,
        successMessage: '清空消息成功'
    });
}
async function getChatSessionStats(sessionId) {
    return _request.httpRequest.get(`${API_PREFIX}/Chat/sessions/${sessionId}/stats`, {
        showErrorNotification: true
    });
}
async function exportChatSession(sessionId, format) {
    return _request.httpRequest.get(`${API_PREFIX}/Chat/sessions/${sessionId}/export?format=${format}`, {
        showSuccessMessage: true,
        showErrorNotification: true,
        successMessage: '导出聊天会话成功'
    });
}
async function batchDeleteChatSessions(sessionIds) {
    return _request.httpRequest.post(`${API_PREFIX}/Chat/sessions/batch-delete`, {
        sessionIds
    }, {
        showSuccessMessage: true,
        showErrorNotification: true,
        successMessage: `成功删除 ${sessionIds.length} 个聊天会话`
    });
}
async function searchChatMessages(params) {
    const queryParams = new URLSearchParams();
    queryParams.append('query', params.query);
    if (params.sessionId) queryParams.append('sessionId', params.sessionId);
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.offset) queryParams.append('offset', params.offset.toString());
    return _request.httpRequest.get(`${API_PREFIX}/Chat/messages/search?${queryParams}`, {
        showErrorNotification: true
    });
}
async function getUserChatSessions(params) {
    const queryParams = new URLSearchParams();
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.pageSize) queryParams.append('pageSize', params.pageSize.toString());
    if (params.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);
    return _request.httpRequest.get(`${API_PREFIX}/Chat/sessions/user?${queryParams}`, {
        showErrorNotification: true
    });
}
async function copyChatSession(sessionId, newName) {
    return _request.httpRequest.post(`${API_PREFIX}/Chat/sessions/${sessionId}/copy`, {
        newName
    }, {
        showSuccessMessage: true,
        showErrorNotification: true,
        successMessage: '复制聊天会话成功'
    });
}
async function updateChatSessionStatus(sessionId, isActive) {
    return _request.httpRequest.patch(`${API_PREFIX}/Chat/sessions/${sessionId}/status`, {
        isActive
    }, {
        showSuccessMessage: true,
        showErrorNotification: true,
        successMessage: isActive ? '激活聊天会话成功' : '停用聊天会话成功'
    });
}
const chatApi = {
    getChatSessions,
    createChatSession,
    getChatSessionById,
    updateChatSession,
    deleteChatSession,
    getChatMessages,
    sendChatMessage,
    deleteChatMessage,
    clearChatMessages,
    getChatSessionStats,
    exportChatSession,
    batchDeleteChatSessions,
    searchChatMessages,
    getUserChatSessions,
    copyChatSession,
    updateChatSessionStatus
};
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/services/rwxai/plugins.ts": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
__mako_require__.e(exports, {
    createPlugin: function() {
        return createPlugin;
    },
    deletePlugin: function() {
        return deletePlugin;
    },
    executePluginFunction: function() {
        return executePluginFunction;
    },
    getPluginById: function() {
        return getPluginById;
    },
    getPluginFunctions: function() {
        return getPluginFunctions;
    },
    getPlugins: function() {
        return getPlugins;
    },
    togglePluginStatus: function() {
        return togglePluginStatus;
    },
    updatePlugin: function() {
        return updatePlugin;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
var _request = __mako_require__("src/utils/request.ts");
var _api = __mako_require__("src/utils/api.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
const API_PREFIX = (0, _api.getApiPrefix)();
async function getPlugins() {
    return _request.httpRequest.get(`${API_PREFIX}/Plugins`, {
        showErrorNotification: true
    });
}
async function createPlugin(data) {
    return _request.httpRequest.post(`${API_PREFIX}/Plugins`, data, {
        showSuccessMessage: true,
        showErrorNotification: true,
        successMessage: '创建插件成功'
    });
}
async function getPluginById(id) {
    return _request.httpRequest.get(`${API_PREFIX}/Plugins/${id}`, {
        showErrorNotification: true
    });
}
async function updatePlugin(id, data) {
    return _request.httpRequest.put(`${API_PREFIX}/Plugins/${id}`, data, {
        showSuccessMessage: true,
        showErrorNotification: true,
        successMessage: '更新插件成功'
    });
}
async function deletePlugin(id) {
    return _request.httpRequest.delete(`${API_PREFIX}/Plugins/${id}`, {
        showSuccessMessage: true,
        showErrorNotification: true,
        successMessage: '删除插件成功'
    });
}
async function togglePluginStatus(id, isEnabled) {
    return _request.httpRequest.patch(`${API_PREFIX}/Plugins/${id}/toggle`, {
        IsEnabled: isEnabled
    }, {
        showSuccessMessage: true,
        showErrorNotification: true,
        successMessage: isEnabled ? '启用插件成功' : '禁用插件成功'
    });
}
async function getPluginFunctions(pluginId) {
    return _request.httpRequest.get(`${API_PREFIX}/Plugins/${pluginId}/functions`, {
        showErrorNotification: true
    });
}
async function executePluginFunction(pluginId, functionName, parameters) {
    return _request.httpRequest.post(`${API_PREFIX}/Plugins/${pluginId}/functions/${functionName}/execute`, parameters, {
        showSuccessMessage: true,
        showErrorNotification: true,
        successMessage: '插件功能执行成功'
    });
}
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/services/rwxai/typings.d.ts": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/types/response.ts": function (module, exports, __mako_require__){
/**
 * API响应类型定义
 */ // HTTP状态码枚举
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
__mako_require__.e(exports, {
    ApiError: function() {
        return ApiError;
    },
    ErrorType: function() {
        return ErrorType;
    },
    HttpStatusCode: function() {
        return HttpStatusCode;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var HttpStatusCode;
(function(HttpStatusCode) {
    HttpStatusCode[HttpStatusCode["OK"] = 200] = "OK";
    HttpStatusCode[HttpStatusCode["CREATED"] = 201] = "CREATED";
    HttpStatusCode[HttpStatusCode["NO_CONTENT"] = 204] = "NO_CONTENT";
    HttpStatusCode[HttpStatusCode["BAD_REQUEST"] = 400] = "BAD_REQUEST";
    HttpStatusCode[HttpStatusCode["UNAUTHORIZED"] = 401] = "UNAUTHORIZED";
    HttpStatusCode[HttpStatusCode["FORBIDDEN"] = 403] = "FORBIDDEN";
    HttpStatusCode[HttpStatusCode["NOT_FOUND"] = 404] = "NOT_FOUND";
    HttpStatusCode[HttpStatusCode["METHOD_NOT_ALLOWED"] = 405] = "METHOD_NOT_ALLOWED";
    HttpStatusCode[HttpStatusCode["CONFLICT"] = 409] = "CONFLICT";
    HttpStatusCode[HttpStatusCode["UNPROCESSABLE_ENTITY"] = 422] = "UNPROCESSABLE_ENTITY";
    HttpStatusCode[HttpStatusCode["TOO_MANY_REQUESTS"] = 429] = "TOO_MANY_REQUESTS";
    HttpStatusCode[HttpStatusCode["INTERNAL_SERVER_ERROR"] = 500] = "INTERNAL_SERVER_ERROR";
    HttpStatusCode[HttpStatusCode["BAD_GATEWAY"] = 502] = "BAD_GATEWAY";
    HttpStatusCode[HttpStatusCode["SERVICE_UNAVAILABLE"] = 503] = "SERVICE_UNAVAILABLE";
    HttpStatusCode[HttpStatusCode["GATEWAY_TIMEOUT"] = 504] = "GATEWAY_TIMEOUT";
})(HttpStatusCode || (HttpStatusCode = {}));
var ErrorType;
(function(ErrorType) {
    ErrorType["NETWORK_ERROR"] = "NETWORK_ERROR";
    ErrorType["TIMEOUT_ERROR"] = "TIMEOUT_ERROR";
    ErrorType["VALIDATION_ERROR"] = "VALIDATION_ERROR";
    ErrorType["AUTHENTICATION_ERROR"] = "AUTHENTICATION_ERROR";
    ErrorType["AUTHORIZATION_ERROR"] = "AUTHORIZATION_ERROR";
    ErrorType["NOT_FOUND_ERROR"] = "NOT_FOUND_ERROR";
    ErrorType["SERVER_ERROR"] = "SERVER_ERROR";
    ErrorType["BUSINESS_ERROR"] = "BUSINESS_ERROR";
    ErrorType["UNKNOWN_ERROR"] = "UNKNOWN_ERROR";
})(ErrorType || (ErrorType = {}));
class ApiError extends Error {
    status;
    errorType;
    errorCode;
    details;
    timestamp;
    constructor(message, status, errorType, errorCode, details){
        super(message);
        this.name = 'ApiError';
        this.status = status;
        this.errorType = errorType;
        this.errorCode = errorCode;
        this.details = details;
        this.timestamp = new Date().toISOString();
    }
}
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/utils/api.ts": function (module, exports, __mako_require__){
/**
 * API 工具函数
 */ /**
 * 获取 API 基础路径
 * 开发环境使用代理，生产环境使用完整 URL
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "getApiPrefix", {
    enumerable: true,
    get: function() {
        return getApiPrefix;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
const process = __mako_require__("node_modules/node-libs-browser-okam/polyfill/process.js");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
const getApiPrefix = ()=>{
    return '/api';
};
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/utils/pageDataHandler.ts": function (module, exports, __mako_require__){
/**
 * 统一的分页数据处理工具
 * 用于处理后台返回的分页格式数据
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
__mako_require__.e(exports, {
    buildPagedQuery: function() {
        return buildPagedQuery;
    },
    createLegacyProTableRequest: function() {
        return createLegacyProTableRequest;
    },
    createProTableRequest: function() {
        return createProTableRequest;
    },
    createSimpleProTableRequest: function() {
        return createSimpleProTableRequest;
    },
    defaultPaginationConfig: function() {
        return defaultPaginationConfig;
    },
    extractPaginationInfo: function() {
        return extractPaginationInfo;
    },
    handleLegacyPagedResponse: function() {
        return handleLegacyPagedResponse;
    },
    handlePagedResponse: function() {
        return handlePagedResponse;
    },
    isPagedResponse: function() {
        return isPagedResponse;
    },
    toQueryString: function() {
        return toQueryString;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
function handlePagedResponse(response) {
    if (!response.success || !response.data) return {
        data: [],
        success: response.success,
        total: 0
    };
    const data = response.data;
    // 处理分页格式
    if (isPagedResponse(data)) return {
        data: data.Items,
        success: true,
        total: data.TotalCount
    };
    // 如果不是标准分页格式，返回空数据
    return {
        data: [],
        success: false,
        total: 0
    };
}
function handleLegacyPagedResponse(response) {
    if (!response.success || !response.data) return {
        data: [],
        success: response.success,
        total: 0
    };
    const data = response.data;
    // 检查是否是新的分页格式
    if (isPagedResponse(data)) return {
        data: data.Items,
        success: true,
        total: data.TotalCount
    };
    // 兼容旧格式（直接返回数组）
    if (Array.isArray(data)) return {
        data: data,
        success: true,
        total: data.length
    };
    // 其他情况返回空数据
    return {
        data: [],
        success: false,
        total: 0
    };
}
function buildPagedQuery(params, fieldMapping = {}, extra) {
    const query = {};
    // 添加分页参数
    query.PageNumber = params.current ?? 1;
    query.PageSize = params.pageSize ?? 20;
    // 添加关键词搜索
    if (params.keyWord) query.SearchKeyword = params.keyWord;
    // 字段映射：将列的 dataIndex 对应值映射到后端字段
    if (fieldMapping) Object.keys(fieldMapping).forEach((from)=>{
        const to = fieldMapping[from];
        const value = params[from];
        if (value !== undefined && value !== null && value !== '') query[to] = value;
    });
    // 添加额外参数
    if (extra) Object.assign(query, extra);
    return query;
}
function toQueryString(obj) {
    const qs = new URLSearchParams();
    Object.entries(obj).forEach(([k, v])=>{
        if (v === undefined || v === null || v === '') return;
        if (v instanceof Date) qs.append(k, v.toISOString());
        else qs.append(k, String(v));
    });
    return qs.toString();
}
function createProTableRequest(apiFunction, fieldMapping = {}, extra) {
    return async (params)=>{
        const query = buildPagedQuery(params, fieldMapping, extra);
        const response = await apiFunction(query);
        return handlePagedResponse(response);
    };
}
function createLegacyProTableRequest(apiFunction, fieldMapping = {}, extra) {
    return async (params)=>{
        const query = buildPagedQuery(params, fieldMapping, extra);
        const response = await apiFunction(query);
        return handleLegacyPagedResponse(response);
    };
}
function createSimpleProTableRequest(apiFunction) {
    return async ()=>{
        var _response_data;
        const response = await apiFunction();
        return {
            data: response.success ? response.data || [] : [],
            success: response.success,
            total: ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.length) || 0
        };
    };
}
const defaultPaginationConfig = {
    showSizeChanger: true,
    pageSize: 20,
    pageSizeOptions: [
        '10',
        '20',
        '50',
        '100'
    ],
    showQuickJumper: true,
    showTotal: (total, range)=>`第 ${range[0]}-${range[1]} 条/总共 ${total} 条`
};
function isPagedResponse(data) {
    return data && typeof data === 'object' && Array.isArray(data.Items) && typeof data.TotalCount === 'number' && typeof data.PageNumber === 'number' && typeof data.PageSize === 'number';
}
function extractPaginationInfo(data) {
    return {
        current: data.PageNumber,
        pageSize: data.PageSize,
        total: data.TotalCount,
        totalPages: data.TotalPages,
        hasPrevious: data.HasPreviousPage,
        hasNext: data.HasNextPage
    };
}
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/utils/request.ts": function (module, exports, __mako_require__){
/**
 * 增强的请求工具
 * 集成统一的响应处理和错误处理
 */ // import { request as umiRequest } from '@umijs/max';
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
__mako_require__.e(exports, {
    enhancedRequest: function() {
        return enhancedRequest;
    },
    errorHandler: function() {
        return errorHandler;
    },
    httpRequest: function() {
        return httpRequest;
    },
    requestInterceptor: function() {
        return requestInterceptor;
    },
    responseInterceptor: function() {
        return responseInterceptor;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
var _auth = __mako_require__("src/utils/auth.ts");
var _responseHandler = __mako_require__("src/utils/responseHandler.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
async function enhancedRequest(url, options = {}) {
    const { method = 'GET', data, headers = {}, showSuccessMessage = false, showErrorNotification = true, successMessage, skipAuth = false, timeout = 30000 } = options;
    try {
        // 添加JWT令牌
        if (!skipAuth && !url.includes('/Auth/login') && !url.includes('/Auth/register')) {
            const token = (0, _auth.getToken)();
            if (token) headers.Authorization = `Bearer ${token}`;
        }
        // 发送请求
        const response = await fetch(url, {
            method,
            headers: {
                'Content-Type': 'application/json',
                ...headers
            },
            body: data ? JSON.stringify(data) : undefined,
            signal: AbortSignal.timeout(timeout)
        });
        // 处理401错误 - 尝试刷新令牌
        if (response.status === 401 && !skipAuth) {
            const refreshResult = await tryRefreshToken();
            if (refreshResult) {
                // 重新发送请求
                headers.Authorization = `Bearer ${refreshResult.token}`;
                const retryResponse = await fetch(url, {
                    method,
                    headers: {
                        'Content-Type': 'application/json',
                        ...headers
                    },
                    body: data ? JSON.stringify(data) : undefined,
                    signal: AbortSignal.timeout(timeout)
                });
                return (0, _responseHandler.handleApiResponse)(retryResponse, {
                    showSuccessMessage,
                    showErrorNotification,
                    successMessage
                });
            }
        }
        // 处理响应
        return (0, _responseHandler.handleApiResponse)(response, {
            showSuccessMessage,
            showErrorNotification,
            successMessage
        });
    } catch (error) {
        // 处理网络错误或超时
        const apiError = (0, _responseHandler.handleApiError)(error, showErrorNotification);
        return {
            success: false,
            error: apiError,
            message: apiError.message
        };
    }
}
// 尝试刷新令牌
async function tryRefreshToken() {
    const refreshTokenValue = (0, _auth.getRefreshToken)();
    if (!refreshTokenValue || (0, _auth.isTokenExpired)(refreshTokenValue)) {
        (0, _auth.logout)();
        return null;
    }
    try {
        // 直接调用刷新令牌API，避免循环依赖
        const response = await fetch('/api/Auth/refresh-token', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                RefreshToken: refreshTokenValue
            })
        });
        if (response.ok) {
            const refreshResult = await response.json();
            if (refreshResult.token) {
                (0, _auth.setToken)(refreshResult.token);
                (0, _auth.setRefreshToken)(refreshResult.refreshToken);
                return refreshResult;
            }
        }
    } catch (error) {
        console.error('刷新令牌失败:', error);
        (0, _auth.logout)();
    }
    return null;
}
const httpRequest = {
    get: (url, options = {})=>enhancedRequest(url, {
            ...options,
            method: 'GET'
        }),
    post: (url, data, options = {})=>enhancedRequest(url, {
            ...options,
            method: 'POST',
            data
        }),
    put: (url, data, options = {})=>enhancedRequest(url, {
            ...options,
            method: 'PUT',
            data
        }),
    delete: (url, options = {})=>enhancedRequest(url, {
            ...options,
            method: 'DELETE'
        }),
    patch: (url, data, options = {})=>enhancedRequest(url, {
            ...options,
            method: 'PATCH',
            data
        })
};
const requestInterceptor = (url, options)=>{
    const token = (0, _auth.getToken)();
    if (token && !url.includes('/Auth/login') && !url.includes('/Auth/register')) {
        const headers = options.headers || {};
        headers.Authorization = `Bearer ${token}`;
        options.headers = headers;
    }
    return {
        url,
        options
    };
};
const responseInterceptor = async (response)=>{
    if (response.status === 401) {
        const refreshResult = await tryRefreshToken();
        if (!refreshResult) (0, _auth.logout)();
    }
    return response;
};
const errorHandler = (error)=>{
    (0, _responseHandler.handleApiError)(error, true);
    throw error;
};
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/utils/responseHandler.ts": function (module, exports, __mako_require__){
/**
 * 统一的API响应处理器
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
__mako_require__.e(exports, {
    handleApiError: function() {
        return handleApiError;
    },
    handleApiResponse: function() {
        return handleApiResponse;
    },
    showErrorNotification: function() {
        return showErrorNotification;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _response = __mako_require__("src/types/response.ts");
var _auth = __mako_require__("src/utils/auth.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
// 错误消息映射
const ERROR_MESSAGES = {
    [_response.HttpStatusCode.BAD_REQUEST]: '请求参数错误',
    [_response.HttpStatusCode.UNAUTHORIZED]: '未授权，请重新登录',
    [_response.HttpStatusCode.FORBIDDEN]: '权限不足，无法访问',
    [_response.HttpStatusCode.NOT_FOUND]: '请求的资源不存在',
    [_response.HttpStatusCode.METHOD_NOT_ALLOWED]: '请求方法不被允许',
    [_response.HttpStatusCode.CONFLICT]: '请求冲突，资源已存在',
    [_response.HttpStatusCode.UNPROCESSABLE_ENTITY]: '请求数据验证失败',
    [_response.HttpStatusCode.TOO_MANY_REQUESTS]: '请求过于频繁，请稍后重试',
    [_response.HttpStatusCode.INTERNAL_SERVER_ERROR]: '服务器内部错误',
    [_response.HttpStatusCode.BAD_GATEWAY]: '网关错误',
    [_response.HttpStatusCode.SERVICE_UNAVAILABLE]: '服务暂时不可用',
    [_response.HttpStatusCode.GATEWAY_TIMEOUT]: '网关超时'
};
// 根据状态码获取错误类型
function getErrorType(status) {
    switch(status){
        case _response.HttpStatusCode.BAD_REQUEST:
        case _response.HttpStatusCode.UNPROCESSABLE_ENTITY:
            return _response.ErrorType.VALIDATION_ERROR;
        case _response.HttpStatusCode.UNAUTHORIZED:
            return _response.ErrorType.AUTHENTICATION_ERROR;
        case _response.HttpStatusCode.FORBIDDEN:
            return _response.ErrorType.AUTHORIZATION_ERROR;
        case _response.HttpStatusCode.NOT_FOUND:
            return _response.ErrorType.NOT_FOUND_ERROR;
        case _response.HttpStatusCode.INTERNAL_SERVER_ERROR:
        case _response.HttpStatusCode.BAD_GATEWAY:
        case _response.HttpStatusCode.SERVICE_UNAVAILABLE:
        case _response.HttpStatusCode.GATEWAY_TIMEOUT:
            return _response.ErrorType.SERVER_ERROR;
        default:
            return _response.ErrorType.UNKNOWN_ERROR;
    }
}
// 处理验证错误
function handleValidationError(errorResponse) {
    if (errorResponse.errors) {
        const errorMessages = [];
        Object.entries(errorResponse.errors).forEach(([field, messages])=>{
            messages.forEach((msg)=>{
                errorMessages.push(`${field}: ${msg}`);
            });
        });
        return errorMessages.join('; ');
    }
    return errorResponse.detail || errorResponse.title || '数据验证失败';
}
function showErrorNotification(error, showNotification = true) {
    const { errorType, message: errorMessage } = error;
    // 根据错误类型决定显示方式
    switch(errorType){
        case _response.ErrorType.AUTHENTICATION_ERROR:
            _antd.message.error('登录已过期，请重新登录');
            // 自动跳转到登录页
            setTimeout(()=>(0, _auth.logout)(), 1000);
            break;
        case _response.ErrorType.AUTHORIZATION_ERROR:
            _antd.message.error('权限不足，无法执行此操作');
            break;
        case _response.ErrorType.VALIDATION_ERROR:
            if (showNotification) _antd.notification.error({
                message: '数据验证失败',
                description: errorMessage,
                duration: 5
            });
            else _antd.message.error(errorMessage);
            break;
        case _response.ErrorType.NOT_FOUND_ERROR:
            _antd.message.error('请求的资源不存在');
            break;
        case _response.ErrorType.SERVER_ERROR:
            _antd.notification.error({
                message: '服务器错误',
                description: errorMessage,
                duration: 8
            });
            break;
        case _response.ErrorType.NETWORK_ERROR:
            _antd.notification.error({
                message: '网络错误',
                description: '请检查网络连接后重试',
                duration: 5
            });
            break;
        default:
            _antd.message.error(errorMessage || '未知错误');
            break;
    }
}
// 处理成功响应
function handleSuccessResponse(_response, data) {
    return {
        success: true,
        data,
        message: '操作成功'
    };
}
// 处理错误响应
async function handleErrorResponse(response) {
    const status = response.status;
    const errorType = getErrorType(status);
    let errorMessage = ERROR_MESSAGES[status] || '未知错误';
    let errorDetails = null;
    try {
        const responseText = await response.text();
        if (responseText) {
            const errorData = JSON.parse(responseText);
            // 根据错误类型处理不同的错误格式
            if (errorType === _response.ErrorType.VALIDATION_ERROR) errorMessage = handleValidationError(errorData);
            else errorMessage = errorData.detail || errorData.title || errorMessage;
            errorDetails = errorData;
        }
    } catch (parseError) {
        console.warn('Failed to parse error response:', parseError);
    }
    const apiError = new _response.ApiError(errorMessage, status, errorType, errorDetails === null || errorDetails === void 0 ? void 0 : errorDetails.type, errorDetails);
    return {
        success: false,
        error: apiError,
        message: errorMessage
    };
}
async function handleApiResponse(response, options = {}) {
    const { showSuccessMessage = false, showErrorNotification: shouldShowErrorNotification = true, successMessage } = options;
    try {
        // 处理成功响应
        if (response.ok) {
            let data;
            // 处理不同的响应类型
            const contentType = response.headers.get('content-type');
            if (contentType === null || contentType === void 0 ? void 0 : contentType.includes('application/json')) {
                const jsonData = await response.json();
                // 如果是标准的API响应格式
                if (typeof jsonData === 'object' && 'success' in jsonData) {
                    const apiResponse = jsonData;
                    data = apiResponse.data;
                    if (showSuccessMessage) _antd.message.success(successMessage || apiResponse.message || '操作成功');
                } else {
                    data = jsonData;
                    if (showSuccessMessage) _antd.message.success(successMessage || '操作成功');
                }
            } else if (response.status === _response.HttpStatusCode.NO_CONTENT) {
                data = null;
                if (showSuccessMessage) _antd.message.success(successMessage || '操作成功');
            } else {
                data = await response.text();
                if (showSuccessMessage) _antd.message.success(successMessage || '操作成功');
            }
            return handleSuccessResponse(response, data);
        }
        // 处理错误响应
        const errorResult = await handleErrorResponse(response);
        if (errorResult.error && shouldShowErrorNotification) showErrorNotification(errorResult.error, true);
        return errorResult;
    } catch (error) {
        // 处理网络错误或其他异常
        const networkError = new _response.ApiError('网络连接失败，请检查网络后重试', 0, _response.ErrorType.NETWORK_ERROR);
        if (shouldShowErrorNotification) showErrorNotification(networkError, false);
        return {
            success: false,
            error: networkError,
            message: networkError.message
        };
    }
}
function handleApiError(error, showNotification = true) {
    if (error instanceof _response.ApiError) {
        if (showNotification) showErrorNotification(error, true);
        return error;
    }
    // 处理其他类型的错误
    const apiError = new _response.ApiError(error.message || '未知错误', error.status || 0, _response.ErrorType.UNKNOWN_ERROR);
    if (showNotification) showErrorNotification(apiError, false);
    return apiError;
}
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=common-async.js.map