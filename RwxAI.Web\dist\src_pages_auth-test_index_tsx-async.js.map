{"version": 3, "sources": ["src/pages/auth-test/index.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { PageContainer } from '@ant-design/pro-components';\nimport { Card, Button, Space, Typography, Descriptions, message } from 'antd';\nimport { getToken, getUserInfo, isLoggedIn, logout } from '@/utils/auth';\nimport { getAIModels } from '@/services/rwxai';\n\nconst { Title, Text, Paragraph } = Typography;\n\nconst AuthTestPage: React.FC = () => {\n  const [tokenInfo, setTokenInfo] = useState<any>(null);\n  const [userInfo, setUserInfo] = useState<any>(null);\n  const [apiTestResult, setApiTestResult] = useState<string>('');\n  const [loading, setLoading] = useState(false);\n\n  useEffect(() => {\n    loadAuthInfo();\n  }, []);\n\n  const loadAuthInfo = () => {\n    const token = getToken();\n    const user = getUserInfo();\n    \n    setTokenInfo({\n      token: token ? `${token.substring(0, 20)}...` : null,\n      isLoggedIn: isLoggedIn(),\n      fullToken: token,\n    });\n    \n    setUserInfo(user);\n  };\n\n  const testApiCall = async () => {\n    setLoading(true);\n    try {\n      const response = await getAIModels();\n      if (response.success) {\n        setApiTestResult(`✅ 成功获取 ${response.data?.length || 0} 个AI模型`);\n      } else {\n        setApiTestResult(`❌ API调用失败: ${response.message}`);\n      }\n    } catch (error: any) {\n      setApiTestResult(`❌ 异常: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleLogout = () => {\n    logout();\n    loadAuthInfo();\n    message.info('已退出登录');\n  };\n\n  return (\n    <PageContainer title=\"JWT认证测试页面\">\n      <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n        <Card title=\"认证状态\">\n          <Descriptions column={1} bordered>\n            <Descriptions.Item label=\"登录状态\">\n              <Text type={tokenInfo?.isLoggedIn ? 'success' : 'danger'}>\n                {tokenInfo?.isLoggedIn ? '已登录' : '未登录'}\n              </Text>\n            </Descriptions.Item>\n            <Descriptions.Item label=\"JWT令牌\">\n              <Text code>{tokenInfo?.token || '无'}</Text>\n            </Descriptions.Item>\n            <Descriptions.Item label=\"用户信息\">\n              {userInfo ? (\n                <div>\n                  <div>用户名: {userInfo.username || userInfo.name}</div>\n                  <div>邮箱: {userInfo.email}</div>\n                  <div>姓名: {userInfo.firstName} {userInfo.lastName}</div>\n                </div>\n              ) : (\n                <Text type=\"secondary\">无用户信息</Text>\n              )}\n            </Descriptions.Item>\n          </Descriptions>\n        </Card>\n\n        <Card title=\"API测试\">\n          <Space direction=\"vertical\" style={{ width: '100%' }}>\n            <Paragraph>\n              点击下面的按钮测试带JWT令牌的API调用：\n            </Paragraph>\n            <Space>\n              <Button \n                type=\"primary\" \n                onClick={testApiCall} \n                loading={loading}\n                disabled={!tokenInfo?.isLoggedIn}\n              >\n                测试获取AI模型列表\n              </Button>\n              <Button onClick={loadAuthInfo}>\n                刷新认证信息\n              </Button>\n              <Button danger onClick={handleLogout} disabled={!tokenInfo?.isLoggedIn}>\n                退出登录\n              </Button>\n            </Space>\n            {apiTestResult && (\n              <Card size=\"small\" style={{ marginTop: 16 }}>\n                <Text>{apiTestResult}</Text>\n              </Card>\n            )}\n          </Space>\n        </Card>\n\n        <Card title=\"JWT令牌详情\" size=\"small\">\n          <Paragraph>\n            <Text code style={{ wordBreak: 'break-all', fontSize: '12px' }}>\n              {tokenInfo?.fullToken || '无令牌'}\n            </Text>\n          </Paragraph>\n        </Card>\n\n        <Card title=\"使用说明\" size=\"small\">\n          <Paragraph>\n            <Title level={5}>测试步骤：</Title>\n            <ol>\n              <li>首先访问 <Text code>/user/login</Text> 页面进行登录</li>\n              <li>登录成功后会自动保存JWT令牌到localStorage</li>\n              <li>返回此页面查看认证状态</li>\n              <li>点击\"测试获取AI模型列表\"按钮验证API调用</li>\n              <li>所有后续的API请求都会自动携带JWT令牌</li>\n            </ol>\n          </Paragraph>\n        </Card>\n      </Space>\n    </PageContainer>\n  );\n};\n\nexport default AuthTestPage;\n"], "names": [], "mappings": ";;;;;;;4BAsIA;;;eAAA;;;;;;wEAtI2C;sCACb;6BACyC;6BACb;8BAC9B;;;;;;;;;;AAE5B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,gBAAU;AAE7C,MAAM,eAAyB;;IAC7B,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAM;IAChD,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAM;IAC9C,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAS;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IAEvC,IAAA,gBAAS,EAAC;QACR;IACF,GAAG,EAAE;IAEL,MAAM,eAAe;QACnB,MAAM,QAAQ,IAAA,cAAQ;QACtB,MAAM,OAAO,IAAA,iBAAW;QAExB,aAAa;YACX,OAAO,QAAQ,CAAC,EAAE,MAAM,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;YAChD,YAAY,IAAA,gBAAU;YACtB,WAAW;QACb;QAEA,YAAY;IACd;IAEA,MAAM,cAAc;QAClB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,IAAA,kBAAW;YAClC,IAAI,SAAS,OAAO,EAAE;oBACO;gBAA3B,iBAAiB,CAAC,OAAO,EAAE,EAAA,iBAAA,SAAS,IAAI,cAAb,qCAAA,eAAe,MAAM,KAAI,EAAE,MAAM,CAAC;YAC/D,OACE,iBAAiB,CAAC,WAAW,EAAE,SAAS,OAAO,CAAC,CAAC;QAErD,EAAE,OAAO,OAAY;YACnB,iBAAiB,CAAC,MAAM,EAAE,MAAM,OAAO,CAAC,CAAC;QAC3C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe;QACnB,IAAA,YAAM;QACN;QACA,aAAO,CAAC,IAAI,CAAC;IACf;IAEA,qBACE,2BAAC,4BAAa;QAAC,OAAM;kBACnB,cAAA,2BAAC,WAAK;YAAC,WAAU;YAAW,MAAK;YAAQ,OAAO;gBAAE,OAAO;YAAO;;8BAC9D,2BAAC,UAAI;oBAAC,OAAM;8BACV,cAAA,2BAAC,kBAAY;wBAAC,QAAQ;wBAAG,QAAQ;;0CAC/B,2BAAC,kBAAY,CAAC,IAAI;gCAAC,OAAM;0CACvB,cAAA,2BAAC;oCAAK,MAAM,CAAA,sBAAA,gCAAA,UAAW,UAAU,IAAG,YAAY;8CAC7C,CAAA,sBAAA,gCAAA,UAAW,UAAU,IAAG,QAAQ;;;;;;;;;;;0CAGrC,2BAAC,kBAAY,CAAC,IAAI;gCAAC,OAAM;0CACvB,cAAA,2BAAC;oCAAK,IAAI;8CAAE,CAAA,sBAAA,gCAAA,UAAW,KAAK,KAAI;;;;;;;;;;;0CAElC,2BAAC,kBAAY,CAAC,IAAI;gCAAC,OAAM;0CACtB,yBACC,2BAAC;;sDACC,2BAAC;;gDAAI;gDAAM,SAAS,QAAQ,IAAI,SAAS,IAAI;;;;;;;sDAC7C,2BAAC;;gDAAI;gDAAK,SAAS,KAAK;;;;;;;sDACxB,2BAAC;;gDAAI;gDAAK,SAAS,SAAS;gDAAC;gDAAE,SAAS,QAAQ;;;;;;;;;;;;yDAGlD,2BAAC;oCAAK,MAAK;8CAAY;;;;;;;;;;;;;;;;;;;;;;8BAM/B,2BAAC,UAAI;oBAAC,OAAM;8BACV,cAAA,2BAAC,WAAK;wBAAC,WAAU;wBAAW,OAAO;4BAAE,OAAO;wBAAO;;0CACjD,2BAAC;0CAAU;;;;;;0CAGX,2BAAC,WAAK;;kDACJ,2BAAC,YAAM;wCACL,MAAK;wCACL,SAAS;wCACT,SAAS;wCACT,UAAU,EAAC,sBAAA,gCAAA,UAAW,UAAU;kDACjC;;;;;;kDAGD,2BAAC,YAAM;wCAAC,SAAS;kDAAc;;;;;;kDAG/B,2BAAC,YAAM;wCAAC,MAAM;wCAAC,SAAS;wCAAc,UAAU,EAAC,sBAAA,gCAAA,UAAW,UAAU;kDAAE;;;;;;;;;;;;4BAIzE,+BACC,2BAAC,UAAI;gCAAC,MAAK;gCAAQ,OAAO;oCAAE,WAAW;gCAAG;0CACxC,cAAA,2BAAC;8CAAM;;;;;;;;;;;;;;;;;;;;;;8BAMf,2BAAC,UAAI;oBAAC,OAAM;oBAAU,MAAK;8BACzB,cAAA,2BAAC;kCACC,cAAA,2BAAC;4BAAK,IAAI;4BAAC,OAAO;gCAAE,WAAW;gCAAa,UAAU;4BAAO;sCAC1D,CAAA,sBAAA,gCAAA,UAAW,SAAS,KAAI;;;;;;;;;;;;;;;;8BAK/B,2BAAC,UAAI;oBAAC,OAAM;oBAAO,MAAK;8BACtB,cAAA,2BAAC;;0CACC,2BAAC;gCAAM,OAAO;0CAAG;;;;;;0CACjB,2BAAC;;kDACC,2BAAC;;4CAAG;0DAAK,2BAAC;gDAAK,IAAI;0DAAC;;;;;;4CAAkB;;;;;;;kDACtC,2BAAC;kDAAG;;;;;;kDACJ,2BAAC;kDAAG;;;;;;kDACJ,2BAAC;kDAAG;;;;;;kDACJ,2BAAC;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB;GA5HM;KAAA;IA8HN,WAAe"}