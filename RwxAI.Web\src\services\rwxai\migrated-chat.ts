/**
 * 聊天会话管理API服务 - 已迁移到统一响应处理系统
 */

import { httpRequest } from '@/utils/request';
import { getApiPrefix } from '@/utils/api';
import { ResponseHandleResult } from '@/types/response';

const API_PREFIX: string = getApiPrefix();

/**
 * 聊天会话管理相关API
 */

// 获取所有聊天会话
export async function getChatSessions(): Promise<ResponseHandleResult<RwxAI.ChatSession[]>> {
  return httpRequest.get<RwxAI.ChatSession[]>(`${API_PREFIX}/Chat/sessions`, {
    showErrorNotification: true,
  });
}

// 创建新的聊天会话
export async function createChatSession(data: RwxAI.ChatSession): Promise<ResponseHandleResult<RwxAI.ChatSession>> {
  return httpRequest.post<RwxAI.ChatSession>(`${API_PREFIX}/Chat/sessions`, data, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: '创建聊天会话成功',
  });
}

// 获取指定ID的聊天会话详情
export async function getChatSessionById(id: string): Promise<ResponseHandleResult<RwxAI.ChatSession>> {
  return httpRequest.get<RwxAI.ChatSession>(`${API_PREFIX}/Chat/sessions/${id}`, {
    showErrorNotification: true,
  });
}

// 更新现有聊天会话的信息
export async function updateChatSession(id: string, data: RwxAI.ChatSession): Promise<ResponseHandleResult<RwxAI.ChatSession>> {
  return httpRequest.put<RwxAI.ChatSession>(`${API_PREFIX}/Chat/sessions/${id}`, data, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: '更新聊天会话成功',
  });
}

// 删除指定ID的聊天会话
export async function deleteChatSession(id: string): Promise<ResponseHandleResult<void>> {
  return httpRequest.delete<void>(`${API_PREFIX}/Chat/sessions/${id}`, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: '删除聊天会话成功',
  });
}

// 获取指定会话的所有消息
export async function getChatMessages(sessionId: string): Promise<ResponseHandleResult<RwxAI.ChatMessage[]>> {
  return httpRequest.get<RwxAI.ChatMessage[]>(`${API_PREFIX}/Chat/sessions/${sessionId}/messages`, {
    showErrorNotification: true,
  });
}

// 发送消息到指定会话
export async function sendChatMessage(sessionId: string, data: RwxAI.SendMessageRequest): Promise<ResponseHandleResult<RwxAI.ChatMessage>> {
  return httpRequest.post<RwxAI.ChatMessage>(`${API_PREFIX}/Chat/sessions/${sessionId}/messages`, data, {
    showErrorNotification: true,
  });
}

// 删除指定的聊天消息
export async function deleteChatMessage(sessionId: string, messageId: string): Promise<ResponseHandleResult<void>> {
  return httpRequest.delete<void>(`${API_PREFIX}/Chat/sessions/${sessionId}/messages/${messageId}`, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: '删除消息成功',
  });
}

// 清空指定会话的所有消息
export async function clearChatMessages(sessionId: string): Promise<ResponseHandleResult<void>> {
  return httpRequest.delete<void>(`${API_PREFIX}/Chat/sessions/${sessionId}/messages`, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: '清空消息成功',
  });
}

// 获取聊天会话统计信息
export async function getChatSessionStats(sessionId: string): Promise<ResponseHandleResult<RwxAI.ChatSessionStats>> {
  return httpRequest.get<RwxAI.ChatSessionStats>(`${API_PREFIX}/Chat/sessions/${sessionId}/stats`, {
    showErrorNotification: true,
  });
}

// 导出聊天会话
export async function exportChatSession(sessionId: string, format: 'json' | 'txt' | 'md'): Promise<ResponseHandleResult<Blob>> {
  return httpRequest.get<Blob>(`${API_PREFIX}/Chat/sessions/${sessionId}/export?format=${format}`, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: '导出聊天会话成功',
  });
}

// 批量删除聊天会话
export async function batchDeleteChatSessions(sessionIds: string[]): Promise<ResponseHandleResult<void>> {
  return httpRequest.post<void>(`${API_PREFIX}/Chat/sessions/batch-delete`, { sessionIds }, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: `成功删除 ${sessionIds.length} 个聊天会话`,
  });
}

// 搜索聊天消息
export async function searchChatMessages(params: {
  query: string;
  sessionId?: string;
  limit?: number;
  offset?: number;
}): Promise<ResponseHandleResult<RwxAI.ChatMessage[]>> {
  const queryParams = new URLSearchParams();
  queryParams.append('query', params.query);
  if (params.sessionId) queryParams.append('sessionId', params.sessionId);
  if (params.limit) queryParams.append('limit', params.limit.toString());
  if (params.offset) queryParams.append('offset', params.offset.toString());

  return httpRequest.get<RwxAI.ChatMessage[]>(`${API_PREFIX}/Chat/messages/search?${queryParams}`, {
    showErrorNotification: true,
  });
}

// 获取用户的聊天会话列表（分页）
export async function getUserChatSessions(params: {
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}): Promise<ResponseHandleResult<{
  sessions: RwxAI.ChatSession[];
  total: number;
  page: number;
  pageSize: number;
}>> {
  const queryParams = new URLSearchParams();
  if (params.page) queryParams.append('page', params.page.toString());
  if (params.pageSize) queryParams.append('pageSize', params.pageSize.toString());
  if (params.sortBy) queryParams.append('sortBy', params.sortBy);
  if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);

  return httpRequest.get(`${API_PREFIX}/Chat/sessions/user?${queryParams}`, {
    showErrorNotification: true,
  });
}

// 复制聊天会话
export async function copyChatSession(sessionId: string, newName?: string): Promise<ResponseHandleResult<RwxAI.ChatSession>> {
  return httpRequest.post<RwxAI.ChatSession>(`${API_PREFIX}/Chat/sessions/${sessionId}/copy`, { newName }, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: '复制聊天会话成功',
  });
}

// 更新聊天会话状态
export async function updateChatSessionStatus(sessionId: string, isActive: boolean): Promise<ResponseHandleResult<RwxAI.ChatSession>> {
  return httpRequest.patch<RwxAI.ChatSession>(`${API_PREFIX}/Chat/sessions/${sessionId}/status`, { isActive }, {
    showSuccessMessage: true,
    showErrorNotification: true,
    successMessage: isActive ? '激活聊天会话成功' : '停用聊天会话成功',
  });
}

// 导出所有API函数
export const chatApi = {
  getChatSessions,
  createChatSession,
  getChatSessionById,
  updateChatSession,
  deleteChatSession,
  getChatMessages,
  sendChatMessage,
  deleteChatMessage,
  clearChatMessages,
  getChatSessionStats,
  exportChatSession,
  batchDeleteChatSessions,
  searchChatMessages,
  getUserChatSessions,
  copyChatSession,
  updateChatSessionStatus,
};
