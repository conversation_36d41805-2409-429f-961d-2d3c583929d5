import React, { useEffect, useState } from 'react';
import { Modal, Form, Input, InputNumber, Select, message } from 'antd';
import { useIntl, FormattedMessage } from '@umijs/max';
import { createKnowledgeBase, updateKnowledgeBase, getAIModels } from '@/services/rwxai';

interface KnowledgeFormProps {
  visible: boolean;
  onVisibleChange: (visible: boolean) => void;
  initialValues?: RwxAI.Knowledge;
  onSuccess: () => void;
}

const KnowledgeForm: React.FC<KnowledgeFormProps> = ({
  visible,
  onVisibleChange,
  initialValues,
  onSuccess,
}) => {
  const intl = useIntl();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [embeddingModels, setEmbeddingModels] = useState<RwxAI.AIModel[]>([]);

  const isEdit = !!initialValues?.Id;

  useEffect(() => {
    if (visible) {
      loadEmbeddingModels();
      if (initialValues) {
        form.setFieldsValue(initialValues);
      } else {
        form.resetFields();
      }
    }
  }, [visible, initialValues]);

  const loadEmbeddingModels = async () => {
    try {
      const response = await getAIModels();
      if (response.success && response.data) {
        // 只显示嵌入类型的模型
        const allModels = response.data.Items || [];
        const embeddingModels = allModels.filter(model => model.Template?.Type === 'Embedding' && model.IsEnabled);
        setEmbeddingModels(embeddingModels);
      }
    } catch (error) {
      message.error(intl.formatMessage({ id: 'pages.knowledge.form.loadModels.error' }));
    }
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      if (isEdit) {
        await updateKnowledgeBase(initialValues!.Id, { ...initialValues, ...values });
        message.success(intl.formatMessage({ id: 'pages.knowledge.form.update.success' }));
      } else {
        await createKnowledgeBase(values);
        message.success(intl.formatMessage({ id: 'pages.knowledge.form.create.success' }));
      }

      onSuccess();
    } catch (error) {
      message.error(
        intl.formatMessage({
          id: isEdit ? 'pages.knowledge.form.update.error' : 'pages.knowledge.form.create.error',
        })
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={intl.formatMessage({
        id: isEdit ? 'pages.knowledge.form.edit.title' : 'pages.knowledge.form.create.title',
      })}
      open={visible}
      onCancel={() => onVisibleChange(false)}
      onOk={handleSubmit}
      confirmLoading={loading}
      width={600}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          ChunkSize: 1000,
          ChunkOverlap: 200,
        }}
      >
        <Form.Item
          name="Name"
          label={<FormattedMessage id="pages.knowledge.form.name" />}
          rules={[{ required: true, message: intl.formatMessage({ id: 'pages.knowledge.form.name.required' }) }]}
        >
          <Input placeholder={intl.formatMessage({ id: 'pages.knowledge.form.name.placeholder' })} />
        </Form.Item>

        <Form.Item
          name="Description"
          label={<FormattedMessage id="pages.knowledge.form.description" />}
        >
          <Input.TextArea
            rows={3}
            placeholder={intl.formatMessage({ id: 'pages.knowledge.form.description.placeholder' })}
          />
        </Form.Item>

        <Form.Item
          name="EmbeddingModelId"
          label={<FormattedMessage id="pages.knowledge.form.embeddingModel" />}
          rules={[{ required: true, message: intl.formatMessage({ id: 'pages.knowledge.form.embeddingModel.required' }) }]}
        >
          <Select placeholder={intl.formatMessage({ id: 'pages.knowledge.form.embeddingModel.placeholder' })}>
            {embeddingModels.map((model) => (
              <Select.Option key={model.Id} value={model.Id}>
                {model.Name} ({model.ModelKey})
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="ChunkSize"
          label={<FormattedMessage id="pages.knowledge.form.chunkSize" />}
          help={intl.formatMessage({ id: 'pages.knowledge.form.chunkSize.help' })}
          rules={[{ required: true, message: intl.formatMessage({ id: 'pages.knowledge.form.chunkSize.required' }) }]}
        >
          <InputNumber
            min={100}
            max={10000}
            style={{ width: '100%' }}
            placeholder={intl.formatMessage({ id: 'pages.knowledge.form.chunkSize.placeholder' })}
          />
        </Form.Item>

        <Form.Item
          name="ChunkOverlap"
          label={<FormattedMessage id="pages.knowledge.form.chunkOverlap" />}
          help={intl.formatMessage({ id: 'pages.knowledge.form.chunkOverlap.help' })}
          rules={[{ required: true, message: intl.formatMessage({ id: 'pages.knowledge.form.chunkOverlap.required' }) }]}
        >
          <InputNumber
            min={0}
            max={1000}
            style={{ width: '100%' }}
            placeholder={intl.formatMessage({ id: 'pages.knowledge.form.chunkOverlap.placeholder' })}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default KnowledgeForm;
