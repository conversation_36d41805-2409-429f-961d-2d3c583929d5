{"version": 3, "sources": ["src/pages/table-list/index.tsx"], "sourcesContent": ["import React, { useRef } from 'react';\nimport { PageContainer, ProTable } from '@ant-design/pro-components';\nimport type { ProColumns, ActionType } from '@ant-design/pro-components';\nimport { Button, Tag } from 'antd';\nimport { PlusOutlined } from '@ant-design/icons';\nimport { useIntl, FormattedMessage } from '@umijs/max';\n\ninterface TableListItem {\n  id: string;\n  name: string;\n  status: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nconst TableList: React.FC = () => {\n  const intl = useIntl();\n  const actionRef = useRef<ActionType>();\n\n  const columns: ProColumns<TableListItem>[] = [\n    {\n      title: 'ID',\n      dataIndex: 'id',\n      key: 'id',\n      width: 80,\n    },\n    {\n      title: '名称',\n      dataIndex: 'name',\n      key: 'name',\n      ellipsis: true,\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (_, record) => (\n        <Tag color={record.status === 'active' ? 'green' : 'red'}>\n          {record.status === 'active' ? '启用' : '禁用'}\n        </Tag>\n      ),\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      valueType: 'dateTime',\n    },\n    {\n      title: '更新时间',\n      dataIndex: 'updatedAt',\n      key: 'updatedAt',\n      valueType: 'dateTime',\n    },\n    {\n      title: '操作',\n      valueType: 'option',\n      key: 'option',\n      render: () => [\n        <Button key=\"edit\" type=\"link\" size=\"small\">\n          编辑\n        </Button>,\n        <Button key=\"delete\" type=\"link\" size=\"small\" danger>\n          删除\n        </Button>,\n      ],\n    },\n  ];\n\n  // 模拟数据\n  const mockData: TableListItem[] = [\n    {\n      id: '1',\n      name: '示例项目1',\n      status: 'active',\n      createdAt: '2024-01-01 10:00:00',\n      updatedAt: '2024-01-01 10:00:00',\n    },\n    {\n      id: '2',\n      name: '示例项目2',\n      status: 'inactive',\n      createdAt: '2024-01-02 10:00:00',\n      updatedAt: '2024-01-02 10:00:00',\n    },\n  ];\n\n  return (\n    <PageContainer\n      title=\"表格列表\"\n      subTitle=\"标准的表格列表页面\"\n    >\n      <ProTable<TableListItem>\n        headerTitle=\"表格列表\"\n        actionRef={actionRef}\n        rowKey=\"id\"\n        search={{\n          labelWidth: 120,\n        }}\n        toolBarRender={() => [\n          <Button\n            type=\"primary\"\n            key=\"primary\"\n            icon={<PlusOutlined />}\n          >\n            新建\n          </Button>,\n        ]}\n        request={async () => {\n          // 模拟API请求\n          return {\n            data: mockData,\n            success: true,\n            total: mockData.length,\n          };\n        }}\n        columns={columns}\n      />\n    </PageContainer>\n  );\n};\n\nexport default TableList;\n"], "names": [], "mappings": ";;;;;;;4BA0HA;;;eAAA;;;;;;wEA1H8B;sCACU;6BAEZ;8BACC;4BACa;;;;;;;;;;AAU1C,MAAM,YAAsB;;IACb,IAAA,YAAO;IACpB,MAAM,YAAY,IAAA,aAAM;IAExB,MAAM,UAAuC;QAC3C;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,OAAO;QACT;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,UAAU;QACZ;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,GAAG,uBACV,2BAAC,SAAG;oBAAC,OAAO,OAAO,MAAM,KAAK,WAAW,UAAU;8BAChD,OAAO,MAAM,KAAK,WAAW,OAAO;;;;;;QAG3C;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,WAAW;QACb;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,WAAW;QACb;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,IAAM;kCACZ,2BAAC,YAAM;wBAAY,MAAK;wBAAO,MAAK;kCAAQ;uBAAhC;;;;;kCAGZ,2BAAC,YAAM;wBAAc,MAAK;wBAAO,MAAK;wBAAQ,MAAM;kCAAC;uBAAzC;;;;;iBAGb;QACH;KACD;IAED,OAAO;IACP,MAAM,WAA4B;QAChC;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,WAAW;YACX,WAAW;QACb;QACA;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,WAAW;YACX,WAAW;QACb;KACD;IAED,qBACE,2BAAC,4BAAa;QACZ,OAAM;QACN,UAAS;kBAET,cAAA,2BAAC,uBAAQ;YACP,aAAY;YACZ,WAAW;YACX,QAAO;YACP,QAAQ;gBACN,YAAY;YACd;YACA,eAAe,IAAM;kCACnB,2BAAC,YAAM;wBACL,MAAK;wBAEL,oBAAM,2BAAC,mBAAY;;;;;kCACpB;uBAFK;;;;;iBAKP;YACD,SAAS;gBACP,UAAU;gBACV,OAAO;oBACL,MAAM;oBACN,SAAS;oBACT,OAAO,SAAS,MAAM;gBACxB;YACF;YACA,SAAS;;;;;;;;;;;AAIjB;GAzGM;;QACS,YAAO;;;KADhB;IA2GN,WAAe"}