{"version": 3, "sources": ["src/pages/chat/components/SessionForm.tsx", "src/services/rwxai/aiModels.ts", "src/services/rwxai/apps.ts", "src/services/rwxai/auth.ts", "src/services/rwxai/chat.ts", "src/services/rwxai/index.ts", "src/services/rwxai/knowledge.ts", "src/services/rwxai/migrated-chat.ts", "src/services/rwxai/plugins.ts", "src/types/response.ts", "src/utils/api.ts", "src/utils/pageDataHandler.ts", "src/utils/request.ts", "src/utils/responseHandler.ts"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { Modal, Form, Input, Select, InputNumber, Switch, message } from 'antd';\nimport { useIntl, FormattedMessage } from '@umijs/max';\nimport { createChatSession, updateChatSession, getAIModels } from '@/services/rwxai';\n\ninterface SessionFormProps {\n  visible: boolean;\n  onVisibleChange: (visible: boolean) => void;\n  initialValues?: RwxAI.ChatSession;\n  onSuccess: () => void;\n}\n\nconst SessionForm: React.FC<SessionFormProps> = ({\n  visible,\n  onVisibleChange,\n  initialValues,\n  onSuccess,\n}) => {\n  const intl = useIntl();\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [models, setModels] = useState<RwxAI.AIModel[]>([]);\n  const [selectedModel, setSelectedModel] = useState<RwxAI.AIModel | null>(null);\n\n  const isEdit = !!initialValues?.Id;\n\n  useEffect(() => {\n    if (visible) {\n      loadModels();\n      if (initialValues) {\n        form.setFieldsValue(initialValues);\n        // 如果是编辑模式，设置选中的模型\n        if (initialValues.ModelId) {\n          const model = models.find(m => m.Id === initialValues.ModelId);\n          setSelectedModel(model || null);\n        }\n      } else {\n        form.resetFields();\n        setSelectedModel(null);\n      }\n    }\n  }, [visible, initialValues, models]);\n\n  const loadModels = async () => {\n    try {\n      const response = await getAIModels();\n      console.log('AI Models API response:', response); // 调试日志\n\n      if (response.success) {\n        // 先显示所有启用的模型，暂时不过滤类型\n        const enabledModels = (response.data || []).filter(model => {\n          console.log('Model:', model); // 调试每个模型\n          return model.IsEnabled === true;\n        });\n\n        console.log('Enabled models:', enabledModels); // 调试日志\n        setModels(enabledModels);\n      } else {\n        console.error('Failed to load models:', response);\n        setModels([]);\n      }\n    } catch (error) {\n      console.error('Error loading models:', error);\n      setModels([]);\n    }\n  };\n\n  // 处理模型选择变化\n  const handleModelChange = (modelId: string) => {\n    const model = models.find(m => m.Id === modelId);\n    setSelectedModel(model || null);\n\n    if (model) {\n      // 自动填充模型的配置参数\n      form.setFieldsValue({\n        Temperature: model.Temperature || 0.7,\n        MaxTokens: model.MaxTokens || 2000,\n        SystemPrompt: model.SystemPrompt || '',\n      });\n    }\n  };\n\n  const handleSubmit = async () => {\n    try {\n      const values = await form.validateFields();\n      setLoading(true);\n\n      let response;\n      if (isEdit) {\n        response = await updateChatSession(initialValues!.Id, { ...initialValues, ...values });\n      } else {\n        response = await createChatSession(values);\n      }\n\n      if (response.success) {\n        onSuccess();\n      }\n      // 成功和错误消息会由统一响应处理系统自动显示\n    } catch (error) {\n      // 表单验证错误等\n      console.error('Form validation error:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Modal\n      title={intl.formatMessage({\n        id: isEdit ? 'pages.chat.form.edit.title' : 'pages.chat.form.create.title',\n      })}\n      open={visible}\n      onCancel={() => onVisibleChange(false)}\n      onOk={handleSubmit}\n      confirmLoading={loading}\n      width={600}\n    >\n      <Form\n        form={form}\n        layout=\"vertical\"\n        initialValues={{\n          Temperature: 0.7,\n          MaxTokens: 2000,\n        }}\n      >\n        <Form.Item\n          name=\"Name\"\n          label={<FormattedMessage id=\"pages.chat.form.name\" />}\n          rules={[{ required: true, message: intl.formatMessage({ id: 'pages.chat.form.name.required' }) }]}\n        >\n          <Input placeholder={intl.formatMessage({ id: 'pages.chat.form.name.placeholder' })} />\n        </Form.Item>\n\n        <Form.Item\n          name=\"ModelId\"\n          label={<FormattedMessage id=\"pages.chat.form.model\" />}\n          rules={[{ required: true, message: intl.formatMessage({ id: 'pages.chat.form.model.required' }) }]}\n        >\n          <Select\n            placeholder={intl.formatMessage({ id: 'pages.chat.form.model.placeholder' })}\n            onChange={handleModelChange}\n          >\n            {models.map((model) => (\n              <Select.Option key={model.Id} value={model.Id}>\n                {model.Name || model.DisplayName} ({model.Template?.ModelId || model.ModelId || '未知模型'})\n              </Select.Option>\n            ))}\n          </Select>\n        </Form.Item>\n\n        {/* 显示从模型自动获取的配置信息 */}\n        {selectedModel && (\n          <>\n            <Form.Item\n              name=\"SystemPrompt\"\n              label={<FormattedMessage id=\"pages.chat.form.systemPrompt\" />}\n              help=\"从选中的模型自动获取\"\n            >\n              <Input.TextArea\n                rows={3}\n                disabled\n                placeholder=\"从模型配置中自动获取\"\n              />\n            </Form.Item>\n\n            <div style={{ display: 'flex', gap: '16px' }}>\n              <Form.Item\n                name=\"Temperature\"\n                label={<FormattedMessage id=\"pages.chat.form.temperature\" />}\n                style={{ flex: 1 }}\n              >\n                <InputNumber\n                  disabled\n                  style={{ width: '100%' }}\n                  placeholder=\"从模型配置中自动获取\"\n                />\n              </Form.Item>\n\n              <Form.Item\n                name=\"MaxTokens\"\n                label={<FormattedMessage id=\"pages.chat.form.maxTokens\" />}\n                style={{ flex: 1 }}\n              >\n                <InputNumber\n                  disabled\n                  style={{ width: '100%' }}\n                  placeholder=\"从模型配置中自动获取\"\n                />\n              </Form.Item>\n            </div>\n          </>\n        )}\n\n\n      </Form>\n    </Modal>\n  );\n};\n\nexport default SessionForm;\n", "/**\r\n * 统一的AI模型管理API服务\r\n * 合并了原aiModels.ts和enhanced-aiModels.ts的功能\r\n * 提供完整的AI模型、模板、提供商管理功能\r\n */\r\n\r\nimport { httpRequest } from '@/utils/request';\r\nimport { toQueryString } from '@/utils/pageDataHandler';\r\nimport { getApiPrefix } from '@/utils/api';\r\nimport { ResponseHandleResult } from '@/types/response';\r\n\r\nconst API_PREFIX: string = getApiPrefix();\r\n\r\n/**\r\n * ===========================================\r\n * AI模型核心CRUD操作\r\n * ===========================================\r\n */\r\n\r\n/**\r\n * 获取AI模型列表（支持查询参数和分页）\r\n */\r\nexport async function getAIModels(params?: any): Promise<ResponseHandleResult<RwxAI.BackendPagedResponse<RwxAI.AIModel>>> {\r\n  const url = params ? `${API_PREFIX}/AIModels?${toQueryString(params)}` : `${API_PREFIX}/AIModels`;\r\n  return httpRequest.get<RwxAI.BackendPagedResponse<RwxAI.AIModel>>(url, {\r\n    showErrorNotification: true\r\n  });\r\n}\r\n\r\n/**\r\n * 根据ID获取AI模型详情\r\n */\r\nexport async function getAIModelById(id: string): Promise<ResponseHandleResult<RwxAI.AIModel>> {\r\n  return httpRequest.get<RwxAI.AIModel>(`${API_PREFIX}/AIModels/${id}`, {\r\n    showErrorNotification: true,\r\n  });\r\n}\r\n\r\n/**\r\n * 创建AI模型\r\n */\r\nexport async function createAIModel(data: RwxAI.CreateAIModelRequest): Promise<ResponseHandleResult<RwxAI.AIModel>> {\r\n  return httpRequest.post<RwxAI.AIModel>(`${API_PREFIX}/AIModels`, data, {\r\n    showSuccessMessage: true,\r\n    showErrorNotification: true,\r\n    successMessage: '创建AI模型成功',\r\n  });\r\n}\r\n\r\n/**\r\n * 更新AI模型\r\n */\r\nexport async function updateAIModel(\r\n  id: string,\r\n  data: RwxAI.UpdateAIModelRequest\r\n): Promise<ResponseHandleResult<RwxAI.AIModel>> {\r\n  return httpRequest.put<RwxAI.AIModel>(`${API_PREFIX}/AIModels/${id}`, data, {\r\n    showSuccessMessage: true,\r\n    showErrorNotification: true,\r\n    successMessage: '更新AI模型成功',\r\n  });\r\n}\r\n\r\n/**\r\n * 删除AI模型\r\n */\r\nexport async function deleteAIModel(id: string): Promise<ResponseHandleResult<void>> {\r\n  return httpRequest.delete<void>(`${API_PREFIX}/AIModels/${id}`, {\r\n    showSuccessMessage: true,\r\n    showErrorNotification: true,\r\n    successMessage: '删除AI模型成功',\r\n  });\r\n}\r\n\r\n/**\r\n * 批量删除AI模型\r\n */\r\nexport async function batchDeleteAIModels(ids: string[]): Promise<ResponseHandleResult<void>> {\r\n  return httpRequest.post<void>(`${API_PREFIX}/AIModels/batch-delete`, { ids }, {\r\n    showSuccessMessage: true,\r\n    showErrorNotification: true,\r\n    successMessage: `成功删除 ${ids.length} 个AI模型`,\r\n  });\r\n}\r\n\r\n/**\r\n * 测试AI模型连接\r\n */\r\nexport async function testAIModelConnection(id: string): Promise<ResponseHandleResult<RwxAI.TestConnectionResult>> {\r\n  return httpRequest.post<RwxAI.TestConnectionResult>(`${API_PREFIX}/AIModels/${id}/test`, {}, {\r\n    showSuccessMessage: true,\r\n    showErrorNotification: true,\r\n    successMessage: 'AI模型连接测试成功',\r\n  });\r\n}\r\n\r\n/**\r\n * 启用/禁用AI模型\r\n */\r\nexport async function toggleAIModelStatus(\r\n  id: string,\r\n  enabled: boolean\r\n): Promise<ResponseHandleResult<RwxAI.AIModel>> {\r\n  return httpRequest.patch<RwxAI.AIModel>(`${API_PREFIX}/AIModels/${id}/status`, { enabled }, {\r\n    showSuccessMessage: true,\r\n    showErrorNotification: true,\r\n    successMessage: enabled ? '启用AI模型成功' : '禁用AI模型成功',\r\n  });\r\n}\r\n\r\n/**\r\n * AI模型模板相关API\r\n */\r\n\r\n// 根据模型类型获取模型模板列表\r\nexport async function getAIModelTemplatesByType(modelType: RwxAI.ModelTypeEnum): Promise<ResponseHandleResult<RwxAI.AIModelTemplate[]>> {\r\n  return httpRequest.get<RwxAI.AIModelTemplate[]>(`${API_PREFIX}/AIModelTemplates/by-type/${modelType}`, {\r\n    showErrorNotification: true,\r\n  });\r\n}\r\n\r\n// 根据提供商ID和模型类型获取模型模板列表\r\nexport async function getAIModelTemplatesByProviderAndType(params: {\r\n  providerId?: string;\r\n  modelType?: RwxAI.ModelTypeEnum;\r\n}): Promise<ResponseHandleResult<RwxAI.AIModelTemplate[]>> {\r\n  const queryParams = new URLSearchParams();\r\n  if (params.providerId) queryParams.append('providerId', params.providerId);\r\n  if (params.modelType) queryParams.append('modelType', params.modelType.toString());\r\n\r\n  return httpRequest.get<RwxAI.AIModelTemplate[]>(`${API_PREFIX}/AIModelTemplates/by-provider-and-type?${queryParams}`, {\r\n    showErrorNotification: true,\r\n  });\r\n}\r\n\r\n// 获取模型模板详情\r\nexport async function getAIModelTemplateById(id: string): Promise<ResponseHandleResult<RwxAI.AIModelTemplate>> {\r\n  return httpRequest.get<RwxAI.AIModelTemplate>(`${API_PREFIX}/AIModelTemplates/${id}`, {\r\n    showErrorNotification: true,\r\n  });\r\n}\r\n\r\n/**\r\n * AI服务提供商相关API\r\n */\r\n\r\n// 获取所有启用的AI服务提供商\r\nexport async function getEnabledAIProviders(): Promise<ResponseHandleResult<RwxAI.AIProvider[]>> {\r\n  return httpRequest.get<RwxAI.AIProvider[]>(`${API_PREFIX}/AIProviders/enabled`, {\r\n    showErrorNotification: true,\r\n  });\r\n}\r\n\r\n// 根据提供商ID获取对应的模型列表\r\nexport async function getModelsByProviderId(providerId: string): Promise<ResponseHandleResult<RwxAI.AIModelTemplate[]>> {\r\n  return httpRequest.get<RwxAI.AIModelTemplate[]>(`${API_PREFIX}/AIProviders/${providerId}/models`, {\r\n    showErrorNotification: true,\r\n  });\r\n}\r\n\r\n// 根据提供商代码获取对应的模型列表\r\nexport async function getModelsByProviderCode(providerCode: string): Promise<ResponseHandleResult<RwxAI.AIModelTemplate[]>> {\r\n  return httpRequest.get<RwxAI.AIModelTemplate[]>(`${API_PREFIX}/AIProviders/by-code/${providerCode}/models`, {\r\n    showErrorNotification: true,\r\n  });\r\n}\r\n\r\n// 根据提供商类型获取模型模板列表\r\nexport async function getModelsByProviderType(providerType: number): Promise<ResponseHandleResult<RwxAI.AIModelTemplate[]>> {\r\n  return httpRequest.get<RwxAI.AIModelTemplate[]>(`${API_PREFIX}/AIProviders/type/${providerType}/models`, {\r\n    showErrorNotification: true,\r\n  });\r\n}\r\n\r\n// 获取所有启用的模型模板\r\nexport async function getAllEnabledModels(): Promise<ResponseHandleResult<RwxAI.AIModelTemplate[]>> {\r\n  return httpRequest.get<RwxAI.AIModelTemplate[]>(`${API_PREFIX}/AIProviders/models/all`, {\r\n    showErrorNotification: true,\r\n  });\r\n}\r\n\r\n/**\r\n * ===========================================\r\n * 统一的API导出对象\r\n * ===========================================\r\n */\r\n\r\n/**\r\n * AI模型管理API统一导出对象\r\n * 提供所有AI模型相关的API函数\r\n */\r\nexport const aiModelsApi = {\r\n  // 核心CRUD操作\r\n  getAIModels,\r\n  getAIModelById,\r\n  createAIModel,\r\n  updateAIModel,\r\n  deleteAIModel,\r\n\r\n  // 增强功能\r\n  batchDeleteAIModels,\r\n  testAIModelConnection,\r\n  toggleAIModelStatus,\r\n\r\n  // 模板管理\r\n  getAIModelTemplatesByType,\r\n  getAIModelTemplatesByProviderAndType,\r\n  getAIModelTemplateById,\r\n\r\n  // 提供商管理\r\n  getEnabledAIProviders,\r\n  getModelsByProviderId,\r\n  getModelsByProviderCode,\r\n  getModelsByProviderType,\r\n  getAllEnabledModels,\r\n};\r\n\r\n/**\r\n * 默认导出统一API对象（可选）\r\n */\r\nexport default aiModelsApi;\r\n", "import { httpRequest } from '@/utils/request';\nimport { getApiPrefix } from '@/utils/api';\nimport { ResponseHandleResult } from '@/types/response';\n\nconst API_PREFIX: string = getApiPrefix();\n\n/**\n * 应用管理相关API\n */\n\n// 获取所有应用\nexport async function getApps(): Promise<ResponseHandleResult<RwxAI.App[]>> {\n  return httpRequest.get<RwxAI.App[]>(`${API_PREFIX}/Apps`, {\n    showErrorNotification: true,\n  });\n}\n\n// 创建新应用\nexport async function createApp(data: RwxAI.App): Promise<ResponseHandleResult<RwxAI.App>> {\n  return httpRequest.post<RwxAI.App>(`${API_PREFIX}/Apps`, data, {\n    showSuccessMessage: true,\n    showErrorNotification: true,\n    successMessage: '创建应用成功',\n  });\n}\n\n// 获取当前用户的所有应用\nexport async function getMyApps(): Promise<ResponseHandleResult<RwxAI.App[]>> {\n  return httpRequest.get<RwxAI.App[]>(`${API_PREFIX}/Apps/my`, {\n    showErrorNotification: true,\n  });\n}\n\n// 获取指定应用详情\nexport async function getAppById(id: string): Promise<ResponseHandleResult<RwxAI.App>> {\n  return httpRequest.get<RwxAI.App>(`${API_PREFIX}/Apps/${id}`, {\n    showErrorNotification: true,\n  });\n}\n\n// 更新应用信息\nexport async function updateApp(id: string, data: RwxAI.App): Promise<ResponseHandleResult<RwxAI.App>> {\n  return httpRequest.put<RwxAI.App>(`${API_PREFIX}/Apps/${id}`, data, {\n    showSuccessMessage: true,\n    showErrorNotification: true,\n    successMessage: '更新应用成功',\n  });\n}\n\n// 删除应用\nexport async function deleteApp(id: string): Promise<ResponseHandleResult<void>> {\n  return httpRequest.delete<void>(`${API_PREFIX}/Apps/${id}`, {\n    showSuccessMessage: true,\n    showErrorNotification: true,\n    successMessage: '删除应用成功',\n  });\n}\n\n// 重新生成API密钥\nexport async function regenerateApiKeys(id: string): Promise<ResponseHandleResult<RwxAI.App>> {\n  return httpRequest.post<RwxAI.App>(`${API_PREFIX}/Apps/${id}/regenerate-keys`, {}, {\n    showSuccessMessage: true,\n    showErrorNotification: true,\n    successMessage: 'API密钥重新生成成功',\n  });\n}\n\n// 修改应用状态\nexport async function updateAppStatus(id: string, data: { isEnabled: boolean }): Promise<ResponseHandleResult<RwxAI.App>> {\n  return httpRequest.patch<RwxAI.App>(`${API_PREFIX}/Apps/${id}/status`, data, {\n    showSuccessMessage: true,\n    showErrorNotification: true,\n    successMessage: '应用状态更新成功',\n  });\n}\n\n// 重置API调用计数\nexport async function resetApiCalls(id: string): Promise<ResponseHandleResult<void>> {\n  return httpRequest.post<void>(`${API_PREFIX}/Apps/${id}/reset-api-calls`, {}, {\n    showSuccessMessage: true,\n    showErrorNotification: true,\n    successMessage: 'API调用计数重置成功',\n  });\n}\n", "import { httpRequest } from '@/utils/request';\nimport { getApiPrefix } from '@/utils/api';\nimport { ResponseHandleResult } from '@/types/response';\n\nconst API_PREFIX: string = getApiPrefix();\n\n/**\n * 用户认证相关API\n */\n\n// 用户登录接口\nexport async function login(data: RwxAI.LoginRequest): Promise<ResponseHandleResult<{\n  token: string;\n  refreshToken: string;\n  user: any;\n  expiresIn: number;\n}>> {\n  return httpRequest.post(`${API_PREFIX}/Auth/login`, data, {\n    skipAuth: true, // 登录接口不需要认证\n    showErrorNotification: true,\n  });\n}\n\n// 用户注册接口\nexport async function register(data: RwxAI.RegisterRequest): Promise<ResponseHandleResult<{\n  success: boolean;\n  message: string;\n}>> {\n  return httpRequest.post(`${API_PREFIX}/Auth/register`, data, {\n    skipAuth: true, // 注册接口不需要认证\n    showErrorNotification: true,\n  });\n}\n\n// 刷新令牌接口\nexport async function refreshToken(data: RwxAI.RefreshTokenRequest): Promise<ResponseHandleResult<{\n  token: string;\n  refreshToken: string;\n  expiresIn: number;\n}>> {\n  return httpRequest.post(`${API_PREFIX}/Auth/refresh-token`, data, {\n    skipAuth: true, // 刷新令牌接口不需要认证\n    showErrorNotification: false, // 刷新令牌失败不显示错误通知\n  });\n}\n\n// 修改密码接口\nexport async function changePassword(data: RwxAI.ChangePasswordRequest): Promise<ResponseHandleResult<{\n  success: boolean;\n  message: string;\n}>> {\n  return httpRequest.post(`${API_PREFIX}/Auth/change-password`, data, {\n    showSuccessMessage: true,\n    showErrorNotification: true,\n    successMessage: '密码修改成功',\n  });\n}\n", "import { httpRequest } from '@/utils/request';\nimport { getApiPrefix } from '@/utils/api';\nimport { ResponseHandleResult } from '@/types/response';\n\nconst API_PREFIX: string = getApiPrefix();\n\n/**\n * 聊天会话相关API\n */\n\n// 获取所有聊天会话\nexport async function getChatSessions(): Promise<ResponseHandleResult<RwxAI.ChatSession[]>> {\n  return httpRequest.get<RwxAI.ChatSession[]>(`${API_PREFIX}/Chat/sessions`, {\n    showErrorNotification: true,\n  });\n}\n\n// 创建新的聊天会话\nexport async function createChatSession(data: RwxAI.ChatSession): Promise<ResponseHandleResult<RwxAI.ChatSession>> {\n  return httpRequest.post<RwxAI.ChatSession>(`${API_PREFIX}/Chat/sessions`, data, {\n    showSuccessMessage: true,\n    showErrorNotification: true,\n    successMessage: '创建聊天会话成功',\n  });\n}\n\n// 获取指定ID的聊天会话详情\nexport async function getChatSessionById(id: string): Promise<ResponseHandleResult<RwxAI.ChatSession>> {\n  return httpRequest.get<RwxAI.ChatSession>(`${API_PREFIX}/Chat/sessions/${id}`, {\n    showErrorNotification: true,\n  });\n}\n\n// 更新现有聊天会话的信息\nexport async function updateChatSession(id: string, data: RwxAI.ChatSession): Promise<ResponseHandleResult<RwxAI.ChatSession>> {\n  return httpRequest.put<RwxAI.ChatSession>(`${API_PREFIX}/Chat/sessions/${id}`, data, {\n    showSuccessMessage: true,\n    showErrorNotification: true,\n    successMessage: '更新聊天会话成功',\n  });\n}\n\n// 删除指定ID的聊天会话\nexport async function deleteChatSession(id: string): Promise<ResponseHandleResult<void>> {\n  return httpRequest.delete<void>(`${API_PREFIX}/Chat/sessions/${id}`, {\n    showSuccessMessage: true,\n    showErrorNotification: true,\n    successMessage: '删除聊天会话成功',\n  });\n}\n\n// 发送消息并获取AI回复\nexport async function sendMessage(sessionId: string, data: RwxAI.ChatMessageRequest): Promise<ResponseHandleResult<RwxAI.ChatMessage>> {\n  return httpRequest.post<RwxAI.ChatMessage>(`${API_PREFIX}/Chat/sessions/${sessionId}/messages`, data, {\n    showErrorNotification: true,\n  });\n}\n\n// 发送消息并以流式方式获取AI回复\nexport async function sendMessageStream(sessionId: string, data: RwxAI.ChatMessageRequest): Promise<ResponseHandleResult<any>> {\n  return httpRequest.post(`${API_PREFIX}/Chat/sessions/${sessionId}/messages/stream`, data, {\n    showErrorNotification: true,\n  });\n}\n", "// 导出所有API服务\r\nexport * from './aiModels';\r\nexport * from './apps';\r\nexport * from './auth';\r\nexport * from './chat';\r\nexport * from './knowledge';\r\nexport * from './plugins';\r\n\r\n// 导出增强版API服务示例（使用别名避免冲突）\r\nexport { chatApi } from './migrated-chat';\r\nexport * from './typings.d';\r\n\r\n// 注意：aiModelsApi 现在从 './aiModels' 中导出，不再需要从 enhanced-aiModels 导出\r\n", "import { httpRequest } from '@/utils/request';\nimport { getApiPrefix } from '@/utils/api';\nimport { ResponseHandleResult } from '@/types/response';\n\nconst API_PREFIX: string = getApiPrefix();\n\n/**\n * 知识库管理相关API\n */\n\n// 获取所有知识库列表\nexport async function getKnowledgeBases(): Promise<ResponseHandleResult<RwxAI.Knowledge[]>> {\n  return httpRequest.get<RwxAI.Knowledge[]>(`${API_PREFIX}/Knowledge`, {\n    showErrorNotification: true,\n  });\n}\n\n// 创建新的知识库\nexport async function createKnowledgeBase(data: RwxAI.Knowledge): Promise<ResponseHandleResult<RwxAI.Knowledge>> {\n  return httpRequest.post<RwxAI.Knowledge>(`${API_PREFIX}/Knowledge`, data, {\n    showSuccessMessage: true,\n    showErrorNotification: true,\n    successMessage: '创建知识库成功',\n  });\n}\n\n// 根据ID获取指定知识库\nexport async function getKnowledgeBaseById(id: string): Promise<ResponseHandleResult<RwxAI.Knowledge>> {\n  return httpRequest.get<RwxAI.Knowledge>(`${API_PREFIX}/Knowledge/${id}`, {\n    showErrorNotification: true,\n  });\n}\n\n// 更新知识库信息\nexport async function updateKnowledgeBase(id: string, data: RwxAI.Knowledge): Promise<ResponseHandleResult<RwxAI.Knowledge>> {\n  return httpRequest.put<RwxAI.Knowledge>(`${API_PREFIX}/Knowledge/${id}`, data, {\n    showSuccessMessage: true,\n    showErrorNotification: true,\n    successMessage: '更新知识库成功',\n  });\n}\n\n// 删除指定的知识库\nexport async function deleteKnowledgeBase(id: string): Promise<ResponseHandleResult<void>> {\n  return httpRequest.delete<void>(`${API_PREFIX}/Knowledge/${id}`, {\n    showSuccessMessage: true,\n    showErrorNotification: true,\n    successMessage: '删除知识库成功',\n  });\n}\n\n// 获取知识库中的所有文件\nexport async function getKnowledgeFiles(knowledgeId: string): Promise<ResponseHandleResult<RwxAI.KnowledgeFile[]>> {\n  return httpRequest.get<RwxAI.KnowledgeFile[]>(`${API_PREFIX}/Knowledge/${knowledgeId}/files`, {\n    showErrorNotification: true,\n  });\n}\n\n// 上传文件到知识库\nexport async function uploadKnowledgeFile(knowledgeId: string, file: File): Promise<ResponseHandleResult<RwxAI.KnowledgeFile>> {\n  const formData = new FormData();\n  formData.append('file', file);\n\n  // 使用fetch直接上传文件，因为httpRequest可能不支持FormData\n  try {\n    const response = await fetch(`${API_PREFIX}/Knowledge/${knowledgeId}/files`, {\n      method: 'POST',\n      body: formData,\n      headers: {\n        'Authorization': `Bearer ${localStorage.getItem('rwxai_token')}`,\n      },\n    });\n\n    if (response.ok) {\n      const data = await response.json();\n      return { success: true, data, message: '文件上传成功' };\n    } else {\n      const errorData = await response.json().catch(() => ({}));\n      return { success: false, message: errorData.message || '文件上传失败' };\n    }\n  } catch (error: any) {\n    return { success: false, message: error.message || '文件上传失败' };\n  }\n}\n\n// 处理知识库文件（解析和向量化）\nexport async function processKnowledgeFile(fileId: string): Promise<ResponseHandleResult<void>> {\n  return httpRequest.post<void>(`${API_PREFIX}/Knowledge/files/${fileId}/process`, {}, {\n    showSuccessMessage: true,\n    showErrorNotification: true,\n    successMessage: '文件处理成功',\n  });\n}\n\n// 删除知识库文件\nexport async function deleteKnowledgeFile(fileId: string): Promise<ResponseHandleResult<void>> {\n  return httpRequest.delete<void>(`${API_PREFIX}/Knowledge/files/${fileId}`, {\n    showSuccessMessage: true,\n    showErrorNotification: true,\n    successMessage: '删除文件成功',\n  });\n}\n", "/**\n * 聊天会话管理API服务 - 已迁移到统一响应处理系统\n */\n\nimport { httpRequest } from '@/utils/request';\nimport { getApiPrefix } from '@/utils/api';\nimport { ResponseHandleResult } from '@/types/response';\n\nconst API_PREFIX: string = getApiPrefix();\n\n/**\n * 聊天会话管理相关API\n */\n\n// 获取所有聊天会话\nexport async function getChatSessions(): Promise<ResponseHandleResult<RwxAI.ChatSession[]>> {\n  return httpRequest.get<RwxAI.ChatSession[]>(`${API_PREFIX}/Chat/sessions`, {\n    showErrorNotification: true,\n  });\n}\n\n// 创建新的聊天会话\nexport async function createChatSession(data: RwxAI.ChatSession): Promise<ResponseHandleResult<RwxAI.ChatSession>> {\n  return httpRequest.post<RwxAI.ChatSession>(`${API_PREFIX}/Chat/sessions`, data, {\n    showSuccessMessage: true,\n    showErrorNotification: true,\n    successMessage: '创建聊天会话成功',\n  });\n}\n\n// 获取指定ID的聊天会话详情\nexport async function getChatSessionById(id: string): Promise<ResponseHandleResult<RwxAI.ChatSession>> {\n  return httpRequest.get<RwxAI.ChatSession>(`${API_PREFIX}/Chat/sessions/${id}`, {\n    showErrorNotification: true,\n  });\n}\n\n// 更新现有聊天会话的信息\nexport async function updateChatSession(id: string, data: RwxAI.ChatSession): Promise<ResponseHandleResult<RwxAI.ChatSession>> {\n  return httpRequest.put<RwxAI.ChatSession>(`${API_PREFIX}/Chat/sessions/${id}`, data, {\n    showSuccessMessage: true,\n    showErrorNotification: true,\n    successMessage: '更新聊天会话成功',\n  });\n}\n\n// 删除指定ID的聊天会话\nexport async function deleteChatSession(id: string): Promise<ResponseHandleResult<void>> {\n  return httpRequest.delete<void>(`${API_PREFIX}/Chat/sessions/${id}`, {\n    showSuccessMessage: true,\n    showErrorNotification: true,\n    successMessage: '删除聊天会话成功',\n  });\n}\n\n// 获取指定会话的所有消息\nexport async function getChatMessages(sessionId: string): Promise<ResponseHandleResult<RwxAI.ChatMessage[]>> {\n  return httpRequest.get<RwxAI.ChatMessage[]>(`${API_PREFIX}/Chat/sessions/${sessionId}/messages`, {\n    showErrorNotification: true,\n  });\n}\n\n// 发送消息到指定会话\nexport async function sendChatMessage(sessionId: string, data: RwxAI.SendMessageRequest): Promise<ResponseHandleResult<RwxAI.ChatMessage>> {\n  return httpRequest.post<RwxAI.ChatMessage>(`${API_PREFIX}/Chat/sessions/${sessionId}/messages`, data, {\n    showErrorNotification: true,\n  });\n}\n\n// 删除指定的聊天消息\nexport async function deleteChatMessage(sessionId: string, messageId: string): Promise<ResponseHandleResult<void>> {\n  return httpRequest.delete<void>(`${API_PREFIX}/Chat/sessions/${sessionId}/messages/${messageId}`, {\n    showSuccessMessage: true,\n    showErrorNotification: true,\n    successMessage: '删除消息成功',\n  });\n}\n\n// 清空指定会话的所有消息\nexport async function clearChatMessages(sessionId: string): Promise<ResponseHandleResult<void>> {\n  return httpRequest.delete<void>(`${API_PREFIX}/Chat/sessions/${sessionId}/messages`, {\n    showSuccessMessage: true,\n    showErrorNotification: true,\n    successMessage: '清空消息成功',\n  });\n}\n\n// 获取聊天会话统计信息\nexport async function getChatSessionStats(sessionId: string): Promise<ResponseHandleResult<RwxAI.ChatSessionStats>> {\n  return httpRequest.get<RwxAI.ChatSessionStats>(`${API_PREFIX}/Chat/sessions/${sessionId}/stats`, {\n    showErrorNotification: true,\n  });\n}\n\n// 导出聊天会话\nexport async function exportChatSession(sessionId: string, format: 'json' | 'txt' | 'md'): Promise<ResponseHandleResult<Blob>> {\n  return httpRequest.get<Blob>(`${API_PREFIX}/Chat/sessions/${sessionId}/export?format=${format}`, {\n    showSuccessMessage: true,\n    showErrorNotification: true,\n    successMessage: '导出聊天会话成功',\n  });\n}\n\n// 批量删除聊天会话\nexport async function batchDeleteChatSessions(sessionIds: string[]): Promise<ResponseHandleResult<void>> {\n  return httpRequest.post<void>(`${API_PREFIX}/Chat/sessions/batch-delete`, { sessionIds }, {\n    showSuccessMessage: true,\n    showErrorNotification: true,\n    successMessage: `成功删除 ${sessionIds.length} 个聊天会话`,\n  });\n}\n\n// 搜索聊天消息\nexport async function searchChatMessages(params: {\n  query: string;\n  sessionId?: string;\n  limit?: number;\n  offset?: number;\n}): Promise<ResponseHandleResult<RwxAI.ChatMessage[]>> {\n  const queryParams = new URLSearchParams();\n  queryParams.append('query', params.query);\n  if (params.sessionId) queryParams.append('sessionId', params.sessionId);\n  if (params.limit) queryParams.append('limit', params.limit.toString());\n  if (params.offset) queryParams.append('offset', params.offset.toString());\n\n  return httpRequest.get<RwxAI.ChatMessage[]>(`${API_PREFIX}/Chat/messages/search?${queryParams}`, {\n    showErrorNotification: true,\n  });\n}\n\n// 获取用户的聊天会话列表（分页）\nexport async function getUserChatSessions(params: {\n  page?: number;\n  pageSize?: number;\n  sortBy?: string;\n  sortOrder?: 'asc' | 'desc';\n}): Promise<ResponseHandleResult<{\n  sessions: RwxAI.ChatSession[];\n  total: number;\n  page: number;\n  pageSize: number;\n}>> {\n  const queryParams = new URLSearchParams();\n  if (params.page) queryParams.append('page', params.page.toString());\n  if (params.pageSize) queryParams.append('pageSize', params.pageSize.toString());\n  if (params.sortBy) queryParams.append('sortBy', params.sortBy);\n  if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);\n\n  return httpRequest.get(`${API_PREFIX}/Chat/sessions/user?${queryParams}`, {\n    showErrorNotification: true,\n  });\n}\n\n// 复制聊天会话\nexport async function copyChatSession(sessionId: string, newName?: string): Promise<ResponseHandleResult<RwxAI.ChatSession>> {\n  return httpRequest.post<RwxAI.ChatSession>(`${API_PREFIX}/Chat/sessions/${sessionId}/copy`, { newName }, {\n    showSuccessMessage: true,\n    showErrorNotification: true,\n    successMessage: '复制聊天会话成功',\n  });\n}\n\n// 更新聊天会话状态\nexport async function updateChatSessionStatus(sessionId: string, isActive: boolean): Promise<ResponseHandleResult<RwxAI.ChatSession>> {\n  return httpRequest.patch<RwxAI.ChatSession>(`${API_PREFIX}/Chat/sessions/${sessionId}/status`, { isActive }, {\n    showSuccessMessage: true,\n    showErrorNotification: true,\n    successMessage: isActive ? '激活聊天会话成功' : '停用聊天会话成功',\n  });\n}\n\n// 导出所有API函数\nexport const chatApi = {\n  getChatSessions,\n  createChatSession,\n  getChatSessionById,\n  updateChatSession,\n  deleteChatSession,\n  getChatMessages,\n  sendChatMessage,\n  deleteChatMessage,\n  clearChatMessages,\n  getChatSessionStats,\n  exportChatSession,\n  batchDeleteChatSessions,\n  searchChatMessages,\n  getUserChatSessions,\n  copyChatSession,\n  updateChatSessionStatus,\n};\n", "import { httpRequest } from '@/utils/request';\nimport { getApiPrefix } from '@/utils/api';\nimport { ResponseHandleResult } from '@/types/response';\n\nconst API_PREFIX: string = getApiPrefix();\n\n/**\n * 插件管理相关API\n */\n\n// 获取所有插件列表\nexport async function getPlugins(): Promise<ResponseHandleResult<RwxAI.Plugin[]>> {\n  return httpRequest.get<RwxAI.Plugin[]>(`${API_PREFIX}/Plugins`, {\n    showErrorNotification: true,\n  });\n}\n\n// 创建新插件\nexport async function createPlugin(data: RwxAI.Plugin): Promise<ResponseHandleResult<RwxAI.Plugin>> {\n  return httpRequest.post<RwxAI.Plugin>(`${API_PREFIX}/Plugins`, data, {\n    showSuccessMessage: true,\n    showErrorNotification: true,\n    successMessage: '创建插件成功',\n  });\n}\n\n// 根据ID获取指定插件\nexport async function getPluginById(id: string): Promise<ResponseHandleResult<RwxAI.Plugin>> {\n  return httpRequest.get<RwxAI.Plugin>(`${API_PREFIX}/Plugins/${id}`, {\n    showErrorNotification: true,\n  });\n}\n\n// 更新插件信息\nexport async function updatePlugin(id: string, data: RwxAI.Plugin): Promise<ResponseHandleResult<RwxAI.Plugin>> {\n  return httpRequest.put<RwxAI.Plugin>(`${API_PREFIX}/Plugins/${id}`, data, {\n    showSuccessMessage: true,\n    showErrorNotification: true,\n    successMessage: '更新插件成功',\n  });\n}\n\n// 删除指定插件\nexport async function deletePlugin(id: string): Promise<ResponseHandleResult<void>> {\n  return httpRequest.delete<void>(`${API_PREFIX}/Plugins/${id}`, {\n    showSuccessMessage: true,\n    showErrorNotification: true,\n    successMessage: '删除插件成功',\n  });\n}\n\n// 启用/禁用插件\nexport async function togglePluginStatus(id: string, isEnabled: boolean): Promise<ResponseHandleResult<RwxAI.Plugin>> {\n  return httpRequest.patch<RwxAI.Plugin>(`${API_PREFIX}/Plugins/${id}/toggle`, { IsEnabled: isEnabled }, {\n    showSuccessMessage: true,\n    showErrorNotification: true,\n    successMessage: isEnabled ? '启用插件成功' : '禁用插件成功',\n  });\n}\n\n// 获取插件的所有功能\nexport async function getPluginFunctions(pluginId: string): Promise<ResponseHandleResult<RwxAI.PluginFunction[]>> {\n  return httpRequest.get<RwxAI.PluginFunction[]>(`${API_PREFIX}/Plugins/${pluginId}/functions`, {\n    showErrorNotification: true,\n  });\n}\n\n// 执行插件功能\nexport async function executePluginFunction(pluginId: string, functionName: string, parameters: any): Promise<ResponseHandleResult<any>> {\n  return httpRequest.post<any>(`${API_PREFIX}/Plugins/${pluginId}/functions/${functionName}/execute`, parameters, {\n    showSuccessMessage: true,\n    showErrorNotification: true,\n    successMessage: '插件功能执行成功',\n  });\n}\n", "/**\n * API响应类型定义\n */\n\n// HTTP状态码枚举\nexport enum HttpStatusCode {\n  OK = 200,\n  CREATED = 201,\n  NO_CONTENT = 204,\n  BAD_REQUEST = 400,\n  UNAUTHORIZED = 401,\n  FORBIDDEN = 403,\n  NOT_FOUND = 404,\n  METHOD_NOT_ALLOWED = 405,\n  CONFLICT = 409,\n  UNPROCESSABLE_ENTITY = 422,\n  TOO_MANY_REQUESTS = 429,\n  INTERNAL_SERVER_ERROR = 500,\n  BAD_GATEWAY = 502,\n  SERVICE_UNAVAILABLE = 503,\n  GATEWAY_TIMEOUT = 504,\n}\n\n// 错误类型枚举\nexport enum ErrorType {\n  NETWORK_ERROR = 'NETWORK_ERROR',\n  TIMEOUT_ERROR = 'TIMEOUT_ERROR',\n  VALIDATION_ERROR = 'VALIDATION_ERROR',\n  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',\n  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',\n  NOT_FOUND_ERROR = 'NOT_FOUND_ERROR',\n  SERVER_ERROR = 'SERVER_ERROR',\n  BUSINESS_ERROR = 'BUSINESS_ERROR',\n  UNKNOWN_ERROR = 'UNKNOWN_ERROR',\n}\n\n// 标准API响应结构\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  message?: string;\n  errorCode?: string;\n  errors?: ValidationError[];\n  timestamp?: string;\n  path?: string;\n}\n\n// 验证错误详情\nexport interface ValidationError {\n  field: string;\n  message: string;\n  code?: string;\n}\n\n// 错误响应详情\nexport interface ErrorResponse {\n  type: string;\n  title: string;\n  status: number;\n  detail?: string;\n  instance?: string;\n  errors?: Record<string, string[]>;\n  traceId?: string;\n}\n\n// 自定义错误类\nexport class ApiError extends Error {\n  public readonly status: number;\n  public readonly errorType: ErrorType;\n  public readonly errorCode?: string;\n  public readonly details?: any;\n  public readonly timestamp: string;\n\n  constructor(\n    message: string,\n    status: number,\n    errorType: ErrorType,\n    errorCode?: string,\n    details?: any\n  ) {\n    super(message);\n    this.name = 'ApiError';\n    this.status = status;\n    this.errorType = errorType;\n    this.errorCode = errorCode;\n    this.details = details;\n    this.timestamp = new Date().toISOString();\n  }\n}\n\n// 响应处理结果\nexport interface ResponseHandleResult<T = any> {\n  success: boolean;\n  data?: T;\n  error?: ApiError;\n  message?: string;\n}\n", "/**\n * API 工具函数\n */\n\n/**\n * 获取 API 基础路径\n * 开发环境使用代理，生产环境使用完整 URL\n */\nexport const getApiPrefix = (): string => {\n  // 开发环境使用代理，直接返回 /api\n  if (process.env.NODE_ENV === 'development') {\n    return '/api';\n  }\n  \n  // 生产环境使用环境变量配置的完整 URL + /api\n  const baseUrl = process.env.REACT_APP_API_BASE_URL || 'https://api.rwxai.com';\n  return `${baseUrl}/api`;\n};\n", "/**\r\n * 统一的分页数据处理工具\r\n * 用于处理后台返回的分页格式数据\r\n */\r\n\r\nimport { ResponseHandleResult } from '@/types/response';\r\n\r\n/**\r\n * API函数类型定义\r\n */\r\nexport type PagedApiFunction<T> = (params?: any) => Promise<ResponseHandleResult<BackendPagedResponse<T>>>;\r\nexport type LegacyApiFunction<T> = (params?: any) => Promise<ResponseHandleResult<BackendPagedResponse<T> | T[]>>;\r\nexport type SimpleApiFunction<T> = () => Promise<ResponseHandleResult<T[]>>;\r\n\r\n/**\r\n * ProTable请求参数接口\r\n */\r\nexport interface ProTableParams {\r\n  current?: number;\r\n  pageSize?: number;\r\n  [key: string]: any;\r\n}\r\n\r\n/**\r\n * ProTable期望的返回格式\r\n */\r\nexport interface ProTableResult<T> {\r\n  data: T[];\r\n  success: boolean;\r\n  total?: number;\r\n}\r\n\r\n/**\r\n * 后台分页响应格式\r\n */\r\nexport interface BackendPagedResponse<T> {\r\n  Items: T[];\r\n  TotalCount: number;\r\n  PageNumber: number;\r\n  PageSize: number;\r\n  TotalPages: number;\r\n  HasPreviousPage: boolean;\r\n  HasNextPage: boolean;\r\n}\r\n\r\n/**\r\n * 统一处理后台分页响应数据\r\n * @param response 后台API响应\r\n * @returns ProTable期望的数据格式\r\n */\r\nexport function handlePagedResponse<T>(\r\n  response: ResponseHandleResult<BackendPagedResponse<T>>\r\n): ProTableResult<T> {\r\n  if (!response.success || !response.data) {\r\n    return {\r\n      data: [],\r\n      success: response.success,\r\n      total: 0,\r\n    };\r\n  }\r\n\r\n  const data = response.data;\r\n\r\n  // 处理分页格式\r\n  if (isPagedResponse<T>(data)) {\r\n    return {\r\n      data: data.Items,\r\n      success: true,\r\n      total: data.TotalCount,\r\n    };\r\n  }\r\n\r\n  // 如果不是标准分页格式，返回空数据\r\n  return {\r\n    data: [],\r\n    success: false,\r\n    total: 0,\r\n  };\r\n}\r\n\r\n/**\r\n * 兼容处理后台分页响应数据（支持旧格式）\r\n * @param response 后台API响应\r\n * @returns ProTable期望的数据格式\r\n */\r\nexport function handleLegacyPagedResponse<T>(\r\n  response: ResponseHandleResult<BackendPagedResponse<T> | T[]>\r\n): ProTableResult<T> {\r\n  if (!response.success || !response.data) {\r\n    return {\r\n      data: [],\r\n      success: response.success,\r\n      total: 0,\r\n    };\r\n  }\r\n\r\n  const data = response.data as any;\r\n\r\n  // 检查是否是新的分页格式\r\n  if (isPagedResponse<T>(data)) {\r\n    return {\r\n      data: data.Items,\r\n      success: true,\r\n      total: data.TotalCount,\r\n    };\r\n  }\r\n\r\n  // 兼容旧格式（直接返回数组）\r\n  if (Array.isArray(data)) {\r\n    return {\r\n      data: data,\r\n      success: true,\r\n      total: data.length,\r\n    };\r\n  }\r\n\r\n  // 其他情况返回空数据\r\n  return {\r\n    data: [],\r\n    success: false,\r\n    total: 0,\r\n  };\r\n}\r\n\r\n/**\r\n * 构建分页查询参数（集成smartQuery功能）\r\n * @param params ProTable传入的参数\r\n * @param fieldMapping 字段映射关系\r\n * @param extra 额外参数\r\n * @returns 后台期望的查询参数\r\n */\r\nexport function buildPagedQuery(\r\n  params: ProTableParams,\r\n  fieldMapping: Record<string, string> = {},\r\n  extra?: Record<string, any>\r\n): Record<string, any> {\r\n  const query: Record<string, any> = {};\r\n\r\n  // 添加分页参数\r\n  query.PageNumber = params.current ?? 1;\r\n  query.PageSize = params.pageSize ?? 20;\r\n\r\n  // 添加关键词搜索\r\n  if (params.keyWord) {\r\n    query.SearchKeyword = params.keyWord as string;\r\n  }\r\n\r\n  // 字段映射：将列的 dataIndex 对应值映射到后端字段\r\n  if (fieldMapping) {\r\n    Object.keys(fieldMapping).forEach((from) => {\r\n      const to = fieldMapping[from];\r\n      const value = (params as any)[from];\r\n      if (value !== undefined && value !== null && value !== '') {\r\n        query[to] = value;\r\n      }\r\n    });\r\n  }\r\n\r\n  // 添加额外参数\r\n  if (extra) {\r\n    Object.assign(query, extra);\r\n  }\r\n\r\n  return query;\r\n}\r\n\r\n/**\r\n * 将查询对象转换为查询字符串\r\n * @param obj 查询对象\r\n * @returns 查询字符串\r\n */\r\nexport function toQueryString(obj: Record<string, any>): string {\r\n  const qs = new URLSearchParams();\r\n  Object.entries(obj).forEach(([k, v]) => {\r\n    if (v === undefined || v === null || v === '') return;\r\n    if (v instanceof Date) {\r\n      qs.append(k, v.toISOString());\r\n    } else {\r\n      qs.append(k, String(v));\r\n    }\r\n  });\r\n  return qs.toString();\r\n}\r\n\r\n/**\r\n * 创建标准的ProTable request函数（新分页格式）\r\n * @param apiFunction 后台API函数\r\n * @param fieldMapping 字段映射关系\r\n * @param extra 额外参数\r\n * @returns ProTable request函数\r\n */\r\nexport function createProTableRequest<T>(\r\n  apiFunction: PagedApiFunction<T>,\r\n  fieldMapping: Record<string, string> = {},\r\n  extra?: Record<string, any>\r\n) {\r\n  return async (params: ProTableParams): Promise<ProTableResult<T>> => {\r\n    const query = buildPagedQuery(params, fieldMapping, extra);\r\n    const response = await apiFunction(query);\r\n    return handlePagedResponse(response);\r\n  };\r\n}\r\n\r\n/**\r\n * 创建兼容的ProTable request函数（支持新旧格式）\r\n * @param apiFunction 后台API函数\r\n * @param fieldMapping 字段映射关系\r\n * @param extra 额外参数\r\n * @returns ProTable request函数\r\n */\r\nexport function createLegacyProTableRequest<T>(\r\n  apiFunction: LegacyApiFunction<T>,\r\n  fieldMapping: Record<string, string> = {},\r\n  extra?: Record<string, any>\r\n) {\r\n  return async (params: ProTableParams): Promise<ProTableResult<T>> => {\r\n    const query = buildPagedQuery(params, fieldMapping, extra);\r\n    const response = await apiFunction(query);\r\n    return handleLegacyPagedResponse(response);\r\n  };\r\n}\r\n\r\n/**\r\n * 创建简单的ProTable request函数（不带分页参数）\r\n * @param apiFunction 后台API函数\r\n * @returns ProTable request函数\r\n */\r\nexport function createSimpleProTableRequest<T>(\r\n  apiFunction: SimpleApiFunction<T>\r\n) {\r\n  return async (): Promise<ProTableResult<T>> => {\r\n    const response = await apiFunction();\r\n    return {\r\n      data: response.success ? (response.data || []) : [],\r\n      success: response.success,\r\n      total: response.data?.length || 0,\r\n    };\r\n  };\r\n}\r\n\r\n/**\r\n * 默认的分页配置\r\n */\r\nexport const defaultPaginationConfig = {\r\n  showSizeChanger: true,\r\n  pageSize: 20,\r\n  pageSizeOptions: ['10', '20', '50', '100'],\r\n  showQuickJumper: true,\r\n  showTotal: (total: number, range: [number, number]) => \r\n    `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,\r\n};\r\n\r\n/**\r\n * 检查响应是否为分页格式\r\n * @param data 响应数据\r\n * @returns 是否为分页格式\r\n */\r\nexport function isPagedResponse<T>(data: any): data is BackendPagedResponse<T> {\r\n  return data && \r\n         typeof data === 'object' && \r\n         Array.isArray(data.Items) && \r\n         typeof data.TotalCount === 'number' &&\r\n         typeof data.PageNumber === 'number' &&\r\n         typeof data.PageSize === 'number';\r\n}\r\n\r\n/**\r\n * 提取分页信息\r\n * @param data 分页响应数据\r\n * @returns 分页信息\r\n */\r\nexport function extractPaginationInfo<T>(data: BackendPagedResponse<T>) {\r\n  return {\r\n    current: data.PageNumber,\r\n    pageSize: data.PageSize,\r\n    total: data.TotalCount,\r\n    totalPages: data.TotalPages,\r\n    hasPrevious: data.HasPreviousPage,\r\n    hasNext: data.HasNextPage,\r\n  };\r\n}\r\n", "/**\n * 增强的请求工具\n * 集成统一的响应处理和错误处理\n */\n\n// import { request as umiRequest } from '@umijs/max';\nimport { getToken, getRefreshToken, setToken, setRefreshToken, logout, isTokenExpired } from './auth';\nimport { handleApiResponse, handleApiError } from './responseHandler';\nimport { ResponseHandleResult } from '@/types/response';\n\n// 请求选项接口\nexport interface RequestOptions {\n  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';\n  data?: any;\n  headers?: Record<string, string>;\n  showSuccessMessage?: boolean;\n  showErrorNotification?: boolean;\n  successMessage?: string;\n  skipAuth?: boolean;\n  timeout?: number;\n}\n\n// 增强的请求函数\nexport async function enhancedRequest<T = any>(\n  url: string,\n  options: RequestOptions = {}\n): Promise<ResponseHandleResult<T>> {\n  const {\n    method = 'GET',\n    data,\n    headers = {},\n    showSuccessMessage = false,\n    showErrorNotification = true,\n    successMessage,\n    skipAuth = false,\n    timeout = 30000,\n  } = options;\n\n  try {\n    // 添加JWT令牌\n    if (!skipAuth && !url.includes('/Auth/login') && !url.includes('/Auth/register')) {\n      const token = getToken();\n      if (token) {\n        headers.Authorization = `Bearer ${token}`;\n      }\n    }\n\n    // 发送请求\n    const response = await fetch(url, {\n      method,\n      headers: {\n        'Content-Type': 'application/json',\n        ...headers,\n      },\n      body: data ? JSON.stringify(data) : undefined,\n      signal: AbortSignal.timeout(timeout),\n    });\n\n    // 处理401错误 - 尝试刷新令牌\n    if (response.status === 401 && !skipAuth) {\n      const refreshResult = await tryRefreshToken();\n      if (refreshResult) {\n        // 重新发送请求\n        headers.Authorization = `Bearer ${refreshResult.token}`;\n        const retryResponse = await fetch(url, {\n          method,\n          headers: {\n            'Content-Type': 'application/json',\n            ...headers,\n          },\n          body: data ? JSON.stringify(data) : undefined,\n          signal: AbortSignal.timeout(timeout),\n        });\n\n        return handleApiResponse<T>(retryResponse, {\n          showSuccessMessage,\n          showErrorNotification,\n          successMessage,\n        });\n      }\n    }\n\n    // 处理响应\n    return handleApiResponse<T>(response, {\n      showSuccessMessage,\n      showErrorNotification,\n      successMessage,\n    });\n\n  } catch (error: any) {\n    // 处理网络错误或超时\n    const apiError = handleApiError(error, showErrorNotification);\n    return {\n      success: false,\n      error: apiError,\n      message: apiError.message,\n    };\n  }\n}\n\n// 尝试刷新令牌\nasync function tryRefreshToken(): Promise<{ token: string; refreshToken: string } | null> {\n  const refreshTokenValue = getRefreshToken();\n\n  if (!refreshTokenValue || isTokenExpired(refreshTokenValue)) {\n    logout();\n    return null;\n  }\n\n  try {\n    // 直接调用刷新令牌API，避免循环依赖\n    const response = await fetch('/api/Auth/refresh-token', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ RefreshToken: refreshTokenValue }),\n    });\n\n    if (response.ok) {\n      const refreshResult = await response.json();\n      if (refreshResult.token) {\n        setToken(refreshResult.token);\n        setRefreshToken(refreshResult.refreshToken);\n        return refreshResult;\n      }\n    }\n  } catch (error) {\n    console.error('刷新令牌失败:', error);\n    logout();\n  }\n\n  return null;\n}\n\n// 便捷的HTTP方法\nexport const httpRequest = {\n  get: <T = any>(url: string, options: Omit<RequestOptions, 'method'> = {}) =>\n    enhancedRequest<T>(url, { ...options, method: 'GET' }),\n\n  post: <T = any>(url: string, data?: any, options: Omit<RequestOptions, 'method' | 'data'> = {}) =>\n    enhancedRequest<T>(url, { ...options, method: 'POST', data }),\n\n  put: <T = any>(url: string, data?: any, options: Omit<RequestOptions, 'method' | 'data'> = {}) =>\n    enhancedRequest<T>(url, { ...options, method: 'PUT', data }),\n\n  delete: <T = any>(url: string, options: Omit<RequestOptions, 'method'> = {}) =>\n    enhancedRequest<T>(url, { ...options, method: 'DELETE' }),\n\n  patch: <T = any>(url: string, data?: any, options: Omit<RequestOptions, 'method' | 'data'> = {}) =>\n    enhancedRequest<T>(url, { ...options, method: 'PATCH', data }),\n};\n\n// 兼容原有的请求拦截器（用于UmiJS的request）\nexport const requestInterceptor = (url: string, options: any) => {\n  const token = getToken();\n\n  if (token && !url.includes('/Auth/login') && !url.includes('/Auth/register')) {\n    const headers = options.headers || {};\n    headers.Authorization = `Bearer ${token}`;\n    options.headers = headers;\n  }\n\n  return { url, options };\n};\n\nexport const responseInterceptor = async (response: Response) => {\n  if (response.status === 401) {\n    const refreshResult = await tryRefreshToken();\n    if (!refreshResult) {\n      logout();\n    }\n  }\n  return response;\n};\n\nexport const errorHandler = (error: any) => {\n  handleApiError(error, true);\n  throw error;\n};\n", "/**\n * 统一的API响应处理器\n */\n\nimport { message, notification } from 'antd';\nimport { \n  HttpStatusCode, \n  ErrorType, \n  ApiError, \n  ApiResponse, \n  ErrorResponse,\n  ResponseHandleResult \n} from '@/types/response';\nimport { logout } from './auth';\n\n// 错误消息映射\nconst ERROR_MESSAGES: Record<number, string> = {\n  [HttpStatusCode.BAD_REQUEST]: '请求参数错误',\n  [HttpStatusCode.UNAUTHORIZED]: '未授权，请重新登录',\n  [HttpStatusCode.FORBIDDEN]: '权限不足，无法访问',\n  [HttpStatusCode.NOT_FOUND]: '请求的资源不存在',\n  [HttpStatusCode.METHOD_NOT_ALLOWED]: '请求方法不被允许',\n  [HttpStatusCode.CONFLICT]: '请求冲突，资源已存在',\n  [HttpStatusCode.UNPROCESSABLE_ENTITY]: '请求数据验证失败',\n  [HttpStatusCode.TOO_MANY_REQUESTS]: '请求过于频繁，请稍后重试',\n  [HttpStatusCode.INTERNAL_SERVER_ERROR]: '服务器内部错误',\n  [HttpStatusCode.BAD_GATEWAY]: '网关错误',\n  [HttpStatusCode.SERVICE_UNAVAILABLE]: '服务暂时不可用',\n  [HttpStatusCode.GATEWAY_TIMEOUT]: '网关超时',\n};\n\n// 根据状态码获取错误类型\nfunction getErrorType(status: number): ErrorType {\n  switch (status) {\n    case HttpStatusCode.BAD_REQUEST:\n    case HttpStatusCode.UNPROCESSABLE_ENTITY:\n      return ErrorType.VALIDATION_ERROR;\n    case HttpStatusCode.UNAUTHORIZED:\n      return ErrorType.AUTHENTICATION_ERROR;\n    case HttpStatusCode.FORBIDDEN:\n      return ErrorType.AUTHORIZATION_ERROR;\n    case HttpStatusCode.NOT_FOUND:\n      return ErrorType.NOT_FOUND_ERROR;\n    case HttpStatusCode.INTERNAL_SERVER_ERROR:\n    case HttpStatusCode.BAD_GATEWAY:\n    case HttpStatusCode.SERVICE_UNAVAILABLE:\n    case HttpStatusCode.GATEWAY_TIMEOUT:\n      return ErrorType.SERVER_ERROR;\n    default:\n      return ErrorType.UNKNOWN_ERROR;\n  }\n}\n\n// 处理验证错误\nfunction handleValidationError(errorResponse: ErrorResponse): string {\n  if (errorResponse.errors) {\n    const errorMessages: string[] = [];\n    Object.entries(errorResponse.errors).forEach(([field, messages]) => {\n      messages.forEach(msg => {\n        errorMessages.push(`${field}: ${msg}`);\n      });\n    });\n    return errorMessages.join('; ');\n  }\n  return errorResponse.detail || errorResponse.title || '数据验证失败';\n}\n\n// 显示错误通知\nexport function showErrorNotification(error: ApiError, showNotification = true) {\n  const { errorType, message: errorMessage } = error;\n\n  // 根据错误类型决定显示方式\n  switch (errorType) {\n    case ErrorType.AUTHENTICATION_ERROR:\n      message.error('登录已过期，请重新登录');\n      // 自动跳转到登录页\n      setTimeout(() => logout(), 1000);\n      break;\n      \n    case ErrorType.AUTHORIZATION_ERROR:\n      message.error('权限不足，无法执行此操作');\n      break;\n      \n    case ErrorType.VALIDATION_ERROR:\n      if (showNotification) {\n        notification.error({\n          message: '数据验证失败',\n          description: errorMessage,\n          duration: 5,\n        });\n      } else {\n        message.error(errorMessage);\n      }\n      break;\n      \n    case ErrorType.NOT_FOUND_ERROR:\n      message.error('请求的资源不存在');\n      break;\n      \n    case ErrorType.SERVER_ERROR:\n      notification.error({\n        message: '服务器错误',\n        description: errorMessage,\n        duration: 8,\n      });\n      break;\n      \n    case ErrorType.NETWORK_ERROR:\n      notification.error({\n        message: '网络错误',\n        description: '请检查网络连接后重试',\n        duration: 5,\n      });\n      break;\n      \n    default:\n      message.error(errorMessage || '未知错误');\n      break;\n  }\n}\n\n// 处理成功响应\nfunction handleSuccessResponse<T>(_response: Response, data: T): ResponseHandleResult<T> {\n  return {\n    success: true,\n    data,\n    message: '操作成功',\n  };\n}\n\n// 处理错误响应\nasync function handleErrorResponse(response: Response): Promise<ResponseHandleResult> {\n  const status = response.status;\n  const errorType = getErrorType(status);\n  let errorMessage = ERROR_MESSAGES[status] || '未知错误';\n  let errorDetails: any = null;\n\n  try {\n    const responseText = await response.text();\n    if (responseText) {\n      const errorData = JSON.parse(responseText) as ErrorResponse;\n      \n      // 根据错误类型处理不同的错误格式\n      if (errorType === ErrorType.VALIDATION_ERROR) {\n        errorMessage = handleValidationError(errorData);\n      } else {\n        errorMessage = errorData.detail || errorData.title || errorMessage;\n      }\n      \n      errorDetails = errorData;\n    }\n  } catch (parseError) {\n    console.warn('Failed to parse error response:', parseError);\n  }\n\n  const apiError = new ApiError(\n    errorMessage,\n    status,\n    errorType,\n    errorDetails?.type,\n    errorDetails\n  );\n\n  return {\n    success: false,\n    error: apiError,\n    message: errorMessage,\n  };\n}\n\n// 主要的响应处理函数\nexport async function handleApiResponse<T = any>(\n  response: Response,\n  options: {\n    showSuccessMessage?: boolean;\n    showErrorNotification?: boolean;\n    successMessage?: string;\n  } = {}\n): Promise<ResponseHandleResult<T>> {\n  const {\n    showSuccessMessage = false,\n    showErrorNotification: shouldShowErrorNotification = true,\n    successMessage,\n  } = options;\n\n  try {\n    // 处理成功响应\n    if (response.ok) {\n      let data: T;\n      \n      // 处理不同的响应类型\n      const contentType = response.headers.get('content-type');\n      if (contentType?.includes('application/json')) {\n        const jsonData = await response.json();\n        // 如果是标准的API响应格式\n        if (typeof jsonData === 'object' && 'success' in jsonData) {\n          const apiResponse = jsonData as ApiResponse<T>;\n          data = apiResponse.data as T;\n          if (showSuccessMessage) {\n            message.success(successMessage || apiResponse.message || '操作成功');\n          }\n        } else {\n          data = jsonData;\n          if (showSuccessMessage) {\n            message.success(successMessage || '操作成功');\n          }\n        }\n      } else if (response.status === HttpStatusCode.NO_CONTENT) {\n        data = null as T;\n        if (showSuccessMessage) {\n          message.success(successMessage || '操作成功');\n        }\n      } else {\n        data = (await response.text()) as T;\n        if (showSuccessMessage) {\n          message.success(successMessage || '操作成功');\n        }\n      }\n\n      return handleSuccessResponse(response, data);\n    }\n\n    // 处理错误响应\n    const errorResult = await handleErrorResponse(response);\n    \n    if (errorResult.error && shouldShowErrorNotification) {\n      showErrorNotification(errorResult.error, true);\n    }\n\n    return errorResult;\n\n  } catch (error) {\n    // 处理网络错误或其他异常\n    const networkError = new ApiError(\n      '网络连接失败，请检查网络后重试',\n      0,\n      ErrorType.NETWORK_ERROR\n    );\n\n    if (shouldShowErrorNotification) {\n      showErrorNotification(networkError, false);\n    }\n\n    return {\n      success: false,\n      error: networkError,\n      message: networkError.message,\n    };\n  }\n}\n\n// 便捷的错误处理函数\nexport function handleApiError(error: any, showNotification = true): ApiError {\n  if (error instanceof ApiError) {\n    if (showNotification) {\n      showErrorNotification(error, true);\n    }\n    return error;\n  }\n\n  // 处理其他类型的错误\n  const apiError = new ApiError(\n    error.message || '未知错误',\n    error.status || 0,\n    ErrorType.UNKNOWN_ERROR\n  );\n\n  if (showNotification) {\n    showErrorNotification(apiError, false);\n  }\n\n  return apiError;\n}\n"], "names": [], "mappings": ";;;;;;;4BAuMA;;;eAAA;;;;;;0DAvM2C;6BAC8B;4BAC/B;8BACwB;;;;;;;;;;AASlE,MAAM,cAA0C,CAAC,EAC/C,OAAO,EACP,eAAe,EACf,aAAa,EACb,SAAS,EACV;;IACC,MAAM,OAAO,IAAA,YAAO;IACpB,MAAM,CAAC,KAAK,GAAG,UAAI,CAAC,OAAO;IAC3B,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,eAAQ,EAAkB,EAAE;IACxD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAuB;IAEzE,MAAM,SAAS,CAAC,EAAC,0BAAA,oCAAA,cAAe,EAAE;IAElC,IAAA,gBAAS,EAAC;QACR,IAAI,SAAS;YACX;YACA,IAAI,eAAe;gBACjB,KAAK,cAAc,CAAC;gBAEpB,IAAI,cAAc,OAAO,EAAE;oBACzB,MAAM,QAAQ,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,cAAc,OAAO;oBAC7D,iBAAiB,SAAS;gBAC5B;YACF,OAAO;gBACL,KAAK,WAAW;gBAChB,iBAAiB;YACnB;QACF;IACF,GAAG;QAAC;QAAS;QAAe;KAAO;IAEnC,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,IAAA,kBAAW;YAClC,QAAQ,GAAG,CAAC,2BAA2B;YAEvC,IAAI,SAAS,OAAO,EAAE;gBAEpB,MAAM,gBAAgB,AAAC,CAAA,SAAS,IAAI,IAAI,EAAE,AAAD,EAAG,MAAM,CAAC,CAAA;oBACjD,QAAQ,GAAG,CAAC,UAAU;oBACtB,OAAO,MAAM,SAAS,KAAK;gBAC7B;gBAEA,QAAQ,GAAG,CAAC,mBAAmB;gBAC/B,UAAU;YACZ,OAAO;gBACL,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,UAAU,EAAE;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,UAAU,EAAE;QACd;IACF;IAGA,MAAM,oBAAoB,CAAC;QACzB,MAAM,QAAQ,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACxC,iBAAiB,SAAS;QAE1B,IAAI,OAEF,KAAK,cAAc,CAAC;YAClB,aAAa,MAAM,WAAW,IAAI;YAClC,WAAW,MAAM,SAAS,IAAI;YAC9B,cAAc,MAAM,YAAY,IAAI;QACtC;IAEJ;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,SAAS,MAAM,KAAK,cAAc;YACxC,WAAW;YAEX,IAAI;YACJ,IAAI,QACF,WAAW,MAAM,IAAA,wBAAiB,EAAC,cAAe,EAAE,EAAE;gBAAE,GAAG,aAAa;gBAAE,GAAG,MAAM;YAAC;iBAEpF,WAAW,MAAM,IAAA,wBAAiB,EAAC;YAGrC,IAAI,SAAS,OAAO,EAClB;QAGJ,EAAE,OAAO,OAAO;YAEd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,WAAW;QACb;IACF;IAEA,OACE,2BAAC,WAAK;QACJ,OAAO,KAAK,aAAa,CAAC;YACxB,IAAI,SAAS,+BAA+B;QAC9C;QACA,MAAM;QACN,UAAU,IAAM,gBAAgB;QAChC,MAAM;QACN,gBAAgB;QAChB,OAAO;kBAEP,2BAAC,UAAI;YACH,MAAM;YACN,QAAO;YACP,eAAe;gBACb,aAAa;gBACb,WAAW;YACb;;gBAEA,2BAAC,UAAI,CAAC,IAAI;oBACR,MAAK;oBACL,OAAO,2BAAC,qBAAgB;wBAAC,IAAG;;;;;;oBAC5B,OAAO;wBAAC;4BAAE,UAAU;4BAAM,SAAS,KAAK,aAAa,CAAC;gCAAE,IAAI;4BAAgC;wBAAG;qBAAE;8BAEjG,2BAAC,WAAK;wBAAC,aAAa,KAAK,aAAa,CAAC;4BAAE,IAAI;wBAAmC;;;;;;;;;;;gBAGlF,2BAAC,UAAI,CAAC,IAAI;oBACR,MAAK;oBACL,OAAO,2BAAC,qBAAgB;wBAAC,IAAG;;;;;;oBAC5B,OAAO;wBAAC;4BAAE,UAAU;4BAAM,SAAS,KAAK,aAAa,CAAC;gCAAE,IAAI;4BAAiC;wBAAG;qBAAE;8BAElG,2BAAC,YAAM;wBACL,aAAa,KAAK,aAAa,CAAC;4BAAE,IAAI;wBAAoC;wBAC1E,UAAU;kCAET,OAAO,GAAG,CAAC,CAAC;gCAE2B;mCADtC,2BAAC,YAAM,CAAC,MAAM;gCAAgB,OAAO,MAAM,EAAE;;oCAC1C,MAAM,IAAI,IAAI,MAAM,WAAW;oCAAC;oCAAG,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,OAAO,KAAI,MAAM,OAAO,IAAI;oCAAO;;+BADrE,MAAM,EAAE;;;;;;;;;;;;;;;;gBAQjC,iBACC;;wBACE,2BAAC,UAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAO,2BAAC,qBAAgB;gCAAC,IAAG;;;;;;4BAC5B,MAAK;sCAEL,2BAAC,WAAK,CAAC,QAAQ;gCACb,MAAM;gCACN,QAAQ;gCACR,aAAY;;;;;;;;;;;wBAIhB,2BAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,KAAK;4BAAO;;gCACzC,2BAAC,UAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAO,2BAAC,qBAAgB;wCAAC,IAAG;;;;;;oCAC5B,OAAO;wCAAE,MAAM;oCAAE;8CAEjB,2BAAC,iBAAW;wCACV,QAAQ;wCACR,OAAO;4CAAE,OAAO;wCAAO;wCACvB,aAAY;;;;;;;;;;;gCAIhB,2BAAC,UAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAO,2BAAC,qBAAgB;wCAAC,IAAG;;;;;;oCAC5B,OAAO;wCAAE,MAAM;oCAAE;8CAEjB,2BAAC,iBAAW;wCACV,QAAQ;wCACR,OAAO;4CAAE,OAAO;wCAAO;wCACvB,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW9B;GAzLM;;QAMS,YAAO;QACL,UAAI,CAAC;;;KAPhB;IA2LN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvMf;;;;CAIC;;;;;;;;;;;IA2LY,WAAW;eAAX;;IAlHS,mBAAmB;eAAnB;;IApCA,aAAa;eAAb;;IAgLtB;;CAEC,GACD,OAA2B;eAA3B;;IA1JsB,aAAa;eAAb;;IAlCA,cAAc;eAAd;;IAwGA,sBAAsB;eAAtB;;IAdA,oCAAoC;eAApC;;IAPA,yBAAyB;eAAzB;;IA7FA,WAAW;eAAX;;IAyJA,mBAAmB;eAAnB;;IA5BA,qBAAqB;eAArB;;IAcA,uBAAuB;eAAvB;;IAPA,qBAAqB;eAArB;;IAcA,uBAAuB;eAAvB;;IAhFA,qBAAqB;eAArB;;IAWA,mBAAmB;eAAnB;;IA/CA,aAAa;eAAb;;;;;gCA9CM;wCACE;4BACD;;;;;;;;;AAG7B,MAAM,aAAqB,IAAA,iBAAY;AAWhC,eAAe,YAAY,MAAY;IAC5C,MAAM,MAAM,SAAS,CAAC,EAAE,WAAW,UAAU,EAAE,IAAA,8BAAa,EAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,WAAW,SAAS,CAAC;IACjG,OAAO,oBAAW,CAAC,GAAG,CAA4C,KAAK;QACrE,uBAAuB;IACzB;AACF;AAKO,eAAe,eAAe,EAAU;IAC7C,OAAO,oBAAW,CAAC,GAAG,CAAgB,CAAC,EAAE,WAAW,UAAU,EAAE,GAAG,CAAC,EAAE;QACpE,uBAAuB;IACzB;AACF;AAKO,eAAe,cAAc,IAAgC;IAClE,OAAO,oBAAW,CAAC,IAAI,CAAgB,CAAC,EAAE,WAAW,SAAS,CAAC,EAAE,MAAM;QACrE,oBAAoB;QACpB,uBAAuB;QACvB,gBAAgB;IAClB;AACF;AAKO,eAAe,cACpB,EAAU,EACV,IAAgC;IAEhC,OAAO,oBAAW,CAAC,GAAG,CAAgB,CAAC,EAAE,WAAW,UAAU,EAAE,GAAG,CAAC,EAAE,MAAM;QAC1E,oBAAoB;QACpB,uBAAuB;QACvB,gBAAgB;IAClB;AACF;AAKO,eAAe,cAAc,EAAU;IAC5C,OAAO,oBAAW,CAAC,MAAM,CAAO,CAAC,EAAE,WAAW,UAAU,EAAE,GAAG,CAAC,EAAE;QAC9D,oBAAoB;QACpB,uBAAuB;QACvB,gBAAgB;IAClB;AACF;AAKO,eAAe,oBAAoB,GAAa;IACrD,OAAO,oBAAW,CAAC,IAAI,CAAO,CAAC,EAAE,WAAW,sBAAsB,CAAC,EAAE;QAAE;IAAI,GAAG;QAC5E,oBAAoB;QACpB,uBAAuB;QACvB,gBAAgB,CAAC,KAAK,EAAE,IAAI,MAAM,CAAC,MAAM,CAAC;IAC5C;AACF;AAKO,eAAe,sBAAsB,EAAU;IACpD,OAAO,oBAAW,CAAC,IAAI,CAA6B,CAAC,EAAE,WAAW,UAAU,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG;QAC3F,oBAAoB;QACpB,uBAAuB;QACvB,gBAAgB;IAClB;AACF;AAKO,eAAe,oBACpB,EAAU,EACV,OAAgB;IAEhB,OAAO,oBAAW,CAAC,KAAK,CAAgB,CAAC,EAAE,WAAW,UAAU,EAAE,GAAG,OAAO,CAAC,EAAE;QAAE;IAAQ,GAAG;QAC1F,oBAAoB;QACpB,uBAAuB;QACvB,gBAAgB,UAAU,aAAa;IACzC;AACF;AAOO,eAAe,0BAA0B,SAA8B;IAC5E,OAAO,oBAAW,CAAC,GAAG,CAA0B,CAAC,EAAE,WAAW,0BAA0B,EAAE,UAAU,CAAC,EAAE;QACrG,uBAAuB;IACzB;AACF;AAGO,eAAe,qCAAqC,MAG1D;IACC,MAAM,cAAc,IAAI;IACxB,IAAI,OAAO,UAAU,EAAE,YAAY,MAAM,CAAC,cAAc,OAAO,UAAU;IACzE,IAAI,OAAO,SAAS,EAAE,YAAY,MAAM,CAAC,aAAa,OAAO,SAAS,CAAC,QAAQ;IAE/E,OAAO,oBAAW,CAAC,GAAG,CAA0B,CAAC,EAAE,WAAW,uCAAuC,EAAE,YAAY,CAAC,EAAE;QACpH,uBAAuB;IACzB;AACF;AAGO,eAAe,uBAAuB,EAAU;IACrD,OAAO,oBAAW,CAAC,GAAG,CAAwB,CAAC,EAAE,WAAW,kBAAkB,EAAE,GAAG,CAAC,EAAE;QACpF,uBAAuB;IACzB;AACF;AAOO,eAAe;IACpB,OAAO,oBAAW,CAAC,GAAG,CAAqB,CAAC,EAAE,WAAW,oBAAoB,CAAC,EAAE;QAC9E,uBAAuB;IACzB;AACF;AAGO,eAAe,sBAAsB,UAAkB;IAC5D,OAAO,oBAAW,CAAC,GAAG,CAA0B,CAAC,EAAE,WAAW,aAAa,EAAE,WAAW,OAAO,CAAC,EAAE;QAChG,uBAAuB;IACzB;AACF;AAGO,eAAe,wBAAwB,YAAoB;IAChE,OAAO,oBAAW,CAAC,GAAG,CAA0B,CAAC,EAAE,WAAW,qBAAqB,EAAE,aAAa,OAAO,CAAC,EAAE;QAC1G,uBAAuB;IACzB;AACF;AAGO,eAAe,wBAAwB,YAAoB;IAChE,OAAO,oBAAW,CAAC,GAAG,CAA0B,CAAC,EAAE,WAAW,kBAAkB,EAAE,aAAa,OAAO,CAAC,EAAE;QACvG,uBAAuB;IACzB;AACF;AAGO,eAAe;IACpB,OAAO,oBAAW,CAAC,GAAG,CAA0B,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE;QACtF,uBAAuB;IACzB;AACF;AAYO,MAAM,cAAc;IACzB,WAAW;IACX;IACA;IACA;IACA;IACA;IAEA,OAAO;IACP;IACA;IACA;IAEA,OAAO;IACP;IACA;IACA;IAEA,QAAQ;IACR;IACA;IACA;IACA;IACA;AACF;IAKA,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IC1MO,SAAS;eAAT;;IAgCA,SAAS;eAAT;;IAhBA,UAAU;eAAV;;IAvBA,OAAO;eAAP;;IAgBA,SAAS;eAAT;;IAgCA,iBAAiB;eAAjB;;IAkBA,aAAa;eAAb;;IApCA,SAAS;eAAT;;IA2BA,eAAe;eAAf;;;;;gCApEM;4BACC;;;;;;;;;AAG7B,MAAM,aAAqB,IAAA,iBAAY;AAOhC,eAAe;IACpB,OAAO,oBAAW,CAAC,GAAG,CAAc,CAAC,EAAE,WAAW,KAAK,CAAC,EAAE;QACxD,uBAAuB;IACzB;AACF;AAGO,eAAe,UAAU,IAAe;IAC7C,OAAO,oBAAW,CAAC,IAAI,CAAY,CAAC,EAAE,WAAW,KAAK,CAAC,EAAE,MAAM;QAC7D,oBAAoB;QACpB,uBAAuB;QACvB,gBAAgB;IAClB;AACF;AAGO,eAAe;IACpB,OAAO,oBAAW,CAAC,GAAG,CAAc,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE;QAC3D,uBAAuB;IACzB;AACF;AAGO,eAAe,WAAW,EAAU;IACzC,OAAO,oBAAW,CAAC,GAAG,CAAY,CAAC,EAAE,WAAW,MAAM,EAAE,GAAG,CAAC,EAAE;QAC5D,uBAAuB;IACzB;AACF;AAGO,eAAe,UAAU,EAAU,EAAE,IAAe;IACzD,OAAO,oBAAW,CAAC,GAAG,CAAY,CAAC,EAAE,WAAW,MAAM,EAAE,GAAG,CAAC,EAAE,MAAM;QAClE,oBAAoB;QACpB,uBAAuB;QACvB,gBAAgB;IAClB;AACF;AAGO,eAAe,UAAU,EAAU;IACxC,OAAO,oBAAW,CAAC,MAAM,CAAO,CAAC,EAAE,WAAW,MAAM,EAAE,GAAG,CAAC,EAAE;QAC1D,oBAAoB;QACpB,uBAAuB;QACvB,gBAAgB;IAClB;AACF;AAGO,eAAe,kBAAkB,EAAU;IAChD,OAAO,oBAAW,CAAC,IAAI,CAAY,CAAC,EAAE,WAAW,MAAM,EAAE,GAAG,gBAAgB,CAAC,EAAE,CAAC,GAAG;QACjF,oBAAoB;QACpB,uBAAuB;QACvB,gBAAgB;IAClB;AACF;AAGO,eAAe,gBAAgB,EAAU,EAAE,IAA4B;IAC5E,OAAO,oBAAW,CAAC,KAAK,CAAY,CAAC,EAAE,WAAW,MAAM,EAAE,GAAG,OAAO,CAAC,EAAE,MAAM;QAC3E,oBAAoB;QACpB,uBAAuB;QACvB,gBAAgB;IAClB;AACF;AAGO,eAAe,cAAc,EAAU;IAC5C,OAAO,oBAAW,CAAC,IAAI,CAAO,CAAC,EAAE,WAAW,MAAM,EAAE,GAAG,gBAAgB,CAAC,EAAE,CAAC,GAAG;QAC5E,oBAAoB;QACpB,uBAAuB;QACvB,gBAAgB;IAClB;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICpCsB,cAAc;eAAd;;IApCA,KAAK;eAAL;;IAwBA,YAAY;eAAZ;;IAXA,QAAQ;eAAR;;;;;gCAxBM;4BACC;;;;;;;;;AAG7B,MAAM,aAAqB,IAAA,iBAAY;AAOhC,eAAe,MAAM,IAAwB;IAMlD,OAAO,oBAAW,CAAC,IAAI,CAAC,CAAC,EAAE,WAAW,WAAW,CAAC,EAAE,MAAM;QACxD,UAAU;QACV,uBAAuB;IACzB;AACF;AAGO,eAAe,SAAS,IAA2B;IAIxD,OAAO,oBAAW,CAAC,IAAI,CAAC,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,MAAM;QAC3D,UAAU;QACV,uBAAuB;IACzB;AACF;AAGO,eAAe,aAAa,IAA+B;IAKhE,OAAO,oBAAW,CAAC,IAAI,CAAC,CAAC,EAAE,WAAW,mBAAmB,CAAC,EAAE,MAAM;QAChE,UAAU;QACV,uBAAuB;IACzB;AACF;AAGO,eAAe,eAAe,IAAiC;IAIpE,OAAO,oBAAW,CAAC,IAAI,CAAC,CAAC,EAAE,WAAW,qBAAqB,CAAC,EAAE,MAAM;QAClE,oBAAoB;QACpB,uBAAuB;QACvB,gBAAgB;IAClB;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICtCsB,iBAAiB;eAAjB;;IAyBA,iBAAiB;eAAjB;;IAhBA,kBAAkB;eAAlB;;IAhBA,eAAe;eAAf;;IAyCA,WAAW;eAAX;;IAOA,iBAAiB;eAAjB;;IAzBA,iBAAiB;eAAjB;;;;;gCAlCM;4BACC;;;;;;;;;AAG7B,MAAM,aAAqB,IAAA,iBAAY;AAOhC,eAAe;IACpB,OAAO,oBAAW,CAAC,GAAG,CAAsB,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE;QACzE,uBAAuB;IACzB;AACF;AAGO,eAAe,kBAAkB,IAAuB;IAC7D,OAAO,oBAAW,CAAC,IAAI,CAAoB,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,MAAM;QAC9E,oBAAoB;QACpB,uBAAuB;QACvB,gBAAgB;IAClB;AACF;AAGO,eAAe,mBAAmB,EAAU;IACjD,OAAO,oBAAW,CAAC,GAAG,CAAoB,CAAC,EAAE,WAAW,eAAe,EAAE,GAAG,CAAC,EAAE;QAC7E,uBAAuB;IACzB;AACF;AAGO,eAAe,kBAAkB,EAAU,EAAE,IAAuB;IACzE,OAAO,oBAAW,CAAC,GAAG,CAAoB,CAAC,EAAE,WAAW,eAAe,EAAE,GAAG,CAAC,EAAE,MAAM;QACnF,oBAAoB;QACpB,uBAAuB;QACvB,gBAAgB;IAClB;AACF;AAGO,eAAe,kBAAkB,EAAU;IAChD,OAAO,oBAAW,CAAC,MAAM,CAAO,CAAC,EAAE,WAAW,eAAe,EAAE,GAAG,CAAC,EAAE;QACnE,oBAAoB;QACpB,uBAAuB;QACvB,gBAAgB;IAClB;AACF;AAGO,eAAe,YAAY,SAAiB,EAAE,IAA8B;IACjF,OAAO,oBAAW,CAAC,IAAI,CAAoB,CAAC,EAAE,WAAW,eAAe,EAAE,UAAU,SAAS,CAAC,EAAE,MAAM;QACpG,uBAAuB;IACzB;AACF;AAGO,eAAe,kBAAkB,SAAiB,EAAE,IAA8B;IACvF,OAAO,oBAAW,CAAC,IAAI,CAAC,CAAC,EAAE,WAAW,eAAe,EAAE,UAAU,gBAAgB,CAAC,EAAE,MAAM;QACxF,uBAAuB;IACzB;AACF;;;;;;;;;;;;;;;;;;;;;;;;;AC/DA,YAAY;;;;;4BASH;;;eAAA,qBAAO;;;;;;gCARF;gCACA;gCACA;gCACA;gCACA;gCACA;qCAGU;gCACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAEd,iEAAiE;;;;;;;;;;;;;;;ICM3C,mBAAmB;eAAnB;;IAyBA,mBAAmB;eAAnB;;IAoDA,mBAAmB;eAAnB;;IApEA,oBAAoB;eAApB;;IAhBA,iBAAiB;eAAjB;;IAyCA,iBAAiB;eAAjB;;IAkCA,oBAAoB;eAApB;;IApDA,mBAAmB;eAAnB;;IAyBA,mBAAmB;eAAnB;;;;;gCA3DM;4BACC;;;;;;;;;AAG7B,MAAM,aAAqB,IAAA,iBAAY;AAOhC,eAAe;IACpB,OAAO,oBAAW,CAAC,GAAG,CAAoB,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE;QACnE,uBAAuB;IACzB;AACF;AAGO,eAAe,oBAAoB,IAAqB;IAC7D,OAAO,oBAAW,CAAC,IAAI,CAAkB,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,MAAM;QACxE,oBAAoB;QACpB,uBAAuB;QACvB,gBAAgB;IAClB;AACF;AAGO,eAAe,qBAAqB,EAAU;IACnD,OAAO,oBAAW,CAAC,GAAG,CAAkB,CAAC,EAAE,WAAW,WAAW,EAAE,GAAG,CAAC,EAAE;QACvE,uBAAuB;IACzB;AACF;AAGO,eAAe,oBAAoB,EAAU,EAAE,IAAqB;IACzE,OAAO,oBAAW,CAAC,GAAG,CAAkB,CAAC,EAAE,WAAW,WAAW,EAAE,GAAG,CAAC,EAAE,MAAM;QAC7E,oBAAoB;QACpB,uBAAuB;QACvB,gBAAgB;IAClB;AACF;AAGO,eAAe,oBAAoB,EAAU;IAClD,OAAO,oBAAW,CAAC,MAAM,CAAO,CAAC,EAAE,WAAW,WAAW,EAAE,GAAG,CAAC,EAAE;QAC/D,oBAAoB;QACpB,uBAAuB;QACvB,gBAAgB;IAClB;AACF;AAGO,eAAe,kBAAkB,WAAmB;IACzD,OAAO,oBAAW,CAAC,GAAG,CAAwB,CAAC,EAAE,WAAW,WAAW,EAAE,YAAY,MAAM,CAAC,EAAE;QAC5F,uBAAuB;IACzB;AACF;AAGO,eAAe,oBAAoB,WAAmB,EAAE,IAAU;IACvE,MAAM,WAAW,IAAI;IACrB,SAAS,MAAM,CAAC,QAAQ;IAExB,2CAA2C;IAC3C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,CAAC,EAAE,WAAW,WAAW,EAAE,YAAY,MAAM,CAAC,EAAE;YAC3E,QAAQ;YACR,MAAM;YACN,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,eAAe,CAAC;YAClE;QACF;QAEA,IAAI,SAAS,EAAE,EAAE;YACf,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;gBAAE,SAAS;gBAAM;gBAAM,SAAS;YAAS;QAClD,OAAO;YACL,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAO,CAAA,CAAC,CAAA;YACtD,OAAO;gBAAE,SAAS;gBAAO,SAAS,UAAU,OAAO,IAAI;YAAS;QAClE;IACF,EAAE,OAAO,OAAY;QACnB,OAAO;YAAE,SAAS;YAAO,SAAS,MAAM,OAAO,IAAI;QAAS;IAC9D;AACF;AAGO,eAAe,qBAAqB,MAAc;IACvD,OAAO,oBAAW,CAAC,IAAI,CAAO,CAAC,EAAE,WAAW,iBAAiB,EAAE,OAAO,QAAQ,CAAC,EAAE,CAAC,GAAG;QACnF,oBAAoB;QACpB,uBAAuB;QACvB,gBAAgB;IAClB;AACF;AAGO,eAAe,oBAAoB,MAAc;IACtD,OAAO,oBAAW,CAAC,MAAM,CAAO,CAAC,EAAE,WAAW,iBAAiB,EAAE,OAAO,CAAC,EAAE;QACzE,oBAAoB;QACpB,uBAAuB;QACvB,gBAAgB;IAClB;AACF;;;;;;;;;;;;;;;;;;;;;;;;;ACrGA;;CAEC;;;;;;;;;;;IAsGqB,uBAAuB;eAAvB;;IAoET,OAAO;eAAP;;IA7FS,iBAAiB;eAAjB;;IA2EA,eAAe;eAAf;;IApIA,iBAAiB;eAAjB;;IAgDA,iBAAiB;eAAjB;;IAvBA,iBAAiB;eAAjB;;IAgDA,iBAAiB;eAAjB;;IAvCA,eAAe;eAAf;;IAzBA,kBAAkB;eAAlB;;IAyDA,mBAAmB;eAAnB;;IAzEA,eAAe;eAAf;;IAoHA,mBAAmB;eAAnB;;IAlBA,kBAAkB;eAAlB;;IAlDA,eAAe;eAAf;;IAzBA,iBAAiB;eAAjB;;IA6HA,uBAAuB;eAAvB;;;;;gCA/JM;4BACC;;;;;;;;;AAG7B,MAAM,aAAqB,IAAA,iBAAY;AAOhC,eAAe;IACpB,OAAO,oBAAW,CAAC,GAAG,CAAsB,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE;QACzE,uBAAuB;IACzB;AACF;AAGO,eAAe,kBAAkB,IAAuB;IAC7D,OAAO,oBAAW,CAAC,IAAI,CAAoB,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,MAAM;QAC9E,oBAAoB;QACpB,uBAAuB;QACvB,gBAAgB;IAClB;AACF;AAGO,eAAe,mBAAmB,EAAU;IACjD,OAAO,oBAAW,CAAC,GAAG,CAAoB,CAAC,EAAE,WAAW,eAAe,EAAE,GAAG,CAAC,EAAE;QAC7E,uBAAuB;IACzB;AACF;AAGO,eAAe,kBAAkB,EAAU,EAAE,IAAuB;IACzE,OAAO,oBAAW,CAAC,GAAG,CAAoB,CAAC,EAAE,WAAW,eAAe,EAAE,GAAG,CAAC,EAAE,MAAM;QACnF,oBAAoB;QACpB,uBAAuB;QACvB,gBAAgB;IAClB;AACF;AAGO,eAAe,kBAAkB,EAAU;IAChD,OAAO,oBAAW,CAAC,MAAM,CAAO,CAAC,EAAE,WAAW,eAAe,EAAE,GAAG,CAAC,EAAE;QACnE,oBAAoB;QACpB,uBAAuB;QACvB,gBAAgB;IAClB;AACF;AAGO,eAAe,gBAAgB,SAAiB;IACrD,OAAO,oBAAW,CAAC,GAAG,CAAsB,CAAC,EAAE,WAAW,eAAe,EAAE,UAAU,SAAS,CAAC,EAAE;QAC/F,uBAAuB;IACzB;AACF;AAGO,eAAe,gBAAgB,SAAiB,EAAE,IAA8B;IACrF,OAAO,oBAAW,CAAC,IAAI,CAAoB,CAAC,EAAE,WAAW,eAAe,EAAE,UAAU,SAAS,CAAC,EAAE,MAAM;QACpG,uBAAuB;IACzB;AACF;AAGO,eAAe,kBAAkB,SAAiB,EAAE,SAAiB;IAC1E,OAAO,oBAAW,CAAC,MAAM,CAAO,CAAC,EAAE,WAAW,eAAe,EAAE,UAAU,UAAU,EAAE,UAAU,CAAC,EAAE;QAChG,oBAAoB;QACpB,uBAAuB;QACvB,gBAAgB;IAClB;AACF;AAGO,eAAe,kBAAkB,SAAiB;IACvD,OAAO,oBAAW,CAAC,MAAM,CAAO,CAAC,EAAE,WAAW,eAAe,EAAE,UAAU,SAAS,CAAC,EAAE;QACnF,oBAAoB;QACpB,uBAAuB;QACvB,gBAAgB;IAClB;AACF;AAGO,eAAe,oBAAoB,SAAiB;IACzD,OAAO,oBAAW,CAAC,GAAG,CAAyB,CAAC,EAAE,WAAW,eAAe,EAAE,UAAU,MAAM,CAAC,EAAE;QAC/F,uBAAuB;IACzB;AACF;AAGO,eAAe,kBAAkB,SAAiB,EAAE,MAA6B;IACtF,OAAO,oBAAW,CAAC,GAAG,CAAO,CAAC,EAAE,WAAW,eAAe,EAAE,UAAU,eAAe,EAAE,OAAO,CAAC,EAAE;QAC/F,oBAAoB;QACpB,uBAAuB;QACvB,gBAAgB;IAClB;AACF;AAGO,eAAe,wBAAwB,UAAoB;IAChE,OAAO,oBAAW,CAAC,IAAI,CAAO,CAAC,EAAE,WAAW,2BAA2B,CAAC,EAAE;QAAE;IAAW,GAAG;QACxF,oBAAoB;QACpB,uBAAuB;QACvB,gBAAgB,CAAC,KAAK,EAAE,WAAW,MAAM,CAAC,MAAM,CAAC;IACnD;AACF;AAGO,eAAe,mBAAmB,MAKxC;IACC,MAAM,cAAc,IAAI;IACxB,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK;IACxC,IAAI,OAAO,SAAS,EAAE,YAAY,MAAM,CAAC,aAAa,OAAO,SAAS;IACtE,IAAI,OAAO,KAAK,EAAE,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;IACnE,IAAI,OAAO,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM,CAAC,QAAQ;IAEtE,OAAO,oBAAW,CAAC,GAAG,CAAsB,CAAC,EAAE,WAAW,sBAAsB,EAAE,YAAY,CAAC,EAAE;QAC/F,uBAAuB;IACzB;AACF;AAGO,eAAe,oBAAoB,MAKzC;IAMC,MAAM,cAAc,IAAI;IACxB,IAAI,OAAO,IAAI,EAAE,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;IAChE,IAAI,OAAO,QAAQ,EAAE,YAAY,MAAM,CAAC,YAAY,OAAO,QAAQ,CAAC,QAAQ;IAC5E,IAAI,OAAO,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;IAC7D,IAAI,OAAO,SAAS,EAAE,YAAY,MAAM,CAAC,aAAa,OAAO,SAAS;IAEtE,OAAO,oBAAW,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,oBAAoB,EAAE,YAAY,CAAC,EAAE;QACxE,uBAAuB;IACzB;AACF;AAGO,eAAe,gBAAgB,SAAiB,EAAE,OAAgB;IACvE,OAAO,oBAAW,CAAC,IAAI,CAAoB,CAAC,EAAE,WAAW,eAAe,EAAE,UAAU,KAAK,CAAC,EAAE;QAAE;IAAQ,GAAG;QACvG,oBAAoB;QACpB,uBAAuB;QACvB,gBAAgB;IAClB;AACF;AAGO,eAAe,wBAAwB,SAAiB,EAAE,QAAiB;IAChF,OAAO,oBAAW,CAAC,KAAK,CAAoB,CAAC,EAAE,WAAW,eAAe,EAAE,UAAU,OAAO,CAAC,EAAE;QAAE;IAAS,GAAG;QAC3G,oBAAoB;QACpB,uBAAuB;QACvB,gBAAgB,WAAW,aAAa;IAC1C;AACF;AAGO,MAAM,UAAU;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IC3KsB,YAAY;eAAZ;;IAyBA,YAAY;eAAZ;;IAyBA,qBAAqB;eAArB;;IAzCA,aAAa;eAAb;;IAkCA,kBAAkB;eAAlB;;IAlDA,UAAU;eAAV;;IAyCA,kBAAkB;eAAlB;;IAlBA,YAAY;eAAZ;;;;;gCAlCM;4BACC;;;;;;;;;AAG7B,MAAM,aAAqB,IAAA,iBAAY;AAOhC,eAAe;IACpB,OAAO,oBAAW,CAAC,GAAG,CAAiB,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE;QAC9D,uBAAuB;IACzB;AACF;AAGO,eAAe,aAAa,IAAkB;IACnD,OAAO,oBAAW,CAAC,IAAI,CAAe,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,MAAM;QACnE,oBAAoB;QACpB,uBAAuB;QACvB,gBAAgB;IAClB;AACF;AAGO,eAAe,cAAc,EAAU;IAC5C,OAAO,oBAAW,CAAC,GAAG,CAAe,CAAC,EAAE,WAAW,SAAS,EAAE,GAAG,CAAC,EAAE;QAClE,uBAAuB;IACzB;AACF;AAGO,eAAe,aAAa,EAAU,EAAE,IAAkB;IAC/D,OAAO,oBAAW,CAAC,GAAG,CAAe,CAAC,EAAE,WAAW,SAAS,EAAE,GAAG,CAAC,EAAE,MAAM;QACxE,oBAAoB;QACpB,uBAAuB;QACvB,gBAAgB;IAClB;AACF;AAGO,eAAe,aAAa,EAAU;IAC3C,OAAO,oBAAW,CAAC,MAAM,CAAO,CAAC,EAAE,WAAW,SAAS,EAAE,GAAG,CAAC,EAAE;QAC7D,oBAAoB;QACpB,uBAAuB;QACvB,gBAAgB;IAClB;AACF;AAGO,eAAe,mBAAmB,EAAU,EAAE,SAAkB;IACrE,OAAO,oBAAW,CAAC,KAAK,CAAe,CAAC,EAAE,WAAW,SAAS,EAAE,GAAG,OAAO,CAAC,EAAE;QAAE,WAAW;IAAU,GAAG;QACrG,oBAAoB;QACpB,uBAAuB;QACvB,gBAAgB,YAAY,WAAW;IACzC;AACF;AAGO,eAAe,mBAAmB,QAAgB;IACvD,OAAO,oBAAW,CAAC,GAAG,CAAyB,CAAC,EAAE,WAAW,SAAS,EAAE,SAAS,UAAU,CAAC,EAAE;QAC5F,uBAAuB;IACzB;AACF;AAGO,eAAe,sBAAsB,QAAgB,EAAE,YAAoB,EAAE,UAAe;IACjG,OAAO,oBAAW,CAAC,IAAI,CAAM,CAAC,EAAE,WAAW,SAAS,EAAE,SAAS,WAAW,EAAE,aAAa,QAAQ,CAAC,EAAE,YAAY;QAC9G,oBAAoB;QACpB,uBAAuB;QACvB,gBAAgB;IAClB;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1EA;;CAEC,GAED,YAAY;;;;;;;;;;;;IA8DC,QAAQ;eAAR;;;;;;;;;;;;;;;;;;;;UA7DD;;;;;;;;;;;;;;;;GAAA,mBAAA;;UAmBA;;;;;;;;;;GAAA,cAAA;AA0CL,MAAM,iBAAiB;IACZ,OAAe;IACf,UAAqB;IACrB,UAAmB;IACnB,QAAc;IACd,UAAkB;IAElC,YACE,OAAe,EACf,MAAc,EACd,SAAoB,EACpB,SAAkB,EAClB,OAAa,CACb;QACA,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,WAAW;IACzC;AACF;;;;;;;;;;;;;;;;;;;;;;;;;ACxFA;;CAEC,GAED;;;CAGC;;;;4BACY;;;eAAA;;;;;MAOK;;;;;;;;;AAPX,MAAM,eAAe;IAGxB,OAAO;AAMX;;;;;;;;;;;;;;;;;;;;;;;;;ACjBA;;;CAGC;;;;;;;;;;;IAgIe,eAAe;eAAf;;IA+EA,2BAA2B;eAA3B;;IAnBA,qBAAqB;eAArB;;IAoCA,2BAA2B;eAA3B;;IAgBH,uBAAuB;eAAvB;;IA4BG,qBAAqB;eAArB;;IA1LA,yBAAyB;eAAzB;;IAnCA,mBAAmB;eAAnB;;IA+MA,eAAe;eAAf;;IAtFA,aAAa;eAAb;;;;;;;;;;;;;AAzHT,SAAS,oBACd,QAAuD;IAEvD,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,SAAS,IAAI,EACrC,OAAO;QACL,MAAM,EAAE;QACR,SAAS,SAAS,OAAO;QACzB,OAAO;IACT;IAGF,MAAM,OAAO,SAAS,IAAI;IAE1B,SAAS;IACT,IAAI,gBAAmB,OACrB,OAAO;QACL,MAAM,KAAK,KAAK;QAChB,SAAS;QACT,OAAO,KAAK,UAAU;IACxB;IAGF,mBAAmB;IACnB,OAAO;QACL,MAAM,EAAE;QACR,SAAS;QACT,OAAO;IACT;AACF;AAOO,SAAS,0BACd,QAA6D;IAE7D,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,SAAS,IAAI,EACrC,OAAO;QACL,MAAM,EAAE;QACR,SAAS,SAAS,OAAO;QACzB,OAAO;IACT;IAGF,MAAM,OAAO,SAAS,IAAI;IAE1B,cAAc;IACd,IAAI,gBAAmB,OACrB,OAAO;QACL,MAAM,KAAK,KAAK;QAChB,SAAS;QACT,OAAO,KAAK,UAAU;IACxB;IAGF,gBAAgB;IAChB,IAAI,MAAM,OAAO,CAAC,OAChB,OAAO;QACL,MAAM;QACN,SAAS;QACT,OAAO,KAAK,MAAM;IACpB;IAGF,YAAY;IACZ,OAAO;QACL,MAAM,EAAE;QACR,SAAS;QACT,OAAO;IACT;AACF;AASO,SAAS,gBACd,MAAsB,EACtB,eAAuC,CAAC,CAAC,EACzC,KAA2B;IAE3B,MAAM,QAA6B,CAAC;IAEpC,SAAS;IACT,MAAM,UAAU,GAAG,OAAO,OAAO,IAAI;IACrC,MAAM,QAAQ,GAAG,OAAO,QAAQ,IAAI;IAEpC,UAAU;IACV,IAAI,OAAO,OAAO,EAChB,MAAM,aAAa,GAAG,OAAO,OAAO;IAGtC,gCAAgC;IAChC,IAAI,cACF,OAAO,IAAI,CAAC,cAAc,OAAO,CAAC,CAAC;QACjC,MAAM,KAAK,YAAY,CAAC,KAAK;QAC7B,MAAM,QAAQ,AAAC,MAAc,CAAC,KAAK;QACnC,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IACrD,KAAK,CAAC,GAAG,GAAG;IAEhB;IAGF,SAAS;IACT,IAAI,OACF,OAAO,MAAM,CAAC,OAAO;IAGvB,OAAO;AACT;AAOO,SAAS,cAAc,GAAwB;IACpD,MAAM,KAAK,IAAI;IACf,OAAO,OAAO,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE;QACjC,IAAI,MAAM,aAAa,MAAM,QAAQ,MAAM,IAAI;QAC/C,IAAI,aAAa,MACf,GAAG,MAAM,CAAC,GAAG,EAAE,WAAW;aAE1B,GAAG,MAAM,CAAC,GAAG,OAAO;IAExB;IACA,OAAO,GAAG,QAAQ;AACpB;AASO,SAAS,sBACd,WAAgC,EAChC,eAAuC,CAAC,CAAC,EACzC,KAA2B;IAE3B,OAAO,OAAO;QACZ,MAAM,QAAQ,gBAAgB,QAAQ,cAAc;QACpD,MAAM,WAAW,MAAM,YAAY;QACnC,OAAO,oBAAoB;IAC7B;AACF;AASO,SAAS,4BACd,WAAiC,EACjC,eAAuC,CAAC,CAAC,EACzC,KAA2B;IAE3B,OAAO,OAAO;QACZ,MAAM,QAAQ,gBAAgB,QAAQ,cAAc;QACpD,MAAM,WAAW,MAAM,YAAY;QACnC,OAAO,0BAA0B;IACnC;AACF;AAOO,SAAS,4BACd,WAAiC;IAEjC,OAAO;YAKI;QAJT,MAAM,WAAW,MAAM;QACvB,OAAO;YACL,MAAM,SAAS,OAAO,GAAI,SAAS,IAAI,IAAI,EAAE,GAAI,EAAE;YACnD,SAAS,SAAS,OAAO;YACzB,OAAO,EAAA,iBAAA,SAAS,IAAI,cAAb,qCAAA,eAAe,MAAM,KAAI;QAClC;IACF;AACF;AAKO,MAAM,0BAA0B;IACrC,iBAAiB;IACjB,UAAU;IACV,iBAAiB;QAAC;QAAM;QAAM;QAAM;KAAM;IAC1C,iBAAiB;IACjB,WAAW,CAAC,OAAe,QACzB,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC;AAC/C;AAOO,SAAS,gBAAmB,IAAS;IAC1C,OAAO,QACA,OAAO,SAAS,YAChB,MAAM,OAAO,CAAC,KAAK,KAAK,KACxB,OAAO,KAAK,UAAU,KAAK,YAC3B,OAAO,KAAK,UAAU,KAAK,YAC3B,OAAO,KAAK,QAAQ,KAAK;AAClC;AAOO,SAAS,sBAAyB,IAA6B;IACpE,OAAO;QACL,SAAS,KAAK,UAAU;QACxB,UAAU,KAAK,QAAQ;QACvB,OAAO,KAAK,UAAU;QACtB,YAAY,KAAK,UAAU;QAC3B,aAAa,KAAK,eAAe;QACjC,SAAS,KAAK,WAAW;IAC3B;AACF;;;;;;;;;;;;;;;;;;;;;;;;;ACxRA;;;CAGC,GAED,sDAAsD;;;;;;;;;;;;IAkBhC,eAAe;eAAf;;IAyJT,YAAY;eAAZ;;IAxCA,WAAW;eAAX;;IAkBA,kBAAkB;eAAlB;;IAYA,mBAAmB;eAAnB;;;;;6BAhKgF;wCAC3C;;;;;;;;;AAgB3C,eAAe,gBACpB,GAAW,EACX,UAA0B,CAAC,CAAC;IAE5B,MAAM,EACJ,SAAS,KAAK,EACd,IAAI,EACJ,UAAU,CAAC,CAAC,EACZ,qBAAqB,KAAK,EAC1B,wBAAwB,IAAI,EAC5B,cAAc,EACd,WAAW,KAAK,EAChB,UAAU,KAAK,EAChB,GAAG;IAEJ,IAAI;QACF,UAAU;QACV,IAAI,CAAC,YAAY,CAAC,IAAI,QAAQ,CAAC,kBAAkB,CAAC,IAAI,QAAQ,CAAC,mBAAmB;YAChF,MAAM,QAAQ,IAAA,cAAQ;YACtB,IAAI,OACF,QAAQ,aAAa,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC;QAE7C;QAEA,OAAO;QACP,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC;YACA,SAAS;gBACP,gBAAgB;gBAChB,GAAG,OAAO;YACZ;YACA,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;YACpC,QAAQ,YAAY,OAAO,CAAC;QAC9B;QAEA,mBAAmB;QACnB,IAAI,SAAS,MAAM,KAAK,OAAO,CAAC,UAAU;YACxC,MAAM,gBAAgB,MAAM;YAC5B,IAAI,eAAe;gBACjB,SAAS;gBACT,QAAQ,aAAa,GAAG,CAAC,OAAO,EAAE,cAAc,KAAK,CAAC,CAAC;gBACvD,MAAM,gBAAgB,MAAM,MAAM,KAAK;oBACrC;oBACA,SAAS;wBACP,gBAAgB;wBAChB,GAAG,OAAO;oBACZ;oBACA,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;oBACpC,QAAQ,YAAY,OAAO,CAAC;gBAC9B;gBAEA,OAAO,IAAA,kCAAiB,EAAI,eAAe;oBACzC;oBACA;oBACA;gBACF;YACF;QACF;QAEA,OAAO;QACP,OAAO,IAAA,kCAAiB,EAAI,UAAU;YACpC;YACA;YACA;QACF;IAEF,EAAE,OAAO,OAAY;QACnB,YAAY;QACZ,MAAM,WAAW,IAAA,+BAAc,EAAC,OAAO;QACvC,OAAO;YACL,SAAS;YACT,OAAO;YACP,SAAS,SAAS,OAAO;QAC3B;IACF;AACF;AAEA,SAAS;AACT,eAAe;IACb,MAAM,oBAAoB,IAAA,qBAAe;IAEzC,IAAI,CAAC,qBAAqB,IAAA,oBAAc,EAAC,oBAAoB;QAC3D,IAAA,YAAM;QACN,OAAO;IACT;IAEA,IAAI;QACF,qBAAqB;QACrB,MAAM,WAAW,MAAM,MAAM,2BAA2B;YACtD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE,cAAc;YAAkB;QACzD;QAEA,IAAI,SAAS,EAAE,EAAE;YACf,MAAM,gBAAgB,MAAM,SAAS,IAAI;YACzC,IAAI,cAAc,KAAK,EAAE;gBACvB,IAAA,cAAQ,EAAC,cAAc,KAAK;gBAC5B,IAAA,qBAAe,EAAC,cAAc,YAAY;gBAC1C,OAAO;YACT;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;QACzB,IAAA,YAAM;IACR;IAEA,OAAO;AACT;AAGO,MAAM,cAAc;IACzB,KAAK,CAAU,KAAa,UAA0C,CAAC,CAAC,GACtE,gBAAmB,KAAK;YAAE,GAAG,OAAO;YAAE,QAAQ;QAAM;IAEtD,MAAM,CAAU,KAAa,MAAY,UAAmD,CAAC,CAAC,GAC5F,gBAAmB,KAAK;YAAE,GAAG,OAAO;YAAE,QAAQ;YAAQ;QAAK;IAE7D,KAAK,CAAU,KAAa,MAAY,UAAmD,CAAC,CAAC,GAC3F,gBAAmB,KAAK;YAAE,GAAG,OAAO;YAAE,QAAQ;YAAO;QAAK;IAE5D,QAAQ,CAAU,KAAa,UAA0C,CAAC,CAAC,GACzE,gBAAmB,KAAK;YAAE,GAAG,OAAO;YAAE,QAAQ;QAAS;IAEzD,OAAO,CAAU,KAAa,MAAY,UAAmD,CAAC,CAAC,GAC7F,gBAAmB,KAAK;YAAE,GAAG,OAAO;YAAE,QAAQ;YAAS;QAAK;AAChE;AAGO,MAAM,qBAAqB,CAAC,KAAa;IAC9C,MAAM,QAAQ,IAAA,cAAQ;IAEtB,IAAI,SAAS,CAAC,IAAI,QAAQ,CAAC,kBAAkB,CAAC,IAAI,QAAQ,CAAC,mBAAmB;QAC5E,MAAM,UAAU,QAAQ,OAAO,IAAI,CAAC;QACpC,QAAQ,aAAa,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC;QACzC,QAAQ,OAAO,GAAG;IACpB;IAEA,OAAO;QAAE;QAAK;IAAQ;AACxB;AAEO,MAAM,sBAAsB,OAAO;IACxC,IAAI,SAAS,MAAM,KAAK,KAAK;QAC3B,MAAM,gBAAgB,MAAM;QAC5B,IAAI,CAAC,eACH,IAAA,YAAM;IAEV;IACA,OAAO;AACT;AAEO,MAAM,eAAe,CAAC;IAC3B,IAAA,+BAAc,EAAC,OAAO;IACtB,MAAM;AACR;;;;;;;;;;;;;;;;;;;;;;;;;ACnLA;;CAEC;;;;;;;;;;;IA0Pe,cAAc;eAAd;;IAjFM,iBAAiB;eAAjB;;IAvGN,qBAAqB;eAArB;;;;;6BAhEsB;iCAQ/B;6BACgB;;;;;;;;;AAEvB,SAAS;AACT,MAAM,iBAAyC;IAC7C,CAAC,wBAAc,CAAC,WAAW,CAAC,EAAE;IAC9B,CAAC,wBAAc,CAAC,YAAY,CAAC,EAAE;IAC/B,CAAC,wBAAc,CAAC,SAAS,CAAC,EAAE;IAC5B,CAAC,wBAAc,CAAC,SAAS,CAAC,EAAE;IAC5B,CAAC,wBAAc,CAAC,kBAAkB,CAAC,EAAE;IACrC,CAAC,wBAAc,CAAC,QAAQ,CAAC,EAAE;IAC3B,CAAC,wBAAc,CAAC,oBAAoB,CAAC,EAAE;IACvC,CAAC,wBAAc,CAAC,iBAAiB,CAAC,EAAE;IACpC,CAAC,wBAAc,CAAC,qBAAqB,CAAC,EAAE;IACxC,CAAC,wBAAc,CAAC,WAAW,CAAC,EAAE;IAC9B,CAAC,wBAAc,CAAC,mBAAmB,CAAC,EAAE;IACtC,CAAC,wBAAc,CAAC,eAAe,CAAC,EAAE;AACpC;AAEA,cAAc;AACd,SAAS,aAAa,MAAc;IAClC,OAAQ;QACN,KAAK,wBAAc,CAAC,WAAW;QAC/B,KAAK,wBAAc,CAAC,oBAAoB;YACtC,OAAO,mBAAS,CAAC,gBAAgB;QACnC,KAAK,wBAAc,CAAC,YAAY;YAC9B,OAAO,mBAAS,CAAC,oBAAoB;QACvC,KAAK,wBAAc,CAAC,SAAS;YAC3B,OAAO,mBAAS,CAAC,mBAAmB;QACtC,KAAK,wBAAc,CAAC,SAAS;YAC3B,OAAO,mBAAS,CAAC,eAAe;QAClC,KAAK,wBAAc,CAAC,qBAAqB;QACzC,KAAK,wBAAc,CAAC,WAAW;QAC/B,KAAK,wBAAc,CAAC,mBAAmB;QACvC,KAAK,wBAAc,CAAC,eAAe;YACjC,OAAO,mBAAS,CAAC,YAAY;QAC/B;YACE,OAAO,mBAAS,CAAC,aAAa;IAClC;AACF;AAEA,SAAS;AACT,SAAS,sBAAsB,aAA4B;IACzD,IAAI,cAAc,MAAM,EAAE;QACxB,MAAM,gBAA0B,EAAE;QAClC,OAAO,OAAO,CAAC,cAAc,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,OAAO,SAAS;YAC7D,SAAS,OAAO,CAAC,CAAA;gBACf,cAAc,IAAI,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC;YACvC;QACF;QACA,OAAO,cAAc,IAAI,CAAC;IAC5B;IACA,OAAO,cAAc,MAAM,IAAI,cAAc,KAAK,IAAI;AACxD;AAGO,SAAS,sBAAsB,KAAe,EAAE,mBAAmB,IAAI;IAC5E,MAAM,EAAE,SAAS,EAAE,SAAS,YAAY,EAAE,GAAG;IAE7C,eAAe;IACf,OAAQ;QACN,KAAK,mBAAS,CAAC,oBAAoB;YACjC,aAAO,CAAC,KAAK,CAAC;YACd,WAAW;YACX,WAAW,IAAM,IAAA,YAAM,KAAI;YAC3B;QAEF,KAAK,mBAAS,CAAC,mBAAmB;YAChC,aAAO,CAAC,KAAK,CAAC;YACd;QAEF,KAAK,mBAAS,CAAC,gBAAgB;YAC7B,IAAI,kBACF,kBAAY,CAAC,KAAK,CAAC;gBACjB,SAAS;gBACT,aAAa;gBACb,UAAU;YACZ;iBAEA,aAAO,CAAC,KAAK,CAAC;YAEhB;QAEF,KAAK,mBAAS,CAAC,eAAe;YAC5B,aAAO,CAAC,KAAK,CAAC;YACd;QAEF,KAAK,mBAAS,CAAC,YAAY;YACzB,kBAAY,CAAC,KAAK,CAAC;gBACjB,SAAS;gBACT,aAAa;gBACb,UAAU;YACZ;YACA;QAEF,KAAK,mBAAS,CAAC,aAAa;YAC1B,kBAAY,CAAC,KAAK,CAAC;gBACjB,SAAS;gBACT,aAAa;gBACb,UAAU;YACZ;YACA;QAEF;YACE,aAAO,CAAC,KAAK,CAAC,gBAAgB;YAC9B;IACJ;AACF;AAEA,SAAS;AACT,SAAS,sBAAyB,SAAmB,EAAE,IAAO;IAC5D,OAAO;QACL,SAAS;QACT;QACA,SAAS;IACX;AACF;AAEA,SAAS;AACT,eAAe,oBAAoB,QAAkB;IACnD,MAAM,SAAS,SAAS,MAAM;IAC9B,MAAM,YAAY,aAAa;IAC/B,IAAI,eAAe,cAAc,CAAC,OAAO,IAAI;IAC7C,IAAI,eAAoB;IAExB,IAAI;QACF,MAAM,eAAe,MAAM,SAAS,IAAI;QACxC,IAAI,cAAc;YAChB,MAAM,YAAY,KAAK,KAAK,CAAC;YAE7B,kBAAkB;YAClB,IAAI,cAAc,mBAAS,CAAC,gBAAgB,EAC1C,eAAe,sBAAsB;iBAErC,eAAe,UAAU,MAAM,IAAI,UAAU,KAAK,IAAI;YAGxD,eAAe;QACjB;IACF,EAAE,OAAO,YAAY;QACnB,QAAQ,IAAI,CAAC,mCAAmC;IAClD;IAEA,MAAM,WAAW,IAAI,kBAAQ,CAC3B,cACA,QACA,WACA,yBAAA,mCAAA,aAAc,IAAI,EAClB;IAGF,OAAO;QACL,SAAS;QACT,OAAO;QACP,SAAS;IACX;AACF;AAGO,eAAe,kBACpB,QAAkB,EAClB,UAII,CAAC,CAAC;IAEN,MAAM,EACJ,qBAAqB,KAAK,EAC1B,uBAAuB,8BAA8B,IAAI,EACzD,cAAc,EACf,GAAG;IAEJ,IAAI;QACF,SAAS;QACT,IAAI,SAAS,EAAE,EAAE;YACf,IAAI;YAEJ,YAAY;YACZ,MAAM,cAAc,SAAS,OAAO,CAAC,GAAG,CAAC;YACzC,IAAI,wBAAA,kCAAA,YAAa,QAAQ,CAAC,qBAAqB;gBAC7C,MAAM,WAAW,MAAM,SAAS,IAAI;gBACpC,gBAAgB;gBAChB,IAAI,OAAO,aAAa,YAAY,aAAa,UAAU;oBACzD,MAAM,cAAc;oBACpB,OAAO,YAAY,IAAI;oBACvB,IAAI,oBACF,aAAO,CAAC,OAAO,CAAC,kBAAkB,YAAY,OAAO,IAAI;gBAE7D,OAAO;oBACL,OAAO;oBACP,IAAI,oBACF,aAAO,CAAC,OAAO,CAAC,kBAAkB;gBAEtC;YACF,OAAO,IAAI,SAAS,MAAM,KAAK,wBAAc,CAAC,UAAU,EAAE;gBACxD,OAAO;gBACP,IAAI,oBACF,aAAO,CAAC,OAAO,CAAC,kBAAkB;YAEtC,OAAO;gBACL,OAAQ,MAAM,SAAS,IAAI;gBAC3B,IAAI,oBACF,aAAO,CAAC,OAAO,CAAC,kBAAkB;YAEtC;YAEA,OAAO,sBAAsB,UAAU;QACzC;QAEA,SAAS;QACT,MAAM,cAAc,MAAM,oBAAoB;QAE9C,IAAI,YAAY,KAAK,IAAI,6BACvB,sBAAsB,YAAY,KAAK,EAAE;QAG3C,OAAO;IAET,EAAE,OAAO,OAAO;QACd,cAAc;QACd,MAAM,eAAe,IAAI,kBAAQ,CAC/B,mBACA,GACA,mBAAS,CAAC,aAAa;QAGzB,IAAI,6BACF,sBAAsB,cAAc;QAGtC,OAAO;YACL,SAAS;YACT,OAAO;YACP,SAAS,aAAa,OAAO;QAC/B;IACF;AACF;AAGO,SAAS,eAAe,KAAU,EAAE,mBAAmB,IAAI;IAChE,IAAI,iBAAiB,kBAAQ,EAAE;QAC7B,IAAI,kBACF,sBAAsB,OAAO;QAE/B,OAAO;IACT;IAEA,YAAY;IACZ,MAAM,WAAW,IAAI,kBAAQ,CAC3B,MAAM,OAAO,IAAI,QACjB,MAAM,MAAM,IAAI,GAChB,mBAAS,CAAC,aAAa;IAGzB,IAAI,kBACF,sBAAsB,UAAU;IAGlC,OAAO;AACT"}