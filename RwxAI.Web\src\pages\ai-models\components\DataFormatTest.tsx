import React from 'react';
import { Card, Typography, Table, Tag, Switch, Button, Space } from 'antd';
import { createProTableRequest, buildPagedQuery, handlePagedResponse } from '@/utils/pageDataHandler';

const { Title, Paragraph, Text } = Typography;

// 模拟您提供的后台返回数据格式
const mockBackendResponse = {
  "Items": [
    {
      "Id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "ModelTemplateId": "cd90513b-e7bb-4710-b46b-f302b818a779",
      "Name": "deepseek",
      "ModelId": "deepseek-chat",
      "Endpoint": "https://api.deepseek.com",
      "ProviderCode": "",
      "DisplayName": "deepseek-chat",
      "Description": "string",
      "ApiKey": "sk-ab6f4de3f1f1454facae3428554f7f36",
      "IsEnabled": true,
      "IsDefault": true,
      "MaxTokens": 0,
      "Temperature": 0,
      "CreatedTime": "2025-05-04T22:31:39.557",
      "UpdatedTime": "2025-05-04T14:31:27.313",
      "Template": {
        "Id": "cd90513b-e7bb-4710-b46b-f302b818a779",
        "Name": "deepseek-chat",
        "DisplayName": "deepseek-chat",
        "ModelId": "deepseek-chat",
        "Endpoint": "https://api.deepseek.com/v1",
        "Type": "Chat",
        "Description": "deepseek的聊天模型",
        "MaxContextLength": 32768,
        "MaxOutputLength": 4096,
        "SupportsStreaming": true,
        "SupportsFunctionCalling": true,
        "SupportsVision": false,
        "Notes": null,
        "Provider": {
          "Id": "55555555-5555-5555-5555-555555555555",
          "Name": "DeepSeek",
          "DisplayName": "DeepSeek",
          "Code": "DeepSeek",
          "Description": "DeepSeek",
          "Website": "https://www.deepseek.com/",
          "IconUrl": null
        }
      }
    }
  ],
  "TotalCount": 1,
  "PageNumber": 1,
  "PageSize": 20,
  "TotalPages": 1,
  "HasPreviousPage": false,
  "HasNextPage": false
};

const DataFormatTest: React.FC = () => {
  const [testResults, setTestResults] = React.useState<any[]>([]);

  const testUnifiedHandler = () => {
    // 测试buildPagedQuery函数
    const testParams = {
      current: 1,
      pageSize: 20,
      Name: 'deepseek',
      ModelId: 'deepseek-chat',
      keyWord: 'test search'
    };

    const fieldMapping = {
      Name: 'Name',
      ModelId: 'ModelId',
      ProviderCode: 'ProviderCode',
      IsEnabled: 'IsEnabled',
    };

    const query = buildPagedQuery(testParams, fieldMapping);

    // 测试handlePagedResponse函数
    const mockResponse = {
      success: true,
      data: mockBackendResponse
    };

    const result = handlePagedResponse(mockResponse);

    setTestResults([
      {
        function: 'buildPagedQuery',
        input: { testParams, fieldMapping },
        output: query
      },
      {
        function: 'handlePagedResponse',
        input: mockResponse,
        output: result
      }
    ]);
  };

  const getModelTypeTag = (type?: string) => {
    const typeMap: Record<string, { text: string; color: string }> = {
      'Chat': { text: '对话模型', color: 'blue' },
      'Embedding': { text: '嵌入模型', color: 'green' },
      'TextToImage': { text: '文本生成图像模型', color: 'purple' },
      'ImageToText': { text: '图像描述模型', color: 'orange' },
    };
    const typeInfo = typeMap[type || ''];
    return typeInfo ? <Tag color={typeInfo.color}>{typeInfo.text}</Tag> : <Tag>-</Tag>;
  };

  const columns = [
    {
      title: '模型名称',
      dataIndex: 'Name',
      key: 'Name',
      render: (_: any, record: any) => {
        return record.DisplayName || record.Name || '-';
      },
    },
    {
      title: '模型ID',
      dataIndex: 'ModelId',
      key: 'ModelId',
    },
    {
      title: '端点',
      dataIndex: 'Endpoint',
      key: 'Endpoint',
      render: (text: string) => text || '-',
    },
    {
      title: '模型类型',
      dataIndex: 'ModelType',
      key: 'ModelType',
      render: (_: any, record: any) => {
        return getModelTypeTag(record.Template?.Type);
      },
    },
    {
      title: '提供商',
      dataIndex: 'Provider',
      key: 'Provider',
      render: (_: any, record: any) => {
        const providerName = record.Template?.Provider?.DisplayName ||
          record.Template?.Provider?.Name ||
          '-';
        return providerName;
      },
    },
    {
      title: '状态',
      dataIndex: 'IsEnabled',
      key: 'IsEnabled',
      render: (_: any, record: any) => (
        <Switch
          checked={record.IsEnabled}
          size="small"
          disabled
        />
      ),
    },
    {
      title: '默认模型',
      dataIndex: 'IsDefault',
      key: 'IsDefault',
      render: (_: any, record: any) => (
        <Switch
          checked={record.IsDefault}
          size="small"
          disabled
        />
      ),
    },
    {
      title: '最大令牌',
      dataIndex: 'MaxTokens',
      key: 'MaxTokens',
    },
    {
      title: '温度',
      dataIndex: 'Temperature',
      key: 'Temperature',
    },
    {
      title: '创建时间',
      dataIndex: 'CreatedTime',
      key: 'CreatedTime',
      render: (text: string) => new Date(text).toLocaleString(),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <Title level={3}>后台数据格式测试</Title>

        <Space style={{ marginBottom: 16 }}>
          <Button type="primary" onClick={testUnifiedHandler}>
            测试统一处理函数
          </Button>
        </Space>

        {testResults.length > 0 && (
          <>
            <Title level={4}>统一处理函数测试结果：</Title>
            {testResults.map((result, index) => (
              <Card key={index} size="small" style={{ marginBottom: 8 }}>
                <Text strong>{result.function}:</Text>
                <pre style={{ background: '#f5f5f5', padding: '8px', borderRadius: '4px', fontSize: '12px' }}>
                  输入: {JSON.stringify(result.input, null, 2)}
                  {'\n'}
                  输出: {JSON.stringify(result.output, null, 2)}
                </pre>
              </Card>
            ))}
          </>
        )}

        <Paragraph>
          <Text strong>后台返回的分页数据格式：</Text>
        </Paragraph>
        <Paragraph>
          <pre style={{ background: '#f5f5f5', padding: '12px', borderRadius: '4px' }}>
            {JSON.stringify({
              Items: "数据数组",
              TotalCount: "总记录数",
              PageNumber: "当前页码",
              PageSize: "每页大小",
              TotalPages: "总页数",
              HasPreviousPage: "是否有上一页",
              HasNextPage: "是否有下一页"
            }, null, 2)}
          </pre>
        </Paragraph>
        
        <Title level={4}>数据展示效果：</Title>
        <Table
          columns={columns}
          dataSource={mockBackendResponse.Items}
          rowKey="Id"
          pagination={{
            current: mockBackendResponse.PageNumber,
            pageSize: mockBackendResponse.PageSize,
            total: mockBackendResponse.TotalCount,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
          }}
        />
        
        <Title level={4}>分页信息：</Title>
        <ul>
          <li>总记录数: {mockBackendResponse.TotalCount}</li>
          <li>当前页码: {mockBackendResponse.PageNumber}</li>
          <li>每页大小: {mockBackendResponse.PageSize}</li>
          <li>总页数: {mockBackendResponse.TotalPages}</li>
          <li>是否有上一页: {mockBackendResponse.HasPreviousPage ? '是' : '否'}</li>
          <li>是否有下一页: {mockBackendResponse.HasNextPage ? '是' : '否'}</li>
        </ul>
      </Card>
    </div>
  );
};

export default DataFormatTest;
