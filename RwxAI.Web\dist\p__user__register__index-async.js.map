{"version": 3, "sources": ["src/pages/user/register/index.tsx"], "sourcesContent": ["import {\n  LockOutlined,\n  MailOutlined,\n  UserOutlined,\n} from '@ant-design/icons';\nimport {\n  LoginForm,\n  ProFormText,\n} from '@ant-design/pro-components';\nimport {\n  FormattedMessage,\n  Helmet,\n  SelectLang,\n  useIntl,\n  history,\n} from '@umijs/max';\nimport { Alert, App } from 'antd';\nimport { createStyles } from 'antd-style';\nimport React, { useState } from 'react';\nimport { Footer } from '@/components';\nimport { register } from '@/services/rwxai';\nimport Settings from '../../../../config/defaultSettings';\n\nconst useStyles = createStyles(({ token }) => {\n  return {\n    action: {\n      marginLeft: '8px',\n      color: 'rgba(0, 0, 0, 0.2)',\n      fontSize: '24px',\n      verticalAlign: 'middle',\n      cursor: 'pointer',\n      transition: 'color 0.3s',\n      '&:hover': {\n        color: token.colorPrimaryActive,\n      },\n    },\n    lang: {\n      width: 42,\n      height: 42,\n      lineHeight: '42px',\n      position: 'fixed',\n      right: 16,\n      borderRadius: token.borderRadius,\n      ':hover': {\n        backgroundColor: token.colorBgTextHover,\n      },\n    },\n    container: {\n      display: 'flex',\n      flexDirection: 'column',\n      height: '100vh',\n      overflow: 'auto',\n      backgroundImage:\n        \"url('https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr')\",\n      backgroundSize: '100% 100%',\n    },\n  };\n});\n\nconst RegisterMessage: React.FC<{\n  content: string;\n}> = ({ content }) => {\n  return (\n    <Alert\n      style={{\n        marginBottom: 24,\n      }}\n      message={content}\n      type=\"error\"\n      showIcon\n    />\n  );\n};\n\n// 定义注册结果类型\ninterface RegisterResult {\n  success?: boolean;\n  message?: string;\n  status?: string;\n}\n\nconst Register: React.FC = () => {\n  const [userRegisterState, setUserRegisterState] = useState<RegisterResult>({});\n  const { styles } = useStyles();\n  const intl = useIntl();\n  const { message } = App.useApp();\n\n  const handleSubmit = async (values: RwxAI.RegisterRequest) => {\n    try {\n      // 注册\n      const msg = await register(values);\n      if (msg.success) {\n        const defaultRegisterSuccessMessage = intl.formatMessage({\n          id: 'pages.register.success',\n          defaultMessage: '注册成功！',\n        });\n        message.success(defaultRegisterSuccessMessage);\n        \n        // 注册成功后跳转到登录页\n        setTimeout(() => {\n          history.push('/user/login');\n        }, 1000);\n        return;\n      }\n      \n      // 如果失败去设置用户错误信息\n      setUserRegisterState({ status: 'error', message: msg.message });\n    } catch (error) {\n      const defaultRegisterFailureMessage = intl.formatMessage({\n        id: 'pages.register.failure',\n        defaultMessage: '注册失败，请重试！',\n      });\n      message.error(defaultRegisterFailureMessage);\n    }\n  };\n\n  const { status, message: registerMessage } = userRegisterState;\n\n  return (\n    <div className={styles.container}>\n      <Helmet>\n        <title>\n          {intl.formatMessage({\n            id: 'menu.register',\n            defaultMessage: '注册页',\n          })}\n          - {Settings.title}\n        </title>\n      </Helmet>\n      <div\n        style={{\n          flex: '1',\n          padding: '32px 0',\n        }}\n      >\n        <LoginForm\n          contentStyle={{\n            minWidth: 280,\n            maxWidth: '75vw',\n          }}\n          logo={<img alt=\"logo\" src=\"/logo.svg\" />}\n          title=\"RwxAI\"\n          subTitle={intl.formatMessage({ id: 'pages.layouts.userLayout.title' })}\n          initialValues={{\n            autoLogin: true,\n          }}\n          onFinish={async (values) => {\n            await handleSubmit(values as RwxAI.RegisterRequest);\n          }}\n          submitter={{\n            searchConfig: {\n              submitText: intl.formatMessage({\n                id: 'pages.register.submit',\n                defaultMessage: '注册',\n              }),\n            },\n          }}\n        >\n          {status === 'error' && registerMessage && (\n            <RegisterMessage content={registerMessage} />\n          )}\n\n          <ProFormText\n            name=\"Username\"\n            fieldProps={{\n              size: 'large',\n              prefix: <UserOutlined />,\n            }}\n            placeholder={intl.formatMessage({\n              id: 'pages.register.username.placeholder',\n              defaultMessage: '用户名',\n            })}\n            rules={[\n              {\n                required: true,\n                message: (\n                  <FormattedMessage\n                    id=\"pages.register.username.required\"\n                    defaultMessage=\"请输入用户名!\"\n                  />\n                ),\n              },\n            ]}\n          />\n          \n          <ProFormText\n            name=\"Email\"\n            fieldProps={{\n              size: 'large',\n              prefix: <MailOutlined />,\n            }}\n            placeholder={intl.formatMessage({\n              id: 'pages.register.email.placeholder',\n              defaultMessage: '邮箱',\n            })}\n            rules={[\n              {\n                required: true,\n                message: (\n                  <FormattedMessage\n                    id=\"pages.register.email.required\"\n                    defaultMessage=\"请输入邮箱!\"\n                  />\n                ),\n              },\n              {\n                type: 'email',\n                message: (\n                  <FormattedMessage\n                    id=\"pages.register.email.invalid\"\n                    defaultMessage=\"邮箱格式错误!\"\n                  />\n                ),\n              },\n            ]}\n          />\n\n          <ProFormText\n            name=\"FirstName\"\n            fieldProps={{\n              size: 'large',\n              prefix: <UserOutlined />,\n            }}\n            placeholder={intl.formatMessage({\n              id: 'pages.register.firstName.placeholder',\n              defaultMessage: '名',\n            })}\n          />\n\n          <ProFormText\n            name=\"LastName\"\n            fieldProps={{\n              size: 'large',\n              prefix: <UserOutlined />,\n            }}\n            placeholder={intl.formatMessage({\n              id: 'pages.register.lastName.placeholder',\n              defaultMessage: '姓',\n            })}\n          />\n\n          <ProFormText.Password\n            name=\"Password\"\n            fieldProps={{\n              size: 'large',\n              prefix: <LockOutlined />,\n            }}\n            placeholder={intl.formatMessage({\n              id: 'pages.register.password.placeholder',\n              defaultMessage: '密码',\n            })}\n            rules={[\n              {\n                required: true,\n                message: (\n                  <FormattedMessage\n                    id=\"pages.register.password.required\"\n                    defaultMessage=\"请输入密码！\"\n                  />\n                ),\n              },\n              {\n                min: 6,\n                message: (\n                  <FormattedMessage\n                    id=\"pages.register.password.min\"\n                    defaultMessage=\"密码长度不能少于6位！\"\n                  />\n                ),\n              },\n            ]}\n          />\n\n          <div\n            style={{\n              marginBottom: 24,\n              textAlign: 'center',\n            }}\n          >\n            <a\n              style={{\n                float: 'right',\n              }}\n              onClick={() => history.push('/user/login')}\n            >\n              <FormattedMessage id=\"pages.register.loginAccount\" defaultMessage=\"已有账户？去登录\" />\n            </a>\n          </div>\n        </LoginForm>\n      </div>\n      <Footer />\n      <SelectLang className={styles.lang} />\n    </div>\n  );\n};\n\nexport default Register;\n"], "names": [], "mappings": ";;;;;;;4BAwSA;;;eAAA;;;;;;;8BApSO;sCAIA;4BAOA;6BACoB;kCACE;wEACG;mCACT;8BACE;iFACJ;;;;;;;;;;AAErB,MAAM,YAAY,IAAA,uBAAY,EAAC,CAAC,EAAE,KAAK,EAAE;IACvC,OAAO;QACL,QAAQ;YACN,YAAY;YACZ,OAAO;YACP,UAAU;YACV,eAAe;YACf,QAAQ;YACR,YAAY;YACZ,WAAW;gBACT,OAAO,MAAM,kBAAkB;YACjC;QACF;QACA,MAAM;YACJ,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,UAAU;YACV,OAAO;YACP,cAAc,MAAM,YAAY;YAChC,UAAU;gBACR,iBAAiB,MAAM,gBAAgB;YACzC;QACF;QACA,WAAW;YACT,SAAS;YACT,eAAe;YACf,QAAQ;YACR,UAAU;YACV,iBACE;YACF,gBAAgB;QAClB;IACF;AACF;AAEA,MAAM,kBAED,CAAC,EAAE,OAAO,EAAE;IACf,qBACE,2BAAC,WAAK;QACJ,OAAO;YACL,cAAc;QAChB;QACA,SAAS;QACT,MAAK;QACL,QAAQ;;;;;;AAGd;KAbM;AAsBN,MAAM,WAAqB;;IACzB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,IAAA,eAAQ,EAAiB,CAAC;IAC5E,MAAM,EAAE,MAAM,EAAE,GAAG;IACnB,MAAM,OAAO,IAAA,YAAO;IACpB,MAAM,EAAE,OAAO,EAAE,GAAG,SAAG,CAAC,MAAM;IAE9B,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,KAAK;YACL,MAAM,MAAM,MAAM,IAAA,eAAQ,EAAC;YAC3B,IAAI,IAAI,OAAO,EAAE;gBACf,MAAM,gCAAgC,KAAK,aAAa,CAAC;oBACvD,IAAI;oBACJ,gBAAgB;gBAClB;gBACA,QAAQ,OAAO,CAAC;gBAEhB,cAAc;gBACd,WAAW;oBACT,YAAO,CAAC,IAAI,CAAC;gBACf,GAAG;gBACH;YACF;YAEA,gBAAgB;YAChB,qBAAqB;gBAAE,QAAQ;gBAAS,SAAS,IAAI,OAAO;YAAC;QAC/D,EAAE,OAAO,OAAO;YACd,MAAM,gCAAgC,KAAK,aAAa,CAAC;gBACvD,IAAI;gBACJ,gBAAgB;YAClB;YACA,QAAQ,KAAK,CAAC;QAChB;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,eAAe,EAAE,GAAG;IAE7C,qBACE,2BAAC;QAAI,WAAW,OAAO,SAAS;;0BAC9B,2BAAC,WAAM;0BACL,cAAA,2BAAC;;wBACE,KAAK,aAAa,CAAC;4BAClB,IAAI;4BACJ,gBAAgB;wBAClB;wBAAG;wBACA,wBAAQ,CAAC,KAAK;;;;;;;;;;;;0BAGrB,2BAAC;gBACC,OAAO;oBACL,MAAM;oBACN,SAAS;gBACX;0BAEA,cAAA,2BAAC,wBAAS;oBACR,cAAc;wBACZ,UAAU;wBACV,UAAU;oBACZ;oBACA,oBAAM,2BAAC;wBAAI,KAAI;wBAAO,KAAI;;;;;;oBAC1B,OAAM;oBACN,UAAU,KAAK,aAAa,CAAC;wBAAE,IAAI;oBAAiC;oBACpE,eAAe;wBACb,WAAW;oBACb;oBACA,UAAU,OAAO;wBACf,MAAM,aAAa;oBACrB;oBACA,WAAW;wBACT,cAAc;4BACZ,YAAY,KAAK,aAAa,CAAC;gCAC7B,IAAI;gCACJ,gBAAgB;4BAClB;wBACF;oBACF;;wBAEC,WAAW,WAAW,iCACrB,2BAAC;4BAAgB,SAAS;;;;;;sCAG5B,2BAAC,0BAAW;4BACV,MAAK;4BACL,YAAY;gCACV,MAAM;gCACN,sBAAQ,2BAAC,mBAAY;;;;;4BACvB;4BACA,aAAa,KAAK,aAAa,CAAC;gCAC9B,IAAI;gCACJ,gBAAgB;4BAClB;4BACA,OAAO;gCACL;oCACE,UAAU;oCACV,uBACE,2BAAC,qBAAgB;wCACf,IAAG;wCACH,gBAAe;;;;;;gCAGrB;6BACD;;;;;;sCAGH,2BAAC,0BAAW;4BACV,MAAK;4BACL,YAAY;gCACV,MAAM;gCACN,sBAAQ,2BAAC,mBAAY;;;;;4BACvB;4BACA,aAAa,KAAK,aAAa,CAAC;gCAC9B,IAAI;gCACJ,gBAAgB;4BAClB;4BACA,OAAO;gCACL;oCACE,UAAU;oCACV,uBACE,2BAAC,qBAAgB;wCACf,IAAG;wCACH,gBAAe;;;;;;gCAGrB;gCACA;oCACE,MAAM;oCACN,uBACE,2BAAC,qBAAgB;wCACf,IAAG;wCACH,gBAAe;;;;;;gCAGrB;6BACD;;;;;;sCAGH,2BAAC,0BAAW;4BACV,MAAK;4BACL,YAAY;gCACV,MAAM;gCACN,sBAAQ,2BAAC,mBAAY;;;;;4BACvB;4BACA,aAAa,KAAK,aAAa,CAAC;gCAC9B,IAAI;gCACJ,gBAAgB;4BAClB;;;;;;sCAGF,2BAAC,0BAAW;4BACV,MAAK;4BACL,YAAY;gCACV,MAAM;gCACN,sBAAQ,2BAAC,mBAAY;;;;;4BACvB;4BACA,aAAa,KAAK,aAAa,CAAC;gCAC9B,IAAI;gCACJ,gBAAgB;4BAClB;;;;;;sCAGF,2BAAC,0BAAW,CAAC,QAAQ;4BACnB,MAAK;4BACL,YAAY;gCACV,MAAM;gCACN,sBAAQ,2BAAC,mBAAY;;;;;4BACvB;4BACA,aAAa,KAAK,aAAa,CAAC;gCAC9B,IAAI;gCACJ,gBAAgB;4BAClB;4BACA,OAAO;gCACL;oCACE,UAAU;oCACV,uBACE,2BAAC,qBAAgB;wCACf,IAAG;wCACH,gBAAe;;;;;;gCAGrB;gCACA;oCACE,KAAK;oCACL,uBACE,2BAAC,qBAAgB;wCACf,IAAG;wCACH,gBAAe;;;;;;gCAGrB;6BACD;;;;;;sCAGH,2BAAC;4BACC,OAAO;gCACL,cAAc;gCACd,WAAW;4BACb;sCAEA,cAAA,2BAAC;gCACC,OAAO;oCACL,OAAO;gCACT;gCACA,SAAS,IAAM,YAAO,CAAC,IAAI,CAAC;0CAE5B,cAAA,2BAAC,qBAAgB;oCAAC,IAAG;oCAA8B,gBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAK1E,2BAAC,kBAAM;;;;;0BACP,2BAAC,eAAU;gBAAC,WAAW,OAAO,IAAI;;;;;;;;;;;;AAGxC;GArNM;;QAEe;QACN,YAAO;QACA,SAAG,CAAC;;;MAJpB;IAuNN,WAAe"}