import React, { useState, useEffect } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import { Card, Button, Space, Typography, Descriptions, message } from 'antd';
import { getToken, getUserInfo, isLoggedIn, logout } from '@/utils/auth';
import { getAIModels } from '@/services/rwxai';

const { Title, Text, Paragraph } = Typography;

const AuthTestPage: React.FC = () => {
  const [tokenInfo, setTokenInfo] = useState<any>(null);
  const [userInfo, setUserInfo] = useState<any>(null);
  const [apiTestResult, setApiTestResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadAuthInfo();
  }, []);

  const loadAuthInfo = () => {
    const token = getToken();
    const user = getUserInfo();
    
    setTokenInfo({
      token: token ? `${token.substring(0, 20)}...` : null,
      isLoggedIn: isLoggedIn(),
      fullToken: token,
    });
    
    setUserInfo(user);
  };

  const testApiCall = async () => {
    setLoading(true);
    try {
      const response = await getAIModels();
      if (response.success) {
        setApiTestResult(`✅ 成功获取 ${response.data?.length || 0} 个AI模型`);
      } else {
        setApiTestResult(`❌ API调用失败: ${response.message}`);
      }
    } catch (error: any) {
      setApiTestResult(`❌ 异常: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    logout();
    loadAuthInfo();
    message.info('已退出登录');
  };

  return (
    <PageContainer title="JWT认证测试页面">
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Card title="认证状态">
          <Descriptions column={1} bordered>
            <Descriptions.Item label="登录状态">
              <Text type={tokenInfo?.isLoggedIn ? 'success' : 'danger'}>
                {tokenInfo?.isLoggedIn ? '已登录' : '未登录'}
              </Text>
            </Descriptions.Item>
            <Descriptions.Item label="JWT令牌">
              <Text code>{tokenInfo?.token || '无'}</Text>
            </Descriptions.Item>
            <Descriptions.Item label="用户信息">
              {userInfo ? (
                <div>
                  <div>用户名: {userInfo.username || userInfo.name}</div>
                  <div>邮箱: {userInfo.email}</div>
                  <div>姓名: {userInfo.firstName} {userInfo.lastName}</div>
                </div>
              ) : (
                <Text type="secondary">无用户信息</Text>
              )}
            </Descriptions.Item>
          </Descriptions>
        </Card>

        <Card title="API测试">
          <Space direction="vertical" style={{ width: '100%' }}>
            <Paragraph>
              点击下面的按钮测试带JWT令牌的API调用：
            </Paragraph>
            <Space>
              <Button 
                type="primary" 
                onClick={testApiCall} 
                loading={loading}
                disabled={!tokenInfo?.isLoggedIn}
              >
                测试获取AI模型列表
              </Button>
              <Button onClick={loadAuthInfo}>
                刷新认证信息
              </Button>
              <Button danger onClick={handleLogout} disabled={!tokenInfo?.isLoggedIn}>
                退出登录
              </Button>
            </Space>
            {apiTestResult && (
              <Card size="small" style={{ marginTop: 16 }}>
                <Text>{apiTestResult}</Text>
              </Card>
            )}
          </Space>
        </Card>

        <Card title="JWT令牌详情" size="small">
          <Paragraph>
            <Text code style={{ wordBreak: 'break-all', fontSize: '12px' }}>
              {tokenInfo?.fullToken || '无令牌'}
            </Text>
          </Paragraph>
        </Card>

        <Card title="使用说明" size="small">
          <Paragraph>
            <Title level={5}>测试步骤：</Title>
            <ol>
              <li>首先访问 <Text code>/user/login</Text> 页面进行登录</li>
              <li>登录成功后会自动保存JWT令牌到localStorage</li>
              <li>返回此页面查看认证状态</li>
              <li>点击"测试获取AI模型列表"按钮验证API调用</li>
              <li>所有后续的API请求都会自动携带JWT令牌</li>
            </ol>
          </Paragraph>
        </Card>
      </Space>
    </PageContainer>
  );
};

export default AuthTestPage;
