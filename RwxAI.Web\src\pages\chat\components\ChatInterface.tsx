import React, { useState, useEffect, useRef } from 'react';
import { 
  Layout, 
  List, 
  Button, 
  Typography, 
  Space, 
  Avatar, 
  Card,
  Divider,
  Badge,
  Tooltip,
  Input,
  message
} from 'antd';
import {
  Conversations,
  Bubble,
  Sender,
  Welcome,
  Prompts
} from '@ant-design/x';
import {
  PlusOutlined,
  MessageOutlined,
  DeleteOutlined,
  EditOutlined,
  MoreOutlined,
  UserOutlined,
  RobotOutlined
} from '@ant-design/icons';
import { useIntl, FormattedMessage } from '@umijs/max';
import { getChatSessions, deleteChatSession, sendMessage } from '@/services/rwxai';

const { Sider, Content } = Layout;
const { Text, Title } = Typography;

interface ChatInterfaceProps {
  onCreateSession?: () => void;
  onEditSession?: (session: RwxAI.ChatSession) => void;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({
  onCreateSession,
  onEditSession
}) => {
  const intl = useIntl();
  const [sessions, setSessions] = useState<RwxAI.ChatSession[]>([]);
  const [currentSession, setCurrentSession] = useState<RwxAI.ChatSession | null>(null);
  const [messages, setMessages] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [sessionsLoading, setSessionsLoading] = useState(true);

  // 加载会话列表
  const loadSessions = async () => {
    try {
      setSessionsLoading(true);
      const response = await getChatSessions();
      if (response.success) {
        setSessions(response.data || []);
        // 如果有会话且没有选中的会话，选中第一个
        if (response.data?.length > 0 && !currentSession) {
          setCurrentSession(response.data[0]);
        }
      }
    } catch (error) {
      message.error('加载会话列表失败');
    } finally {
      setSessionsLoading(false);
    }
  };

  // 加载消息
  const loadMessages = (session: RwxAI.ChatSession) => {
    if (session.Messages) {
      const formattedMessages = session.Messages.map(msg => ({
        id: msg.Id,
        content: msg.Content,
        role: msg.Role,
        createdAt: msg.CreatedTime,
      }));
      setMessages(formattedMessages);
    } else {
      setMessages([]);
    }
  };

  useEffect(() => {
    loadSessions();
  }, []);

  useEffect(() => {
    if (currentSession) {
      loadMessages(currentSession);
    }
  }, [currentSession]);

  // 发送消息的处理函数
  const handleSendMessage = async (messageText: string) => {
    if (!currentSession) {
      message.error('请先选择一个会话');
      return;
    }

    if (!messageText.trim()) {
      return;
    }

    try {
      setLoading(true);

      // 添加用户消息到本地状态
      const userMessage = {
        id: Date.now().toString(),
        content: messageText,
        role: 'user' as const,
        timestamp: new Date().toISOString()
      };

      setMessages(prev => [...prev, userMessage]);

      // 发送消息到后端
      const response = await sendMessage(currentSession.Id, {
        Content: messageText,
        ModelId: currentSession.ModelId,
        Temperature: currentSession.Temperature,
        MaxTokens: currentSession.MaxTokens,
      });

      // 添加AI回复到本地状态
      if (response.success && response.data) {
        const aiMessage = {
          id: (Date.now() + 1).toString(),
          content: response.data.Content || '抱歉，我现在无法回复。',
          role: 'assistant' as const,
          timestamp: new Date().toISOString()
        };

        setMessages(prev => [...prev, aiMessage]);
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      message.error('发送消息失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 删除会话
  const handleDeleteSession = async (session: RwxAI.ChatSession) => {
    try {
      await deleteChatSession(session.Id);
      message.success('删除成功');
      await loadSessions();
      if (currentSession?.Id === session.Id) {
        setCurrentSession(null);
        setMessages([]);
      }
    } catch (error) {
      message.error('删除失败');
    }
  };

  // 会话列表项渲染
  const renderSessionItem = (session: RwxAI.ChatSession) => {
    const isActive = currentSession?.Id === session.Id;
    const messageCount = session.Messages?.length || 0;
    
    return (
      <div
        key={session.Id}
        className={`session-item ${isActive ? 'active' : ''}`}
        onClick={() => setCurrentSession(session)}
        onDoubleClick={() => {
          // 双击激活对话，确保会话被选中并且可以继续对话
          setCurrentSession(session);
          // 可以在这里添加额外的激活逻辑，比如聚焦到输入框
          setTimeout(() => {
            const inputElement = document.querySelector('.ant-input') as HTMLInputElement;
            if (inputElement) {
              inputElement.focus();
            }
          }, 100);
        }}
        style={{
          padding: '12px 16px',
          cursor: 'pointer',
          borderRadius: '8px',
          margin: '4px 8px',
          backgroundColor: isActive ? '#e6f7ff' : 'transparent',
          border: isActive ? '1px solid #91d5ff' : '1px solid transparent',
          transition: 'all 0.2s ease',
        }}
        onMouseEnter={(e) => {
          if (!isActive) {
            e.currentTarget.style.backgroundColor = '#f5f5f5';
          }
        }}
        onMouseLeave={(e) => {
          if (!isActive) {
            e.currentTarget.style.backgroundColor = 'transparent';
          }
        }}
      >
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <div style={{ flex: 1, minWidth: 0 }}>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '4px' }}>
              <Text strong ellipsis style={{ fontSize: '14px' }}>
                {session.Name}
              </Text>
              {session.IsActive && (
                <Badge 
                  status="processing" 
                  style={{ marginLeft: '8px' }}
                />
              )}
            </div>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {session.Model?.Name || '未知模型'}
            </Text>
            <div style={{ marginTop: '4px' }}>
              <Text type="secondary" style={{ fontSize: '11px' }}>
                {messageCount} 条消息
              </Text>
            </div>
          </div>
          <Space size="small">
            <Tooltip title="编辑">
              <Button
                type="text"
                size="small"
                icon={<EditOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  onEditSession?.(session);
                }}
              />
            </Tooltip>
            <Tooltip title="删除">
              <Button
                type="text"
                size="small"
                danger
                icon={<DeleteOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleDeleteSession(session);
                }}
              />
            </Tooltip>
          </Space>
        </div>
      </div>
    );
  };

  // 欢迎页面的建议提示
  const welcomePrompts = [
    {
      key: 'help',
      label: '如何使用这个AI助手？',
    },
    {
      key: 'features',
      label: '有哪些功能可以使用？',
    },
    {
      key: 'examples',
      label: '给我一些使用示例',
    },
    {
      key: 'settings',
      label: '如何调整对话设置？',
    },
  ];

  return (
    <Layout style={{ height: '100vh', backgroundColor: '#fff' }}>
      {/* 左侧会话列表 */}
      <Sider 
        width={280} 
        style={{ 
          backgroundColor: '#fafafa',
          borderRight: '1px solid #f0f0f0',
          overflow: 'hidden'
        }}
      >
        <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
          {/* 头部 */}
          <div style={{ padding: '16px', borderBottom: '1px solid #f0f0f0' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Title level={4} style={{ margin: 0 }}>
                <FormattedMessage id="pages.chat.sessions" />
              </Title>
              <Button
                type="primary"
                size="small"
                icon={<PlusOutlined />}
                onClick={onCreateSession}
              >
                新建
              </Button>
            </div>
          </div>
          
          {/* 会话列表 */}
          <div style={{ flex: 1, overflow: 'auto' }}>
            {sessionsLoading ? (
              <div style={{ padding: '20px', textAlign: 'center' }}>
                <Text type="secondary">加载中...</Text>
              </div>
            ) : sessions.length === 0 ? (
              <div style={{ padding: '20px', textAlign: 'center' }}>
                <Text type="secondary">暂无会话</Text>
              </div>
            ) : (
              <div style={{ padding: '8px 0' }}>
                {sessions.map(renderSessionItem)}
              </div>
            )}
          </div>
        </div>
      </Sider>

      {/* 右侧聊天区域 */}
      <Content style={{ display: 'flex', flexDirection: 'column' }}>
        {currentSession ? (
          <Conversations
            items={messages}
            renderItem={(item) => (
              <Bubble
                content={item.content}
                avatar={
                  item.role === 'user' ? (
                    <Avatar icon={<UserOutlined />} style={{ backgroundColor: '#1890ff' }} />
                  ) : (
                    <Avatar icon={<RobotOutlined />} style={{ backgroundColor: '#52c41a' }} />
                  )
                }
                placement={item.role === 'user' ? 'end' : 'start'}
                typing={item.role === 'assistant' && loading}
              />
            )}
            renderHeader={() => (
              <div style={{ 
                padding: '16px 24px', 
                borderBottom: '1px solid #f0f0f0',
                backgroundColor: '#fff'
              }}>
                <Title level={4} style={{ margin: 0 }}>
                  {currentSession.Name}
                </Title>
                <Text type="secondary">
                  {currentSession.Model?.Name} • 温度: {currentSession.Temperature} • 最大令牌: {currentSession.MaxTokens}
                </Text>
              </div>
            )}
            renderFooter={() => (
              <div style={{ padding: '16px 24px', borderTop: '1px solid #f0f0f0' }}>
                <Sender
                  loading={loading}
                  onSubmit={handleSendMessage}
                  placeholder="输入消息..."
                />
              </div>
            )}
            style={{ height: '100%' }}
          />
        ) : (
          <div style={{ 
            height: '100%', 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center',
            flexDirection: 'column'
          }}>
            <Welcome
              title="欢迎使用 RwxAI 聊天助手"
              description="选择一个会话开始对话，或创建新的会话"
              extra={
                <Prompts
                  title="快速开始"
                  items={welcomePrompts}
                  onItemClick={(item) => {
                    // 这里可以处理快速提示的点击
                    message.info(`您选择了: ${item.label}`);
                  }}
                />
              }
            />
          </div>
        )}
      </Content>
    </Layout>
  );
};

export default ChatInterface;
