/**
 * 认证相关工具函数
 */

// 存储键名
const TOKEN_KEY = 'rwxai_token';
const REFRESH_TOKEN_KEY = 'rwxai_refresh_token';
const USER_INFO_KEY = 'rwxai_user_info';

/**
 * 获取存储的JWT令牌
 */
export const getToken = (): string | null => {
  return localStorage.getItem(TOKEN_KEY);
};

/**
 * 设置JWT令牌
 */
export const setToken = (token: string): void => {
  localStorage.setItem(TOKEN_KEY, token);
};

/**
 * 获取刷新令牌
 */
export const getRefreshToken = (): string | null => {
  return localStorage.getItem(REFRESH_TOKEN_KEY);
};

/**
 * 设置刷新令牌
 */
export const setRefreshToken = (refreshToken: string): void => {
  localStorage.setItem(REFRESH_TOKEN_KEY, refreshToken);
};

/**
 * 获取用户信息
 */
export const getUserInfo = (): any => {
  const userInfo = localStorage.getItem(USER_INFO_KEY);
  return userInfo ? JSON.parse(userInfo) : null;
};

/**
 * 设置用户信息
 */
export const setUserInfo = (userInfo: any): void => {
  localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo));
};

/**
 * 清除所有认证信息
 */
export const clearAuth = (): void => {
  localStorage.removeItem(TOKEN_KEY);
  localStorage.removeItem(REFRESH_TOKEN_KEY);
  localStorage.removeItem(USER_INFO_KEY);
};

/**
 * 检查是否已登录
 */
export const isLoggedIn = (): boolean => {
  const token = getToken();
  return !!token && !isTokenExpired(token);
};

/**
 * 检查令牌是否过期
 */
export const isTokenExpired = (token: string): boolean => {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    const currentTime = Date.now() / 1000;
    return payload.exp < currentTime;
  } catch (error) {
    return true;
  }
};

/**
 * 获取令牌过期时间
 */
export const getTokenExpiration = (token: string): number | null => {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    return payload.exp * 1000; // 转换为毫秒
  } catch (error) {
    return null;
  }
};

/**
 * 保存登录信息
 */
export const saveLoginInfo = (loginResponse: {
  token: string;
  refreshToken: string;
  user: any;
  expiresIn: number;
}): void => {
  setToken(loginResponse.token);
  setRefreshToken(loginResponse.refreshToken);
  setUserInfo(loginResponse.user);
};

/**
 * 登出
 */
export const logout = (): void => {
  clearAuth();
  // 跳转到登录页
  window.location.href = '/user/login';
};
