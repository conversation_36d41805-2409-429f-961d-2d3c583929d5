((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_ant-design-pro"] || []).push([
        ['src/.umi/plugin-openapi/openapi.tsx'],
{ "src/.umi/plugin-openapi/openapi.tsx": function (module, exports, __mako_require__){
// @ts-nocheck
// This file is generated by Umi automatically
// DO NOT CHANGE IT MANUALLY!
// This file is generated by Umi automatically
// DO NOT CHANGE IT MANUALLY!
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = __mako_require__("node_modules/react/index.js");
var _swaggeruidist = __mako_require__("node_modules/swagger-ui-dist/index.js");
"";
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const App = ()=>{
    _s();
    const [value, setValue] = (0, _react.useState)("openapi");
    (0, _react.useEffect)(()=>{
        (0, _swaggeruidist.SwaggerUIBundle)({
            url: `/umi-plugins_${value}.json`,
            dom_id: '#swagger-ui'
        });
    }, [
        value
    ]);
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        style: {
            padding: 24
        },
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("select", {
                style: {
                    position: "fixed",
                    right: "16px",
                    top: "8px"
                },
                onChange: (e)=>setValue(e.target.value),
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("option", {
                        value: "openapi",
                        children: "openapi"
                    }, void 0, false, {
                        fileName: "src/.umi/plugin-openapi/openapi.tsx",
                        lineNumber: 32,
                        columnNumber: 15
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("option", {
                        value: "swagger",
                        children: "swagger"
                    }, void 0, false, {
                        fileName: "src/.umi/plugin-openapi/openapi.tsx",
                        lineNumber: 33,
                        columnNumber: 1
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/.umi/plugin-openapi/openapi.tsx",
                lineNumber: 24,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                id: "swagger-ui"
            }, void 0, false, {
                fileName: "src/.umi/plugin-openapi/openapi.tsx",
                lineNumber: 35,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/.umi/plugin-openapi/openapi.tsx",
        lineNumber: 19,
        columnNumber: 11
    }, this);
};
_s(App, "78J76v3s+9vmxcVwMv8h0H2fvwk=");
_c = App;
var _default = App;
var _c;
$RefreshReg$(_c, "App");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=src__umi_plugin-openapi_openapi_tsx-async.js.map